[{"source": "QuickBooks_Raw", "type": "sqldb", "source_type": "big<PERSON>y", "sections": ["tax_rate", "sales_receipt_line", "bill_line", "bill", "purchase", "estimate_line_bundle", "budget", "purchase_tax_line", "vendor", "purchase_order_line", "term", "payment", "bill_payment_line", "tax_rate_detail", "currency", "sales_receipt_tax_line", "vendor_credit", "refund_receipt", "address", "payment_line", "refund_receipt_tax_line", "invoice", "credit_memo_line", "vendor_credit_line", "bill_linked_txn", "bundle_item", "journal_entry_tax_line", "purchase_order_tax_line", "credit_card_payment_txn", "item", "bill_payment", "bundle", "employee", "department", "credit_memo_line_bundle", "tax_agency", "sales_receipt", "time_activity", "refund_receipt_line", "estimate_tax_line", "sales_receipt_line_bundle", "purchase_line", "purchase_order_linked_txn", "payment_method", "budget_detail", "journal_entry_line", "tax_code", "invoice_tax_line", "deposit_line", "journal_entry", "credit_memo", "account", "estimate_line", "estimate", "invoice_linked_txn", "transfer", "deposit", "refund_receipt_line_bundle", "class", "estimate_linked_txn", "invoice_line_bundle", "purchase_order", "invoice_line", "customer"], "overview": "This data source serves as the centralized backbone for the company’s financial, sales, and operational management, encompassing a comprehensive range of core business records—including customers, vendors, employees, invoices, bills, products and services, accounts, tax structures, payment methods, budgets, and various transaction types. Its primary function is to enable end-to-end tracking and management of business transactions—such as sales, purchases, invoicing, payments, credits, refunds, and tax compliance—while supporting robust financial reporting, analytics, and regulatory obligations. This unified platform underpins key business objectives around revenue growth, operational efficiency, and customer and vendor management.\n\nKey tables and their roles include:\n- **Customer & Vendor**: Store detailed profiles, balances, and address references for buyers and suppliers, supporting both sales and purchasing workflows.\n- **Invoice & Invoice Line**: Capture issued invoices and itemized billing details, linking to customers, products, and tax breakdowns for precise revenue and receivable tracking.\n- **Bill, Bill Line, and Bill Payment**: Manage vendor invoices, line-level expense details, and payment allocations to maintain visibility into accounts payable.\n- **Purchase, Purchase Order, and Related Lines**: Track immediate and future vendor purchases, order fulfillment, and expense categorization.\n- **Account**: Maintains granular financial account structures, classifications, and balances, essential for accurate bookkeeping and reporting.\n- **Item & Bundle**: Catalog products, services, and bundled offerings, supporting flexible sales and purchasing scenarios.\n- **Tax-related tables (tax_rate, tax_code, tax_agency, tax_rate_detail, invoice_tax_line, and others)**: Define, map, and apply tax rates, codes, and agencies across all transaction types to ensure accurate calculation and compliance.\n- **Address**: Standardizes address information for customers, vendors, and transactions, promoting data consistency.\n- **Reference tables (currency, payment_method, term, class, department)**: Store standardized business configuration data, enabling multi-currency support, payment terms, organizational segmentation, and reporting.\n- **Budget & Budget Detail**: Support financial planning and variance analysis by capturing budgeted amounts at multiple levels.\n- **Employee & Time Activity**: Record employee information and activities for workforce management and potential payroll integration.\n- **Journal Entry & Line**: Facilitate manual accounting entries for adjustments and complex transactions.\n- **Refunds, Credits, and Receipts**: Handle customer returns, vendor credits, and various receipt types to ensure comprehensive coverage of all financial flows.\n- **Linking and Tax Line Tables**: Ensure traceability between related transactions (e.g., invoice_linked_txn, bill_linked_txn) and provide itemized tax reporting at the transaction line level.\n\nThe ER diagram illustrates a normalized, relational structure with well-defined linkages between customers, vendors, employees, invoices, bills, products, line items, tax calculations, and addresses. These relationships ensure transaction traceability, regulatory compliance, and data integrity. The design’s extensiveness allows for detailed tracking of all financial and operational activities, supporting both daily business execution and strategic decision-making.\n\nIn summary, this data source is architected to deliver a single source of truth for the company’s financial operations, customer and vendor management, employee tracking, and regulatory reporting, offering a robust foundation for performance measurement, KPI optimization, and long-term business growth.", "ER_Diagram": {"entities": {"account": {"description": "Stores financial accounts, including type, classification, balances, and timestamps. Each account has a unique id. No foreign keys.", "primary_key": ["id"]}, "address": {"description": "Stores address details (street, city, postal code, country, province). Each address has a unique id. No foreign keys.", "primary_key": ["id"]}, "bill": {"description": "Stores vendor bills. One row per bill.", "primary_key": ["id"]}, "bill_line": {"description": "Contains individual expenses or splits under each bill.", "primary_key": ["bill_id", "index"]}, "bill_linked_txn": {"description": "Acts as a bridge that connects each Bill to its source or related transactions, enabling full traceability and reconciliation in purchasing workflows.", "primary_key": ["bill_id", "index"]}, "budget": {"description": "Represents budget entries.", "primary_key": ["id"]}, "budget_detail": {"description": "Contains line items of each budget records.", "primary_key": ["budget_id", "index"]}, "bundle": {"description": "Represents container that has multiple items.", "primary_key": ["id"]}, "bundle_item": {"description": "An intermediate table that maintains relationship between bundle and item.", "primary_key": ["bundle_id", "item_id"]}, "class": {"description": "Stores class entities, each with unique id, names, status, and timestamps. Standalone entity.", "primary_key": ["id"]}, "credit_memo": {"description": "Contains transactions that represents money you owe back to a customer", "primary_key": ["id"]}, "credit_memo_line": {"description": "Contains line items describing what is being credited per each credit memo", "primary_key": ["credit_memo_id", "index"]}, "credit_memo_line_bundle": {"description": "Is a table that links each line back to the original bundle of items.", "primary_key": ["credit_memo_id", "credit_memo_line_index"]}, "credit_card_payment_txn": {"description": "Stores transactions done via credit card.", "primary_key": ["id"]}, "currency": {"description": "Stores supported currencies. Only CAD exists in sample. Reference table.", "primary_key": ["id"]}, "customer": {"description": "Stores customer and company info, balances, contact, and address references. No explicit relationships but links to address and invoice.", "primary_key": ["id"]}, "department": {"description": "Stores the departments", "primary_key": ["id"]}, "deposit": {"description": "Stores the deposit transactions", "primary_key": ["id"]}, "deposit_line": {"description": "Contains the line items under each deposit, each line shows where the deposited money came from: a payment, a sales receipt, or direct entry.", "primary_key": ["deposit_id", "index"]}, "employee": {"description": "Stores the employees or staff of the organization", "primary_key": ["id"]}, "estimate": {"description": "Stores the main record for customer estimates or quotes, including customer, shipping, pricing terms, and document-level settings before conversion to invoice or sales receipt.", "primary_key": ["id"]}, "estimate_line": {"description": "Contains individual line items within an estimate, representing proposed products or services with associated amounts, quantities, discounts, and classifications.", "primary_key": ["estimate_id", "index"]}, "estimate_tax_line": {"description": "Breaks down tax calculations for each estimate, showing taxable amounts and associated tax rates for regulatory reporting and customer display.", "primary_key": ["estimate_id", "index"]}, "estimate_line_bundle": {"description": "Links each estimate line back to a bundle definition, tracking which items were grouped together in a product or service package.", "primary_key": ["estimate_id", "estimate_line_index"]}, "estimate_linked_txn": {"description": "Links each estimate with invoice records.", "primary_key": ["estimate_id", "index"]}, "journal_entry": {"description": "Captures manual or system-generated accounting entries that affect multiple accounts, used when transactions don't fall neatly into standard forms like invoices or bills.", "primary_key": ["id"]}, "journal_entry_line": {"description": "Stores line level credits and debits under each journal entry", "primary_key": ["journal_entry_id", "index"]}, "invoice": {"description": "Stores invoices, billing and shipping address references, financial details, and customer links. Related to account and customer.", "primary_key": ["id"]}, "invoice_line": {"description": "Stores details of invoice line items. Linked to invoice via invoice_id.", "primary_key": ["invoice_id", "index"]}, "invoice_line_bundle": {"description": "Is a supporting table that connects a single invoice_line to the bundle it came from.", "primary_key": ["invoice_id", "invoide_line_index"]}, "invoice_linked_txn": {"description": "Is a linking or association table that tracks any transactions related to or derived from an invoice.", "primary_key": ["invoice_id", "index"]}, "invoice_tax_line": {"description": "Stores invoice tax breakdowns per tax rate. Linked to invoice (invoice_id) and tax_rate (tax_rate_id).", "primary_key": ["invoice_id", "index"]}, "item": {"description": "Product and service catalog. Each item has a unique id. No foreign keys.", "primary_key": ["id"]}, "payment": {"description": "Represents customer payments in the Accounts Receivable (A/R) process.", "primary_key": ["id"]}, "payment_line": {"description": "For each payment transaction, the details showing which individual invoices were paid and how much was applied.", "primary_key": ["payment_id", "index"]}, "payment_method": {"description": "Stores available payment methods. Standalone reference table.", "primary_key": ["id"]}, "purchase": {"description": "Has one record per each purchase transaction from vendor", "primary_key": ["id"]}, "purchase_order": {"description": "Has one record per each purchase order", "primary_key": ["id"]}, "purchase_order_line": {"description": "Multiple line items or splits of each purchase order", "primary_key": ["purchase_order_id", "index"]}, "purchase_tax_line": {"description": "Stores a breakdown of tax of each purchase transaction.", "primary_key": ["purchase_id", "index"]}, "purchase_order_tax_line": {"description": "Stores a breakdown of expected amounts of tax for a purchase order.", "primary_key": ["purchase_order_id", "index"]}, "purchase_order_linked_txn": {"description": "Links purchase orders to transactions like bills or purchases that fulfill or reference them, enabling traceability in procurement and matching workflows.", "primary_key": ["purchase_order_id", "index"]}, "refund_receipt": {"description": "Represents a top-level transaction showing that a vendor owes you money or you have a credit balance with them.", "primary_key": ["id"]}, "refund_receipt_line": {"description": "Line-level items or accounts refunded to the customer as part of a refund receipt.", "primary_key": ["id"]}, "refund_receipt_tax_line": {"description": "Tax details applied to a refund receipt, broken down by tax rate.", "primary_key": ["refund_receipt_id", "index"]}, "refund_receipt_line_bundle": {"description": "Maps each refund line to its originating bundle for grouped item returns.", "primary_key": ["refund_receipt_id", "refund_receipt_line_index"]}, "sales_receipt": {"description": "Stores the main record of a customer sale that is immediately paid, including payment method, customer info, and transaction-level totals.", "primary_key": ["id"]}, "sales_receipt_line": {"description": "Contains detailed line items for each sales receipt, representing products or services sold, with pricing, quantity, tax, and discount breakdowns.", "primary_key": ["sales_receipt_id", "index"]}, "sales_receipt_tax_line": {"description": "Captures the tax breakdown for each sales receipt, including tax rate, taxable amount, and total tax charged per rate.", "primary_key": ["sales_receipt_id", "index"]}, "sales_receipt_line_bundle": {"description": "Links individual sales receipt lines to their corresponding product bundles, preserving group structure and metadata like quantity and discount for each bundled item.", "primary_key": ["sales_receipt_id", "sales_receipt_line_index"]}, "vendor": {"description": "Represents a supplier", "primary_key": ["id"]}, "vendor_credit": {"description": "Represents a top-level transaction showing that a vendor owes you money or you have a credit balance with them.", "primary_key": ["id"]}, "vendor_credit_line": {"description": "Contains each line of the vendor credit explains what the credit was for — typically mirroring the structure of a bill (items, accounts, classes, etc.)", "primary_key": ["vendor_credit_id", "index"]}, "tax_agency": {"description": "Stores tax agencies; referenced by tax_rate. Standalone except as parent in tax_rate.", "primary_key": ["id"]}, "tax_code": {"description": "Stores tax codes. Referenced by tax_rate_detail.", "primary_key": ["id"]}, "tax_rate": {"description": "Stores tax rate definitions. Linked to tax_agency (tax_agency_id) and referenced by tax_rate_detail and invoice_tax_line.", "primary_key": ["id"]}, "tax_rate_detail": {"description": "Links tax codes to tax rates, specifying type and order. Referenced by tax_code and tax_rate.", "primary_key": ["tax_code_id", "tax_rate_id"]}, "term": {"description": "Stores payment terms. Standalone reference table.", "primary_key": ["id"]}, "time_activity": {"description": "Captures timesheet entries or billable hours — whether by employees, vendors, or contractors. Represents a single unit of time worked, typically one line on a timesheet and stores all relevant details about who worked, for whom, how long, when, and why.", "primary_key": ["id"]}, "transfer": {"description": "Captures the movements between the accounts", "primary_key": ["id"]}}, "relationships": [{"from_entity": "account", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Each account is denominated in a specific currency for financial reporting."}, {"from_entity": "account", "to_entity": "account", "type": "many-to-one", "from_column": "parent_account_id", "to_column": "id", "reasoning": "Accounts can be nested using a parent-child structure for chart of accounts hierarchy."}, {"from_entity": "account", "to_entity": "tax_code", "type": "many-to-one", "from_column": "default_tax_code_id", "to_column": "id", "reasoning": "Each account can optionally define a default tax code for transactions using it."}, {"from_entity": "item", "to_entity": "account", "type": "many-to-one", "from_column": "expense_account_id", "to_column": "id", "reasoning": "Each item specifies an expense account used when purchased or used internally."}, {"from_entity": "item", "to_entity": "account", "type": "many-to-one", "from_column": "income_account_id", "to_column": "id", "reasoning": "Each item specifies an income account for recognizing revenue when sold."}, {"from_entity": "item", "to_entity": "account", "type": "many-to-one", "from_column": "asset_account_id", "to_column": "id", "reasoning": "Inventory-type items use an asset account to track stock value on the balance sheet."}, {"from_entity": "item", "to_entity": "tax_code", "type": "many-to-one", "from_column": "purchase_tax_code_id", "to_column": "id", "reasoning": "Each item can define a default tax code used when purchased."}, {"from_entity": "item", "to_entity": "tax_code", "type": "many-to-one", "from_column": "sales_tax_code_id", "to_column": "id", "reasoning": "Each item can define a default tax code used when sold."}, {"from_entity": "item", "to_entity": "item", "type": "many-to-one", "from_column": "parent_item_id", "to_column": "id", "reasoning": "Items may belong to a parent item to support hierarchical item structures or bundles."}, {"from_entity": "bundle_item", "to_entity": "item", "type": "many-to-one", "from_column": "item_id", "to_column": "id", "reasoning": "Item side of the many-to-many relationship between item and bundle."}, {"from_entity": "bundle_item", "to_entity": "bundle", "type": "many-to-one", "from_column": "bundle_id", "to_column": "id", "reasoning": "Bundle side of the many-to-many relationship between item and bundle."}, {"from_entity": "credit_card_payment_txn", "to_entity": "account", "type": "many-to-one", "from_column": "cc_account_id", "to_column": "id", "reasoning": "Specifies the credit card account used to make the payment transaction."}, {"from_entity": "credit_card_payment_txn", "to_entity": "account", "type": "many-to-one", "from_column": "bank_account_id", "to_column": "id", "reasoning": "Indicates the bank account from which funds are drawn to pay the credit card."}, {"from_entity": "credit_card_payment_txn", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Records the currency in which the credit card payment transaction is denominated."}, {"from_entity": "invoice", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Multiple invoices can belong to the same customer. This supports customer billing and reporting."}, {"from_entity": "invoice_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each invoice_line is part of a single invoice, but each invoice can have multiple lines."}, {"from_entity": "invoice_tax_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each tax line belongs to a specific invoice, supporting detailed tax tracking per invoice."}, {"from_entity": "invoice_tax_line", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "Each invoice tax line references a tax_rate, allowing tracking of which tax was applied."}, {"from_entity": "tax_rate", "to_entity": "tax_agency", "type": "many-to-one", "from_column": "tax_agency_id", "to_column": "id", "reasoning": "Each tax_rate is managed by a single tax_agency, but each agency can have many tax rates."}, {"from_entity": "tax_rate_detail", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "tax_rate_detail links a tax_code_id to a tax_rate. This allows multiple details per rate or code."}, {"from_entity": "tax_rate_detail", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "tax_rate_detail references both tax_code and tax_rate, supporting mapping between codes and rates."}, {"from_entity": "tax_rate_detail", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Each tax_rate_detail references a tax_code, allowing for taxonomy and grouping of tax rules."}, {"from_entity": "estimate_line", "to_entity": "estimate", "type": "many-to-one", "from_column": "estimate_id", "to_column": "id", "reasoning": "Each line item is part of a single estimate document."}, {"from_entity": "estimate_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "sales_item_tax_code_id", "to_column": "id", "reasoning": "Defines the tax code applied to the item or service in the estimate line."}, {"from_entity": "estimate_line", "to_entity": "class", "type": "many-to-one", "from_column": "sales_item_class_id", "to_column": "id", "reasoning": "Assigns the line item to a business class for reporting."}, {"from_entity": "estimate_line", "to_entity": "item", "type": "many-to-one", "from_column": "sales_item_item_id", "to_column": "id", "reasoning": "References the product or service offered in the estimate."}, {"from_entity": "estimate_line", "to_entity": "account", "type": "many-to-one", "from_column": "sales_item_account_id", "to_column": "id", "reasoning": "Specifies the revenue account that would be affected if this line is invoiced."}, {"from_entity": "estimate_line", "to_entity": "item", "type": "many-to-one", "from_column": "bundle_id", "to_column": "id", "reasoning": "Links to a bundle if this line is part of a product/service bundle."}, {"from_entity": "estimate_line", "to_entity": "class", "type": "many-to-one", "from_column": "discount_class_id", "to_column": "id", "reasoning": "Assigns a business class to the discount on this line."}, {"from_entity": "estimate_line", "to_entity": "account", "type": "many-to-one", "from_column": "discount_account_id", "to_column": "id", "reasoning": "Specifies the account used for any discount on the line."}, {"from_entity": "estimate_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "discount_tax_code_id", "to_column": "id", "reasoning": "Tax code applicable to the discount portion of the estimate line."}, {"from_entity": "estimate_line", "to_entity": "item", "type": "many-to-one", "from_column": "sub_total_item_id", "to_column": "id", "reasoning": "References a subtotal item if this line is a subtotal of multiple items."}, {"from_entity": "estimate_line_bundle", "to_entity": "estimate", "type": "many-to-one", "from_column": "estimate_id", "to_column": "id", "reasoning": "Each bundle line is associated with a specific estimate."}, {"from_entity": "estimate_line_bundle", "to_entity": "estimate_line", "type": "many-to-one", "from_column": "estimate_line_index", "to_column": "index", "reasoning": "Links the bundle component to the parent estimate line by index."}, {"from_entity": "estimate_line_bundle", "to_entity": "item", "type": "many-to-one", "from_column": "item_id", "to_column": "id", "reasoning": "Specifies the bundled product or service."}, {"from_entity": "estimate_line_bundle", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Classifies the bundle line for reporting."}, {"from_entity": "estimate_line_bundle", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Account used for the bundle line entry."}, {"from_entity": "estimate_line_bundle", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Tax code applied to the bundle line."}, {"from_entity": "estimate", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency in which the estimate is issued."}, {"from_entity": "estimate", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Specifies the shipping address for the estimate."}, {"from_entity": "estimate", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Specifies the billing address for the estimate."}, {"from_entity": "estimate", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Links the estimate to a business department."}, {"from_entity": "estimate", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Indicates the customer to whom the estimate is provided."}, {"from_entity": "estimate", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Categorizes the estimate for internal segmentation."}, {"from_entity": "estimate", "to_entity": "term", "type": "many-to-one", "from_column": "sales_term_id", "to_column": "id", "reasoning": "Specifies the terms under which the estimate is issued."}, {"from_entity": "estimate_tax_line", "to_entity": "estimate", "type": "many-to-one", "from_column": "estimate_id", "to_column": "id", "reasoning": "Tax lines provide tax breakdowns for a specific estimate."}, {"from_entity": "estimate_tax_line", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "Each tax line references the rate used to calculate the tax for the estimate."}, {"from_entity": "estimate_linked_txn", "to_entity": "estimate", "type": "many-to-one", "from_column": "estimate_id", "to_column": "id", "reasoning": "Links the record to the original estimate involved in this transaction."}, {"from_entity": "estimate_linked_txn", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Connects the estimate to an invoice generated from it."}, {"from_entity": "invoice", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency in which the invoice is issued."}, {"from_entity": "invoice", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Assigns the invoice to a specific department for reporting and tracking."}, {"from_entity": "invoice", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Specifies the shipping address for the goods or services invoiced."}, {"from_entity": "invoice", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Specifies the billing address for the invoice recipient."}, {"from_entity": "invoice", "to_entity": "term", "type": "many-to-one", "from_column": "sales_term_id", "to_column": "id", "reasoning": "Defines the payment terms under which the invoice is issued."}, {"from_entity": "invoice", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Each invoice is issued to a specific customer being billed."}, {"from_entity": "invoice", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Assigns a class for segmenting invoice activity in financial reporting."}, {"from_entity": "invoice", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Specifies the tax code that applies to this invoice at the header level."}, {"from_entity": "invoice", "to_entity": "account", "type": "many-to-one", "from_column": "deposit_to_account_id", "to_column": "id", "reasoning": "Specifies the account where payment for the invoice will be deposited."}, {"from_entity": "invoice_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each invoice line belongs to a specific invoice header."}, {"from_entity": "invoice_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "sales_item_tax_code_id", "to_column": "id", "reasoning": "Defines the tax code applied to this specific invoice line."}, {"from_entity": "invoice_line", "to_entity": "class", "type": "many-to-one", "from_column": "sales_item_class_id", "to_column": "id", "reasoning": "Classifies the invoice line under a specific class for reporting."}, {"from_entity": "invoice_line", "to_entity": "item", "type": "many-to-one", "from_column": "sales_item_item_id", "to_column": "id", "reasoning": "Identifies the product or service being sold on the line."}, {"from_entity": "invoice_line", "to_entity": "account", "type": "many-to-one", "from_column": "sales_item_account_id", "to_column": "id", "reasoning": "Specifies the income or revenue account for the invoice line."}, {"from_entity": "invoice_line", "to_entity": "item", "type": "many-to-one", "from_column": "bundle_id", "to_column": "id", "reasoning": "Links the line to a parent bundle item, if it's part of a grouped product."}, {"from_entity": "invoice_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "discount_tax_code_id", "to_column": "id", "reasoning": "Defines the tax code associated with the discount on this line."}, {"from_entity": "invoice_line", "to_entity": "class", "type": "many-to-one", "from_column": "discount_class_id", "to_column": "id", "reasoning": "Assigns a class to the discount for better tracking."}, {"from_entity": "invoice_line", "to_entity": "account", "type": "many-to-one", "from_column": "discount_account_id", "to_column": "id", "reasoning": "Account used for recording the discount applied to this line."}, {"from_entity": "invoice_line", "to_entity": "item", "type": "many-to-one", "from_column": "sub_total_item_id", "to_column": "id", "reasoning": "Used when the line is a subtotal of multiple items, referencing a subtotal item."}, {"from_entity": "invoice_line_bundle", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each bundle line is tied to a specific invoice header."}, {"from_entity": "invoice_line_bundle", "to_entity": "invoice_line", "type": "many-to-one", "from_column": "invoice_line_index", "to_column": "index", "reasoning": "Links a bundle line back to the main invoice line it expands from."}, {"from_entity": "invoice_line_bundle", "to_entity": "item", "type": "many-to-one", "from_column": "item_id", "to_column": "id", "reasoning": "Specifies the individual item within the bundle."}, {"from_entity": "invoice_line_bundle", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Assigns a class to the bundle component for category tracking."}, {"from_entity": "invoice_line_bundle", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Income or expense account for the bundled component."}, {"from_entity": "invoice_line_bundle", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Tax code applied to the bundle component item."}, {"from_entity": "invoice_tax_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each tax line contributes to the tax total of one invoice."}, {"from_entity": "invoice_tax_line", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "Specifies the tax rate used for calculating this portion of tax."}, {"from_entity": "invoice_linked_txn", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Links the record to the invoice being referenced or affected by the transaction."}, {"from_entity": "invoice_linked_txn", "to_entity": "payment", "type": "many-to-one", "from_column": "payment_id", "to_column": "id", "reasoning": "Indicates that this payment was applied to the associated invoice."}, {"from_entity": "invoice_linked_txn", "to_entity": "estimate", "type": "many-to-one", "from_column": "estimate_id", "to_column": "id", "reasoning": "Indicates the invoice was created from a prior estimate."}, {"from_entity": "invoice_linked_txn", "to_entity": "time_activity", "type": "many-to-one", "from_column": "time_activity_id", "to_column": "id", "reasoning": "Links billable time activities that were added to the invoice."}, {"from_entity": "payment", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Identifies the customer who made the payment."}, {"from_entity": "payment", "to_entity": "account", "type": "many-to-one", "from_column": "receivable_account_id", "to_column": "id", "reasoning": "Specifies the A/R account affected by this payment."}, {"from_entity": "payment", "to_entity": "account", "type": "many-to-one", "from_column": "deposit_to_account_id", "to_column": "id", "reasoning": "Indicates where the payment funds were deposited."}, {"from_entity": "payment", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency of the payment."}, {"from_entity": "payment", "to_entity": "payment_method", "type": "many-to-one", "from_column": "payment_method_id", "to_column": "id", "reasoning": "Indicates the method used for the payment (e.g., credit card, ACH)."}, {"from_entity": "payment_line", "to_entity": "payment", "type": "many-to-one", "from_column": "payment_id", "to_column": "id", "reasoning": "Each line records how a portion of the payment was applied."}, {"from_entity": "payment_line", "to_entity": "journal_entry", "type": "many-to-one", "from_column": "journal_entry_id", "to_column": "id", "reasoning": "Specifies the journal entry that reflects the accounting for this payment line."}, {"from_entity": "payment_line", "to_entity": "credit_memo", "type": "many-to-one", "from_column": "credit_memo_id", "to_column": "id", "reasoning": "Connects to a credit memo applied as part of the payment."}, {"from_entity": "payment_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Indicates the invoice that this portion of the payment is applied to."}, {"from_entity": "payment_line", "to_entity": "deposit", "type": "many-to-one", "from_column": "deposit_id", "to_column": "id", "reasoning": "Links to a deposit transaction that includes this payment line."}, {"from_entity": "credit_memo", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency used to issue the credit memo."}, {"from_entity": "credit_memo", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Indicates the shipping address associated with the credit memo."}, {"from_entity": "credit_memo", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Indicates the billing address associated with the credit memo."}, {"from_entity": "credit_memo", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Specifies the customer to whom the credit memo is issued."}, {"from_entity": "credit_memo", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Assigns the credit memo to a specific department."}, {"from_entity": "credit_memo", "to_entity": "term", "type": "many-to-one", "from_column": "sales_term_id", "to_column": "id", "reasoning": "Defines the payment terms for the credit memo."}, {"from_entity": "credit_memo", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Categorizes the credit memo under a specific class."}, {"from_entity": "credit_memo", "to_entity": "payment_method", "type": "many-to-one", "from_column": "payment_method_id", "to_column": "id", "reasoning": "Specifies the method of payment or refund related to the credit memo."}, {"from_entity": "credit_memo_line", "to_entity": "credit_memo", "type": "many-to-one", "from_column": "credit_memo_id", "to_column": "id", "reasoning": "Each line represents an item or service being credited in a specific credit memo."}, {"from_entity": "credit_memo_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "sales_item_tax_code_id", "to_column": "id", "reasoning": "Defines the tax code applied to the credited item or service."}, {"from_entity": "credit_memo_line", "to_entity": "class", "type": "many-to-one", "from_column": "sales_item_class_id", "to_column": "id", "reasoning": "Assigns the line item to a reporting class."}, {"from_entity": "credit_memo_line", "to_entity": "item", "type": "many-to-one", "from_column": "sales_item_item_id", "to_column": "id", "reasoning": "References the product or service being credited."}, {"from_entity": "credit_memo_line", "to_entity": "account", "type": "many-to-one", "from_column": "sales_item_account_id", "to_column": "id", "reasoning": "Specifies the revenue or expense account affected by the credit."}, {"from_entity": "credit_memo_line", "to_entity": "class", "type": "many-to-one", "from_column": "discount_class_id", "to_column": "id", "reasoning": "Assigns a class to the discount applied on this line."}, {"from_entity": "credit_memo_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "discount_tax_code_id", "to_column": "id", "reasoning": "Specifies the tax code related to the discount."}, {"from_entity": "credit_memo_line", "to_entity": "account", "type": "many-to-one", "from_column": "discount_account_id", "to_column": "id", "reasoning": "Account affected by any discount recorded on this line."}, {"from_entity": "credit_memo_line", "to_entity": "item", "type": "many-to-one", "from_column": "bundle_id", "to_column": "id", "reasoning": "Links the line to the bundle it was part of, if applicable."}, {"from_entity": "credit_memo_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "description_tax_code_id", "to_column": "id", "reasoning": "Applies a tax code to the description or note line, if needed."}, {"from_entity": "credit_memo_line", "to_entity": "item", "type": "many-to-one", "from_column": "sub_total_item_id", "to_column": "id", "reasoning": "Used if the line represents a subtotal for a group of items."}, {"from_entity": "credit_memo_line_bundle", "to_entity": "credit_memo", "type": "many-to-one", "from_column": "credit_memo_id", "to_column": "id", "reasoning": "Each bundle line is associated with a specific credit memo."}, {"from_entity": "credit_memo_line_bundle", "to_entity": "estimate_line", "type": "many-to-one", "from_column": "credit_memo_line_index", "to_column": "index", "reasoning": "Links bundle detail lines to the corresponding main line by index."}, {"from_entity": "credit_memo_line_bundle", "to_entity": "item", "type": "many-to-one", "from_column": "item_id", "to_column": "id", "reasoning": "References the component item in the bundle."}, {"from_entity": "credit_memo_line_bundle", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Assigns a reporting class to the bundle component."}, {"from_entity": "credit_memo_line_bundle", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Specifies the account used for the bundle component line."}, {"from_entity": "credit_memo_line_bundle", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Defines the tax code used for the bundle component."}, {"from_entity": "deposit", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Assigns the deposit to a department for internal tracking."}, {"from_entity": "deposit", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency in which the deposit was made."}, {"from_entity": "deposit", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Defines the account receiving the deposited funds."}, {"from_entity": "deposit", "to_entity": "account", "type": "many-to-one", "from_column": "cash_back_account_id", "to_column": "id", "reasoning": "Used when part of the deposit is returned as cash back."}, {"from_entity": "deposit_line", "to_entity": "deposit", "type": "many-to-one", "from_column": "deposit_id", "to_column": "id", "reasoning": "Each deposit line belongs to a specific deposit record."}, {"from_entity": "deposit_line", "to_entity": "class", "type": "many-to-one", "from_column": "deposit_class_id", "to_column": "id", "reasoning": "Assigns the deposit line to a class for classification."}, {"from_entity": "deposit_line", "to_entity": "payment_method", "type": "many-to-one", "from_column": "deposit_payment_method_id", "to_column": "id", "reasoning": "Indicates how this part of the deposit was received."}, {"from_entity": "deposit_line", "to_entity": "account", "type": "many-to-one", "from_column": "deposit_account_id", "to_column": "id", "reasoning": "Specifies the account credited by this deposit line."}, {"from_entity": "deposit_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "deposit_tax_code_id", "to_column": "id", "reasoning": "Specifies the tax code that applies to this deposit line."}, {"from_entity": "deposit_line", "to_entity": "customer", "type": "many-to-one", "from_column": "deposit_customer_id", "to_column": "id", "reasoning": "Associates the deposit line with a specific customer."}, {"from_entity": "transfer", "to_entity": "account", "type": "many-to-one", "from_column": "to_account_id", "to_column": "id", "reasoning": "Specifies the destination account receiving the funds."}, {"from_entity": "transfer", "to_entity": "account", "type": "many-to-one", "from_column": "from_account_id", "to_column": "id", "reasoning": "Indicates the source account from which funds are transferred."}, {"from_entity": "transfer", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency in which the transfer is recorded."}, {"from_entity": "bill", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Associates the bill with a department for reporting and filtering."}, {"from_entity": "bill", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency in which the bill is issued."}, {"from_entity": "bill", "to_entity": "account", "type": "many-to-one", "from_column": "payable_account_id", "to_column": "id", "reasoning": "Indicates the liability account for the bill."}, {"from_entity": "bill", "to_entity": "term", "type": "many-to-one", "from_column": "sales_term_id", "to_column": "id", "reasoning": "Defines the payment terms for the bill."}, {"from_entity": "bill", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "Specifies the vendor who issued the bill."}, {"from_entity": "bill_line", "to_entity": "bill", "type": "many-to-one", "from_column": "bill_id", "to_column": "id", "reasoning": "Each line belongs to a single bill and stores individual expense details."}, {"from_entity": "bill_line", "to_entity": "account", "type": "many-to-one", "from_column": "account_expense_account_id", "to_column": "id", "reasoning": "Defines the account charged for the expense in this bill line."}, {"from_entity": "bill_line", "to_entity": "class", "type": "many-to-one", "from_column": "account_expense_class_id", "to_column": "id", "reasoning": "Categorizes the expense by class for reporting purposes."}, {"from_entity": "bill_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "account_expense_tax_code", "to_column": "id", "reasoning": "Specifies the tax treatment of the expense line."}, {"from_entity": "bill_line", "to_entity": "customer", "type": "many-to-one", "from_column": "account_expense_customer_id", "to_column": "id", "reasoning": "Associates the expense with a customer, often for billable jobs."}, {"from_entity": "bill_line", "to_entity": "item", "type": "many-to-one", "from_column": "item_expense_item_id", "to_column": "id", "reasoning": "References the product or service tied to this expense item."}, {"from_entity": "bill_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "item_expense_tax_code_id", "to_column": "id", "reasoning": "Applies tax rules to item-based expenses in this line."}, {"from_entity": "bill_line", "to_entity": "class", "type": "many-to-one", "from_column": "item_expense_class_id", "to_column": "id", "reasoning": "Used for tracking item-level expenses by business class or department."}, {"from_entity": "bill_line", "to_entity": "customer", "type": "many-to-one", "from_column": "item_expense_customer_id", "to_column": "id", "reasoning": "Connects the item-level expense to a customer for job costing or billing."}, {"from_entity": "bill_linked_txn", "to_entity": "bill", "type": "many-to-one", "from_column": "bill_id", "to_column": "id", "reasoning": "Links a bill to related transactions such as payments."}, {"from_entity": "bill_linked_txn", "to_entity": "bill_payment", "type": "many-to-one", "from_column": "bill_payment_id", "to_column": "id", "reasoning": "Connects the bill to the payment made against it."}, {"from_entity": "bill_payment", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Associates the payment with a specific department."}, {"from_entity": "bill_payment", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Currency used in the payment transaction."}, {"from_entity": "bill_payment", "to_entity": "account", "type": "many-to-one", "from_column": "credit_card_account_id", "to_column": "id", "reasoning": "Credit card account used for the bill payment."}, {"from_entity": "bill_payment", "to_entity": "account", "type": "many-to-one", "from_column": "cash_back_account_id", "to_column": "id", "reasoning": "Account where cash back is recorded from payment."}, {"from_entity": "bill_payment", "to_entity": "account", "type": "many-to-one", "from_column": "payable_account_id", "to_column": "id", "reasoning": "Payable account associated with the bill payment."}, {"from_entity": "bill_payment", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "V<PERSON>or to whom the bill payment is made."}, {"from_entity": "bill_payment_line", "to_entity": "bill_payment", "type": "many-to-one", "from_column": "bill_payment_id", "to_column": "id", "reasoning": "Each line belongs to a specific bill payment record."}, {"from_entity": "bill_payment_line", "to_entity": "bill", "type": "many-to-one", "from_column": "bill_id", "to_column": "id", "reasoning": "Represents a portion of the total payment applied to a bill."}, {"from_entity": "bill_payment_line", "to_entity": "deposit", "type": "many-to-one", "from_column": "deposit_id", "to_column": "id", "reasoning": "Payment may be funded from an existing deposit record."}, {"from_entity": "bill_payment_line", "to_entity": "journal_entry", "type": "many-to-one", "from_column": "journal_entry_id", "to_column": "id", "reasoning": "Payment line may be linked to an adjusting journal entry."}, {"from_entity": "bill_payment_line", "to_entity": "bill_payment_line", "type": "many-to-one", "from_column": "linked_bill_payment_id", "to_column": "id", "reasoning": "Used to cross-reference a related bill payment line if needed."}, {"from_entity": "bill_payment_line", "to_entity": "vendor_credit", "type": "many-to-one", "from_column": "vendor_credit_id", "to_column": "id", "reasoning": "Vendor credit being applied as part of the payment."}, {"from_entity": "journal_entry_line", "to_entity": "journal_entry", "type": "many-to-one", "from_column": "journal_entry_id", "to_column": "id", "reasoning": "Each line entry is part of a journal entry and records a portion of the transaction."}, {"from_entity": "journal_entry_line", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Each journal entry line is posted to a specific general ledger account."}, {"from_entity": "journal_entry_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Defines the tax treatment for the journal entry line item."}, {"from_entity": "journal_entry_line", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Tracks which department the journal entry line is associated with."}, {"from_entity": "journal_entry_line", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Classifies the transaction line for reporting purposes."}, {"from_entity": "journal_entry_line", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Links the journal entry line to a customer when the transaction is customer-related."}, {"from_entity": "journal_entry_line", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "Associates the journal entry line with a vendor for expense or payable tracking."}, {"from_entity": "journal_entry_line", "to_entity": "employee", "type": "many-to-one", "from_column": "employee_id", "to_column": "id", "reasoning": "Used for employee reimbursements or payroll-related journal entries."}, {"from_entity": "journal_entry", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency used for the entire journal entry transaction."}, {"from_entity": "journal_entry", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Default tax code applied to the journal entry if applicable."}, {"from_entity": "vendor_credit", "to_entity": "account", "type": "many-to-one", "from_column": "payable_account_id", "to_column": "id", "reasoning": "Indicates the account where the credit is applied."}, {"from_entity": "vendor_credit", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "<PERSON><PERSON><PERSON> who issued the credit."}, {"from_entity": "vendor_credit", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency of the vendor credit."}, {"from_entity": "vendor_credit", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Department responsible for the vendor credit."}, {"from_entity": "purchase_order_line", "to_entity": "purchase_order", "type": "many-to-one", "from_column": "purchase_order_id", "to_column": "id", "reasoning": "Each line item specifies an individual product or service within a purchase order."}, {"from_entity": "purchase_order_tax_line", "to_entity": "purchase_order", "type": "many-to-one", "from_column": "purchase_order_id", "to_column": "id", "reasoning": "Links calculated tax amounts associated with a specific purchase order."}, {"from_entity": "purchase_order", "to_entity": "account", "type": "many-to-one", "from_column": "payable_account_id", "to_column": "id", "reasoning": "Defines the liability account used for recording payables from this purchase order."}, {"from_entity": "purchase_order", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "Identifies the vendor who is supplying the items or services in the purchase order."}, {"from_entity": "purchase_order", "to_entity": "term", "type": "many-to-one", "from_column": "sales_term_id", "to_column": "id", "reasoning": "Specifies the payment terms agreed upon for the purchase."}, {"from_entity": "purchase_order", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Associates the purchase order with a class for internal cost tracking."}, {"from_entity": "purchase_order", "to_entity": "address", "type": "many-to-one", "from_column": "vendor_address_id", "to_column": "id", "reasoning": "Stores the supplier's address used for billing and delivery."}, {"from_entity": "purchase_order", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Specifies the delivery address for the goods ordered."}, {"from_entity": "purchase_order_linked_txn", "to_entity": "purchase_order", "type": "many-to-one", "from_column": "purchase_order_id", "to_column": "id", "reasoning": "Links a transaction to its originating purchase order for audit and tracking purposes."}, {"from_entity": "purchase_order_linked_txn", "to_entity": "purchase", "type": "many-to-one", "from_column": "purchase_id", "to_column": "id", "reasoning": "Connects a purchase record that fulfills the linked purchase order."}, {"from_entity": "sales_receipt_line", "to_entity": "sales_receipt", "type": "many-to-one", "from_column": "sales_receipt_id", "to_column": "id", "reasoning": "Each line item belongs to a sales receipt detailing product/service and price."}, {"from_entity": "sales_receipt_line_bundle", "to_entity": "sales_receipt", "type": "many-to-one", "from_column": "sales_receipt_id", "to_column": "id", "reasoning": "Bundles multiple line items under a single sales receipt transaction."}, {"from_entity": "sales_receipt_line_bundle", "to_entity": "sales_receipt_line", "type": "many-to-one", "from_column": "sales_receipt_line_index", "to_column": "index", "reasoning": "Each bundle row refers to a specific sales receipt line by index."}, {"from_entity": "sales_receipt_tax_line", "to_entity": "sales_receipt", "type": "many-to-one", "from_column": "sales_receipt_id", "to_column": "id", "reasoning": "Links tax calculation details to a specific sales receipt transaction."}, {"from_entity": "vendor_credit_line", "to_entity": "vendor_credit", "type": "many-to-one", "from_column": "vendor_credit_id", "to_column": "id", "reasoning": "Each line in vendor_credit_line belongs to a single vendor credit transaction."}, {"from_entity": "purchase", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Links the purchase to the department responsible for the expense."}, {"from_entity": "purchase", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Defines the currency in which the purchase was made."}, {"from_entity": "purchase", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Indicates the account charged for the purchase transaction."}, {"from_entity": "purchase", "to_entity": "address", "type": "many-to-one", "from_column": "remit_to_address_id", "to_column": "id", "reasoning": "Specifies the remit-to address for the purchase payment."}, {"from_entity": "purchase", "to_entity": "payment_method", "type": "many-to-one", "from_column": "payment_method_id", "to_column": "id", "reasoning": "Indicates the method used to process the payment for the purchase."}, {"from_entity": "purchase", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Links the purchase to a specific customer, if applicable."}, {"from_entity": "purchase", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "Identifies the vendor from whom the goods or services were purchased."}, {"from_entity": "purchase", "to_entity": "employee", "type": "many-to-one", "from_column": "employee_id", "to_column": "id", "reasoning": "Indicates the employee responsible or associated with the purchase."}, {"from_entity": "purchase", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Applies a general tax code to the entire purchase transaction."}, {"from_entity": "purchase_tax_line", "to_entity": "purchase", "type": "many-to-one", "from_column": "purchase_id", "to_column": "id", "reasoning": "Represents tax line details for a specific purchase record."}, {"from_entity": "purchase_line", "to_entity": "purchase", "type": "many-to-one", "from_column": "purchase_id", "to_column": "id", "reasoning": "Contains itemized lines or expenses associated with a purchase."}, {"from_entity": "vendor", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the vendor’s preferred transaction currency."}, {"from_entity": "vendor", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Provides the billing address for the vendor."}, {"from_entity": "vendor", "to_entity": "term", "type": "many-to-one", "from_column": "term_id", "to_column": "id", "reasoning": "Defines the default payment terms for the vendor."}, {"from_entity": "employee", "to_entity": "address", "type": "many-to-one", "from_column": "address_id", "to_column": "id", "reasoning": "Links the employee record to their contact address."}, {"from_entity": "time_activity", "to_entity": "employee", "type": "many-to-one", "from_column": "employee_id", "to_column": "id", "reasoning": "Each time activity is logged by a specific employee."}, {"from_entity": "time_activity", "to_entity": "item", "type": "many-to-one", "from_column": "item_id", "to_column": "id", "reasoning": "Indicates the service item or type of work performed."}, {"from_entity": "time_activity", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Tracks the department under which the time activity falls."}, {"from_entity": "time_activity", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Classifies the time activity by a specific internal class."}, {"from_entity": "time_activity", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Links the activity to a billable customer or project."}, {"from_entity": "time_activity", "to_entity": "vendor", "type": "many-to-one", "from_column": "vendor_id", "to_column": "id", "reasoning": "Associates a vendor in case of outsourced labor for the activity."}, {"from_entity": "budget_detail", "to_entity": "budget", "type": "many-to-one", "from_column": "budget_id", "to_column": "id", "reasoning": "Each detail entry is part of a specific budget."}, {"from_entity": "budget_detail", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Specifies which account the budget detail amount applies to."}, {"from_entity": "budget_detail", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Links budgeted spending to a specific customer if applicable."}, {"from_entity": "budget_detail", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Categorizes the budget entry by business class or category."}, {"from_entity": "budget_detail", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Allocates the budget amount to a department."}, {"from_entity": "customer", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Sets the preferred currency for the customer’s transactions."}, {"from_entity": "customer", "to_entity": "payment_method", "type": "many-to-one", "from_column": "payment_method_id", "to_column": "id", "reasoning": "Stores the customer's default payment method."}, {"from_entity": "customer", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Specifies the customer's shipping address."}, {"from_entity": "customer", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Specifies the customer’s billing address."}, {"from_entity": "customer", "to_entity": "tax_code", "type": "many-to-one", "from_column": "default_tax_code_id", "to_column": "id", "reasoning": "Assigns a default tax code to the customer."}, {"from_entity": "customer", "to_entity": "term", "type": "many-to-one", "from_column": "sales_term_id", "to_column": "id", "reasoning": "Defines the payment terms for sales made to the customer."}, {"from_entity": "customer", "to_entity": "account", "type": "many-to-one", "from_column": "ar_account_id", "to_column": "id", "reasoning": "Specifies the accounts receivable account for this customer."}, {"from_entity": "customer", "to_entity": "customer", "type": "many-to-one", "from_column": "parent_customer_id", "to_column": "id", "reasoning": "Links the customer to a parent company or organization."}, {"from_entity": "refund_receipt", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Defines the billing address used in the refund receipt."}, {"from_entity": "refund_receipt", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Categorizes the refund by a specific class or segment."}, {"from_entity": "refund_receipt", "to_entity": "currency", "type": "many-to-one", "from_column": "currency_id", "to_column": "id", "reasoning": "Specifies the currency in which the refund was processed."}, {"from_entity": "refund_receipt", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Identifies the customer to whom the refund was issued."}, {"from_entity": "refund_receipt", "to_entity": "department", "type": "many-to-one", "from_column": "department_id", "to_column": "id", "reasoning": "Associates the refund with a specific department."}, {"from_entity": "refund_receipt", "to_entity": "account", "type": "many-to-one", "from_column": "deposit_to_account_id", "to_column": "id", "reasoning": "Indicates the account to which the refund was deposited."}, {"from_entity": "refund_receipt", "to_entity": "payment_method", "type": "many-to-one", "from_column": "payment_method_id", "to_column": "id", "reasoning": "Specifies the method used to issue the refund."}, {"from_entity": "refund_receipt", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Defines the shipping address associated with the refund."}, {"from_entity": "refund_receipt_tax_line", "to_entity": "refund_receipt", "type": "many-to-one", "from_column": "refund_receipt_id", "to_column": "id", "reasoning": "Each tax line record belongs to a specific refund receipt."}, {"from_entity": "refund_receipt_tax_line", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "Defines the specific tax rate used for the refund line."}, {"from_entity": "refund_receipt_line", "to_entity": "refund_receipt", "type": "many-to-one", "from_column": "refund_receipt_id", "to_column": "id", "reasoning": "Each line item records the details for a specific refund receipt."}, {"from_entity": "refund_receipt_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "sales_item_tax_code_id", "to_column": "id", "reasoning": "Defines the tax code applied to this refund line item."}, {"from_entity": "refund_receipt_line", "to_entity": "class", "type": "many-to-one", "from_column": "sales_item_class_id", "to_column": "id", "reasoning": "Classifies the refund line by business class or segment."}, {"from_entity": "refund_receipt_line", "to_entity": "item", "type": "many-to-one", "from_column": "sales_item_item_id", "to_column": "id", "reasoning": "Indicates the item or service being refunded."}, {"from_entity": "refund_receipt_line", "to_entity": "account", "type": "many-to-one", "from_column": "sales_item_account_id", "to_column": "id", "reasoning": "References the account affected by the refund."}, {"from_entity": "refund_receipt_line", "to_entity": "item", "type": "many-to-one", "from_column": "bundle_id", "to_column": "id", "reasoning": "Links the refund line to a product bundle if applicable."}, {"from_entity": "refund_receipt_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "description_tax_code_id", "to_column": "id", "reasoning": "Tax code applied to any description/service notes for this line."}, {"from_entity": "refund_receipt_line", "to_entity": "class", "type": "many-to-one", "from_column": "discount_class_id", "to_column": "id", "reasoning": "Classifies the discount associated with the refund line."}, {"from_entity": "refund_receipt_line", "to_entity": "tax_code", "type": "many-to-one", "from_column": "discount_tax_code_id", "to_column": "id", "reasoning": "Tax code for any discount applied to this line."}, {"from_entity": "refund_receipt_line", "to_entity": "account", "type": "many-to-one", "from_column": "discount_account_id", "to_column": "id", "reasoning": "Account used to record discounts for the refund line."}, {"from_entity": "refund_receipt_line", "to_entity": "item", "type": "many-to-one", "from_column": "sub_total_item_id", "to_column": "id", "reasoning": "Subtotal item, if this refund line represents a subtotal."}, {"from_entity": "refund_receipt_line_bundle", "to_entity": "refund_receipt", "type": "many-to-one", "from_column": "refund_receipt_id", "to_column": "id", "reasoning": "Each bundle line belongs to a specific refund receipt transaction."}, {"from_entity": "refund_receipt_line_bundle", "to_entity": "refund_receipt_line", "type": "many-to-one", "from_column": "refund_receipt_line_index", "to_column": "index", "reasoning": "Links a bundle row to its corresponding line in the refund receipt."}, {"from_entity": "refund_receipt_line_bundle", "to_entity": "item", "type": "many-to-one", "from_column": "item_id", "to_column": "id", "reasoning": "Specifies the item in the bundle being refunded."}, {"from_entity": "refund_receipt_line_bundle", "to_entity": "class", "type": "many-to-one", "from_column": "class_id", "to_column": "id", "reasoning": "Classifies the bundle item by business class or category."}, {"from_entity": "refund_receipt_line_bundle", "to_entity": "account", "type": "many-to-one", "from_column": "account_id", "to_column": "id", "reasoning": "Account used for the bundle item's refund entry."}, {"from_entity": "refund_receipt_line_bundle", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Tax code applied to the bundle item."}], "notes": {}}}, {"source": "QuickBooks_Analytics", "type": "sqldb", "source_type": "big<PERSON>y", "sections": ["quickbooks__balance_sheet", "quickbooks__ap_ar_enhanced", "quickbooks__expenses_sales_enhanced", "quickbooks__profit_and_loss", "quickbooks__general_ledger", "quickbooks__general_ledger_by_period", "quickbooks__cash_flow_statement"], "overview": "This data source is designed to provide robust, end-to-end financial management and reporting capabilities by integrating core accounting datasets sourced from QuickBooks. The database aggregates both daily and period-level financial data, enabling in-depth analysis and real-time monitoring of the company’s financial health and performance across multiple dimensions.\n\nKey tables and their purposes include:\n- **quickbooks__balance_sheet:** Delivers daily snapshots of all account balances, enabling timely and accurate balance sheet reporting and analysis.\n- **quickbooks__ap_ar_enhanced:** Tracks customer invoice balances, payment statuses, and due dates—supporting detailed monitoring and management of accounts receivable and accounts payable.\n- **quickbooks__expenses_sales_enhanced:** Stores granular sales invoice line items, including product details and transaction amounts, which are essential for revenue recognition, sales analysis, and expense tracking.\n- **quickbooks__profit_and_loss:** Provides daily profit and loss figures broken down by income account, facilitating continuous assessment of operational performance and profitability.\n- **quickbooks__general_ledger** and **quickbooks__general_ledger_by_period:** Capture all individual account transaction line items and summarize balances by reporting period, supporting both detailed transaction auditing and high-level financial trend analysis.\n- **quickbooks__cash_flow_statement:** Compiles cash movement data by account and period, enabling comprehensive cash flow analysis and liquidity management.\n\nThe database is structured to align with key business objectives and KPIs, including revenue growth, customer satisfaction, operational efficiency, and financial transparency. It empowers users to track critical financial metrics—such as revenue, expenses, cash flow, and account balances—in support of short-term tactical goals and long-term strategic planning. Additionally, the detailed and summarized views across tables facilitate compliance, audit readiness, and more granular financial insights, ensuring the organization can make informed decisions and adapt to evolving business needs.", "ER_Diagram": {}}]