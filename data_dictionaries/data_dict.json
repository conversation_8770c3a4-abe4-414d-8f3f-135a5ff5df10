[{"source": "superlube", "type": "sqldb", "source_type": "mysql", "sections": ["Salaries", "ServiceRecords", "CustomerData", "CustomerFeedbacks", "Invoices", "ServiceTypes", "Appointments", "EmployeeData"], "overview": "This data source underpins an automotive service business by comprehensively supporting operations across appointment scheduling, customer and vehicle management, service execution, employee tracking, and billing. The database is structured around six core tables—CustomerData, Appointments, ServiceRecords, EmployeeData, ServiceTypes, and Invoices—that together capture the complete business lifecycle from customer engagement and booking to service fulfillment and financial transactions.\n\n- **CustomerData** serves as the master repository for customer profiles and their vehicle details, enabling segmentation, targeted communication, and analysis of vehicle ownership patterns.\n- **Appointments** records all scheduled service visits, associating each appointment with a customer, a specific vehicle, and a requested service type, along with appointment and scheduling dates.\n- **ServiceRecords** documents the actual services performed, linking each service instance to the customer, the servicing employee, and the relevant service type. This enables detailed workload, operational, and staff performance analysis.\n- **EmployeeData** maintains information on employees, including job roles, service specializations, and employment status (active or former), supporting resource planning and performance management.\n- **ServiceTypes** catalogs all standard automotive services offered, including pricing (base charges), average time taken, and time ranges—providing a foundation for accurate scheduling, billing, and service reporting.\n- **Invoices** captures completed billing transactions, tying together service records, customers, and service types to support comprehensive financial and revenue reporting.\n\nThe entity relationships closely mirror real-world business processes: customers may own multiple vehicles and can book multiple appointments for various service types. Appointments transition to service execution (logged in ServiceRecords, performed by employees), which then generate invoices for completed services. The schema is designed to support robust KPI measurement—such as appointment volume, service throughput, employee utilization, customer engagement, and revenue tracking—directly supporting both short- and long-term business objectives. Additionally, the design enables historical analysis of customer activity, service demand trends, workforce allocation, and billing cycles, making it foundational for operational efficiency, customer satisfaction, and strategic business growth. \n\nNotably, the database structure allows for future scalability, such as normalization of vehicles for customers with multiple vehicle ownership, and can be extended to support advanced analytics for customer retention, service optimization, and profitability analysis.", "ER_Diagram": {"entities": [{"name": "Salaries", "primaryKeys": [["Salary_ID"]]}, {"name": "ServiceRecords", "primaryKeys": [["Service_ID"]]}, {"name": "CustomerData", "primaryKeys": []}, {"name": "CustomerFeedbacks", "primaryKeys": []}, {"name": "Invoices", "primaryKeys": [["Invoice_ID"], ["Service_ID"]]}, {"name": "ServiceTypes", "primaryKeys": [["Service_Type_ID"]]}, {"name": "Appointments", "primaryKeys": [["Appointment_ID"]]}, {"name": "EmployeeData", "primaryKeys": [["Employee_ID"]]}], "relationships": [{"from": "Salaries", "to": "EmployeeData", "type": "many-to-one", "via": {"sourceKeys": ["Employee_ID"], "targetKeys": ["Employee_ID"]}}, {"from": "ServiceRecords", "to": "ServiceTypes", "type": "many-to-one", "via": {"sourceKeys": ["Service_Type_ID"], "targetKeys": ["Service_Type_ID"]}}, {"from": "ServiceRecords", "to": "EmployeeData", "type": "many-to-one", "via": {"sourceKeys": ["Employee_ID"], "targetKeys": ["Employee_ID"]}}, {"from": "Invoices", "to": "CustomerData", "type": "many-to-one", "via": {"sourceKeys": ["Customer_ID"], "targetKeys": ["Customer_ID"]}}, {"from": "Invoices", "to": "ServiceRecords", "type": "one-to-one", "via": {"sourceKeys": ["Service_ID"], "targetKeys": ["Service_ID"]}}, {"from": "Invoices", "to": "ServiceTypes", "type": "many-to-one", "via": {"sourceKeys": ["Service_Type_ID"], "targetKeys": ["Service_Type_ID"]}}, {"from": "Appointments", "to": "CustomerData", "type": "many-to-one", "via": {"sourceKeys": ["Customer_ID"], "targetKeys": ["Customer_ID"]}}, {"from": "Appointments", "to": "CustomerData", "type": "many-to-one", "via": {"sourceKeys": ["Vehicle_ID"], "targetKeys": ["Vehicle_ID"]}}, {"from": "Appointments", "to": "ServiceTypes", "type": "many-to-one", "via": {"sourceKeys": ["Service_Type_ID"], "targetKeys": ["Service_Type_ID"]}}, {"from": "EmployeeData", "to": "ServiceTypes", "type": "many-to-one", "via": {"sourceKeys": ["Service_Type_ID"], "targetKeys": ["Service_Type_ID"]}}]}}, {"source": "QuickBooks_Raw", "type": "sqldb", "source_type": "big<PERSON>y", "sections": ["tax_rate", "sales_receipt_line", "bill_line", "bill", "purchase", "estimate_line_bundle", "budget", "purchase_tax_line", "vendor", "purchase_order_line", "term", "payment", "bill_payment_line", "tax_rate_detail", "currency", "sales_receipt_tax_line", "vendor_credit", "refund_receipt", "address", "payment_line", "refund_receipt_tax_line", "invoice", "credit_memo_line", "vendor_credit_line", "bill_linked_txn", "bundle_item", "journal_entry_tax_line", "purchase_order_tax_line", "credit_card_payment_txn", "item", "bill_payment", "bundle", "employee", "department", "credit_memo_line_bundle", "tax_agency", "sales_receipt", "time_activity", "refund_receipt_line", "estimate_tax_line", "sales_receipt_line_bundle", "purchase_line", "purchase_order_linked_txn", "payment_method", "budget_detail", "journal_entry_line", "tax_code", "invoice_tax_line", "deposit_line", "journal_entry", "credit_memo", "account", "estimate_line", "estimate", "invoice_linked_txn", "transfer", "deposit", "refund_receipt_line_bundle", "class", "estimate_linked_txn", "invoice_line_bundle", "purchase_order", "invoice_line", "customer"], "overview": "This data source serves as the centralized backbone for the company’s financial, sales, and operational management, encompassing a comprehensive range of core business records—including customers, vendors, employees, invoices, bills, products and services, accounts, tax structures, payment methods, budgets, and various transaction types. Its primary function is to enable end-to-end tracking and management of business transactions—such as sales, purchases, invoicing, payments, credits, refunds, and tax compliance—while supporting robust financial reporting, analytics, and regulatory obligations. This unified platform underpins key business objectives around revenue growth, operational efficiency, and customer and vendor management.\n\nKey tables and their roles include:\n- **Customer & Vendor**: Store detailed profiles, balances, and address references for buyers and suppliers, supporting both sales and purchasing workflows.\n- **Invoice & Invoice Line**: Capture issued invoices and itemized billing details, linking to customers, products, and tax breakdowns for precise revenue and receivable tracking.\n- **Bill, Bill Line, and Bill Payment**: Manage vendor invoices, line-level expense details, and payment allocations to maintain visibility into accounts payable.\n- **Purchase, Purchase Order, and Related Lines**: Track immediate and future vendor purchases, order fulfillment, and expense categorization.\n- **Account**: Maintains granular financial account structures, classifications, and balances, essential for accurate bookkeeping and reporting.\n- **Item & Bundle**: Catalog products, services, and bundled offerings, supporting flexible sales and purchasing scenarios.\n- **Tax-related tables (tax_rate, tax_code, tax_agency, tax_rate_detail, invoice_tax_line, and others)**: Define, map, and apply tax rates, codes, and agencies across all transaction types to ensure accurate calculation and compliance.\n- **Address**: Standardizes address information for customers, vendors, and transactions, promoting data consistency.\n- **Reference tables (currency, payment_method, term, class, department)**: Store standardized business configuration data, enabling multi-currency support, payment terms, organizational segmentation, and reporting.\n- **Budget & Budget Detail**: Support financial planning and variance analysis by capturing budgeted amounts at multiple levels.\n- **Employee & Time Activity**: Record employee information and activities for workforce management and potential payroll integration.\n- **Journal Entry & Line**: Facilitate manual accounting entries for adjustments and complex transactions.\n- **Refunds, Credits, and Receipts**: Handle customer returns, vendor credits, and various receipt types to ensure comprehensive coverage of all financial flows.\n- **Linking and Tax Line Tables**: Ensure traceability between related transactions (e.g., invoice_linked_txn, bill_linked_txn) and provide itemized tax reporting at the transaction line level.\n\nThe ER diagram illustrates a normalized, relational structure with well-defined linkages between customers, vendors, employees, invoices, bills, products, line items, tax calculations, and addresses. These relationships ensure transaction traceability, regulatory compliance, and data integrity. The design’s extensiveness allows for detailed tracking of all financial and operational activities, supporting both daily business execution and strategic decision-making.\n\nIn summary, this data source is architected to deliver a single source of truth for the company’s financial operations, customer and vendor management, employee tracking, and regulatory reporting, offering a robust foundation for performance measurement, KPI optimization, and long-term business growth.", "ER_Diagram": {"entities": {"account": {"description": "Stores financial accounts, including type, classification, balances, and timestamps. Each account has a unique id. No foreign keys.", "primary_key": ["id"], "attributes": ["fully_qualified_name", "account_type", "name", "classification", "account_sub_type", "created_at", "updated_at", "balance_with_sub_accounts", "balance", "_fivetran_synced"]}, "address": {"description": "Stores address details (street, city, postal code, country, province). Each address has a unique id. No foreign keys.", "primary_key": ["id"], "attributes": ["line_1", "city", "postal_code", "country", "country_sub_division_code", "_fivetran_synced"]}, "class": {"description": "Stores class entities, each with unique id, names, status, and timestamps. Standalone entity.", "primary_key": ["id"], "attributes": ["fully_qualified_name", "name", "active", "created_at", "updated_at", "_fivetran_synced"]}, "currency": {"description": "Stores supported currencies. Only CAD exists in sample. Reference table.", "primary_key": ["id"], "attributes": ["name", "_fivetran_synced"]}, "customer": {"description": "Stores customer and company info, balances, contact, and address references. No explicit relationships but links to address and invoice.", "primary_key": ["id"], "attributes": ["family_name", "fully_qualified_name", "given_name", "company_name", "display_name", "print_on_check_name", "sync_token", "balance_with_jobs", "balance", "created_at", "updated_at", "email", "phone_number", "shipping_address_id", "bill_address_id", "_fivetran_synced"]}, "invoice": {"description": "Stores invoices, billing and shipping address references, financial details, and customer links. Related to account and customer.", "primary_key": ["id"], "attributes": ["doc_number", "balance", "sync_token", "created_at", "updated_at", "total_tax", "shipping_address_id", "billing_address_id", "due_date", "total_amount", "transaction_date", "customer_id", "_fivetran_synced"]}, "invoice_line": {"description": "Stores details of invoice line items. Linked to invoice via invoice_id.", "primary_key": ["id"], "attributes": ["invoice_id", "index", "line_num", "description", "amount", "detail_type", "sales_item_item_id", "sales_item_tax_code_id", "_fivetran_synced"]}, "invoice_tax_line": {"description": "Stores invoice tax breakdowns per tax rate. Linked to invoice (invoice_id) and tax_rate (tax_rate_id).", "primary_key": ["invoice_id", "index"], "attributes": ["amount", "percent_based", "net_amount_taxable", "tax_percent", "tax_rate_id", "_fivetran_synced"]}, "item": {"description": "Product and service catalog. Each item has a unique id. No foreign keys.", "primary_key": ["id"], "attributes": ["fully_qualified_name", "name", "type", "created_at", "updated_at", "_fivetran_synced"]}, "payment_method": {"description": "Stores available payment methods. Standalone reference table.", "primary_key": ["id"], "attributes": ["name", "type", "_fivetran_synced"]}, "tax_agency": {"description": "Stores tax agencies; referenced by tax_rate. Standalone except as parent in tax_rate.", "primary_key": ["id"], "attributes": ["display_name", "created_at", "updated_at", "_fivetran_synced"]}, "tax_code": {"description": "Stores tax codes. Referenced by tax_rate_detail.", "primary_key": ["id"], "attributes": ["description", "name", "created_at", "updated_at", "_fivetran_synced"]}, "tax_rate": {"description": "Stores tax rate definitions. Linked to tax_agency (tax_agency_id) and referenced by tax_rate_detail and invoice_tax_line.", "primary_key": ["id"], "attributes": ["name", "special_tax_type", "rate_value", "description", "display_type", "effective_tax_rate", "created_at", "updated_at", "tax_agency_id", "_fivetran_synced"]}, "tax_rate_detail": {"description": "Links tax codes to tax rates, specifying type and order. Referenced by tax_code and tax_rate.", "primary_key": ["tax_code_id", "tax_rate_id"], "attributes": ["type", "tax_order", "_fivetran_synced"]}, "term": {"description": "Stores payment terms. Standalone reference table.", "primary_key": ["id"], "attributes": ["name", "active", "type", "due_days", "created_at", "updated_at", "_fivetran_synced"]}}, "relationships": [{"from_entity": "invoice", "to_entity": "customer", "type": "many-to-one", "from_column": "customer_id", "to_column": "id", "reasoning": "Multiple invoices can belong to the same customer. This supports customer billing and reporting."}, {"from_entity": "invoice_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each invoice_line is part of a single invoice, but each invoice can have multiple lines."}, {"from_entity": "invoice_tax_line", "to_entity": "invoice", "type": "many-to-one", "from_column": "invoice_id", "to_column": "id", "reasoning": "Each tax line belongs to a specific invoice, supporting detailed tax tracking per invoice."}, {"from_entity": "invoice_tax_line", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "Each invoice tax line references a tax_rate, allowing tracking of which tax was applied."}, {"from_entity": "tax_rate", "to_entity": "tax_agency", "type": "many-to-one", "from_column": "tax_agency_id", "to_column": "id", "reasoning": "Each tax_rate is managed by a single tax_agency, but each agency can have many tax rates."}, {"from_entity": "tax_rate_detail", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "tax_rate_detail links a tax_code_id to a tax_rate. This allows multiple details per rate or code."}, {"from_entity": "tax_rate_detail", "to_entity": "tax_rate", "type": "many-to-one", "from_column": "tax_rate_id", "to_column": "id", "reasoning": "tax_rate_detail references both tax_code and tax_rate, supporting mapping between codes and rates."}, {"from_entity": "tax_rate_detail", "to_entity": "tax_code", "type": "many-to-one", "from_column": "tax_code_id", "to_column": "id", "reasoning": "Each tax_rate_detail references a tax_code, allowing for taxonomy and grouping of tax rules."}, {"from_entity": "invoice", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Each invoice can have a shipping address; addresses can be reused across invoices."}, {"from_entity": "invoice", "to_entity": "address", "type": "many-to-one", "from_column": "billing_address_id", "to_column": "id", "reasoning": "Each invoice can have a billing address; addresses can be reused."}, {"from_entity": "customer", "to_entity": "address", "type": "many-to-one", "from_column": "shipping_address_id", "to_column": "id", "reasoning": "Each customer can have a shipping address; addresses may be shared."}, {"from_entity": "customer", "to_entity": "address", "type": "many-to-one", "from_column": "bill_address_id", "to_column": "id", "reasoning": "Each customer can have a billing address; addresses may be shared."}], "notes": {"entities_with_no_relationships": ["account", "item", "payment_method", "term", "class", "currency"], "entities_with_partial_metadata": ["Only entities with columns and/or relationships are modeled. Many tables (e.g., vendor, payment, bill, etc.) have no metadata and are omitted from this ERD."], "entity_aliases": ["customer_id on invoice can point to either account or customer (see original relationships), but customer is selected for clarity."], "relationship_decisions": ["Where multiple possible parent tables exist (e.g., account and customer for invoice), the customer relationship is preferred for the ER model, as account is not referenced elsewhere and customer contains more granular customer info.", "Address relationships added based on foreign key-like columns in customer and invoice."], "general": "All relationships are many-to-one unless otherwise noted. Standalone reference tables are included with no explicit foreign keys."}}}, {"source": "QuickBooks_Analytics", "type": "sqldb", "source_type": "big<PERSON>y", "sections": ["quickbooks__balance_sheet", "quickbooks__ap_ar_enhanced", "quickbooks__expenses_sales_enhanced", "quickbooks__profit_and_loss", "quickbooks__general_ledger", "quickbooks__general_ledger_by_period", "quickbooks__cash_flow_statement"], "overview": "This data source is designed to provide robust, end-to-end financial management and reporting capabilities by integrating core accounting datasets sourced from QuickBooks. The database aggregates both daily and period-level financial data, enabling in-depth analysis and real-time monitoring of the company’s financial health and performance across multiple dimensions.\n\nKey tables and their purposes include:\n- **quickbooks__balance_sheet:** Delivers daily snapshots of all account balances, enabling timely and accurate balance sheet reporting and analysis.\n- **quickbooks__ap_ar_enhanced:** Tracks customer invoice balances, payment statuses, and due dates—supporting detailed monitoring and management of accounts receivable and accounts payable.\n- **quickbooks__expenses_sales_enhanced:** Stores granular sales invoice line items, including product details and transaction amounts, which are essential for revenue recognition, sales analysis, and expense tracking.\n- **quickbooks__profit_and_loss:** Provides daily profit and loss figures broken down by income account, facilitating continuous assessment of operational performance and profitability.\n- **quickbooks__general_ledger** and **quickbooks__general_ledger_by_period:** Capture all individual account transaction line items and summarize balances by reporting period, supporting both detailed transaction auditing and high-level financial trend analysis.\n- **quickbooks__cash_flow_statement:** Compiles cash movement data by account and period, enabling comprehensive cash flow analysis and liquidity management.\n\nThe database is structured to align with key business objectives and KPIs, including revenue growth, customer satisfaction, operational efficiency, and financial transparency. It empowers users to track critical financial metrics—such as revenue, expenses, cash flow, and account balances—in support of short-term tactical goals and long-term strategic planning. Additionally, the detailed and summarized views across tables facilitate compliance, audit readiness, and more granular financial insights, ensuring the organization can make informed decisions and adapt to evolving business needs.", "ER_Diagram": {}}]