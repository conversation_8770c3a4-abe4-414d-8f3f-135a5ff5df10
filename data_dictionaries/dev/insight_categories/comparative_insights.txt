comparative_insights.txt:

Overview:
    - How does X compare with Y?
    - Compare different datasets, time periods, or segments to highlight differences or similarities.
    - Example: How do the sales figures of this year compare to last year's?

Description:
    For comparison, the relevant data might be available in the same data  source or multiple data sources. In order to retrieve all relevant data for comparison you must identify what data being compared.
    Example: How do the sales figures of this year compare to last year's?
    This example can be classified as comparative_insights. In order to compare sales figures between the two years, it is required to retrieve data from both years and present them in a format tailored for comparison. 