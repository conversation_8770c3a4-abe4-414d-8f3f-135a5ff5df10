exploratory_insights.txt:

Overview:

    - What patterns or relationships exist in the data?
    - Involve exploring the data without a predefined hypothesis to identify unexpected patterns or relationships.
    - Example: Are there any unexpected buying patterns among different customer segments?

Description:

    In order to find pattern it is required to retrieve data over long time period. To find pattern there will be special tools and models that can be used to identify patterns.
    Example: Are there any unexpected buying patterns among different customer segments?
    This example can be classified as a exploratory_insights. In this example, the retrieved data must have time (date) information for accurate pattern detection and comparison.
    It is important to present retrieved data in an appropriate format for tools and models to process data.