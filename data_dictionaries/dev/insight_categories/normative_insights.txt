normative_insights.txt:

Overview:

    - What should the standard or norm be?
    - Establish benchmarks or best practices based on data analysis.
    - Example: What is the optimal inventory level for our main warehouse to maximize efficiency and minimize costs?

Description:

    In order to generate normative_insights, it is important to retrieve relevant historical data over a long period of time.
    Example: What is the optimal inventory level for our main warehouse to maximize efficiency and minimize costs?
    This example can be classified as normative_insights. To find the insights, the data that carry information about both inventory level and cost must be retrieved. 
    In order to find the correlations, it is critical to retrieve data with time information (date).