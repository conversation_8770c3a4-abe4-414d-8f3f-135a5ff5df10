[{"rule": "Calculate the average revenue per order item by computing the average of PRODUCT_PRICE across all records in the ORDER_ITEMS table, grouped by the ORDERED_AT timestamp.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "Calculate the cumulative revenue for all orders by summing the PRODUCT_PRICE for all records in the ORDER_ITEMS table, with aggregation over the ORDERED_AT timestamp.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "Count the distinct number of customers who have placed orders, using the customer identifier and associating order events by the time they occurred.", "table_columns": [{"table_name": "RAW_ORDERS", "columns": ["CUSTOMER", "ORDERED_AT"]}]}, {"rule": "Calculate the total revenue from food items in orders by summing the price of products for order items where the item is classified as a food item (IS_FOOD_ITEM = 1), considering the time each item was ordered.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "IS_FOOD_ITEM", "ORDERED_AT"]}]}, {"rule": "Calculate the percentage of order revenue that comes from food items by dividing the sum of PRODUCT_PRICE for items where IS_FOOD_ITEM = 1 by the total sum of PRODUCT_PRICE for all order items, and expressing the result as a percentage. Both operations are grouped by ORDERED_AT.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "IS_FOOD_ITEM", "ORDERED_AT"]}]}, {"rule": "Count the number of orders where ORDER_TOTAL exceeds 20.", "table_columns": [{"table_name": "RAW_ORDERS", "columns": ["ORDER_TOTAL"]}]}, {"rule": "Calculate the median of PRODUCT_PRICE for each order item, excluding tax.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE"]}]}, {"rule": "Count the number of distinct CUSTOMER_IDs to determine the unique number of new customers, grouped by the order date.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["ORDERED_AT"]}]}, {"rule": "Calculate the sum of SUPPLY_COST for each order item to determine the total cost per order item.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["SUPPLY_COST"]}]}, {"rule": "The total revenue for order gross profit is calculated by summing the PRODUCT_PRICE for all items, grouped by the time each item was ordered (ORDERED_AT).", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "The total number of orders is calculated by summing 1 for each record in the orders data, grouped by the time the order was placed (ORDERED_AT).", "table_columns": [{"table_name": "RAW_ORDERS", "columns": ["ORDERED_AT"]}]}, {"rule": "Revenue is calculated as the sum of PRODUCT_PRICE for each order item, excluding tax.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE"]}]}, {"rule": "Revenue is calculated as the sum of PRODUCT_PRICE for all order items, aggregated by the ORDERED_AT timestamp.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "Calculate the average revenue per order item by averaging the PRODUCT_PRICE for all rows in the ORDER_ITEMS table, with the aggregation performed over the ORDERED_AT time dimension.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "Calculate the cumulative revenue for all orders by summing the PRODUCT_PRICE for all records in the ORDER_ITEMS table, with the aggregation performed over the ORDERED_AT time dimension.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "Count the number of distinct customers who have placed orders, aggregating by the order placement time.", "table_columns": [{"table_name": "RAW_ORDERS", "columns": ["CUSTOMER", "ORDERED_AT"]}]}, {"rule": "Count the number of orders that contain food items. An order is considered a food order if at least one associated order item is marked as a food item.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["ORDER_ID", "IS_FOOD_ITEM"]}]}, {"rule": "For each time period, calculate the total revenue from food items in orders by summing the product price of order items where the item is marked as a food item (IS_FOOD_ITEM = 1).", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "IS_FOOD_ITEM", "ORDERED_AT"]}]}, {"rule": "Calculate the percentage of order revenue that comes from food items. This is determined by summing the PRODUCT_PRICE of items where IS_FOOD_ITEM equals 1, divided by the total sum of PRODUCT_PRICE for all order items, for each order or over a period as needed.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "IS_FOOD_ITEM"]}]}, {"rule": "Count the number of orders where the ORDER_TOTAL exceeds 20.", "table_columns": [{"table_name": "RAW_ORDERS", "columns": ["ORDER_TOTAL"]}]}, {"rule": "Calculate the median of PRODUCT_PRICE for each order item in ORDER_ITEMS, excluding tax. The median is determined over the PRODUCT_PRICE values, grouped by ORDER_ITEM_ID, and can be analyzed over the ORDERED_AT time dimension.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDER_ITEM_ID", "ORDERED_AT"]}]}, {"rule": "Count the number of unique CUSTOMER identifiers (customer_id) associated with orders, representing the unique count of new customers, aggregated over the ORDERED_AT time dimension.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["ORDERED_AT"]}, {"table_name": "RAW_ORDERS", "columns": ["CUSTOMER"]}]}, {"rule": "Compute the sum of SUPPLY_COST for each order item in ORDER_ITEMS to determine the total cost associated with order items.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["SUPPLY_COST"]}]}, {"rule": "The total revenue for gross profit calculation is determined by summing the PRODUCT_PRICE column for all order items, grouped or aggregated over the ORDERED_AT timestamp.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}, {"rule": "The total order count is calculated by summing 1 for each order record, resulting in the count of all orders, aggregated over the ORDERED_AT timestamp.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["ORDERED_AT"]}]}, {"rule": "Calculate the total revenue by summing the PRODUCT_PRICE for each order item in the ORDER_ITEMS table. Tax is not included in this calculation.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE"]}]}, {"rule": "Revenue is calculated as the sum of PRODUCT_PRICE for all order items, aggregated over the ORDERED_AT timestamp.", "table_columns": [{"table_name": "ORDER_ITEMS", "columns": ["PRODUCT_PRICE", "ORDERED_AT"]}]}]