{"knowledge_blocks": [{"purpose": "Provide a breakdown of service durations (time taken) for each service type in the Superlube system.", "business_rules": ["Service duration is stored in superlube.ServiceRecords.Time_Taken (unit: minutes).", "Service type information is stored in superlube.ServiceTypes (field: Description).", "All ServiceRecords rows are included unless the user specifies additional filters (e.g., date range, employee).", "Return, for each service type, the number of services, average Time_Taken, minimum Time_Taken, and maximum Time_Taken.", "Present readable service type descriptions by joining on Service_Type_ID."], "linked_knowledge_block_references": [], "sql_query_logic": "Join <PERSON>R<PERSON>ords (alias SR) with ServiceTypes (alias ST) on Service_Type_ID. Group by service type and aggregate Time_Taken to obtain COUNT, AVG, MIN, MAX.", "sql": "SELECT\n    SR.Service_Type_ID                       AS service_type_id,\n    ST.Description                           AS service_type_description,\n    COUNT(*)                                 AS service_count,\n    AVG(SR.Time_Taken)                       AS avg_time_taken,\n    MIN(SR.Time_Taken)                       AS min_time_taken,\n    MAX(SR.Time_Taken)                       AS max_time_taken\nFROM superlube.ServiceRecords SR\nJOIN superlube.ServiceTypes ST ON SR.Service_Type_ID = ST.Service_Type_ID\nGROUP BY SR.Service_Type_ID, ST.Description\nORDER BY SR.Service_Type_ID;", "data_processing_logic": "1. Execute the SQL query to retrieve the aggregated table.\n2. Optionally export the result to a CSV file with identical column order and headings if the user requests a download.", "donts": "• Do not filter by date or status unless explicitly requested.\n• Do not join to Invoices or other tables unrelated to service duration.\n• Do not convert Time_Taken units—assume they are already in minutes.", "additional_details": "Time_Taken represents the recorded duration of each service job. If Time_Taken contains nulls, decide (per future business rule) whether to exclude or treat as zero before aggregation.", "block_id": "services_duration_breakdown_1", "pattern_keys": ["services_duration_breakdown"]}, {"purpose": "Identify which service type generates the highest lifetime-to-date revenue in the Superlube system and provide a ranked revenue table.", "business_rules": ["Revenue amounts are stored in superlube.Invoices.Total_Charge.", "Service identity is stored in superlube.Invoices.Service_Type_ID which joins to superlube.ServiceTypes.Service_Type_ID for the human-readable description.", "The current schema contains no status, refund, or cancellation columns; therefore every invoice row is treated as valid revenue unless the user specifies additional filters.", "Unless the user requests a date range or other constraints, aggregate over ALL rows in Invoices.", "Return revenue in raw numeric form (no scientific notation, no thousand separators) so downstream tools can consume the data directly."], "linked_knowledge_block_references": ["sales_revenue_1"], "sql_query_logic": "Aggregate Total_Charge by Service_Type_ID, join to ServiceTypes for the description, order by summed revenue descending, and (optionally) limit 1 to get the top service.", "sql": "SELECT\n    I.Service_Type_ID,\n    ST.Description       AS service_description,\n    SUM(I.Total_Charge)  AS total_revenue\nFROM superlube.Invoices I\nJOIN superlube.ServiceTypes ST ON I.Service_Type_ID = ST.Service_Type_ID\nGROUP BY I.Service_Type_ID, ST.Description\nORDER BY total_revenue DESC;  -- add \"LIMIT 1\" if only the top service is needed", "data_processing_logic": "1. Execute the SQL query.\n2. Cast/format total_revenue values as plain decimals (avoid scientific notation).\n3. Keep the result set ordered by total_revenue DESC.\n4. The first row represents the highest-revenue service; optionally export the full table (CSV) for user download.", "donts": "• Do not join to tables other than ServiceTypes (e.g., ServiceRecords, Payments) for this calculation.\n• Do not attempt to filter by invoice status, refunds, or cancellations—those fields do not exist.\n• Do not apply date filters unless explicitly requested by the user.\n• Do not format numbers with thousand separators or currency symbols inside the raw dataset.", "additional_details": "If future schema versions introduce status/refund indicators, revisit the business rules to exclude invalid revenue. The query can be parameterised to accept date ranges or additional filters when needed.", "block_id": "services_revenue_ranking_1", "pattern_keys": ["services_revenue_ranking"]}, {"purpose": "Provide a reusable pattern to calculate, by calendar month, the expected revenue over the next three months from all active PMA contracts.", "business_rules": ["Active contract definition: Contracts.Contract_Status = 'Active' AND LOWER(Contracts.Contract_Type) LIKE 'pma%' AND (Contracts.Contract_ContractRenewalDate IS NULL OR Contracts.Contract_ContractRenewalDate >= CURRENT_DATE).", "Expected revenue is the sum of three mutually-exclusive components for the chosen horizon: 1) open/unpaid invoices, 2) not-yet-invoiced jobs that are Ready to Schedule or In Progress, 3) pending bids still In Development with no associated job.", "Use invoice amounts only if InvoiceSummary_InvoiceStatus <> 'Voided' AND (InvoiceSummary_PaidStatus IS NULL OR <> 'Paid').", "Exclude jobs already invoiced by checking LEFT JOIN InvoiceSummary ON InvoiceSummary_JobID = JobsSummary_ID and keeping rows where InvoiceSummary_InvoiceID IS NULL.", "Exclude bids that have already produced a job by LEFT JOIN to JobsSummary and keeping rows where JobsSummary_ID IS NULL.", "Date window must be consistent: revenue/expected dates >= CURRENT_DATE and < DATEADD(month,3,CURRENT_DATE).", "Group results by DATE_TRUNC('month', relevant_date) for monthly aggregation; provide TOTAL if required."], "linked_knowledge_block_references": ["3-component_revenue_estimation_rule", "FieldServio_contracts_metadata"], "sql_query_logic": "1. CTE active_pma selects ContractID & ContractNumber per active contract rule.\n2. invoice_comp: sum InvoiceSummary_CurrencyTotalAmt grouped by month of InvoiceSummary_InvoiceDate subject to filters.\n3. job_comp: sum JobsSummary_QuoteAmount grouped by month of COALESCE(JobsSummary_ScheduledDate, JobsSummary_PromisedDate) with status & invoice NULL checks.\n4. bid_comp: sum BidsSummary_QuoteAmount grouped by month of COALESCE(BidsSummary_StartDate, BidsSummary_BidDate) with status & job NULL checks.\n5. UNION ALL the three component result sets, then GROUP BY revenue_month to obtain expected_revenue_amount per month.", "sql": "WITH active_pma AS (\n  SELECT Contract_ContractID   AS ContractID,\n         Contract_Number       AS ContractNumber\n  FROM   Contracts\n  WHERE  Contract_Status = 'Active'\n    AND  LOWER(Contract_Type) LIKE 'pma%'\n    AND (Contract_ContractRenewalDate IS NULL OR Contract_ContractRenewalDate >= CURRENT_DATE)\n),\ninvoice_comp AS (\n  SELECT DATE_TRUNC('month', i.InvoiceSummary_InvoiceDate) AS revenue_month,\n         SUM(i.InvoiceSummary_CurrencyTotalAmt)            AS amount\n  FROM   InvoiceSummary i\n  JOIN   active_pma p ON (i.InvoiceSummary_ContractID = p.ContractID OR i.InvoiceSummary_ContractNumber = p.ContractNumber)\n  WHERE  i.InvoiceSummary_InvoiceDate >= CURRENT_DATE\n    AND  i.InvoiceSummary_InvoiceDate <  DATEADD(month,3,CURRENT_DATE)\n    AND  i.InvoiceSummary_InvoiceStatus <> 'Voided'\n    AND (i.InvoiceSummary_PaidStatus IS NULL OR i.InvoiceSummary_PaidStatus <> 'Paid')\n  GROUP BY DATE_TRUNC('month', i.InvoiceSummary_InvoiceDate)\n),\njob_comp AS (\n  SELECT DATE_TRUNC('month', COALESCE(j.JobsSummary_ScheduledDate, j.JobsSummary_PromisedDate)) AS revenue_month,\n         SUM(j.JobsSummary_QuoteAmount)                                                         AS amount\n  FROM   JobsSummary j\n  LEFT  JOIN InvoiceSummary iv ON iv.InvoiceSummary_JobID = j.JobsSummary_ID\n  JOIN   active_pma p ON (j.JobsSummary_ContractID = p.ContractID OR j.JobsSummary_ContractNumber = p.ContractNumber)\n  WHERE  j.JobsSummary_InvoiceStatus <> 'Invoiced'\n    AND  j.JobsSummary_Status IN ('Ready to Schedule','In Progress')\n    AND  COALESCE(j.JobsSummary_ScheduledDate, j.JobsSummary_PromisedDate) >= CURRENT_DATE\n    AND  COALESCE(j.JobsSummary_ScheduledDate, j.JobsSummary_PromisedDate) <  DATEADD(month,3,CURRENT_DATE)\n    AND  iv.InvoiceSummary_InvoiceID IS NULL\n  GROUP BY DATE_TRUNC('month', COALESCE(j.JobsSummary_ScheduledDate, j.JobsSummary_PromisedDate))\n),\nbid_comp AS (\n  SELECT DATE_TRUNC('month', COALESCE(b.BidsSummary_StartDate, b.BidsSummary_BidDate)) AS revenue_month,\n         SUM(b.BidsSummary_QuoteAmount)                                                 AS amount\n  FROM   BidsSummary b\n  LEFT  JOIN JobsSummary j ON j.JobsSummary_ID = b.BidsSummary_ID\n  JOIN   active_pma p ON (b.BidsSummary_Contract_ID = p.ContractID OR b.BidsSummary_Contract_Number = p.ContractNumber)\n  WHERE  b.BidsSummary_Status = 'In Development'\n    AND  COALESCE(b.BidsSummary_StartDate, b.BidsSummary_BidDate) >= CURRENT_DATE\n    AND  COALESCE(b.BidsSummary_StartDate, b.BidsSummary_BidDate) <  DATEADD(month,3,CURRENT_DATE)\n    AND  j.JobsSummary_ID IS NULL\n  GROUP BY DATE_TRUNC('month', COALESCE(b.BidsSummary_StartDate, b.BidsSummary_BidDate))\n),\nunioned AS (\n  SELECT * FROM invoice_comp\n  UNION ALL\n  SELECT * FROM job_comp\n  UNION ALL\n  SELECT * FROM bid_comp\n)\nSELECT TO_CHAR(revenue_month,'YYYY-MM') AS revenue_month,\n       SUM(amount)                      AS expected_revenue_amount\nFROM   unioned\nGROUP  BY revenue_month\nORDER  BY revenue_month;", "data_processing_logic": "1. Run SQL and save result to .dat.\n2. Convert .dat to CSV (same rows) for user download.\n3. Optionally compute TOTAL row by summing revenue_month amounts; check consistency with independent grand-total query.", "donts": "• Do not reference non-existent column InvoiceSummary_ID; use InvoiceSummary_InvoiceID.\n• Do not include invoices with status 'Voided' or paid invoices.\n• Do not count jobs already invoiced or bids that have become jobs (avoid duplication).\n• Ensure the date window is identical across all components; mismatched windows cause total vs monthly discrepancies.", "additional_details": "Adjust CURRENT_DATE handling if database requires GETDATE(); ensure DATE_TRUNC syntax matches SQL dialect (e.g., use DATEFROMPARTS(YEAR(date), MONTH(date),1) in T-SQL).  The pattern returns one row per calendar month starting with the current month.", "block_id": "contracts_monthly_revenue_forecast_1", "pattern_keys": ["contracts_monthly_revenue_forecast", "revenue_short_term_trend"]}, {"purpose": "Compute and deliver lifetime-to-date total revenue for the Superlube system.", "business_rules": ["Revenue is stored in superlube.Invoices.Total_Charge.", "All invoice rows are included because no status/refund columns exist in the current schema.", "Return a single numeric value representing currency units (no formatting or sign adjustments)."], "linked_knowledge_block_references": [], "sql_query_logic": "SELECT SUM(Total_Charge) AS Total_Revenue FROM superlube.Invoices;", "sql": "", "data_processing_logic": "1. Execute aggregation query.\n2. If the DB engine returns scientific notation, convert to standard decimal.\n3. Present the value without additional calculations or filters.", "donts": "• Do not attempt to filter by transaction status, refunds, or cancellations—those fields are absent.\n• Do not aggregate from other tables (e.g., Payments, ServiceRecords) for revenue purposes in this context.", "additional_details": "If future schema versions add status or refund indicators, revisit business rules to exclude invalid revenue.", "block_id": "sales_revenue_1", "pattern_keys": ["sales_revenue"]}, {"purpose": "Identify PMA contracts whose scheduled service value differs from the total invoiced value by more than $10 and return ContractNumber, ServiceScheduleValue, InvoiceValue, and Difference.", "business_rules": ["PMA contracts are those with Contracts.Contract_Type IN ('PMA - GEN', 'PMA-COM').", "ServiceScheduleValue is Contracts.Contract_Amount (scheduled contract value).", "Invoices considered must satisfy InvoiceSummary.InvoiceSummary_InvoiceType = 'Invoice' AND InvoiceSummary.InvoiceSummary_InvoiceStatus <> 'Voided'.", "Invoice totals are summed from InvoiceSummary.InvoiceSummary_CurrencyTotalAmt grouped by contract number.", "Difference = COALESCE(ServiceScheduleValue,0) - COALESCE(InvoiceValue,0).", "Return only rows where ABS(Difference) > 10."], "linked_knowledge_block_references": [], "sql_query_logic": "1) Filter Contracts to PMA types.\n2) Aggregate qualifying invoices by contract number.\n3) Left join Contracts to aggregated invoices.\n4) Compute Difference and filter for |Difference| > 10.\n5) Select required columns and order by ABS(Difference) DESC.", "sql": "WITH pma_contracts AS (\n   SELECT Contract_Number, Contract_Amount AS ServiceScheduleValue\n   FROM Contracts\n   WHERE Contract_Type IN ('PMA - GEN','PMA-COM')\n),\ninvoice_totals AS (\n   SELECT InvoiceSummary_ContractNumber AS Contract_Number,\n          SUM(InvoiceSummary_CurrencyTotalAmt) AS InvoiceValue\n   FROM InvoiceSummary\n   WHERE InvoiceSummary_InvoiceType = 'Invoice'\n     AND InvoiceSummary_InvoiceStatus <> 'Voided'\n   GROUP BY InvoiceSummary_ContractNumber\n)\nSELECT pc.Contract_Number          AS ContractNumber,\n       pc.ServiceScheduleValue,\n       it.InvoiceValue,\n       COALESCE(pc.ServiceScheduleValue,0) - COALESCE(it.InvoiceValue,0) AS Difference\nFROM pma_contracts pc\nLEFT JOIN invoice_totals it ON pc.Contract_Number = it.Contract_Number\nWHERE ABS(COALESCE(pc.ServiceScheduleValue,0) - COALESCE(it.InvoiceValue,0)) > 10\nORDER BY ABS(COALESCE(pc.ServiceScheduleValue,0) - COALESCE(it.InvoiceValue,0)) DESC;", "data_processing_logic": "Execute SQL; if InvoiceValue is NULL treat as 0; export full result set to CSV for user download; optionally show preview (first N rows) in response.", "donts": "Do not include contracts with other Contract_Type values; do not include InvoiceType 'Credit' or 'Deposit'; do not include invoices with status 'Voided'; do not forget to treat missing invoice totals as 0 before computing difference.", "additional_details": "Common extension: adjust $10 threshold or include additional invoice statuses by altering filter. Works on FieldServio data model.", "block_id": "contracts_invoice_variance_1", "pattern_keys": ["contracts_invoice_variance"]}, {"purpose": "Calculate technician utilisation (%) from SuperLube operational data and provide a downloadable table for managers.", "business_rules": ["Identify technicians via EmployeeData.Role = 'Technician'.", "ServiceRecords.Time_Taken is stored in minutes; convert to hours by dividing by 60.", "Each ServiceRecords row is unique (primary key Service_ID); no deduplication required.", "Each distinct Salaries.Pay_Date represents one monthly pay period equating to 160 available hours (assumption when no roster/leave data exists).", "Utilisation % = (Σ productive_hours) / (pay_periods × 160) × 100, rounded to 2 decimals.", "Return one row per technician containing Employee_ID, productive_hours, pay_periods, available_hours, utilisation_percent."], "linked_knowledge_block_references": ["services_duration_breakdown_1"], "sql_query_logic": "1. CTE tech: select Employee_<PERSON> from EmployeeData where Role = 'Technician'.\n2. CTE prod: join ServiceRecords to tech, sum Time_Taken/60 as productive_hours grouped by Employee_ID.\n3. CTE pay: join Salaries to tech, count distinct Pay_Date as pay_periods grouped by Employee_ID.\n4. Final select joins prod and pay, calculates available_hours = pay_periods*160 and utilisation_percent = productive_hours / available_hours * 100; order by utilisation_percent DESC.", "sql": "WITH tech AS (\n  SELECT Employee_ID FROM EmployeeData WHERE Role = 'Technician'\n),\nprod AS (\n  SELECT SR.Employee_ID, SUM(SR.Time_Taken)/60.0 AS productive_hours\n  FROM ServiceRecords SR\n  JOIN tech T ON SR.Employee_ID = T.Employee_ID\n  GROUP BY SR.Employee_ID\n),\npay AS (\n  SELECT S.Employee_ID, COUNT(DISTINCT S.Pay_Date) AS pay_periods\n  FROM Salaries S\n  JOIN tech T ON S.Employee_ID = T.Employee_ID\n  GROUP BY S.Employee_ID\n)\nSELECT\n  prod.Employee_ID,\n  prod.productive_hours,\n  pay.pay_periods,\n  pay.pay_periods * 160 AS available_hours,\n  ROUND(prod.productive_hours / (pay.pay_periods * 160) * 100, 2) AS utilisation_percent\nFROM prod\nJOIN pay ON prod.Employee_ID = pay.Employee_ID\nORDER BY utilisation_percent DESC;", "data_processing_logic": "Execute the SQL query, retrieve result set (~57 rows). Export to CSV 'technician_utilisation.csv' preserving column order. No further transformation needed but flag any utilisation_percent > 100 for potential overtime or data-quality review.", "donts": "• Do not forget to convert minutes to hours before aggregation.\n• Do not attempt deduplication of ServiceRecords unless Service_ID duplicates are detected.\n• Do not include non-technician roles.\n• Do not apply leave or roster adjustments unless additional tables become available.\n• Do not hard-code technician names; use Employee_ID or join to name column only if required.", "additional_details": "This approximation may overstate utilisation where technicians take leave or understate where standard hours differ from 160/month. Consider enhancing denominator with actual roster or leave data when available. Over-100 % results indicate overtime or data anomalies needing follow-up.", "block_id": "employees_technician_utilisation_1", "pattern_keys": ["employees_technician_utilisation"]}, {"purpose": "Return overall total revenue (and optional basic context) from the Superlube system when the user simply asks for \"total revenue.\"", "business_rules": ["Revenue is stored in data source \"superlube\", table \"Invoices\", column \"Total_Charge\".", "Unless the user specifies a timeframe or additional filters, aggregate over ALL rows in Invoices.", "If the user requests a timeframe, apply WHERE Invoice_Date BETWEEN <start> AND <end> before aggregation.", "Provide helpful context by also returning COUNT(*) of invoices and MIN/MAX of Invoice_Date where appropriate."], "linked_knowledge_block_references": [], "sql_query_logic": "Aggregate the Total_Charge column in Invoices; optionally include invoice count and date range.", "sql": "SELECT \n    SUM(Total_Charge)   AS total_revenue,\n    COUNT(*)            AS invoice_count,\n    MIN(Invoice_Date)   AS earliest_invoice_date,\n    MAX(Invoice_Date)   AS latest_invoice_date\nFROM superlube.Invoices\n[WHERE Invoice_Date BETWEEN :start_date AND :end_date];", "data_processing_logic": "1. Execute the SQL and retrieve the single-row result.\n2. (Optional) Save the result to a user-friendly file, e.g., CSV, if download is requested.", "donts": "• Do NOT join to other tables unless additional dimensions are needed.\n• Do NOT apply status/refund filters—those columns do not exist in this schema.\n• Do NOT restrict dates unless explicitly requested by the user.", "additional_details": "The Invoices table has no status or refund fields; all rows are considered valid revenue. Column names are case-sensitive exactly as shown.", "block_id": "sales_revenue_1", "pattern_keys": ["sales_revenue"]}], "knowledge_tree": {"Contracts": [{"pattern": "Contract invoice variance analysis", "pattern_key": "contracts_invoice_variance"}], "Employees": [{"pattern": "Technician utilisation percentage calculation", "pattern_key": "employees_technician_utilisation"}], "Sales": [{"node_title": "Sales revenue", "node_key": "sales_revenue"}], "Services": [{"pattern": "Service type duration breakdown", "pattern_key": "services_duration_breakdown"}, {"pattern": "Service type revenue ranking", "pattern_key": "services_revenue_ranking"}]}}