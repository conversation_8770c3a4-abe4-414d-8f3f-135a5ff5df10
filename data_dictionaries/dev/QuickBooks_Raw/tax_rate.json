{"tableName": "tax_rate", "description": "The tax_rate table defines individual tax rates, including their unique ID, name, type, percentage value, and display settings. Each tax rate is associated with a tax agency (tax_agency_id) and includes reporting details such as effective tax rate and descriptions. This table is referenced by invoice and tax detail tables to determine and apply taxes in transactions, supporting compliance, reporting, and integration through Fivetran synchronization.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier for each tax rate record, ensuring distinctness across all entries in the table.", "subsetOfAvailableValues": ["2", "3", "4", "5", "16", "and 48 more..."], "totalDistinctValueCount": 53, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "A unique string representing the name of the tax rate, facilitating easy identification of specific tax rates.", "subsetOfAvailableValues": ["PST (MB) on purchase", "PST (BC) Sales", "PST (BC) Purchase", "HST BC", "PST (SK) on purchases", "and 48 more..."], "totalDistinctValueCount": 53, "is_unstructured": false}, {"name": "special_tax_type", "dataType": "STRING", "description": "A string that categorizes the type of tax applicable, with values including ZERO_RATE, NONE, and ADJUSTMENT_RATE, clarifying the nature of tax treatment.", "availableValues": ["NONE", "ADJUSTMENT_RATE", "ZERO_RATE"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "rate_value", "dataType": "BIGNUMERIC", "description": "A positive numeric value, stored as BIGNUMERIC, representing the percentage rate of tax applied, which is expected to be less than 100.", "subsetOfAvailableValues": ["0", "5", "6", "7", "8", "and 5 more..."], "is_unstructured": false}, {"name": "description", "dataType": "STRING", "description": "An optional textual field providing contextual details about the tax rate, typically unique for groups of records.", "availableValues": ["GST", "PST (MB)", "PST (SK)", "QST", "HST (BC)", "PST (BC)", "HST (ON)", "HST (NB)", "HST (NB) 2016", "HST (NS)", "HST (NS) 2025", "No Tax"], "totalDistinctValueCount": 12, "is_unstructured": false}, {"name": "display_type", "dataType": "STRING", "description": "A string indicating the visibility of the tax rate in user interfaces, with values such as 'HideInTransactionForms' and 'HideInAll'.", "availableValues": ["HideInAll", "HideInTransactionForms"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "effective_tax_rate", "dataType": "STRING", "description": "A string that denotes the effective tax rate applicable, which may vary based on specific conditions or calculations, used for reporting and analysis.", "availableValues": ["[{\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":0,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":7,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":15,\"EffectiveDate\":\"2016-07-01T00:00:00-07:00\"}]", "[{\"RateValue\":13,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":5,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\",\"EndDate\":\"2017-03-22T00:00:00-07:00\"},{\"RateValue\":6,\"EffectiveDate\":\"2017-03-23T00:00:00-07:00\"}]", "[{\"RateValue\":14,\"EffectiveDate\":\"2025-04-01T00:00:00-07:00\"}]", "[{\"RateValue\":9.975,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":8,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":5,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":12,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":13,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\",\"EndDate\":\"2016-06-30T00:00:00-07:00\"}]", "[{\"RateValue\":15,\"EffectiveDate\":\"1970-01-01T00:00:00-08:00\"}]", "[{\"RateValue\":6,\"EffectiveDate\":\"2017-03-23T00:00:00-07:00\"}]"], "totalDistinctValueCount": 14, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating when the tax rate record was created, essential for tracking record age.", "subsetOfAvailableValues": ["2021-03-19 07:44:58+00:00", "2021-03-19 07:46:29+00:00", "2021-03-19 08:00:29+00:00", "2021-03-19 08:01:09+00:00", "2021-03-19 08:01:22+00:00", "and 3 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp showing when the tax rate record was last updated, helping to determine the currency of the information.", "subsetOfAvailableValues": ["2021-03-19 07:44:58+00:00", "2021-03-19 07:46:29+00:00", "2021-03-19 08:00:29+00:00", "2021-03-19 08:01:09+00:00", "2021-03-19 08:01:22+00:00", "and 3 more..."], "is_unstructured": false}, {"name": "tax_agency_id", "dataType": "STRING", "description": "A string linking the tax rate to a specific tax agency, establishing a relationship with the 'tax_agency' table.", "availableValues": ["2", "3", "4", "5", "6", "7"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp indicating when the record was last synced with Fivetran, primarily for synchronization tracking.", "subsetOfAvailableValues": ["2025-07-29 15:47:40.139000+00:00", "2025-07-29 15:47:40.130000+00:00", "2025-07-29 15:47:40.136000+00:00", "2025-07-29 15:47:40.128000+00:00", "2025-07-29 15:47:40.145000+00:00", "and 20 more..."], "is_unstructured": false}], "relationships": [{"child_table": "tax_rate", "parent_table": "tax_agency", "key_column_mapping": [{"parent_column": "id", "child_column": "tax_agency_id"}], "relationship_type": "one-to-many"}]}