{"tableName": "address", "description": "The address table stores unique address records, capturing details such as street (line_1), city, postal code, country (typically 'CA' for Canada), and province code. Some fields may be missing for certain records. The table is referenced by customer and invoice tables for billing and shipping addresses. The _fivetran_synced column tracks the last data integration update.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique identifier for each address record, allowing distinct references in queries and ensuring data integrity.", "availableValues": ["4", "20", "22", "24", "25", "84", "92", "96", "98", "99"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "line_1", "dataType": "STRING", "description": "The primary line of the address, typically including the street number and name. This field is crucial for identifying the specific location of the address.", "availableValues": ["456 Brown Street", "987 Red Avenue", "456 Orange Street"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "city", "dataType": "STRING", "description": "The city corresponding to the address, providing regional context and aiding in location identification. It may contain missing values in some records.", "availableValues": ["Calgary", "Saskatoon", "<PERSON><PERSON><PERSON>", "Regina", "Saskatooon", "Winnipeg"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "postal_code", "dataType": "STRING", "description": "The postal code associated with the address, essential for accurate mail delivery and geographic identification. This field may also have missing values.", "availableValues": ["R1R 2A2", "S9S S8S", "T2Y 5B8", "S0S 0S0"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "country", "dataType": "STRING", "description": "The country where the address is located, consistently represented as 'CA' for Canada, although it may be absent for some records.", "availableValues": ["CA"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "country_sub_division_code", "dataType": "STRING", "description": "A code representing the province or territory within Canada, providing additional geographic specificity. This field may have missing values.", "availableValues": ["SK", "MB", "AB", "BC"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp indicating the last synchronization with the Fivetran data integration platform, which is vital for tracking data updates.", "subsetOfAvailableValues": ["2025-07-29 15:47:31.609000+00:00", "2025-07-29 15:47:31.621000+00:00", "2025-07-29 15:47:31.592000+00:00", "2025-07-29 15:47:31.616000+00:00", "2025-07-29 15:47:31.611000+00:00", "and 5 more..."], "is_unstructured": false}]}