{"tableName": "tax_agency", "description": "The tax_agency table contains records for tax agencies, each uniquely identified by an id. It stores the agency’s display name, creation and update timestamps, and a Fivetran sync timestamp. This standalone reference table is primarily used to associate tax rates with the appropriate tax agency but does not have foreign key relationships to other tables.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier assigned to each tax agency record, ensuring distinct identification across the database.", "availableValues": ["2", "3", "4", "5", "6", "7"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "display_name", "dataType": "STRING", "description": "The official name of the tax agency as displayed in the system, providing clear reference for users.", "availableValues": ["Saskatchewan Finance", "No Tax Agency", "Manitoba Finance", "Minister of Finance", "Revenu Quebec", "Canada Revenue Agency"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the exact date and time when the tax agency record was initially created, useful for tracking record history.", "availableValues": ["2021-03-19 07:44:58+00:00", "2021-03-19 08:00:28+00:00", "2021-03-19 07:46:28+00:00", "2021-03-19 08:01:09+00:00"], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp reflecting the last date and time when the tax agency's information was modified, aiding in data maintenance.", "availableValues": ["2021-03-19 07:44:58+00:00", "2021-03-19 08:00:28+00:00", "2021-03-19 07:46:28+00:00", "2021-03-19 08:01:09+00:00"], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp that shows when the tax agency record was last synchronized with the Fivetran service, ensuring data consistency.", "availableValues": ["2025-07-29 15:47:39.087000+00:00", "2025-07-29 22:01:13.459000+00:00", "2025-07-29 15:47:39.086000+00:00", "2025-07-29 22:01:13.460000+00:00", "2025-07-29 22:01:13.452000+00:00"], "is_unstructured": false}]}