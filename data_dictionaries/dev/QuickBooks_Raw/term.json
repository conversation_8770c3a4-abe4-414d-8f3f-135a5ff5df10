{"tableName": "term", "description": "The term table stores standardized payment terms, each uniquely identified by id. It includes the term name, the number of days until payment is due (due_days), active status, and a type field (always 'STANDARD'). Creation, last update, and Fivetran synchronization timestamps are tracked. This standalone reference table supports defining consistent payment conditions across the business.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier for each payment term record, ensuring that each entry can be distinctly referenced without duplicates.", "availableValues": ["2", "3", "4", "5"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "The designated string name of the payment term, which defines the specific payment conditions, such as 'Net 30' or 'Due on receipt'.", "availableValues": ["Net 60", "Net 30", "Due on receipt", "Net 15"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "active", "dataType": "BOOL", "description": "A boolean flag indicating the current status of the payment term, where 'true' signifies that the term is actively in use, and 'false' indicates it is inactive.", "is_unstructured": false}, {"name": "type", "dataType": "STRING", "description": "A string field that consistently denotes the classification of the term as 'STANDARD', indicating it adheres to predefined payment conventions.", "availableValues": ["STANDARD"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "due_days", "dataType": "INT64", "description": "An integer representing the specified number of days until payment is due, with values typically ranging from 0 to 99, such as '30' or '60'.", "availableValues": ["0", "15", "30", "60"], "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp recording the exact date and time when the payment term record was created, providing a historical reference for the data.", "availableValues": ["2021-03-18 21:10:47+00:00"], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the most recent date and time when the payment term record was modified, reflecting updates and changes made.", "availableValues": ["2021-03-18 21:10:47+00:00"], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp showing the last date and time the payment term record was synchronized with the Fivetran data pipeline, indicating data freshness.", "availableValues": ["2025-07-29 22:01:14.982000+00:00", "2025-07-29 22:01:14.978000+00:00"], "is_unstructured": false}]}