{"tableName": "item", "description": "The item table contains the business’s catalog of products and services, each uniquely identified by id. It includes detailed and short names (fully_qualified_name, name), type classification (e.g., ‘Service’, ‘NonInventory’), and timestamps for creation, modification, and data synchronization. This standalone table has no foreign key relationships.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique identifier for each item in the catalog, serving as the primary key that ensures each record is distinct.", "availableValues": ["2", "3", "4", "5", "6", "7", "8", "9"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "fully_qualified_name", "dataType": "STRING", "description": "A detailed classification of the item, providing clarity on the specific product or service being offered.", "availableValues": ["Other", "Paintless Dent Repair", "Ceramic Coating", "Hours", "Sales", "<PERSON><PERSON>", "Window Tint", "Paint Protection Film"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "A shorter or alternative name for the item, which may be used for display purposes or internal reference.", "availableValues": ["Other", "Paintless Dent Repair", "Ceramic Coating", "Hours", "Sales", "<PERSON><PERSON>", "Window Tint", "Paint Protection Film"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "type", "dataType": "STRING", "description": "Indicates whether the item is a service or a non-inventory product, aiding in the categorization of offerings.", "availableValues": ["NonInventory", "Service"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "Records the timestamp of when the item was added to the catalog, useful for auditing and historical tracking.", "subsetOfAvailableValues": ["2021-03-18 21:10:47+00:00", "2021-03-19 10:26:02+00:00", "2021-03-19 10:05:20+00:00", "2021-03-19 10:02:59+00:00", "2021-03-19 10:06:30+00:00", "and 2 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "Captures the timestamp of the last modification made to the item, essential for maintaining data accuracy.", "subsetOfAvailableValues": ["2021-03-18 21:10:47+00:00", "2021-03-19 10:26:02+00:00", "2021-03-19 10:05:20+00:00", "2021-03-19 10:02:59+00:00", "2021-03-19 10:06:30+00:00", "and 2 more..."], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "Denotes the last time the item data was synchronized with the Fivetran integration, ensuring data currency.", "availableValues": ["2025-07-29 15:47:35.092000+00:00", "2025-07-29 15:47:35.093000+00:00", "2025-07-29 15:47:35.091000+00:00", "2025-07-29 22:01:09.781000+00:00", "2025-07-29 22:01:09.784000+00:00"], "is_unstructured": false}]}