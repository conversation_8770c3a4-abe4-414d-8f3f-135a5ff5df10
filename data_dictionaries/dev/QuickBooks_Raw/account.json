{"tableName": "account", "description": "The account table stores financial account records, each uniquely identified by id. It captures account names, types, classifications, sub-types, and both individual and aggregate balances. Timestamps track creation, updates, and last data synchronization. The table does not reference other tables and serves as the primary source for the structure, status, and financial details of all accounts, supporting reporting and analysis.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier for each account, allowing distinct reference and access to individual account records.", "subsetOfAvailableValues": ["2", "3", "4", "5", "6", "and 22 more..."], "totalDistinctValueCount": 27, "is_unstructured": false}, {"name": "fully_qualified_name", "dataType": "STRING", "description": "The complete and descriptive name of the account, ensuring clarity in its purpose and type, unique for each account.", "subsetOfAvailableValues": ["PST Expense (BC)", "Sales", "Uncategorized Income", "Billable Expense Income", "Sales of Product Income", "and 22 more..."], "totalDistinctValueCount": 27, "is_unstructured": false}, {"name": "account_type", "dataType": "STRING", "description": "Categorizes the account into distinct types such as liability, asset, income, or expense, aiding in the identification of its general financial nature.", "availableValues": ["Other Current Liability", "Income", "Expense", "Other Current Asset", "Accounts Receivable", "Cost of Goods Sold", "Equity"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "The common name used for identifying the account in reports and queries, unique to each record.", "subsetOfAvailableValues": ["PST Expense (BC)", "Sales", "Uncategorized Income", "Billable Expense Income", "Sales of Product Income", "and 22 more..."], "totalDistinctValueCount": 27, "is_unstructured": false}, {"name": "classification", "dataType": "STRING", "description": "Specifies the financial classification of the account, such as Asset or Liability, essential for accurate financial reporting.", "availableValues": ["Liability", "Expense", "Revenue", "<PERSON><PERSON>", "Equity"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "Provides a more detailed categorization of the account, offering additional context about its specific functions or types.", "availableValues": ["GlobalTaxPayable", "SalesOfProductIncome", "GlobalTaxSuspense", "GlobalTaxExpense", "UndepositedFunds", "SuppliesMaterialsCogs", "RetainedEarnings", "OtherMiscellaneousServiceCost", "DiscountsRefundsGiven", "AccountsReceivable", "OtherCurrentAssets", "Inventory", "SuppliesMaterials"], "totalDistinctValueCount": 13, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the date and time when the account record was first created, useful for historical tracking.", "subsetOfAvailableValues": ["2021-03-18 21:10:47+00:00", "2021-03-19 07:44:58+00:00", "2021-03-19 10:01:30+00:00", "2021-03-19 08:00:28+00:00", "2021-03-19 07:46:28+00:00", "and 5 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp reflecting the last modification date and time of the account record, important for maintaining data integrity.", "subsetOfAvailableValues": ["2021-03-18 21:10:47+00:00", "2021-03-19 10:01:30+00:00", "2021-09-10 11:20:02+00:00", "2021-03-19 07:44:58+00:00", "2021-03-19 07:46:28+00:00", "and 8 more..."], "is_unstructured": false}, {"name": "balance_with_sub_accounts", "dataType": "BIGNUMERIC", "description": "The total balance of the account, including any associated sub-accounts, indicating net credit or debit status.", "availableValues": ["0", "7292", "-182.25", "-255.15"], "is_unstructured": false}, {"name": "balance", "dataType": "BIGNUMERIC", "description": "Represents the current balance of the account itself, providing insight into its financial standing without considering sub-accounts.", "availableValues": ["0", "7292", "-182.25", "-255.15"], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp showing the last synchronization of the account record with the Fivetran data integration tool, important for ensuring data freshness.", "subsetOfAvailableValues": ["2025-07-29 22:01:04.625000+00:00", "2025-07-29 15:47:28.483000+00:00", "2025-07-29 15:47:28.455000+00:00", "2025-07-29 15:47:28.460000+00:00", "2025-07-29 15:47:28.464000+00:00", "and 21 more..."], "is_unstructured": false}]}