{"tableName": "invoice", "description": "The invoice table records individual invoices, capturing unique identifiers, financial amounts (balance, tax, total), key dates (creation, due, transaction), and references to customers and billing/shipping addresses. Each invoice links to a customer, enabling customer billing and payment tracking. The table supports historical tracking and data synchronization, and each invoice is uniquely identified by id and doc_number.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique identifier for each invoice, ensuring distinct reference within the database.", "availableValues": ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "doc_number", "dataType": "STRING", "description": "A unique document number assigned to the invoice, crucial for external tracking and referencing.", "availableValues": ["1116", "1118", "1129", "2553", "2593", "2602", "2605", "2607", "2609", "2612", "2613"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "balance", "dataType": "BIGNUMERIC", "description": "The outstanding balance remaining on the invoice, indicating the amount yet to be paid.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "96.32", "and 6 more..."], "is_unstructured": false}, {"name": "sync_token", "dataType": "STRING", "description": "A token utilized for synchronization purposes, reflecting the version of the invoice record.", "availableValues": ["0", "1"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp marking the creation date and time of the invoice, essential for historical tracking.", "subsetOfAvailableValues": ["2021-09-01 10:05:02+00:00", "2021-07-16 08:23:25+00:00", "2021-07-16 08:30:03+00:00", "2021-07-16 06:00:03+00:00", "2021-09-10 10:55:02+00:00", "and 6 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the last modification date and time of the invoice record, ensuring data integrity.", "subsetOfAvailableValues": ["2021-09-01 10:05:02+00:00", "2021-07-16 08:23:25+00:00", "2021-07-16 08:30:03+00:00", "2021-07-16 06:00:03+00:00", "2021-09-10 10:55:02+00:00", "and 6 more..."], "is_unstructured": false}, {"name": "total_tax", "dataType": "BIGNUMERIC", "description": "The total tax amount applicable to the invoice, reflecting the financial impact of taxes included.", "subsetOfAvailableValues": ["0", "9", "108", "132", "10.32", "and 5 more..."], "is_unstructured": false}, {"name": "shipping_address_id", "dataType": "STRING", "description": "A reference to the ID of the shipping address associated with the invoice, used for delivery purposes.", "availableValues": ["20", "22"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "billing_address_id", "dataType": "STRING", "description": "A reference to the ID of the billing address linked to the invoice, used for payment processing.", "availableValues": ["20", "22"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "due_date", "dataType": "DATE", "description": "The date by which the payment for the invoice is expected, serving as a payment deadline.", "subsetOfAvailableValues": ["2021-10-10", "2021-10-01", "2021-08-12", "2021-08-18", "2021-09-01", "and 2 more..."], "is_unstructured": false}, {"name": "total_amount", "dataType": "BIGNUMERIC", "description": "The total charge on the invoice, encompassing all fees and taxes, indicating the financial total owed.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "96.32", "and 6 more..."], "is_unstructured": false}, {"name": "transaction_date", "dataType": "DATE", "description": "The date on which the transaction related to the invoice occurred, providing context for the billing.", "subsetOfAvailableValues": ["2021-09-10", "2021-09-01", "2021-07-13", "2021-07-19", "2021-08-02", "and 2 more..."], "is_unstructured": false}, {"name": "customer_id", "dataType": "STRING", "description": "A reference to the ID of the customer associated with the invoice, linking it to customer information.", "availableValues": ["2", "4", "5"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp indicating the last synchronization event with Fivetran, ensuring data consistency.", "subsetOfAvailableValues": ["2025-07-29 15:47:34.287000+00:00", "2025-07-29 15:47:34.251000+00:00", "2025-07-29 15:47:34.262000+00:00", "2025-07-29 15:47:34.219000+00:00", "2025-07-29 15:47:34.302000+00:00", "and 6 more..."], "is_unstructured": false}], "relationships": [{"child_table": "invoice", "parent_table": "account", "key_column_mapping": [{"parent_column": "id", "child_column": "customer_id"}], "relationship_type": "one-to-many"}, {"child_table": "invoice", "parent_table": "customer", "key_column_mapping": [{"parent_column": "id", "child_column": "customer_id"}], "relationship_type": "one-to-many"}]}