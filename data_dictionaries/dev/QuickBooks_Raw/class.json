{"tableName": "class", "description": "The class table stores standalone class entities, each uniquely identified by an id, name, and fully qualified name. It captures whether a class is active and maintains timestamps for creation, last update, and Fivetran synchronization. This table has no relationships to other tables and is used for categorizing or grouping data independently within the database.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier for each class record, ensuring distinct reference across the database.", "availableValues": ["500000000000616071"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "fully_qualified_name", "dataType": "STRING", "description": "A comprehensive string representation of the class entity's name, designed to uniquely identify the entity in a complete manner.", "availableValues": ["Saskatoon"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "The distinct name of the class entity, which must be unique within the table for easy recognition and categorization.", "availableValues": ["Saskatoon"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "active", "dataType": "BOOL", "description": "A boolean value indicating the operational status of the class entity, where 'True' signifies that the entity is currently active.", "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp marking the exact date and time when the class record was created, essential for historical tracking.", "availableValues": ["2021-03-30 10:10:28+00:00"], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the most recent date and time the class record was updated, crucial for data accuracy and integrity.", "availableValues": ["2021-03-30 10:10:28+00:00"], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp that records the last synchronization time with the Fivetran data integration tool, providing insights into data freshness.", "availableValues": ["2025-07-29 22:01:05.723000+00:00"], "is_unstructured": false}]}