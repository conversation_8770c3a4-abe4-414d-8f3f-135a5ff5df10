{"tableName": "tax_code", "description": "The tax_code table contains unique tax codes, each identified by a primary key (id) and a unique name. It includes a descriptive explanation, creation and update timestamps, and a Fivetran sync timestamp. Tax codes in this table are referenced by the tax_rate_detail table, supporting tax classification, reporting, and mapping to specific tax rates.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier for each tax code record, serving as the primary key to ensure distinct entries and prevent duplication.", "availableValues": ["3", "4", "5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20", "21", "22", "23", "24", "25", "26", "27"], "totalDistinctValueCount": 25, "is_unstructured": false}, {"name": "description", "dataType": "STRING", "description": "A detailed textual explanation of the tax code's purpose, which may not always be unique across records, providing context for its application.", "availableValues": ["Tax adjustment", "Combined federal and provincial tax (Manitoba)", "Combined federal and provincial tax (15% New Brunswick)", "Harmonized federal and provincial tax (British Columbia)", "Combined federal and provincial tax (British Columbia)", "Provincial sales tax only (Saskatchewan)", "Harmonized federal and provincial tax (Ontario)", "Provincial sales tax (8%) only (Manitoba)", "Provincial sales tax only, after Jan. 1, 2013 (Quebec)", "Harmonized federal and provincial tax (New Brunswick)", "Adjustment", "Combined federal and provincial tax (Saskatchewan)", "Combined federal and provincial tax (Quebec), after Jan. 1, 2013", "Provincial sales tax only (Manitoba)", "Out of scope", "Federal goods and services tax", "Zero-rated", "Harmonized federal and provincial tax (Nova Scotia) 2025", "Harmonized federal and provincial tax (Nova Scotia)", "Provincial sales tax only (British Columbia)", "Combined federal and provincial tax (8% Manitoba)", "Tax-exempt"], "totalDistinctValueCount": 22, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "A unique string representing the tax code, crucial for identification and reference in reporting and queries within the database.", "availableValues": ["GST/PST MB", "PST MB 2013", "PST MB Adjustment", "GST/PST BC", "PST BC Adjustment", "PST BC", "GST/PST SK", "PST SK", "HST ON", "GST/QST QC - 9.975", "QC Tax Adjustment", "HST NB", "HST NB 2016", "HST NS", "HST NS 2025", "PST SK Adjustment", "PST MB", "Out of Scope", "GST", "Zero-rated", "QST QC - 9.975", "HST BC", "GST/PST MB 2013", "GST/HST Adjustment", "Exempt"], "totalDistinctValueCount": 25, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the exact date and time when the tax code record was created, useful for historical tracking of entries.", "subsetOfAvailableValues": ["2021-03-19 07:44:58+00:00", "2021-03-19 07:46:29+00:00", "2021-03-19 08:01:09+00:00", "2021-03-19 07:44:59+00:00", "2021-03-19 08:00:29+00:00", "and 4 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp that records the last date and time the tax code record was modified, aiding in auditing changes to the records.", "subsetOfAvailableValues": ["2021-03-19 07:44:58+00:00", "2021-03-19 07:46:29+00:00", "2021-03-19 08:01:09+00:00", "2021-03-19 07:44:59+00:00", "2021-03-19 08:00:29+00:00", "and 4 more..."], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp that indicates when this record was last synchronized with Fivetran, ensuring data consistency and reflecting the latest updates.", "subsetOfAvailableValues": ["2025-07-29 15:47:39.656000+00:00", "2025-07-29 15:47:39.643000+00:00", "2025-07-29 15:47:39.658000+00:00", "2025-07-29 15:47:39.654000+00:00", "2025-07-29 15:47:39.650000+00:00", "and 11 more..."], "is_unstructured": false}]}