{"tableName": "tax_rate_detail", "description": "The tax_rate_detail table maps tax codes to tax rates, specifying the transaction type (Purchase or Sales) and the sequence (tax_order) in which rates are applied. Each entry is uniquely identified by tax_code_id and tax_rate_id, enabling multiple rates per code. The table facilitates tax calculation logic and links to both tax_code and tax_rate, with synchronization status tracked by _fivetran_synced.", "fields": [{"name": "tax_code_id", "dataType": "STRING", "description": "A unique identifier for the tax code linked to the tax rate, enabling the association of multiple tax rates with a single tax code.", "availableValues": ["3", "4", "5", "6", "8", "9", "10", "11", "13", "14", "15", "17", "18", "20", "21", "23", "24", "25", "26", "27"], "totalDistinctValueCount": 20, "is_unstructured": false}, {"name": "tax_rate_id", "dataType": "STRING", "description": "A unique identifier for the tax rate linked to the tax code, indicating that some tax codes may correspond to different tax rates.", "subsetOfAvailableValues": ["2", "3", "4", "5", "6", "and 25 more..."], "totalDistinctValueCount": 30, "is_unstructured": false}, {"name": "type", "dataType": "STRING", "description": "Specifies the nature of the transaction (either 'Purchase' or 'Sales') that the tax rate applies to, facilitating differentiation between transaction types.", "availableValues": ["Purchase", "Sales"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "tax_order", "dataType": "INT64", "description": "An integer that determines the sequence in which tax rates are applied, with lower values indicating primary rates and higher values indicating secondary rates.", "availableValues": ["0", "1"], "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp that records the last time this record was synchronized with Fivetran, helping to monitor data freshness and integrity.", "subsetOfAvailableValues": ["2025-07-29 15:47:39.647000+00:00", "2025-07-29 15:47:39.650000+00:00", "2025-07-29 15:47:39.658000+00:00", "2025-07-29 15:47:39.643000+00:00", "2025-07-29 15:47:39.653000+00:00", "and 12 more..."], "is_unstructured": false}], "relationships": [{"child_table": "tax_rate_detail", "parent_table": "tax_rate", "key_column_mapping": [{"parent_column": "id", "child_column": "tax_code_id"}], "relationship_type": "one-to-many"}, {"child_table": "tax_rate_detail", "parent_table": "tax_rate", "key_column_mapping": [{"parent_column": "id", "child_column": "tax_rate_id"}], "relationship_type": "one-to-many"}, {"child_table": "tax_rate_detail", "parent_table": "tax_code", "key_column_mapping": [{"parent_column": "id", "child_column": "tax_code_id"}], "relationship_type": "one-to-many"}]}