{"tableName": "customer", "description": "The customer table contains records for individual customers and related companies, capturing names, contact details, financial balances, and references to billing and shipping addresses. Each entry is uniquely identified by id. The table also tracks synchronization status and timestamps for creation and updates, supporting customer management, billing, and financial reporting.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique identifier for each customer record, essential for distinguishing individual customers and maintaining data integrity.", "availableValues": ["2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "family_name", "dataType": "STRING", "description": "The surname of the customer, which may be absent for some records, helping to identify customers with common last names.", "availableValues": ["Thumb", "Green"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "fully_qualified_name", "dataType": "STRING", "description": "A complete name that combines the customer's first and last names, ensuring unique identification for each record.", "availableValues": ["DCCHail Winnipeg", "Pickled Peppers Bodyshop", "<PERSON>", "ABC Company", "<PERSON><PERSON>", "<PERSON>", "Carl manit CST", "Cash Customer - SK", "VPS SK", "ZOOMi Technologies", "Uptown Bodyshop"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "given_name", "dataType": "STRING", "description": "The first name of the customer, which may also be missing for certain records, providing further identification alongside the family name.", "availableValues": ["<PERSON>", "<PERSON>"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "company_name", "dataType": "STRING", "description": "The name of the company associated with the customer, which can be absent for some entries, aiding in categorizing business customers.", "availableValues": ["VPS SK", "ABC Company", "Pickled Peppers Bodyshop", "ZOOMi Technologies", "Cash Customer - SK", "Uptown Bodyshop", "DCCHail Winnipeg", "<PERSON><PERSON>"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "display_name", "dataType": "STRING", "description": "A user-friendly and unique name for representing the customer in user interfaces, enhancing the customer experience.", "availableValues": ["DCCHail Winnipeg", "Pickled Peppers Bodyshop", "<PERSON>", "ABC Company", "<PERSON><PERSON>", "<PERSON>", "Carl manit CST", "Cash Customer - SK", "VPS SK", "ZOOMi Technologies", "Uptown Bodyshop"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "print_on_check_name", "dataType": "STRING", "description": "The name that will appear on financial documents such as checks, ensuring accurate representation for payment processing.", "availableValues": ["DCCHail Winnipeg", "Pickled Peppers Bodyshop", "<PERSON>", "ABC Company", "<PERSON><PERSON>", "<PERSON>", "Carl manit CST", "Cash Customer - SK", "VPS SK", "ZOOMi Technologies", "Uptown Bodyshop"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "sync_token", "dataType": "STRING", "description": "A status token indicating the synchronization state of the customer record across systems, facilitating data consistency.", "availableValues": ["0", "1", "2"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "balance_with_jobs", "dataType": "BIGNUMERIC", "description": "The customer's balance accounting for jobs, providing insight into financial standing related to specific tasks or projects.", "availableValues": ["0", "3347.36", "2562.56", "1382.08"], "is_unstructured": false}, {"name": "balance", "dataType": "BIGNUMERIC", "description": "The overall account balance of the customer, crucial for financial assessments and transaction processing.", "availableValues": ["0", "3347.36", "2562.56", "1382.08"], "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp marking the creation time of the customer record, serving as a historical reference for data management.", "subsetOfAvailableValues": ["2021-03-30 16:50:10+00:00", "2021-03-24 21:16:42+00:00", "2021-03-23 20:09:19+00:00", "2021-03-23 19:47:13+00:00", "2021-03-30 16:57:02+00:00", "and 6 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating the last modification of the record, essential for tracking changes and data updates.", "subsetOfAvailableValues": ["2021-04-08 08:19:47+00:00", "2021-03-25 10:10:02+00:00", "2021-07-16 06:00:03+00:00", "2021-03-30 16:57:02+00:00", "2021-08-29 19:01:33+00:00", "and 4 more..."], "is_unstructured": false}, {"name": "email", "dataType": "STRING", "description": "The customer's email address, which may be missing for some records, serving as a primary contact method for communication.", "availableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "phone_number", "dataType": "STRING", "description": "The contact phone number for the customer, which may be absent for certain records, providing an additional method for customer outreach.", "availableValues": ["************", "************"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "shipping_address_id", "dataType": "STRING", "description": "An identifier linking to the customer's shipping address, critical for order fulfillment and delivery processes.", "availableValues": ["4", "20", "22", "24", "25", "84", "92", "96", "98", "99"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "bill_address_id", "dataType": "STRING", "description": "An identifier linking to the customer's billing address, essential for invoicing and payment processing.", "availableValues": ["4", "20", "22", "24", "25", "84", "92", "96", "98", "99"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp indicating the last synchronization of the record with the Fivetran system, ensuring data consistency across platforms.", "subsetOfAvailableValues": ["2025-07-29 15:47:31.616000+00:00", "2025-07-29 15:47:31.606000+00:00", "2025-07-29 15:47:31.605000+00:00", "2025-07-29 15:47:31.604000+00:00", "2025-07-29 15:47:31.617000+00:00", "and 6 more..."], "is_unstructured": false}]}