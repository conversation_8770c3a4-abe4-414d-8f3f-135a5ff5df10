{"tableName": "currency", "description": "The currency table is a reference table listing supported currencies, currently limited to a single record for the Canadian Dollar (CAD). It includes the unique currency code (id), the full currency name (name), and the timestamp of the latest data synchronization (_fivetran_synced), ensuring data freshness for currency-related operations.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique identifier for the currency record, currently set to 'CAD' for Canadian Dollar, ensuring distinct identification.", "availableValues": ["CAD"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "The full name of the currency, specifically 'Canadian Dollar', providing a clear human-readable reference for the currency code.", "availableValues": ["Canadian Dollar"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp indicating the exact moment when the currency data was last synchronized with Fivetran, ensuring data freshness.", "availableValues": ["2025-07-29 22:01:09.151000+00:00"], "is_unstructured": false}]}