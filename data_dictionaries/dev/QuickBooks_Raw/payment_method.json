{"tableName": "payment_method", "description": "The payment_method table is a standalone reference table listing all available payment methods for transactions. Each record includes a unique identifier, a descriptive name, and a type indicating whether it is a ‘CREDIT_CARD’ or ‘NON_CREDIT_CARD’. The _fivetran_synced column records the last data synchronization timestamp. This table has no foreign key relationships.", "fields": [{"name": "id", "dataType": "STRING", "description": "A unique string identifier for each payment method record, ensuring distinct reference across all payment methods.", "availableValues": ["2", "3", "4", "5"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "name", "dataType": "STRING", "description": "The descriptive name of the payment method used in transactions, such as 'Cheque', 'Cash', or 'Credit Card', which aids in identification.", "availableValues": ["Cheque", "Cash", "Direct Debit", "Credit Card"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "type", "dataType": "STRING", "description": "Categorizes the payment method as either 'CREDIT_CARD' or 'NON_CREDIT_CARD', allowing for quick classification and filtering of payment methods.", "availableValues": ["NON_CREDIT_CARD", "CREDIT_CARD"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp indicating the last synchronization with the FiveTran service, providing a record of data freshness and update intervals.", "availableValues": ["2025-07-29 22:01:10.994000+00:00", "2025-07-29 22:01:10.992000+00:00", "2025-07-29 22:01:10.993000+00:00"], "is_unstructured": false}]}