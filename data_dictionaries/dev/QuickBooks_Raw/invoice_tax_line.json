{"tableName": "invoice_tax_line", "description": "The invoice_tax_line table records detailed tax breakdowns for each invoice, with multiple rows per invoice representing different tax rates applied. Each entry specifies the tax amount, calculation method (percent-based or fixed), taxable base, tax percentage, and references the corresponding tax rate. The table links to both invoice and tax_rate tables via invoice_id and tax_rate_id, supporting precise analysis of all taxes charged on each invoice. Rows are uniquely identified by the combination of invoice_id and index, enabling tracking of multiple tax lines per invoice. Data freshness is tracked by the _fivetran_synced timestamp.", "fields": [{"name": "invoice_id", "dataType": "STRING", "description": "A unique identifier for each invoice, linking to the corresponding record in the parent 'invoice' table. This field can appear multiple times to represent different tax lines associated with the same invoice.", "availableValues": ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "index", "dataType": "INT64", "description": "An integer representing the order of the tax line within an invoice, used to sequence multiple tax lines for a given invoice. Values typically range from 0 to 1, indicating the position of each tax line.", "availableValues": ["0", "1"], "is_unstructured": false}, {"name": "amount", "dataType": "BIGNUMERIC", "description": "The calculated monetary amount of the tax line, expressed in BIGNUMERIC format. This field reflects the specific tax amount associated with the tax line and is always a positive value.", "subsetOfAvailableValues": ["0", "1", "4", "45", "55", "and 14 more..."], "is_unstructured": false}, {"name": "percent_based", "dataType": "BOOL", "description": "A boolean indicating whether the tax amount is calculated as a percentage of the net taxable amount, providing clarity on the method of tax calculation for each line.", "is_unstructured": false}, {"name": "net_amount_taxable", "dataType": "BIGNUMERIC", "description": "The portion of the invoice that is subject to tax, represented in BIGNUMERIC format. This field indicates the base amount from which the tax is calculated.", "subsetOfAvailableValues": ["20", "27", "75", "80", "86", "and 6 more..."], "is_unstructured": false}, {"name": "tax_percent", "dataType": "BIGNUMERIC", "description": "The percentage rate applied to the net taxable amount for calculating the tax, stored as a BIGNUMERIC value. This field indicates the specific tax rate applicable to the transaction.", "availableValues": ["0", "5", "7"], "is_unstructured": false}, {"name": "tax_rate_id", "dataType": "STRING", "description": "A unique identifier for the tax rate associated with the tax line, linking to the 'tax_rate' table for detailed information about the tax rate applied.", "availableValues": ["3", "5", "17"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A TIMESTAMP indicating the last synchronization time with Fivetran, allowing tracking of data freshness and updates for this record.", "subsetOfAvailableValues": ["2025-07-29 15:47:34.282000+00:00", "2025-07-29 15:47:34.268000+00:00", "2025-07-29 15:47:34.304000+00:00", "2025-07-29 15:47:34.245000+00:00", "2025-07-29 15:47:34.313000+00:00", "and 6 more..."], "is_unstructured": false}], "relationships": [{"child_table": "invoice_tax_line", "parent_table": "tax_rate", "key_column_mapping": [{"parent_column": "id", "child_column": "tax_rate_id"}], "relationship_type": "one-to-many"}, {"child_table": "invoice_tax_line", "parent_table": "invoice", "key_column_mapping": [{"parent_column": "id", "child_column": "invoice_id"}], "relationship_type": "one-to-many"}]}