{"tableName": "invoice_line", "description": "The invoice_line table contains individual line items associated with each invoice, detailing products, services, subtotals, or discounts billed. Each entry links to its parent invoice via invoice_id and includes fields for item order, description, amount, detail type, related sales item, applicable tax code, and last sync timestamp. This table enables itemized billing and supports one-to-many relationships from invoices to their detailed line items.", "fields": [{"name": "invoice_id", "dataType": "STRING", "description": "A unique identifier linking this line item to its parent invoice, establishing a one-to-many relationship between invoices and their line items.", "availableValues": ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "index", "dataType": "INT64", "description": "An integer that specifies the position of this line item within the invoice, used to maintain the order of line items.", "subsetOfAvailableValues": ["0", "1", "2", "3", "4", "and 23 more..."], "is_unstructured": false}, {"name": "id", "dataType": "STRING", "description": "A unique identifier for the line item, which may be absent for some records, distinguishing each line item in the invoice.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 22 more..."], "totalDistinctValueCount": 27, "is_unstructured": false}, {"name": "line_num", "dataType": "INT64", "description": "An integer representing the sequential number of the line item within its invoice, helping to order the items alongside the index field.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 22 more..."], "is_unstructured": false}, {"name": "description", "dataType": "STRING", "description": "A textual description of the line item, detailing the service or product billed, which may be missing for some records.", "subsetOfAvailableValues": ["PPF-C", "<PERSON><PERSON>", "PDR", "Paint Protection Film", "Window Tint", "and 34 more..."], "totalDistinctValueCount": 39, "is_unstructured": false}, {"name": "amount", "dataType": "BIGNUMERIC", "description": "The monetary value of the line item, represented as a numeric field capable of handling large values.", "subsetOfAvailableValues": ["1", "5", "9", "10", "11", "and 21 more..."], "is_unstructured": false}, {"name": "detail_type", "dataType": "STRING", "description": "A string indicating the category of the line item, such as a standard sale, subtotal, or discount, providing context for its role in the invoice.", "availableValues": ["SalesItemLineDetail", "SubTotalLineDetail", "DiscountLineDetail"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "sales_item_item_id", "dataType": "STRING", "description": "A string identifying the specific sales item associated with this line, which may be absent for a portion of records.", "availableValues": ["4", "5", "6", "7", "8", "9"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "sales_item_tax_code_id", "dataType": "STRING", "description": "A string denoting the tax code applicable to the sales item, with potential absence in some records.", "availableValues": ["3", "8"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "_fivetran_synced", "dataType": "TIMESTAMP", "description": "A timestamp that records the last synchronization time with Fivetran, indicating when the data was last updated.", "subsetOfAvailableValues": ["2025-07-29 15:47:34.312000+00:00", "2025-07-29 15:47:34.311000+00:00", "2025-07-29 15:47:34.310000+00:00", "2025-07-29 15:47:34.256000+00:00", "2025-07-29 15:47:34.313000+00:00", "and 18 more..."], "is_unstructured": false}], "relationships": [{"child_table": "invoice_line", "parent_table": "invoice", "key_column_mapping": [{"parent_column": "id", "child_column": "invoice_id"}], "relationship_type": "one-to-many"}]}