{"tableName": "quickbooks__general_ledger", "description": "The quickbooks__general_ledger table captures detailed financial transactions, including transaction amounts, account and customer identifiers, account classifications, transaction types, and sources. Each record is uniquely identified and supports tracking of adjustments, running balances, and timestamps. This table enables comprehensive analysis of account activity, customer-specific transactions, and supports accurate financial reporting and auditing within the accounting system.", "fields": [{"name": "unique_id", "dataType": "STRING", "description": "A unique string identifier for each record in the general ledger, ensuring distinct reference for every entry.", "subsetOfAvailableValues": ["23a45771e15fcb4f1f65b4820fc7b6b1", "86732109980bb963e6882bface89b485", "e40355c76f0a117ba0a6cbb55e1f3930", "28b68dfe8ab493fa05745def68b7b3ad", "ea1e4c74db4a78056991657d9bb770ae", "and 93 more..."], "totalDistinctValueCount": 98, "is_unstructured": false}, {"name": "transaction_id", "dataType": "STRING", "description": "A unique identifier for the transaction tied to each record, facilitating connections between related entries.", "availableValues": ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "transaction_index", "dataType": "INT64", "description": "An integer representing the sequential order of transactions within the ledger, crucial for tracking transaction sequences.", "subsetOfAvailableValues": ["0", "1", "2", "3", "4", "and 22 more..."], "is_unstructured": false}, {"name": "transaction_date", "dataType": "DATE", "description": "The specific date when the transaction occurred, represented in a date format, allowing for chronological tracking.", "subsetOfAvailableValues": ["2021-09-10", "2021-07-13", "2021-09-01", "2021-08-02", "2021-04-28", "and 2 more..."], "is_unstructured": false}, {"name": "customer_id", "dataType": "STRING", "description": "A string identifier for the customer linked to the transaction, enabling customer-specific transaction analysis.", "availableValues": ["2", "4", "5"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "amount", "dataType": "BIGNUMERIC", "description": "The monetary value of the transaction, represented as a bignumeric type, reflecting the financial impact on the ledger.", "subsetOfAvailableValues": ["1", "5", "9", "10", "11", "and 13 more..."], "is_unstructured": false}, {"name": "account_id", "dataType": "STRING", "description": "A string identifier for the account associated with the transaction, used for account-specific tracking and reporting.", "availableValues": ["6", "27", "28"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "account_name", "dataType": "STRING", "description": "The name of the account related to the transaction, providing clarity on the financial category involved.", "availableValues": ["Accounts Receivable (A/R)", "Sales", "Discounts given"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "parent_account_name", "dataType": "STRING", "description": "The name of the parent account in the hierarchy, potentially mirroring the account_name for clarity in account relationships.", "availableValues": ["Accounts Receivable (A/R)", "Sales", "Discounts given"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "account_type", "dataType": "STRING", "description": "The classification of the account, indicating its category such as 'Assets' or 'Income', essential for financial reporting.", "availableValues": ["Accounts Receivable", "Income"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "A more detailed classification of the account, providing specific types for nuanced financial categorization.", "availableValues": ["AccountsReceivable", "SalesOfProductIncome", "DiscountsRefundsGiven"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "financial_statement_helper", "dataType": "STRING", "description": "Indicates whether the account is part of a balance sheet or income statement, guiding financial reporting orientation.", "availableValues": ["balance_sheet", "income_statement"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_current_balance", "dataType": "BIGNUMERIC", "description": "The current balance of the account, expressed as a bignumeric, showing the financial standing at the time of the transaction.", "availableValues": ["0", "7292"], "is_unstructured": false}, {"name": "account_class", "dataType": "STRING", "description": "The classification type of the account, such as 'Asset' or 'Revenue', aiding in financial analysis and reporting.", "availableValues": ["<PERSON><PERSON>", "Revenue"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "transaction_type", "dataType": "STRING", "description": "Indicates whether the transaction is a 'debit' or 'credit', crucial for understanding the nature of the financial impact.", "availableValues": ["debit", "credit"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "transaction_source", "dataType": "STRING", "description": "The origin of the transaction, detailing where it was initiated, with common sources like 'invoice' or 'invoice discount'.", "availableValues": ["invoice", "invoice discount"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_transaction_type", "dataType": "STRING", "description": "Reflects the nature of the transaction for the account, indicating whether it is a 'debit' or 'credit', similar to transaction_type.", "availableValues": ["debit", "credit"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "created_at", "dataType": "TIMESTAMP", "description": "A timestamp indicating when the record was created, essential for tracking the history of entries.", "subsetOfAvailableValues": ["2021-09-10 11:00:02+00:00", "2021-07-16 08:23:25+00:00", "2021-09-10 11:20:02+00:00", "2021-08-18 11:23:51+00:00", "2021-09-10 10:55:02+00:00", "and 6 more..."], "is_unstructured": false}, {"name": "updated_at", "dataType": "TIMESTAMP", "description": "A timestamp showing when the record was last modified, useful for auditing changes over time.", "subsetOfAvailableValues": ["2021-09-10 11:00:02+00:00", "2021-07-16 08:23:25+00:00", "2021-09-10 11:20:02+00:00", "2021-08-18 11:23:51+00:00", "2021-09-10 10:55:02+00:00", "and 6 more..."], "is_unstructured": false}, {"name": "adjusted_amount", "dataType": "BIGNUMERIC", "description": "The financial amount adjusted for the transaction, which may be positive or negative, reflecting corrections made.", "subsetOfAvailableValues": ["1", "5", "9", "10", "11", "and 13 more..."], "is_unstructured": false}, {"name": "adjusted_converted_amount", "dataType": "BIGNUMERIC", "description": "The converted amount reflecting any adjustments made, also potentially containing both positive and negative values.", "subsetOfAvailableValues": ["1", "5", "9", "10", "11", "and 13 more..."], "is_unstructured": false}, {"name": "running_balance", "dataType": "BIGNUMERIC", "description": "The cumulative total of transactions up to the current record, showing ongoing financial status that may vary.", "subsetOfAvailableValues": ["1000", "2000", "3234", "4534", "4646", "and 91 more..."], "is_unstructured": false}, {"name": "running_converted_balance", "dataType": "BIGNUMERIC", "description": "The converted cumulative balance indicating the total after adjustments, also varying between positive and negative values.", "subsetOfAvailableValues": ["1000", "2000", "3234", "4534", "4646", "and 91 more..."], "is_unstructured": false}], "relationships": [{"child_table": "quickbooks__general_ledger", "parent_table": "quickbooks__ap_ar_enhanced", "key_column_mapping": [{"parent_column": "transaction_id", "child_column": "transaction_id"}], "relationship_type": "one-to-many"}]}