{"tableName": "quickbooks__balance_sheet", "description": "The quickbooks__balance_sheet table captures daily financial positions for each account, identified by calendar_date and account_id. It includes account classifications (Asset or Equity), types, subtypes, names, and both original and converted monetary values. Period_first_day and period_last_day define the reporting interval. The account_ordinal column organizes account order. This standalone table supports detailed balance sheet analysis by tracking account-level financial data across reporting periods.", "fields": [{"name": "calendar_date", "dataType": "DATE", "description": "The specific date for which the balance sheet data is recorded, used to track the company's financial position on that day.", "subsetOfAvailableValues": ["2024-06-01", "2022-12-01", "2021-06-01", "2022-08-01", "2024-07-01", "and 47 more..."], "is_unstructured": false}, {"name": "period_first_day", "dataType": "DATE", "description": "The start date of the financial period covered by the balance sheet entry, providing context for the associated financial data.", "subsetOfAvailableValues": ["2024-06-01", "2022-12-01", "2021-06-01", "2022-08-01", "2024-07-01", "and 47 more..."], "is_unstructured": false}, {"name": "period_last_day", "dataType": "DATE", "description": "The end date of the financial period represented in the balance sheet record, indicating the duration of the financial data being analyzed.", "subsetOfAvailableValues": ["2024-06-30", "2022-12-31", "2021-06-30", "2022-08-31", "2024-07-31", "and 47 more..."], "is_unstructured": false}, {"name": "account_class", "dataType": "STRING", "description": "A categorization of the account as either 'Asset' or 'Equity', aiding in the understanding of the account's nature for financial analysis.", "availableValues": ["<PERSON><PERSON>", "Equity"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "parent_account_name", "dataType": "STRING", "description": "The name of the parent account linked to the balance sheet entry, typically providing broader context, though often missing in records.", "availableValues": ["Accounts Receivable (A/R)"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_type", "dataType": "STRING", "description": "A further classification of the account as 'Accounts Receivable' or 'Equity', helping analysts differentiate between account types in financial assessments.", "availableValues": ["Accounts Receivable", "Equity"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "An additional categorization of accounts, specifying whether they are 'AccountsReceivable' or 'RetainedEarnings' for detailed financial analysis.", "availableValues": ["AccountsReceivable", "RetainedEarnings"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_id", "dataType": "STRING", "description": "A unique identifier for each account within the balance sheet, essential for distinguishing accounts during data queries and analysis.", "availableValues": ["27", "9999"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_name", "dataType": "STRING", "description": "The descriptive name of the account, providing clarity on the financial data associated with each account in the balance sheet.", "availableValues": ["Accounts Receivable (A/R)", "Net Income Adjustment"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "amount", "dataType": "BIGNUMERIC", "description": "The financial value associated with the account for the specified date, critical for evaluating the monetary state of accounts.", "availableValues": ["2000", "6854.6", "5343.6", "6566.6"], "is_unstructured": false}, {"name": "converted_amount", "dataType": "BIGNUMERIC", "description": "The potentially adjusted financial value of the account for different reporting standards or currencies, important for understanding account valuation.", "availableValues": ["2000", "6854.6", "5343.6", "6566.6"], "is_unstructured": false}, {"name": "account_ordinal", "dataType": "INT64", "description": "The order of the accounts within the balance sheet, facilitating logical organization and readability of the financial data.", "availableValues": ["1", "3"], "is_unstructured": false}]}