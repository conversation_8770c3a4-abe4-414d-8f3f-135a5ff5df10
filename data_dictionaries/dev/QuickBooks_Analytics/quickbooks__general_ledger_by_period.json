{"tableName": "quickbooks__general_ledger_by_period", "description": "quickbooks__general_ledger_by_period contains summarized general ledger account data by financial period, including balances, net changes, and classifications for both original and converted currencies. Each record details account identifiers, hierarchy, type, and financial statement relevance, along with the period’s start and end dates. The table enables period-over-period analysis for income statement and balance sheet accounts, with no enforced relationships or unique constraints.", "fields": [{"name": "account_id", "dataType": "STRING", "description": "A unique string identifier for each account in the general ledger, representing various account types.", "availableValues": ["6", "27", "28", "9999"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "account_number", "dataType": "STRING", "description": "The associated account number for transactions, often defaulting to '9999-00' for many records.", "availableValues": ["9999-00"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_name", "dataType": "STRING", "description": "The descriptive name of the account as listed in the general ledger, such as 'Accounts Receivable' or 'Sales'.", "availableValues": ["Accounts Receivable (A/R)", "Net Income Adjustment", "Sales", "Discounts given"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "parent_account_name", "dataType": "STRING", "description": "The name of the parent account to which this account is linked, which may be absent for some records.", "availableValues": ["Accounts Receivable (A/R)", "Sales", "Discounts given"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "account_type", "dataType": "STRING", "description": "The classification of the account indicating its category for financial reporting, such as 'Income' or 'Equity'.", "availableValues": ["Income", "Equity", "Accounts Receivable"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "A more detailed classification of the account providing insights into its specific nature, such as 'SalesOfProductIncome'.", "availableValues": ["AccountsReceivable", "RetainedEarnings", "SalesOfProductIncome", "DiscountsRefundsGiven"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "account_class", "dataType": "STRING", "description": "The broader classification of the account that defines its role in financial statements, like 'Asset' or 'Revenue'.", "availableValues": ["Revenue", "Equity", "<PERSON><PERSON>"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "financial_statement_helper", "dataType": "STRING", "description": "Indicates whether the record pertains to a balance sheet or income statement, with equal distribution in records.", "availableValues": ["balance_sheet", "income_statement"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "date_year", "dataType": "DATE", "description": "The year associated with the financial records, formatted as a date and typically unique for groups of records.", "availableValues": ["2024-01-01", "2023-01-01", "2022-01-01", "2021-01-01", "2025-01-01"], "is_unstructured": false}, {"name": "period_first_day", "dataType": "DATE", "description": "The starting date of the financial period represented in this record, usually unique for smaller groups of records.", "subsetOfAvailableValues": ["2021-04-01", "2021-11-01", "2022-10-01", "2022-12-01", "2022-09-01", "and 47 more..."], "is_unstructured": false}, {"name": "period_last_day", "dataType": "DATE", "description": "The ending date of the financial period represented in this record, typically unique for smaller groups of records.", "subsetOfAvailableValues": ["2021-04-30", "2021-11-30", "2022-10-31", "2022-12-31", "2022-09-30", "and 47 more..."], "is_unstructured": false}, {"name": "period_net_change", "dataType": "BIGNUMERIC", "description": "The net change in the account balance over the financial period, which can be positive or negative.", "subsetOfAvailableValues": ["0", "288", "1223", "2000", "3646", "and 2 more..."], "is_unstructured": false}, {"name": "period_beginning_balance", "dataType": "BIGNUMERIC", "description": "The balance of the account at the start of the financial period, which may occasionally be missing.", "availableValues": ["0", "2000", "6854.6", "6566.6", "5343.6"], "is_unstructured": false}, {"name": "period_ending_balance", "dataType": "BIGNUMERIC", "description": "The balance of the account at the end of the financial period, providing a summary of account status.", "availableValues": ["0", "2000", "6854.6", "6566.6", "5343.6"], "is_unstructured": false}, {"name": "period_net_converted_change", "dataType": "BIGNUMERIC", "description": "The net change in the account balance for the period, converted into another currency or format.", "subsetOfAvailableValues": ["0", "288", "1223", "2000", "3646", "and 2 more..."], "is_unstructured": false}, {"name": "period_beginning_converted_balance", "dataType": "BIGNUMERIC", "description": "The converted balance of the account at the beginning of the financial period, which may be missing in some records.", "availableValues": ["0", "2000", "6854.6", "6566.6", "5343.6"], "is_unstructured": false}, {"name": "period_ending_converted_balance", "dataType": "BIGNUMERIC", "description": "The converted balance of the account at the end of the financial period, reflecting the final status.", "availableValues": ["0", "2000", "6854.6", "6566.6", "5343.6"], "is_unstructured": false}, {"name": "account_ordinal", "dataType": "INT64", "description": "An integer representing the order of accounts, typically with values under 100, indicating their sequence.", "availableValues": ["1", "3"], "is_unstructured": false}]}