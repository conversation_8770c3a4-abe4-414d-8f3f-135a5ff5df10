{"tableName": "quickbooks__ap_ar_enhanced", "description": "The quickbooks__ap_ar_enhanced table captures detailed customer invoice transactions, identified by transaction_id or doc_number. It includes invoice amounts, currency conversions, due dates, customer names, balances, and limited geographic details. Focused on accounts receivable, the table supports financial reporting, customer account tracking, and cash flow management by providing comprehensive invoice and customer balance data.", "fields": [{"name": "transaction_type", "dataType": "STRING", "description": "Indicates the nature of the transaction, consistently labeled as 'invoice', to categorize records specific to invoice-related activities.", "availableValues": ["invoice"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "transaction_id", "dataType": "STRING", "description": "A unique string identifier assigned to each transaction, ensuring distinct identification and reference for data integrity.", "availableValues": ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "doc_number", "dataType": "STRING", "description": "A unique document identifier for each invoice, facilitating tracking and management of individual invoice records.", "availableValues": ["1116", "1118", "1129", "2553", "2593", "2602", "2605", "2607", "2609", "2612", "2613"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "transaction_with", "dataType": "STRING", "description": "Specifies the counterpart involved in the transaction, set to 'customer' to denote the relational context of the data.", "availableValues": ["customer"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "customer_vendor_name", "dataType": "STRING", "description": "The name of the customer or vendor associated with the transaction, essential for identifying the parties involved and for reporting purposes.", "availableValues": ["Carl manit CST", "ABC Company", "<PERSON>"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "customer_vendor_balance", "dataType": "BIGNUMERIC", "description": "Reflects the financial balance for the customer or vendor at the time of the transaction, indicating amounts owed or available credit.", "availableValues": ["2562.56", "3347.36", "1382.08"], "is_unstructured": false}, {"name": "customer_vendor_address_city", "dataType": "STRING", "description": "The city of the customer or vendor's address, providing geographic context for less than 50% of records, useful for segmentation.", "availableValues": ["Calgary", "Winnipeg"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "total_amount", "dataType": "BIGNUMERIC", "description": "Captures the total invoice amount, a critical numeric value for accurate financial reporting and revenue tracking.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "89.6", "and 6 more..."], "is_unstructured": false}, {"name": "total_converted_amount", "dataType": "BIGNUMERIC", "description": "Represents the total amount converted to a standard currency, important for organizations handling multiple currencies.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "89.6", "and 6 more..."], "is_unstructured": false}, {"name": "current_balance", "dataType": "BIGNUMERIC", "description": "Indicates the current financial balance of the customer or vendor at the time of the transaction, crucial for account management.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "89.6", "and 6 more..."], "is_unstructured": false}, {"name": "due_date", "dataType": "DATE", "description": "Specifies the payment due date for the invoice, vital for monitoring payment schedules and cash flow management.", "subsetOfAvailableValues": ["2021-10-10", "2021-08-12", "2021-10-01", "2021-09-01", "2021-08-18", "and 2 more..."], "is_unstructured": false}]}