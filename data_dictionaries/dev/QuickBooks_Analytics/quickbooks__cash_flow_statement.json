{"tableName": "quickbooks__cash_flow_statement", "description": "The quickbooks__cash_flow_statement table captures account-level cash flow data for specific periods, distinguishing equity and asset accounts. It includes beginning, ending, and net cash balances, both reported and adjusted, with unique identification by account_unique_id or by cash_flow_period and account_id. This table enables analysis of operational cash flows and liquidity, and aligns with the quickbooks__balance_sheet via cash_flow_period and account_id for integrated financial reporting.", "fields": [{"name": "cash_flow_period", "dataType": "DATE", "description": "The specific date for which the cash flow statement entry applies, serving as a key reference for each record.", "subsetOfAvailableValues": ["2024-08-01", "2024-01-01", "2024-10-01", "2022-08-01", "2025-02-01", "and 47 more..."], "is_unstructured": false}, {"name": "account_class", "dataType": "STRING", "description": "Categorizes accounts as either 'Equity' or 'Asset', essential for understanding their contribution to the cash flow statement.", "availableValues": ["Equity", "<PERSON><PERSON>"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "parent_account_name", "dataType": "STRING", "description": "Indicates the higher-level account associated with the entry, often defaulting to 'Accounts Receivable (A/R)', relevant for receivable accounts.", "availableValues": ["Accounts Receivable (A/R)"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_type", "dataType": "STRING", "description": "Specifies the type of account, such as 'Equity' or 'Accounts Receivable', aiding in differentiating account types in the cash flow statement.", "availableValues": ["Equity", "Accounts Receivable"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "Provides further detail on the account type, typically including classifications like 'RetainedEarnings' or 'AccountsReceivable', facilitating deeper financial analysis.", "availableValues": ["RetainedEarnings", "AccountsReceivable"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_number", "dataType": "STRING", "description": "An optional field usually containing '9999-00', serving as a unique identifier for accounts, though often missing in records.", "availableValues": ["9999-00"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_id", "dataType": "STRING", "description": "Uniquely identifies each account within the cash flow statement, critical for establishing relationships with other tables.", "availableValues": ["27", "9999"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_name", "dataType": "STRING", "description": "Holds the name of the account, such as 'Net Income Adjustment' or 'Accounts Receivable (A/R)', helping to identify specific accounts in the cash flow statement.", "availableValues": ["Net Income Adjustment", "Accounts Receivable (A/R)"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "cash_ending_period", "dataType": "BIGNUMERIC", "description": "Represents the total cash available at the end of the period, essential for assessing the cash position.", "availableValues": ["2000", "6854.6", "5343.6", "6566.6"], "is_unstructured": false}, {"name": "cash_converted_ending_period", "dataType": "BIGNUMERIC", "description": "Indicates the adjusted cash amount at the end of the period, reflecting potential accounting adjustments.", "availableValues": ["2000", "6854.6", "5343.6", "6566.6"], "is_unstructured": false}, {"name": "account_unique_id", "dataType": "STRING", "description": "Contains a unique identifier for each record, allowing for precise tracking and identification of entries within the cash flow statement.", "subsetOfAvailableValues": ["06845afdabb0e83a0e2c6b582f36b75a", "91356420db1018f78d23c7c125faea94", "48f07e5ed9a3dce2e973d9214a2b3e91", "64410122f213e134e8a2e7ad968efba4", "f8754a7a920281448ef8f2720382dbab", "and 99 more..."], "totalDistinctValueCount": 104, "is_unstructured": false}, {"name": "cash_flow_type", "dataType": "STRING", "description": "Categorizes the cash flow entry as 'Operating', crucial for financial analysis focused on operational performance.", "availableValues": ["Operating"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "cash_flow_ordinal", "dataType": "INT64", "description": "Indicates the position of the cash flow entry in the sequence of transactions, consistently holding a fixed value of '2'.", "availableValues": ["2"], "is_unstructured": false}, {"name": "cash_beginning_period", "dataType": "BIGNUMERIC", "description": "Shows the cash amount at the start of the period, important for understanding cash flow dynamics.", "availableValues": ["0", "2000", "6854.6", "6566.6", "5343.6"], "is_unstructured": false}, {"name": "cash_net_period", "dataType": "BIGNUMERIC", "description": "Reflects the net cash flow during the period, crucial for evaluating cash inflow and outflow.", "availableValues": ["0", "288", "1223", "2000", "3343.6"], "is_unstructured": false}, {"name": "cash_converted_beginning_period", "dataType": "BIGNUMERIC", "description": "Indicates the adjusted cash amount at the start of the period, facilitating comparisons against expected cash positions.", "availableValues": ["0", "2000", "6854.6", "6566.6", "5343.6"], "is_unstructured": false}, {"name": "cash_converted_net_period", "dataType": "BIGNUMERIC", "description": "Represents the adjusted net cash flow during the period, aiding in understanding effective cash flow after adjustments.", "availableValues": ["0", "288", "1223", "2000", "3343.6"], "is_unstructured": false}], "relationships": [{"child_table": "quickbooks__cash_flow_statement", "parent_table": "quickbooks__balance_sheet", "key_column_mapping": [{"parent_column": "calendar_date", "child_column": "cash_flow_period"}, {"parent_column": "account_id", "child_column": "account_id"}], "relationship_type": "one-to-one"}]}