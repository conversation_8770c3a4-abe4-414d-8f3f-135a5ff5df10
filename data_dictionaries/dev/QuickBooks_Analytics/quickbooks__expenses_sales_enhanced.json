{"tableName": "quickbooks__expenses_sales_enhanced", "description": "The quickbooks__expenses_sales_enhanced table contains detailed line-item data for sales invoices, with each row uniquely identified by transaction_id and transaction_line_id. It records transaction dates, document numbers, item and customer details, account categorization, descriptions, and both original and converted monetary amounts. All entries represent sales income, supporting comprehensive sales, customer, and product analysis, as well as multi-currency financial reporting.", "fields": [{"name": "transaction_source", "dataType": "STRING", "description": "Indicates the source of the transaction, consistently marked as 'sales' to categorize all entries as sales transactions.", "availableValues": ["sales"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "transaction_id", "dataType": "STRING", "description": "A unique identifier for each transaction, ensuring the distinct identification of individual sales records.", "availableValues": ["88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "transaction_line_id", "dataType": "INT64", "description": "Represents the specific line item number within a transaction, allowing for precise tracking of each item sold.", "subsetOfAvailableValues": ["0", "1", "2", "3", "4", "and 22 more..."], "is_unstructured": false}, {"name": "doc_number", "dataType": "STRING", "description": "Contains the associated document number for the transaction, facilitating easy reference to related invoices or documents.", "availableValues": ["1116", "1118", "1129", "2553", "2593", "2602", "2605", "2607", "2609", "2612", "2613"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "transaction_type", "dataType": "STRING", "description": "Identifies the nature of the transaction, consistently labeled as 'invoice', confirming that all records pertain to invoicing.", "availableValues": ["invoice"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "transaction_date", "dataType": "DATE", "description": "Records the date when the transaction took place, enabling analysis of sales trends over time.", "subsetOfAvailableValues": ["2021-09-10", "2021-07-13", "2021-09-01", "2021-08-02", "2021-04-28", "and 2 more..."], "is_unstructured": false}, {"name": "item_id", "dataType": "STRING", "description": "Represents the unique identifier for the sold item, allowing for tracking and reporting on specific products.", "availableValues": ["4", "5", "6", "7", "8", "9"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "account_id", "dataType": "STRING", "description": "Links each transaction to a specific account, consistently holding the value '6' for categorization purposes.", "availableValues": ["6"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_name", "dataType": "STRING", "description": "Indicates the account name associated with the transaction, always labeled as 'Sales' to reinforce transaction categorization.", "availableValues": ["Sales"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "Details the type of account involved, consistently marked as 'SalesOfProductIncome', clarifying the nature of income generated.", "availableValues": ["SalesOfProductIncome"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "customer_id", "dataType": "STRING", "description": "Provides the identifier for the customer involved in the transaction, facilitating customer-focused reporting.", "availableValues": ["2", "4", "5"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "customer_name", "dataType": "STRING", "description": "Contains the name of the customer related to the transaction, enabling straightforward identification of customers.", "availableValues": ["Carl manit CST", "ABC Company", "<PERSON>"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "description", "dataType": "STRING", "description": "Offers a brief description of items sold or services rendered, providing specific insights into each transaction.", "subsetOfAvailableValues": ["PPF-C", "<PERSON><PERSON>", "PDR", "Window Tint", "Paint Protection Film", "and 34 more..."], "totalDistinctValueCount": 39, "is_unstructured": false}, {"name": "amount", "dataType": "BIGNUMERIC", "description": "Represents the monetary value associated with the transaction line item, crucial for financial analysis and reporting.", "subsetOfAvailableValues": ["1", "5", "9", "10", "11", "and 12 more..."], "is_unstructured": false}, {"name": "converted_amount", "dataType": "BIGNUMERIC", "description": "Indicates the amount converted into a different currency, if applicable, supporting multi-currency transaction reporting.", "subsetOfAvailableValues": ["1", "5", "9", "10", "11", "and 12 more..."], "is_unstructured": false}, {"name": "total_amount", "dataType": "BIGNUMERIC", "description": "Represents the total amount for the entire transaction, summing all line items for comprehensive financial reporting.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "30.24", "and 6 more..."], "is_unstructured": false}, {"name": "total_converted_amount", "dataType": "BIGNUMERIC", "description": "Signifies the total amount converted into another currency, aiding in financial reporting across various currencies.", "subsetOfAvailableValues": ["84", "1008", "1232", "2000", "30.24", "and 6 more..."], "is_unstructured": false}], "relationships": [{"child_table": "quickbooks__expenses_sales_enhanced", "parent_table": "quickbooks__ap_ar_enhanced", "key_column_mapping": [{"parent_column": "transaction_id", "child_column": "transaction_id"}], "relationship_type": "one-to-many"}]}