{"tableName": "quickbooks__profit_and_loss", "description": "The quickbooks__profit_and_loss table records daily profit and loss metrics at the account level, specifically for revenue and its subtypes. It captures details such as reporting period, account hierarchy, type, sub-type, financial amounts (original and converted), and account order. Each row is identified by calendar_date and account_id, supporting granular financial analysis of income streams. No foreign keys are present.", "fields": [{"name": "calendar_date", "dataType": "DATE", "description": "The date for which the profit and loss data is recorded, crucial for tracking daily financial performance.", "subsetOfAvailableValues": ["2021-05-01", "2021-11-01", "2022-01-01", "2022-04-01", "2023-12-01", "and 47 more..."], "is_unstructured": false}, {"name": "period_first_day", "dataType": "DATE", "description": "The starting date of the reporting period for which the profit and loss data is aggregated.", "subsetOfAvailableValues": ["2021-05-01", "2021-11-01", "2022-01-01", "2022-04-01", "2023-12-01", "and 47 more..."], "is_unstructured": false}, {"name": "period_last_day", "dataType": "DATE", "description": "The concluding date of the reporting period for which the profit and loss data is reported.", "subsetOfAvailableValues": ["2021-05-31", "2021-11-30", "2022-01-31", "2022-04-30", "2023-12-31", "and 47 more..."], "is_unstructured": false}, {"name": "account_class", "dataType": "STRING", "description": "Categorizes the account type specifically as 'Revenue', aiding in grouping income-related records.", "availableValues": ["Revenue"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "parent_account_name", "dataType": "STRING", "description": "The name of the parent account linked to this record, which typically indicates the source of revenue such as 'Discounts given' or 'Sales'.", "availableValues": ["Discounts given", "Sales"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_type", "dataType": "STRING", "description": "Indicates the account classification as 'Income', differentiating it from other account types for financial reporting.", "availableValues": ["Income"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "account_sub_type", "dataType": "STRING", "description": "Provides further classification of the account, indicating specific categories like 'DiscountsRefundsGiven' or 'SalesOfProductIncome'.", "availableValues": ["DiscountsRefundsGiven", "SalesOfProductIncome"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_id", "dataType": "STRING", "description": "A unique identifier for the account associated with this record, allowing for distinct tracking of financial entries.", "availableValues": ["6", "28"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "account_name", "dataType": "STRING", "description": "The name of the account, typically reflecting the specific revenue source such as 'Discounts given' or 'Sales'.", "availableValues": ["Discounts given", "Sales"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "amount", "dataType": "BIGNUMERIC", "description": "Represents the financial amount associated with this record, capturing both income and losses in a numeric format.", "subsetOfAvailableValues": ["0", "288", "1223", "2000", "3646", "and 1 more..."], "is_unstructured": false}, {"name": "converted_amount", "dataType": "BIGNUMERIC", "description": "Represents a financial amount similar to 'amount', potentially adjusted for currency conversion or other formats.", "subsetOfAvailableValues": ["0", "288", "1223", "2000", "3646", "and 1 more..."], "is_unstructured": false}, {"name": "account_ordinal", "dataType": "INT64", "description": "Indicates the hierarchical order of the account, consistently reflecting the same level of account classification.", "availableValues": ["1"], "is_unstructured": false}]}