Overview of Insight Categories:

"SuperLube" is a car maintenance company that has been in operation for the past ten years. Their operational data covers the last four years and includes information on day-to-day activities. The company offers services in four main categories: tyre changes, oil changes, brake repairs, and car washes. These services cater to the various needs of vehicle owners, providing solutions for routine maintenance and repairs. Superlube focuses on delivering reliable service with a team of skilled technicians committed to customer satisfaction. 
It's noteworthy that loyal customers typically use the tyre change service twice a year and the oil change service once a year, reflecting the company's regular clientele and their maintenance habits.

Data driven business intelligence is crucial in strategic decision making for "SuperLube". 
Both the identification of relevant data sources and data processing will be based on category of insights.

The following are the different insight categories:

descriptive_insights.txt:
    - What is happening?
    - Focus on understanding the current state or historical trends.
    - Example: What were the total sales for each product category last quarter?
diagnostic_insights.txt:
    - Why is it happening?
    - Aim to identify causes or factors contributing to a particular outcome.
    - Example: Why did sales for Product X decline in the last month?
predictive_insights.txt:
    - What is likely to happen in the future?
    - Utilize historical data and statistical models to forecast future events or trends.
    - Example: What will be the demand for Product Y in the next six months?
prescriptive_insights.txt:
    - What should we do about it?
    - Offer recommendations on actions to take based on the analysis of data.
    - Example: What strategies should be implemented to improve customer retention?
comparative_insights.txt:
    - How does X compare with Y?
    - Compare different datasets, time periods, or segments to highlight differences or similarities.
    - Example: How do the sales figures of this year compare to last year's?
exploratory_insights.txt:
    - What patterns or relationships exist in the data?
    - Involve exploring the data without a predefined hypothesis to identify unexpected patterns or relationships.
    - Example: Are there any unexpected buying patterns among different customer segments?
impact_insights.txt:
    - What is the impact or effect of X on Y?
    - Assess the influence or outcomes of specific actions, events, or variables.
    - Example: What has been the impact of the new marketing campaign on sales?
normative_insights.txt:
    - What should the standard or norm be?
    - Establish benchmarks or best practices based on data analysis.
    - Example: What is the optimal inventory level for our main warehouse to maximize efficiency and minimize costs?
constructive_insights.txt:
    - How can we improve or optimize X?
    - Focus on identifying opportunities for improvement or optimization in processes, products, or services.
    - Example: How can we optimize our production process to reduce waste and increase output?


