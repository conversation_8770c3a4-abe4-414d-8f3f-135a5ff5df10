{"tableName": "JobRecommendationTasks", "description": "JobRecommendationTasks captures tasks and recommendations related to service jobs, detailing job status, urgency, type, associated equipment, customer, contact, and location. It tracks the creation, status, and invoicing of each task or recommendation, supporting operational and financial analysis. Each record is uniquely identified by Jobid and may reference master data for customers, equipment, contacts, and locations, enabling comprehensive tracking and reporting of service activities and recommendations.", "fields": [{"name": "RecTasks_Jobid", "dataType": "int", "description": "Unique integer identifier for each job recommendation task, ensuring distinct record referencing.", "subsetOfAvailableValues": ["1821189", "1821256", "1821279", "1824524", "1827364", "and 1729 more..."], "is_unstructured": false}, {"name": "RecTasks_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "String representing the name of the job linked to the task, expected to be unique within job recommendations.", "subsetOfAvailableValues": ["ES-3679201", "PFB-3680820", "SR-3788671", "SR-4029710", "WR-4063065", "and 1729 more..."], "totalDistinctValueCount": 1734, "is_unstructured": false}, {"name": "RecTasks_ ProjectName", "dataType": "<PERSON><PERSON><PERSON>", "description": "", "subsetOfAvailableValues": ["Spot call", "Level 3 ", "Level 1", "Inspection", "Annual ", "and 897 more..."], "totalDistinctValueCount": 902, "is_unstructured": false}, {"name": "RecTasks_JobStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorical field signifying the current status of the job, with values such as 'Completed', 'Cancelled', 'In Progress', etc.", "availableValues": ["Completed", "Cancelled", "In Progress", "Waiting on Parts", "Scheduled", "Ready to Schedule"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "RecTasks_JobUrgency", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorical field indicating the urgency level of the job, primarily showing values like '2 - Normal' and '1 - High/Rush'.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "RecTasks_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "String specifying the type of job, providing a general description of the job category.", "availableValues": ["Service Request-COM", "Service Request - GEN", "PMA-COM", "PM Fixed Bid - GEN", "PM Fixed Bid-COM", "PMA - GEN", "Call Back-COM", "Asset maintenance", "Rental asset maintenance", "Equipment Sale-COM", "Warranty Request - GEN", "Warranty Request-COM", "Call Back - GEN", "Courtesy Call-COM", "Courtesy Call - GEN", "Project - GEN", "Project - COM", "Rental Contract", "Equipment Sale - GEN", "Part Sale-COM", "Specialty Services-COM", "Part Sale - GEN", "Rental Incidentals"], "totalDistinctValueCount": 23, "is_unstructured": false}, {"name": "RecTasks_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "String indicating the office location responsible for managing the job, with predominant values being 'MB Office' and 'SK Office'.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "RecTasks_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "String denoting the type of task, categorized as either 'Task' or 'Recommendation' based on the nature of the entry.", "availableValues": ["Task", "Recommendation"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "RecTasks_EquipmentID", "dataType": "int", "description": "Integer identifier for the equipment linked to the job, with some records missing this value.", "subsetOfAvailableValues": ["951291", "951997", "952042", "952169", "952190", "and 876 more..."], "is_unstructured": false}, {"name": "RecTasks_Equipment", "dataType": "<PERSON><PERSON><PERSON>", "description": "String describing the equipment involved in the job recommendation task, typically unique within groups of four records.", "subsetOfAvailableValues": ["Mis.System Component", "Sullivan Palatek DC185P3IZ-C", "UP6-10TAS-150", "47675079001", "Sullivan <PERSON>latek DC375PDJD", "and 641 more..."], "totalDistinctValueCount": 646, "is_unstructured": false}, {"name": "RecTasks_Serial", "dataType": "<PERSON><PERSON><PERSON>", "description": "String representing the serial number of the equipment, with some records missing this value and typically unique among groups.", "subsetOfAvailableValues": ["35497", "391086", "526835", "541430", "1806006", "and 873 more..."], "totalDistinctValueCount": 878, "is_unstructured": false}, {"name": "RecTasks_Task", "dataType": "<PERSON><PERSON><PERSON>", "description": "String indicating the specific task related to the job recommendation; often carries the value 'Other' for missing records.", "availableValues": ["Other"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "RecTasks_TaskDescription", "dataType": "<PERSON><PERSON><PERSON>", "description": "String providing detailed information about the task, unique to each record but missing for a significant percentage.", "subsetOfAvailableValues": ["Level 3 service", "Lev 3 pm and line filter service", "Lvl3 service", "Troubleshoot compressor", "Level 1 service", "and 1585 more..."], "totalDistinctValueCount": 1590, "is_unstructured": false}, {"name": "RecTasks_RecommendationUrgency", "dataType": "<PERSON><PERSON><PERSON>", "description": "String indicating the urgency level of the recommendation, with values available for less than half of the records.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "RecTasks_RecommendationType", "dataType": "n<PERSON><PERSON><PERSON>", "description": "String categorizing the type of recommendation made, with values available for less than half of the records.", "availableValues": ["Service Recommendation", "Equipment sale", "PM Contract"], "is_unstructured": false}, {"name": "RecTasks_Recommendation", "dataType": "<PERSON><PERSON><PERSON>", "description": "String containing specifics of the recommendation; generally unique for groups of two records but limited availability.", "subsetOfAvailableValues": ["Customer is still waiting for electrician to install power source to compressor. Also, customer is not ready for us to complete tie in as he needs to call Vipond prior to. Period, customer will let us know when this is done and come back to site to finish finish commissioning on compressor and dryer system", "Coolant heater failed, arrived to low coolant temp warning. Recommend new heater", "Customer noted when it was cold outside they had some moisture in the lines, recommend replacing desiccant in dryer , replacement kit ED-2, and cartridge CCN 85565703 and CCN 85566404 (3hr)", "test", "Perform full annual service ( including 2 hr load test), on site training for monthly testing and mouse proof enclosure (bring black spray foam)", "and 1173 more..."], "totalDistinctValueCount": 1178, "is_unstructured": false}, {"name": "RecTasks_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "String indicating the user or system responsible for creating the job recommendation task record.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "RecTasks_RecStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorical field representing the current status of the recommendation, with possible values like 'Closed', 'Open', etc.", "availableValues": ["Closed", "Open", "Quoted", "In Progress"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "RecTasks_CreatedDate", "dataType": "datetime", "description": "Datetime field indicating when the job recommendation task record was created.", "is_unstructured": false}, {"name": "RecTasks_CustomerID", "dataType": "int", "description": "Integer identifier for the customer associated with the job recommendation, usually unique among groups of five records.", "subsetOfAvailableValues": ["379954", "380039", "380079", "380204", "380214", "and 511 more..."], "is_unstructured": false}, {"name": "RecTasks_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "String representing the name of the customer linked to the job recommendation task, typically unique for groups.", "subsetOfAvailableValues": ["Air Unlimited Inc.", "Genrep Ltd.", "The Mosaic Company", "GFL Environmental", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "and 510 more..."], "totalDistinctValueCount": 515, "is_unstructured": false}, {"name": "RecTasks_AccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "String indicating the account number associated with the customer, generally unique among groups of five records.", "subsetOfAvailableValues": ["AIR00001", "GEN10792", "THE10255", "GFL10128", "SOB11224", "and 511 more..."], "totalDistinctValueCount": 516, "is_unstructured": false}, {"name": "RecTasks_LocationID", "dataType": "int", "description": "Integer identifier for the location associated with the job recommendation task, typically unique for groups.", "subsetOfAvailableValues": ["658879", "658900", "658913", "658928", "659012", "and 685 more..."], "is_unstructured": false}, {"name": "RecTasks_LocationName", "dataType": "<PERSON><PERSON><PERSON>", "description": "String representing the name of the location tied to the job recommendation task, usually unique within groups.", "subsetOfAvailableValues": ["Winnipeg-Head Office", "GFL Environmental", "Wonder Brands Inc", "CertainTeed Canada Inc.", "MacDon Industries Ltd.", "and 649 more..."], "totalDistinctValueCount": 654, "is_unstructured": false}, {"name": "RecTasks_ContactID", "dataType": "int", "description": "Integer identifier for the contact person associated with the job recommendation task, with some records missing this value.", "subsetOfAvailableValues": ["904289", "905037", "905092", "905198", "905317", "and 750 more..."], "is_unstructured": false}, {"name": "RecTasks_ContactName", "dataType": "<PERSON><PERSON><PERSON>", "description": "String storing the name of the contact person related to the job recommendation tasks; may be missing for a small percentage.", "subsetOfAvailableValues": ["Accounts Payable", "General Inbox", "<PERSON><PERSON>", "<PERSON>", "<PERSON> switzer", "and 660 more..."], "totalDistinctValueCount": 665, "is_unstructured": false}, {"name": "RecTasks_InvoiceStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "String indicating the invoicing status of the job recommendation tasks, categorized as 'Invoiced', 'Not Invoiced', etc.", "availableValues": ["Invoiced", "Not Invoiced", "Partially Invoiced"], "totalDistinctValueCount": 3, "is_unstructured": false}], "relationships": [{"child_table": "JobRecommendationTasks", "parent_table": "ContactInformation", "key_column_mapping": [{"parent_column": "[Contact.ID]", "child_column": "[RecTasks.ContactID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobRecommendationTasks", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[RecTasks.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobRecommendationTasks", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.Account<PERSON><PERSON>ber]", "child_column": "[RecTasks.AccountNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "JobRecommendationTasks", "parent_table": "EquipmentDetail", "key_column_mapping": [{"parent_column": "[Equipment.ID]", "child_column": "[RecTasks.EquipmentID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobRecommendationTasks", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[RecTasks<PERSON>Jobid]"}], "relationship_type": "one-to-many"}, {"child_table": "JobRecommendationTasks", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[RecTasks<PERSON>Jobid]"}], "relationship_type": "one-to-many"}, {"child_table": "JobRecommendationTasks", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[RecTasks.LocationID]"}], "relationship_type": "one-to-many"}]}