{"tableName": "PurchaseOrderSummary", "description": "The PurchaseOrderSummary table captures essential information for each purchase order, including unique identifiers, order status, type, payment and delivery terms, office and warehouse details, supplier and job associations (where available), relevant dates, and total cost. It supports procurement operations, financial analysis, and reporting, even when some purchase orders lack associated job or supplier data.", "fields": [{"name": "PurchaseOrderSummary_ID", "dataType": "int", "description": "A unique integer identifier for each purchase order, ensuring distinct reference across all records.", "subsetOfAvailableValues": ["2053318", "2053326", "2053330", "2053340", "2053344", "and 5197 more..."], "is_unstructured": false}, {"name": "PurchaseOrderSummary_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character name assigned to each purchase order for easy identification within the database.", "subsetOfAvailableValues": ["ES-3679731-1", "PMA-3678911-1", "00PO-M", "CB-4161570-1", "ES-3762895-1", "and 5174 more..."], "totalDistinctValueCount": 5179, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Job_ID", "dataType": "int", "description": "An integer that uniquely identifies the job associated with the purchase order, which may be absent for some records.", "subsetOfAvailableValues": ["1820877", "1821189", "1821278", "1824524", "1826236", "and 2805 more..."], "is_unstructured": false}, {"name": "Purchase<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_Job_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field containing the name of the job related to the purchase order, often missing in many records.", "subsetOfAvailableValues": ["ESP-3703966", "WR-3679665", "ES-3679201", "AM-3760202", "AM-3788176", "and 2802 more..."], "totalDistinctValueCount": 2807, "is_unstructured": false}, {"name": "PurchaseOrder<PERSON>ummary_Bid_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field that specifies the name of the bid connected to the purchase order, with some records lacking this information.", "subsetOfAvailableValues": ["Equipment Sale Project - GEN - 3703966 (Pending)", "Warranty Request-COM - 3679665 (Pending)", "Equipment Sale-COM - 3679201 (Pending)", "Asset maintenance - 3760202 (Pending)", "Asset maintenance - 3788176 (Pending)", "and 2751 more..."], "totalDistinctValueCount": 2756, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field indicating the current status of the purchase order, with predefined statuses such as 'Shipped' and 'In Development'.", "availableValues": ["All Arrived", "Ordered", "In Development", "Partial Arrived", "Waiting on Parts", "Shipped"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field categorizing the purchase order type, including classifications such as 'Job Order' and 'Pick List'.", "availableValues": ["Pick List", "Job Order", "Stock Order", "Miscellaneous"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "PurchaseOrderSummary_PaymentTerms", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field detailing the payment terms for the purchase order, which may not be present in all records.", "availableValues": ["N30", "COD", "2% net 15", "Credit Card", "1% net 10"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "PurchaseOrderSummary_DueDays", "dataType": "int", "description": "An integer representing the number of days until payment is due, applicable for less than half of the records.", "availableValues": ["0", "15", "30"], "is_unstructured": false}, {"name": "PurchaseOrderSummary_Terms", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field specifying additional terms related to the purchase order, with possible values indicating urgency or conditions.", "availableValues": ["None", "Standard", "Over Night", "RUSH", "OVERNIGHT", "See Notes", ""], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field identifying the office associated with the purchase order, such as 'MB Office' or 'SK Office'.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Supplier_ID", "dataType": "int", "description": "An integer that uniquely identifies the supplier associated with the purchase order, often missing or only partially populated.", "subsetOfAvailableValues": ["81061", "81079", "81111", "81113", "81115", "and 275 more..."], "is_unstructured": false}, {"name": "PurchaseOrderSummary_Supplier_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field containing the name of the supplier linked to the purchase order, which may not be available for all records.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Corporate Traveller", "<PERSON>", "Day & Ross Inc.", "and 272 more..."], "totalDistinctValueCount": 277, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Warehouse_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field specifying the name of the warehouse related to the purchase order, with some records lacking this information.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "Shared Services", "S/N 1143909 - <PERSON>", "~Regina Warehouse", "S/N 1249338 - <PERSON>", "S/N Z412284 - <PERSON>", "S/N 1296840 - <PERSON>", "S/N KB09748 - <PERSON>", "Thermo King", "S/N 1143253 - <PERSON>", "S/N KE44716 - <PERSON>", "S/N S545722 - <PERSON>", "S/N G367219 - <PERSON>", "S/N 1196248 - <PERSON>", "S/N 1143618 - <PERSON>"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Date", "dataType": "datetime", "description": "A datetime field indicating when the purchase order was created or logged in the system.", "is_unstructured": false}, {"name": "PurchaseOrderSummary_OrderedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field that captures the name of the individual or entity placing the order.", "subsetOfAvailableValues": ["Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Job_Location", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field indicating the location of the job associated with the purchase order, often missing from records.", "subsetOfAvailableValues": ["Winnipeg-Head Office, 2116 Logan Ave, Winnipeg, MB R2R 0J2", "GFL Environmental, 335 Mazenod Rd, , Winnipeg, MB R2J 3S8", "ARDENT MILLS, ULC., 95 33 St E, , Saskatoon, SK S7K 0R8", "JCV Mechanical Inc, 1369 Border St, Winnipeg, MB R3H 0N1", "The J. <PERSON> HVAC Group, 2116 Logan Ave, Winnipeg, MB R2R 0J2", "and 1395 more..."], "totalDistinctValueCount": 1400, "is_unstructured": false}, {"name": "PurchaseOrderSummary_Via", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character field detailing the method of delivery for the purchase order, typically unique for various records.", "subsetOfAvailableValues": ["Purolator Ground 9211283", "Prepaid & Add - Ground", "Bestway", "Pick Up", "Prepaid No Charge", "and 116 more..."], "totalDistinctValueCount": 121, "is_unstructured": false}, {"name": "PurchaseOrderSummary_DeliveryDate", "dataType": "smalldatetime", "description": "A smalldatetime field indicating the expected delivery date for the purchase order, which is not present in all records.", "is_unstructured": false}, {"name": "PurchaseOrderSummary_DateRequired", "dataType": "datetime", "description": "A datetime field specifying the required date for the order, often missing in records.", "is_unstructured": false}, {"name": "PurchaseOrderSummary_Total", "dataType": "decimal", "description": "A decimal field representing the total cost associated with the purchase order.", "is_unstructured": false}], "relationships": [{"child_table": "PurchaseOrderSummary", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[PurchaseOrderSummary.Supplier.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderSummary", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[PurchaseOrderSummary.Job.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderSummary", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[PurchaseOrderSummary.Job.ID]"}], "relationship_type": "one-to-many"}]}