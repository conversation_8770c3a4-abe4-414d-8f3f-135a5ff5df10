{"tableName": "SupplierInformation", "description": "The SupplierInformation table contains comprehensive details for each supplier, including unique identifiers, contact information, account numbers, tax IDs, payment terms, and status indicators. It enables effective management of supplier relationships and payment processes for air and power equipment procurement. The table does not reference other tables and serves as the primary source for supplier-related data within the system.", "fields": [{"name": "Supplier_ID", "dataType": "int", "description": "A unique integer identifier for each supplier, ensuring distinct reference for every supplier record.", "subsetOfAvailableValues": ["81017", "81019", "81021", "81023", "81025", "and 561 more..."], "is_unstructured": false}, {"name": "Supplier_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string representing the supplier's name, facilitating easy identification and retrieval.", "subsetOfAvailableValues": ["Atlas Copco Compressors Canada", "Brandt Tractor Ltd.", "Toromont Cat", "Airtek Limited", "Air Unlimited Inc", "and 558 more..."], "totalDistinctValueCount": 563, "is_unstructured": false}, {"name": "Supplier_AccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string that denotes the specific account number assigned to each supplier for account management.", "subsetOfAvailableValues": ["5001", "5004", "5008", "5011", "5012", "and 561 more..."], "totalDistinctValueCount": 566, "is_unstructured": false}, {"name": "Supplier_TaxID", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for the supplier's tax identification number, generally unique within small groups of records.", "subsetOfAvailableValues": ["*********", "*********", "*********", "*********", "*********", "and 49 more..."], "totalDistinctValueCount": 54, "is_unstructured": false}, {"name": "Supplier_Terms", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that details the agreed payment terms with the supplier, using standardized categorical values.", "availableValues": ["COD", "N30", "Credit Card", "N07", "1% net 10", "2% net 15"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "Supplier_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that classifies the supplier, currently not holding specific values.", "availableValues": [" "], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Supplier_QualityRating", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string representing the supplier's quality rating, currently not holding specific values.", "availableValues": [" "], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Supplier_1099Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the type of 1099 reporting applicable to the supplier, currently set to 'null'.", "availableValues": ["None"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Supplier_Notes", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for additional notes or details regarding the supplier, generally unique within small groups.", "subsetOfAvailableValues": [" ", "Payment: EFT", "Payment: Wire", "Only for Banking Purposes - ***DO NOT USE THIS SUPPLIER*** ", "Payment: Cheque", "and 49 more..."], "totalDistinctValueCount": 54, "is_unstructured": false}, {"name": "Supplier_Email", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string containing the supplier's email address, typically unique within small groups of records.", "subsetOfAvailableValues": [" ", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 257 more..."], "totalDistinctValueCount": 262, "is_unstructured": false}, {"name": "Supplier_Website", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for the supplier's website URL, usually unique within small groups of records.", "subsetOfAvailableValues": [" ", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 89 more..."], "totalDistinctValueCount": 94, "is_unstructured": false}, {"name": "Supplier_Phone", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string representing the primary contact number for the supplier, ensuring direct communication.", "subsetOfAvailableValues": [" ", "************", "************", "************", "************", "and 480 more..."], "totalDistinctValueCount": 485, "is_unstructured": false}, {"name": "Supplier_Fax", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for the supplier's fax number, generally unique within small groups of records.", "subsetOfAvailableValues": [" ", "************", "************", "************", "************", "and 30 more..."], "totalDistinctValueCount": 35, "is_unstructured": false}, {"name": "Supplier_Phone2", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for an optional secondary phone number of the supplier, typically unique within small groups.", "subsetOfAvailableValues": [" ", "************", "************", "************", "************", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "Supplier_Address", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string for the supplier's primary address, ensuring accurate location identification.", "subsetOfAvailableValues": [" ", "2116 Logan Ave", "100-1025 South Belt Line Rd", "1407 Dugald Rd", "320-830 <PERSON>", "and 519 more..."], "totalDistinctValueCount": 524, "is_unstructured": false}, {"name": "Supplier_Address2", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for a secondary address for the supplier, often unique within small groups of records.", "subsetOfAvailableValues": [" ", "Station A", "Station M", "PO Box 4918 STN A", "Unit 1-4", "and 56 more..."], "totalDistinctValueCount": 61, "is_unstructured": false}, {"name": "Supplier_City", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string representing the city of the supplier's location, generally unique within small groups of records.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", " ", "Toronto", "and 135 more..."], "totalDistinctValueCount": 140, "is_unstructured": false}, {"name": "Supplier_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the state where the supplier operates, usually unique within small groups of records.", "subsetOfAvailableValues": ["MB", "SK", "ON", "AB", " ", "and 32 more..."], "totalDistinctValueCount": 37, "is_unstructured": false}, {"name": "Supplier_Zip", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string holding the postal code of the supplier's address for precise location identification.", "subsetOfAvailableValues": ["46360", "56701", "75019", " ", "R2R 0J2", "and 480 more..."], "totalDistinctValueCount": 485, "is_unstructured": false}, {"name": "Supplier_Internal", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating if the supplier is classified as internal, with predominant values of 'No' and '0'.", "availableValues": ["0", "No"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Supplier_Freight", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string showing the freight status of the supplier, with most records indicating 'No'.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Supplier_PaidACH", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating if the supplier is paid through ACH, which is consistently marked as 'No'.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Supplier_Currency", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A variable character string specifying the currency for supplier transactions, predominantly Canadian Dollars.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "Supplier_Active", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the supplier's active status, with most records marked as 'Yes'.", "availableValues": ["Yes", "No"], "totalDistinctValueCount": 2, "is_unstructured": false}]}