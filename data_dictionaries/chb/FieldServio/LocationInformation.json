{"tableName": "LocationInformation", "description": "The LocationInformation table contains comprehensive data on customer locations, including unique identifiers, address details, customer associations, service scheduling, technician preferences, and tax settings. It enables tracking of operational, sales, and service activities at each site, supporting efficient service delivery, rental management, and customer account management. Each record links to a customer via CustomerID and AccountNumber, facilitating integrated business processes across service, sales, and customer support functions.", "fields": [{"name": "Location_ID", "dataType": "int", "description": "Unique integer identifier for each location record, serving as the primary key for the LocationInformation table.", "subsetOfAvailableValues": ["658863", "658864", "658865", "658866", "658867", "and 3505 more..."], "is_unstructured": false}, {"name": "Location_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "Unique variable character name representing the location, with approximately 1% of records missing this value.", "subsetOfAvailableValues": ["REGINA HOUSING AUTHORITY", "Redhead Equipment", "MacDon Industries Ltd.", "Brandt Tractor Ltd", "Golden West Broadcasting Ltd.", "and 2937 more..."], "totalDistinctValueCount": 2942, "is_unstructured": false}, {"name": "Location_CustomerID", "dataType": "int", "description": "Integer identifier linking the location to its respective customer, ensuring distinct association for each record.", "subsetOfAvailableValues": ["379997", "380135", "380230", "380267", "380288", "and 2189 more..."], "is_unstructured": false}, {"name": "Location_Customer_AccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "Unique variable character account number assigned to the customer, facilitating account identification and management.", "subsetOfAvailableValues": ["CIT10642", "SOB11224", "CAN10656", "GEN10792", "MAN10963", "and 2189 more..."], "totalDistinctValueCount": 2194, "is_unstructured": false}, {"name": "Location_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Unique variable character name of the customer associated with the location, providing clear identification.", "subsetOfAvailableValues": ["City of Winnipeg", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "Canadian National Railway", "Genrep Ltd.", "Manitoba Hydro", "and 2175 more..."], "totalDistinctValueCount": 2180, "is_unstructured": false}, {"name": "Location_Customer_Currency", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Variable character field indicating the currency used by the customer, predominantly 'Canadian Dollars' with minimal use of 'US Dollars'.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "Location_Customer_Active", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field indicating whether the customer is active, with 99% of records marked as 'Yes'.", "availableValues": ["Yes", "No"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Location_Customer_NextServiceDate", "dataType": "smalldatetime", "description": "Scheduled service date for the customer, available for less than 50% of records, indicating variability in service scheduling.", "is_unstructured": false}, {"name": "Location_CustomerOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field identifying the specific office associated with the customer, missing for about 1% of records.", "availableValues": ["MB Office", "SK Office", "", "Winnipeg-Head Office", "Shared Services"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "Location_PreferredTechnician", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field denoting the preferred technician for servicing the location, recorded for less than 10% of entries.", "availableValues": ["<PERSON>", "<PERSON>"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Location_Address1", "dataType": "<PERSON><PERSON><PERSON>", "description": "Primary address line for the location, unique for each record but missing for approximately 1%.", "subsetOfAvailableValues": ["", "2116 Logan Ave", "2055 Notre Dame Avenue", "55 Pavilion Crescent", "70 Poseidon Bay", "and 3215 more..."], "totalDistinctValueCount": 3220, "is_unstructured": false}, {"name": "Location_Address2", "dataType": "<PERSON><PERSON><PERSON>", "description": "Secondary address line for the location, generally unique for groups of about 9 records, missing for 46% of records.", "subsetOfAvailableValues": ["", "Unit# 1", "Attn: <PERSON>", "C/O A.S.H. Management Group", "Attn: Accounts Payable", "and 374 more..."], "totalDistinctValueCount": 379, "is_unstructured": false}, {"name": "Location_City", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field specifying the city of the location, typically unique for groups of about 5 records, with around 1% missing.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "<PERSON>", "", "and 580 more..."], "totalDistinctValueCount": 585, "is_unstructured": false}, {"name": "Location_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field indicating the state of the location, missing for about 1% of records.", "subsetOfAvailableValues": ["MB", "SK", "ON", "AB", "", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "Location_Zip", "dataType": "<PERSON><PERSON><PERSON>", "description": "Unique postal code for the location, with approximately 1% of records missing this value.", "subsetOfAvailableValues": ["", "R2C 2Z2", "R2R 0J2", "R0M 2C0", "R3C 2E6", "and 2290 more..."], "totalDistinctValueCount": 2295, "is_unstructured": false}, {"name": "Location_Latitude", "dataType": "decimal", "description": "Decimal field representing the latitude coordinate of the location, available for less than 50% of records.", "is_unstructured": false}, {"name": "Location_Longitude", "dataType": "decimal", "description": "Decimal field denoting the longitude coordinate of the location, available for less than 50% of records.", "is_unstructured": false}, {"name": "Location_DateEntered", "dataType": "datetime", "description": "Datetime field capturing the date and time when the record was created, with no additional notes provided.", "is_unstructured": false}, {"name": "Location_EnteredBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field indicating the individual who entered the record, with values available for less than 50% of records.", "subsetOfAvailableValues": ["<EMAIL>", "AirUnlimitedSupport", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 30 more..."], "totalDistinctValueCount": 35, "is_unstructured": false}, {"name": "Location_SalespersonPrimary", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field identifying the primary salesperson associated with the location, recorded for less than 10% of entries.", "availableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Location_TaxSchedule", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field specifying the applicable tax schedule for the location, with no additional notes.", "subsetOfAvailableValues": ["MB - Sales Tax - GST 5% + PST 7%", "SK - Sales Tax - GST 5% + PST 6%", "MB - Sales Tax - GST 5%", "SK - Sales Tax - GST 5%", "ON - Sales Tax - HST 13%", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "Location_DefaultJobOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "Variable character field indicating the default job office for the location, available for less than 50% of records.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}], "relationships": [{"child_table": "LocationInformation", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[Location.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "LocationInformation", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.Account<PERSON><PERSON>ber]", "child_column": "[Location.Customer.AccountNumber]"}], "relationship_type": "one-to-many"}]}