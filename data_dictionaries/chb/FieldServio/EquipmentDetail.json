{"tableName": "EquipmentDetail", "description": "EquipmentDetail contains comprehensive records for each equipment asset, including identification, serial number, operational status, customer and sales assignments, installation location, warranty, invoice, contract, and purchase order details. Each entry is uniquely identified by Equipment.ID. The table enables tracking of equipment lifecycle, ownership, service, and financial transactions. Note that some sales, contract, and location fields may be incomplete. Use this table to support asset management, customer service, warranty monitoring, and integration with procurement and billing processes across Air Unlimited’s operations.", "fields": [{"name": "Equipment_ID", "dataType": "int", "description": "A unique integer identifier for each piece of equipment, ensuring that no two records share the same ID.", "subsetOfAvailableValues": ["950342", "950343", "950344", "950347", "950348", "and 4902 more..."], "is_unstructured": false}, {"name": "Equipment_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The distinctive name of the equipment, which aids in identifying different types within the database.", "subsetOfAvailableValues": ["2475N7.5-V", "2340L5-V ", "UP6-15C-150-TAS", "SS3L3", "UP6-15C-150", "and 2560 more..."], "totalDistinctValueCount": 2565, "is_unstructured": false}, {"name": "Equipment_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed string description of the equipment's features and specifications, potentially shared among a few records.", "subsetOfAvailableValues": ["Oil Flooded", "Transfer Switch", "Gas", "2475N7.5-V", "2340L5-V", "and 2210 more..."], "totalDistinctValueCount": 2215, "is_unstructured": false}, {"name": "Equipment_SerialNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique alphanumeric code assigned to each equipment unit, ensuring individual tracking and identification.", "subsetOfAvailableValues": ["2275", "123456", "3014298410", "Unknown", "N/A", "and 4773 more..."], "totalDistinctValueCount": 4778, "is_unstructured": false}, {"name": "Equipment_EquipmentType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A classification string identifying the specific type or category of the equipment, with potential gaps in data availability.", "availableValues": ["Low Pressure (<600 PSI)  compressor", "Dryers", "Diesel Generator", "Gas Generator", "Transfer switch", "Portable Equipment-Compressor", "Portable Equipment-Generator", "Oil Free Screw compressor", "Centrifugal compressor", "portable equipment - generator", "portable equipment - compressor", "portable equipment - compressors"], "totalDistinctValueCount": 12, "is_unstructured": false}, {"name": "Equipment_ManufacturerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the company that manufactured the equipment, providing context on quality and support.", "subsetOfAvailableValues": ["Ingersoll Rand", "Generac", "DeVillbiss/DeVair", "SULLAIR", "CUMMINS", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "Equipment_StartDate", "dataType": "smalldatetime", "description": "The date when the equipment was first utilized, helping to track its operational lifespan.", "is_unstructured": false}, {"name": "Equipment_WarrantyExpiration", "dataType": "smalldatetime", "description": "The date indicating when the equipment's warranty period ends, crucial for maintenance and support decisions.", "is_unstructured": false}, {"name": "Equipment_EquipmentStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "The current operational status of the equipment, primarily indicating whether it is 'Active' or 'Inactive'.", "availableValues": ["Active", "Inactive"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Equipment_EndCustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the ultimate end-user of the equipment, which may aid in customer relationship management.", "subsetOfAvailableValues": ["Air Unlimited Inc.", "Air Unlimited Inc", "Canadian National Railway", "Public Health Agency of Canada", "Stephenson Equipment", "and 1300 more..."], "totalDistinctValueCount": 1305, "is_unstructured": false}, {"name": "Equipment_SoldCustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the client or business entity to whom the equipment was sold, relevant for sales tracking.", "subsetOfAvailableValues": ["Air Unlimited Inc.", "PCS Inc. (Nutrien)", "TransGas Limited", "SaskPower", "HyLife Ltd", "and 246 more..."], "totalDistinctValueCount": 251, "is_unstructured": false}, {"name": "Equipment_SalesPersonName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the salesperson responsible for the equipment sale, assisting in performance assessment.", "availableValues": ["<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "totalDistinctValueCount": 14, "is_unstructured": false}, {"name": "Equipment_LocationName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the physical location where the equipment is installed, with potential data gaps.", "subsetOfAvailableValues": ["Air Unlimited Inc", "Winnipeg-Head Office", "Canadian Science Centre For Human & Animal Health", "Stephenson Equipment", "MacDon Industries Ltd.", "and 1311 more..."], "totalDistinctValueCount": 1316, "is_unstructured": false}, {"name": "Equipment_LocationAddr1", "dataType": "<PERSON><PERSON><PERSON>", "description": "The primary address line for the equipment's location, essential for logistics and service coordination.", "subsetOfAvailableValues": ["1-3247 Millar Ave", "2116 Logan Ave", "", "1015 Arlington St", "21 Murray Park Rd", "and 1326 more..."], "totalDistinctValueCount": 1331, "is_unstructured": false}, {"name": "Equipment_LocationAddr2", "dataType": "<PERSON><PERSON><PERSON>", "description": "An optional secondary address line for the equipment's location, providing additional address details.", "subsetOfAvailableValues": ["", "HWY 397  - 12KM off HWY 16E", "Building 408", "PO Box 1320", "Off Highway 7 West of Vanscoy", "and 188 more..."], "totalDistinctValueCount": 193, "is_unstructured": false}, {"name": "Equipment_LocationCity", "dataType": "<PERSON><PERSON><PERSON>", "description": "The city where the equipment is situated, important for regional service management.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "Portage La Prairie", "<PERSON>", "and 308 more..."], "totalDistinctValueCount": 313, "is_unstructured": false}, {"name": "Equipment_LocationState", "dataType": "<PERSON><PERSON><PERSON>", "description": "The state where the equipment is located, aiding in geographic data organization.", "availableValues": ["MB", "SK", "ON", "AB", "", "QC", "FL", "BC", "UT", "TN", "YT"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "Equipment_LocationZip", "dataType": "<PERSON><PERSON><PERSON>", "description": "The postal code for the equipment's location, which helps in logistical planning and service dispatch.", "subsetOfAvailableValues": ["S7K 5Y3", "", "R2R 0J2", "R3E 3P6", "R3J 3S2", "and 1089 more..."], "totalDistinctValueCount": 1094, "is_unstructured": false}, {"name": "Equipment_Hours", "dataType": "decimal", "description": "A decimal value representing the total operational hours of the equipment, useful for maintenance scheduling.", "is_unstructured": false}, {"name": "Equipment_HoursLastUpdated", "dataType": "datetime", "description": "The date and time when the operational hours were last recorded, crucial for tracking usage.", "is_unstructured": false}, {"name": "Equipment_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The name of the specific item associated with the equipment, which can help in inventory management.", "subsetOfAvailableValues": ["42672949", "Ingersoll Rand", "Generator - Generac", "AD - Ingersoll Rand", "AC - Ingersoll Rand", "and 451 more..."], "is_unstructured": false}, {"name": "Equipment_ItemLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "A designation for the category or line of items related to the equipment, aiding in product classification.", "availableValues": ["Dryers ", "Low Pressure (<600 PSI)  compressor", "Diesel Generator", "Gas Generator", "Transfer Switch", "Miscellaneous Parts", "Portable Equipment-Compressor", "Oil Free Screw compressor", "Portable Equipment-Generator", "Parts-Compressor", "Used Equipment", "Construction Equipment", "Custom build", "Parts-Generator", "Portable Equipment-Pump", "Blowers", "Load Bank", "Air Audit", "Compressor Filtration", "High Pressure (>600 PSI) compressor", "Subcontractor"], "totalDistinctValueCount": 21, "is_unstructured": false}, {"name": "Equipment_InvoiceID", "dataType": "int", "description": "An integer linking the equipment to its associated invoice, facilitating financial tracking.", "subsetOfAvailableValues": ["2595689", "2643837", "2656980", "2686051", "2688049", "and 208 more..."], "is_unstructured": false}, {"name": "Equipment_InvoiceNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique identifier for the invoice associated with the equipment, important for billing processes.", "subsetOfAvailableValues": ["8178-1", "6658-1", "7733-1", "6903-1", "7530-1", "and 218 more..."], "totalDistinctValueCount": 223, "is_unstructured": false}, {"name": "Equipment_InvoiceDate", "dataType": "datetime", "description": "The date when the invoice for the equipment was issued, relevant for payment tracking.", "is_unstructured": false}, {"name": "Equipment_CreatedDate", "dataType": "datetime", "description": "The timestamp marking when the equipment record was created in the system, aiding in data history tracking.", "is_unstructured": false}, {"name": "Equipment_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The individual or system entity responsible for creating the equipment record, important for accountability.", "subsetOfAvailableValues": ["Support User", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "and 30 more..."], "totalDistinctValueCount": 35, "is_unstructured": false}, {"name": "Equipment_ModifiedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The user or system entity that last updated the equipment record, key for maintaining data integrity.", "subsetOfAvailableValues": ["Support User", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "Equipment_ModifiedDate", "dataType": "datetime", "description": "The timestamp indicating the last modification made to the equipment record, vital for auditing.", "is_unstructured": false}, {"name": "Equipment_PurchaseOrderID", "dataType": "int", "description": "An integer representing the unique identifier of the purchase order linked to the equipment.", "subsetOfAvailableValues": ["2053326", "2053797", "2053819", "2060260", "2071117", "and 21 more..."], "is_unstructured": false}, {"name": "Equipment_PurchaseOrderName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A description of the purchase order associated with the equipment, providing context for procurement.", "subsetOfAvailableValues": ["SO2014", "SO1160", "SO1708", "SO1794", "SO1813", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "Equipment_PurchaseOrderBillID", "dataType": "int", "description": "An integer that uniquely identifies the bill related to the purchase order, facilitating financial management.", "subsetOfAvailableValues": ["1489782", "1504978", "1508766", "1517779", "1531810", "and 47 more..."], "is_unstructured": false}, {"name": "Equipment_PurchaseOrderBillNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "The number associated with the purchase order bill, important for tracking payments and invoices.", "subsetOfAvailableValues": ["6925210", "6930120", "6930940", "6935661", "6958089", "and 44 more..."], "totalDistinctValueCount": 49, "is_unstructured": false}, {"name": "Equipment_PurchaseOrderBillDate", "dataType": "date", "description": "The date related to the purchase order bill, crucial for financial record-keeping.", "is_unstructured": false}, {"name": "Equipment_ContractID", "dataType": "int", "description": "An integer that uniquely identifies the contract associated with the equipment, aiding in contract management.", "subsetOfAvailableValues": ["192370", "192395", "192401", "192772", "192828", "and 449 more..."], "is_unstructured": false}, {"name": "Equipment_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the unique number of the contract linked to the equipment.", "subsetOfAvailableValues": ["PFB209178", "PMA193811", "PFB221168", "ESP195325", "PFB221359", "and 449 more..."], "totalDistinctValueCount": 454, "is_unstructured": false}, {"name": "Equipment_ContractExpiration", "dataType": "date", "description": "The expiration date of the contract related to the equipment, essential for maintenance and renewal planning.", "is_unstructured": false}, {"name": "Equipment_ContractStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the current status of the contract, providing insight into its validity and active periods.", "availableValues": ["Active", "Inactive"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Equipment_IsInventory", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating whether the equipment is classified as inventory, primarily reflecting its availability status.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}], "relationships": [{"child_table": "EquipmentDetail", "parent_table": "InvoiceOpportunityEvents", "key_column_mapping": [{"parent_column": "[InvoiceOpportunityEvents.InvoiceName]", "child_column": "[Equipment.InvoiceNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentDetail", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[Equipment.ContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentDetail", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.Number]", "child_column": "[Equipment.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentDetail", "parent_table": "PurchaseOrderSummary", "key_column_mapping": [{"parent_column": "[PurchaseOrderSummary.ID]", "child_column": "[Equipment.PurchaseOrderID]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentDetail", "parent_table": "PurchaseOrderSummary", "key_column_mapping": [{"parent_column": "[PurchaseOrderSummary.ID]", "child_column": "[Equipment.PurchaseOrderBillID]"}], "relationship_type": "one-to-many"}]}