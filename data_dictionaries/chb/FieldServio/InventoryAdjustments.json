{"tableName": "InventoryAdjustments", "description": "The InventoryAdjustments table tracks all inventory changes, recording item details, adjustment quantities, reasons, financial impact, and location information. Each entry links to relevant inventory items, warehouses, suppliers, and users, ensuring traceability and auditability. This table supports inventory accuracy, cost analysis, and operational reporting, providing essential data for managing stock levels, understanding adjustment causes, and maintaining accountability within Air Unlimited’s service and supply operations.", "fields": [{"name": "InventoryAdjustments_ItemID", "dataType": "int", "description": "A unique integer identifier for each inventory adjustment record, ensuring distinct entries.", "subsetOfAvailableValues": ["2825307", "2826031", "2826805", "2827199", "2827391", "and 914 more..."], "is_unstructured": false}, {"name": "InventoryAdjustments_AdjustmentDate", "dataType": "datetime", "description": "The exact date and time when the inventory adjustment was recorded, stored in datetime format for precise tracking.", "is_unstructured": false}, {"name": "InventoryAdjustments_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string code that identifies the specific item involved in the inventory adjustment.", "subsetOfAvailableValues": ["********", "********", "********", "********", "********", "and 914 more..."], "totalDistinctValueCount": 919, "is_unstructured": false}, {"name": "InventoryAdjustments_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The name of the item related to the adjustment, provided as a string for clear identification.", "subsetOfAvailableValues": ["********", "********", "********", "********", "********", "and 914 more..."], "is_unstructured": false}, {"name": "InventoryAdjustments_ItemDescription", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A detailed text description that outlines the characteristics and specifications of the adjusted item.", "subsetOfAvailableValues": ["Oil Filter", "Air Filter", "Separator", "Fuel Filter", "Electronic Drain Valve 1/4\" 115Volt", "and 764 more..."], "is_unstructured": false}, {"name": "InventoryAdjustments_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the office location where the inventory adjustment occurred, indicating the operational context.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryAdjustments_WarehouseName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the warehouse that manages the adjusted inventory item, providing locational context.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "S/N KB09767 - <PERSON>", "S/N 1143909 - <PERSON>", "S/N KB09748 - <PERSON>", "~Regina Warehouse", "S/N 1143618 - <PERSON>", "S/N 1296840 - <PERSON>", "S/N S545722 - <PERSON>", "S/N 1249338 - <PERSON>", "S/N Z412284 - <PERSON>", "S/N 1196248 - <PERSON>", "S/N KE44716 - <PERSON>", "S/N G367219 - <PERSON>", "S/N 1143253 - <PERSON>", "S/N FA50095 - <PERSON>"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "InventoryAdjustments_AdjustedQuantity", "dataType": "decimal", "description": "The numeric value indicating the amount of the item adjusted, which may be positive (increase) or negative (decrease).", "is_unstructured": false}, {"name": "InventoryAdjustments_AdjustmentReason", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A text description detailing the reason for the inventory adjustment, offering insights into the adjustment's context.", "subsetOfAvailableValues": ["Physical Count Adjustment", "Adjustment", "correction", "Inventory Correction", "<PERSON><PERSON>", "and 92 more..."], "is_unstructured": false}, {"name": "InventoryAdjustments_Cost", "dataType": "decimal", "description": "The monetary value associated with the adjusted quantity of the item, reflecting its financial impact on inventory.", "is_unstructured": false}, {"name": "InventoryAdjustments_Currency", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The currency type in which the cost is represented, typically noted for relevant records.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "InventoryAdjustments_ItemCategory", "dataType": "<PERSON><PERSON><PERSON>", "description": "The classification of the item being adjusted, primarily distinguishing between 'Parts' and 'Equipment'.", "availableValues": ["Parts", "Equipment"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryAdjustments_ItemProductLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "The specific product line designation of the item, which may encompass various categories and types.", "availableValues": ["Parts-Compressor", "Parts-Generator", "Portable Equipment-Compressor", "Compressor Oil ", "Compressor Filtration", "Generator Oil", "Low Pressure (<600 PSI)  compressor", "Used Equipment"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InventoryAdjustments_Supplier", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the supplier providing the item, essential for tracking procurement sources.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Green Line Hose & Fittings Ltd", "Royal Fluid Power Ltd.", "<PERSON>", "and 56 more..."], "totalDistinctValueCount": 61, "is_unstructured": false}, {"name": "InventoryAdjustments_Manufacturer", "dataType": "<PERSON><PERSON><PERSON>", "description": "The brand or manufacturer associated with the item, aiding in understanding product sourcing.", "availableValues": ["Ingersoll Rand", "Generac", "Toromont Cat", "N/A", "Atlas Copco", "SULLAIR"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "InventoryAdjustments_AdjustedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the individual responsible for executing the inventory adjustment, indicating accountability.", "availableValues": ["<PERSON>", "Dallas Skiftun", "<PERSON><PERSON>", "<PERSON>"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "InventoryAdjustments_AdjustmentTransactionID", "dataType": "int", "description": "A unique integer identifier linking this adjustment to its corresponding transaction, ensuring tracking integrity.", "subsetOfAvailableValues": ["********", "********", "********", "********", "********", "and 193 more..."], "is_unstructured": false}, {"name": "InventoryAdjustments_InventoryID", "dataType": "int", "description": "An integer identifier that connects this adjustment entry to the respective inventory item for relational integrity.", "subsetOfAvailableValues": ["********", "********", "********", "********", "********", "and 1338 more..."], "is_unstructured": false}], "relationships": [{"child_table": "InventoryAdjustments", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.Name]", "child_column": "[InventoryAdjustments.Supplier]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryAdjustments", "parent_table": "WarehouseInventory", "key_column_mapping": [{"parent_column": "[Warehouse.Inventory.ID]", "child_column": "[InventoryAdjustments.InventoryID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryAdjustments", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[InventoryAdjustments.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryAdjustments", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryAdjustments.ItemCode]"}], "relationship_type": "one-to-many"}]}