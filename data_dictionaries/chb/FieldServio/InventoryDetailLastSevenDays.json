{"tableName": "InventoryDetailLastSevenDays", "description": "InventoryDetailLastSevenDays captures detailed inventory records created within the last seven days, including item, warehouse, bin, equipment, job, and customer information. Each entry tracks quantities, costs, statuses, and links to related purchase orders and suppliers. This table supports operational and financial analysis by providing a timely view of inventory movements and positions across locations, enabling effective management for air and power equipment services.", "fields": [{"name": "InventoryDetailLastSevenDays_ID", "dataType": "int", "description": "A unique integer identifier for each inventory record, ensuring distinct identification across the table.", "subsetOfAvailableValues": ["14821882", "14821932", "14822097", "14822117", "14822118", "and 331 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field categorizing the inventory entry type, such as 'Work In Progress', 'Return', or 'Inventory', reflecting its operational status.", "availableValues": ["Work In Progress", "Inventory", "Return"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the current operational status of the inventory record, with common values including 'Active' and 'Closed'.", "availableValues": ["Active", "Closed"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_CreatedDate", "dataType": "date", "description": "A date field capturing the creation date of the inventory entry, useful for tracking the age and lifecycle of inventory.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_POItemID", "dataType": "int", "description": "An integer field representing the unique Purchase Order Item ID associated with the inventory, with potential missing values indicating unlinked items.", "subsetOfAvailableValues": ["6132414", "6185256", "6185257", "6185258", "6185259", "and 273 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_WarehouseID", "dataType": "int", "description": "An integer field that identifies the warehouse storing the inventory item, though its presence is limited in less than half of the records.", "availableValues": ["29913", "29914", "29916", "29917"], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Warehouse", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field providing the name of the associated warehouse, available for less than half of the records and may include specific identifiers.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "S/N 1296840 - <PERSON>", "S/N KB09748 - <PERSON>"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_BinID", "dataType": "int", "description": "An integer field identifying the specific storage bin for the inventory item, with some records lacking this identifier.", "subsetOfAvailableValues": ["0", "54454", "54459", "54464", "54478", "and 82 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_BinName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the name of the storage bin for the inventory item, often unique but with significant missing data.", "subsetOfAvailableValues": ["W-01-04-02", "W-09-01-03", "W-09-01-04", "3C2", "W-03-01-04", "and 81 more..."], "totalDistinctValueCount": 86, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_ItemID", "dataType": "int", "description": "A unique integer identifier for each inventory item, ensuring clear recognition of individual items.", "subsetOfAvailableValues": ["2825307", "2826815", "2827101", "2827709", "2827868", "and 186 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_ItemLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field categorizing the item line, such as 'Parts-Compressor' or 'Generator Oil', usually unique to specific records.", "availableValues": ["Parts-Compressor", "Parts-Generator", "Generator Oil", "Compressor Oil ", "Low Pressure (<600 PSI)  compressor", "Compressor Filtration", "Dryers "], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field providing the name or identifier for the inventory item, unique for each record.", "subsetOfAvailableValues": ["2118342", "23564446", "23708423", "24121212", "38035531", "and 186 more..."], "totalDistinctValueCount": 191, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field containing a unique code assigned to each inventory item, aiding in clear identification.", "subsetOfAvailableValues": ["2118342", "23564446", "23708423", "24121212", "38035531", "and 186 more..."], "totalDistinctValueCount": 191, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_ItemInventoryCosting", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the inventory costing method, predominantly using 'AVCO' for the majority of records.", "availableValues": ["AVCO", "SPID"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_ItemType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field specifying the item type, mainly classified as 'Parts' or 'Equipment'.", "availableValues": ["Parts", "Equipment"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field representing the item's category, which may include 'Parts', 'Equipment', or 'Miscellaneous'.", "availableValues": ["Parts", "Equipment", "Miscellaneous"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field providing a descriptive text for the item, typically unique for small groups of records.", "subsetOfAvailableValues": ["Oil Filter", "Separator", "Air Filter", "Engine Oil, 15W-40, 4L", "Antifreeze, Diesel/Gas Universal, 50/50 Premix, 3.78L (Yellow)", "and 160 more..."], "totalDistinctValueCount": 165, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_SupplierID", "dataType": "int", "description": "An integer field identifying the supplier of the item, generally unique but may appear in groups.", "subsetOfAvailableValues": ["81061", "81079", "81111", "81113", "81115", "and 29 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_SupplierName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field containing the supplier's name associated with the item, often unique for groups of records.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Oil Mart Ltd", "ALS Laboratory Group Tribology", "Green Line Hose & Fittings Ltd", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_Price", "dataType": "decimal", "description": "A decimal field representing the monetary value assigned to each inventory item.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_PriceExtended", "dataType": "decimal", "description": "A decimal field indicating the total price of the item based on quantity sold or in stock.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Quantity", "dataType": "decimal", "description": "A decimal field showing the current available quantity of the item in inventory.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_OrigQuantity", "dataType": "decimal", "description": "A decimal field recording the original quantity of the item before adjustments or sales.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Cost", "dataType": "decimal", "description": "A decimal field indicating the cost associated with the inventory item for accounting purposes.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Item_CostExtended", "dataType": "decimal", "description": "A decimal field reflecting the total cost of the item based on its quantity in stock.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_WarehouseMaxQty", "dataType": "decimal", "description": "A decimal field representing the maximum allowable quantity of inventory in the warehouse, with limited availability.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_WarehouseMinQty", "dataType": "decimal", "description": "A decimal field indicating the minimum quantity of inventory that should be maintained in the warehouse, with sparse data entries.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_WarehouseOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field identifying the office linked to the warehouse, with limited records indicating regional management.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Warehouse_ItemQty", "dataType": "decimal", "description": "A decimal field recording the quantity of items currently available in the warehouse, with data availability issues.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_EquipmentID", "dataType": "int", "description": "An integer field serving as a unique identifier for associated equipment, with some records missing this value.", "subsetOfAvailableValues": ["0", "1057672", "1064418", "1084185", "1089013", "and 2 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_LocationID", "dataType": "int", "description": "An integer field identifying the specific location of the inventory, available for a limited number of records.", "availableValues": ["659642", "659971", "660463", "660937", "722812"], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Equipment_SerialNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field containing the equipment's serial number, with limited tracking across records.", "availableValues": ["251412-CSA", "CBV1006013", "CBV999742", "OC-23103", "P102026032", "S77963-8146"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_Equipment_Model", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field capturing the model of associated equipment, available for a small fraction of records.", "availableValues": ["\tCC-07530-01 - Omega HVAC Simplex 3/4HP 115-230V/1/60 30Gallon Horizontal", "2340L5-V 230/1/60", "2475N7.5-V", "MHUS-25", "OTS1101 - Powerex Air Compressor", "US30/x6 575V"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobID", "dataType": "int", "description": "An integer field identifying the job associated with the inventory line, with some records missing this information.", "subsetOfAvailableValues": ["1927653", "2078729", "2089459", "2089461", "2089462", "and 110 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobItemId", "dataType": "int", "description": "An integer field uniquely identifying the job item, though many records show missing values.", "subsetOfAvailableValues": ["10851758", "10851759", "10851762", "10851763", "10910744", "and 196 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobDateEntered", "dataType": "datetime", "description": "A datetime field indicating when the job was entered into the system, with significant missing data affecting analysis.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobCustomerID", "dataType": "int", "description": "An integer field identifying the customer linked to the job, with many records missing this critical information.", "subsetOfAvailableValues": ["380145", "380158", "380356", "380460", "380499", "and 86 more..."], "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobCustomer", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field representing the name of the customer linked to the job, with a notable missing value rate.", "subsetOfAvailableValues": ["Town of Lac du Bonnet", "City of Winnipeg", "Certainteed Canada Inc.", "IHT Group", "Western Marble & Tile", "and 86 more..."], "totalDistinctValueCount": 91, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field capturing the job's name, with missing values affecting the ability to track job-specific inventory.", "subsetOfAvailableValues": ["SR-4278652", "PMA-3679909", "PMA-3724149", "PS-4270755", "PFB-4193445", "and 110 more..."], "totalDistinctValueCount": 115, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_JobStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the current status of the job, with limited availability potentially impacting job progress analysis.", "availableValues": ["Completed", "Waiting on Parts", "Ready to Schedule", "In Progress", "Scheduled"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_LastInvoiceDate", "dataType": "date", "description": "A date field indicating the date of the last associated invoice, with some records missing this information.", "is_unstructured": false}, {"name": "InventoryDetailLastSevenDays_InvoiceCount", "dataType": "int", "description": "An integer field counting the number of invoices linked to the inventory items, with some records lacking this data.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 48 more..."], "is_unstructured": false}], "relationships": [{"child_table": "InventoryDetailLastSevenDays", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[InventoryDetailLastSevenDays.Item.SupplierID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "PurchaseOrderDetail", "key_column_mapping": [{"parent_column": "[PurchaseOrderDetail.POLineID]", "child_column": "[InventoryDetailLastSevenDays.POItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[InventoryDetailLastSevenDays.JobCustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InventoryDetailLastSevenDays.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[InventoryDetailLastSevenDays.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryDetailLastSevenDays.Item]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryDetailLastSevenDays.ItemCode]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InventoryDetailLastSevenDays.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[InventoryDetailLastSevenDays.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetailLastSevenDays", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[InventoryDetailLastSevenDays.LocationID]"}], "relationship_type": "one-to-many"}]}