{"tableName": "VendorBills", "description": "The VendorBills table stores comprehensive details for supplier invoices, including monetary amounts, tax, payment status, key dates, and related jobs, items, purchase orders, and offices. It supports multi-currency and tracks tax authorities, bill types, and approval statuses. Foreign key links to suppliers, jobs, items, and tax authorities enable robust accounts payable tracking, financial reporting, and integration with purchasing and inventory processes for Air Unlimited’s operations.", "fields": [{"name": "VendorBills_BillId", "dataType": "int", "description": "A unique integer identifier for each bill record, ensuring distinct referencing of entries.", "subsetOfAvailableValues": ["1486017", "1500576", "1502251", "1511492", "1530883", "and 6063 more..."], "is_unstructured": false}, {"name": "VendorBills_ReferenceNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field that holds a reference number associated with the bill, typically unique among related bills.", "subsetOfAvailableValues": ["799284", "814001", "6917614", "6924753", "6932270", "and 5284 more..."], "totalDistinctValueCount": 5289, "is_unstructured": false}, {"name": "VendorBills_Total", "dataType": "decimal", "description": "A decimal field representing the overall amount of the bill, accommodating both positive and negative values for credits or adjustments.", "is_unstructured": false}, {"name": "VendorBills_SubTotal", "dataType": "decimal", "description": "A decimal value showing the subtotal amount before tax and adjustments, which can also be positive or negative.", "is_unstructured": false}, {"name": "VendorBills_TaxAmount", "dataType": "decimal", "description": "A decimal field indicating the tax amount applied to the bill, flexible to accommodate positive and negative values for tax reversals.", "is_unstructured": false}, {"name": "VendorBills_AmountPaid", "dataType": "decimal", "description": "A decimal field recording the amount paid towards the bill, which can be positive for payments or negative for refunds.", "is_unstructured": false}, {"name": "VendorBills_Balance", "dataType": "decimal", "description": "The outstanding balance on the bill after payments, which can be either positive or negative, indicating amounts still owed.", "is_unstructured": false}, {"name": "VendorBills_CurrencyTotal", "dataType": "decimal", "description": "This decimal field captures the total amount of the bill in the specified currency, allowing for multi-currency transactions.", "is_unstructured": false}, {"name": "VendorBills_CurrencySubTotal", "dataType": "decimal", "description": "Reflects the subtotal amount in the specified currency, accommodating both positive and negative values.", "is_unstructured": false}, {"name": "VendorBills_CurrencyTax", "dataType": "decimal", "description": "Indicates the tax amount in the specified currency, similar to the TaxAmount field, and can also be positive or negative.", "is_unstructured": false}, {"name": "VendorBills_CurrencyPaid", "dataType": "decimal", "description": "Captures the amount paid in the specified currency, allowing for both positive payments and negative adjustments.", "is_unstructured": false}, {"name": "VendorBills_CurrencyBalance", "dataType": "decimal", "description": "Represents the remaining balance in the specified currency after accounting for payments, which can be positive or negative.", "is_unstructured": false}, {"name": "VendorBills_BillDate", "dataType": "datetime", "description": "A datetime field recording the date when the bill was issued, crucial for financial tracking.", "is_unstructured": false}, {"name": "VendorBills_DiscountDate", "dataType": "datetime", "description": "A datetime field indicating when discounts were applied to the bill, with potential missing values for some records.", "is_unstructured": false}, {"name": "VendorBills_DateCreated", "dataType": "datetime", "description": "A datetime field that captures when the bill record was created, essential for auditing purposes.", "is_unstructured": false}, {"name": "VendorBills_DueDate", "dataType": "datetime", "description": "A datetime field showing the payment due date for the bill, important for managing accounts payable.", "is_unstructured": false}, {"name": "VendorBills_SupplierID", "dataType": "int", "description": "An integer foreign key linking to the SupplierInformation table, indicating the supplier associated with the bill.", "subsetOfAvailableValues": ["81061", "81073", "81079", "81111", "81113", "and 372 more..."], "is_unstructured": false}, {"name": "VendorBills_SupplierName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field holding the name of the supplier related to the bill, typically unique within groups of records.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Geo. H. Young & Co. Ltd.", "<PERSON>", "Oil Mart Ltd", "and 369 more..."], "totalDistinctValueCount": 374, "is_unstructured": false}, {"name": "VendorBills_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field representing the current processing state of the bill, with values such as 'Paid in Full', 'Approved', or 'Pending Approval'.", "availableValues": ["Paid in Full", "Approved", "Pending Approval", "Voided"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "VendorBills_Memo", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field for additional comments or notes related to the bill, generally unique within groups of records.", "subsetOfAvailableValues": ["Import", "Items Returned to supplier", "", "Bill Created for PO: SO2116", "Bill Created for PO: SO1117", "and 4197 more..."], "totalDistinctValueCount": 4202, "is_unstructured": false}, {"name": "VendorBills_Items_ID", "dataType": "int", "description": "An integer foreign key linking to the Items table, with potential missing values in a subset of records.", "subsetOfAvailableValues": ["2825095", "2825758", "2825785", "2826223", "2826327", "and 2770 more..."], "is_unstructured": false}, {"name": "VendorBills_Items_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A nvarchar field holding the name of the item associated with the bill, with possible missing values in some records.", "subsetOfAvailableValues": ["23424922", "38035531", "38459582", "39911631", "92692284", "and 2768 more..."], "is_unstructured": false}, {"name": "VendorBills_Item_Description", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A nvarchar field providing item descriptions, which may be missing in a significant number of records.", "subsetOfAvailableValues": ["Freight Charges", "Oil Filter", "Air Filter", "Separator", "Fuel Filter", "and 2103 more..."], "is_unstructured": false}, {"name": "VendorBills_Item_Manufacturer", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field recording the item's manufacturer, with limited data availability in the records.", "availableValues": ["Generac", "Ingersoll Rand", "N/A", "CUMMINS", "Toromont Cat", "CAG", "<PERSON><PERSON><PERSON>", "Atlas Copco", "KOHLER", "SULLAIR", "DeVillbiss/DeVair", "<PERSON>", "Detroit Diesel", "Chicago Pneumatic", "Omega", "<PERSON>", "MTU", "<PERSON><PERSON>", "<PERSON><PERSON>", "Asco"], "totalDistinctValueCount": 20, "is_unstructured": false}, {"name": "VendorBills_Items_Quantity", "dataType": "decimal", "description": "A decimal field indicating the quantity of items on the bill, essential for inventory management.", "is_unstructured": false}, {"name": "VendorBills_Items_Cost", "dataType": "decimal", "description": "A decimal field storing the costs of items associated with vendor bills, capable of holding both positive and negative values.", "is_unstructured": false}, {"name": "VendorBills_JobID", "dataType": "int", "description": "An integer representing the unique identifier for jobs linked to vendor bills, with values available in less than half of the records.", "subsetOfAvailableValues": ["1827955", "1834090", "1841923", "1842402", "1845449", "and 1937 more..."], "is_unstructured": false}, {"name": "VendorBills_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the name of the job related to the vendor bill, with limited data availability.", "subsetOfAvailableValues": ["PS-4058635", "PS-4016174", "PS-4178671", "PS-4113259", "PS-4204386", "and 1936 more..."], "totalDistinctValueCount": 1941, "is_unstructured": false}, {"name": "VendorBills_POID", "dataType": "int", "description": "An integer serving as a unique identifier for purchase orders linked to the vendor bills, with some records missing values.", "subsetOfAvailableValues": ["5406644", "5431196", "5443464", "5467506", "5596817", "and 6886 more..."], "is_unstructured": false}, {"name": "VendorBills_POName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field holding the name of the purchase order related to the vendor bill, with potential missing values.", "subsetOfAvailableValues": ["SO2116", "SO1117", "SO1591", "SO2260", "SO1239", "and 2338 more..."], "totalDistinctValueCount": 2343, "is_unstructured": false}, {"name": "VendorBills_VendorBillID", "dataType": "int", "description": "An integer uniquely identifying each vendor bill, with some records having missing values.", "subsetOfAvailableValues": ["1486017", "1500576", "1502251", "1511492", "1530883", "and 2898 more..."], "is_unstructured": false}, {"name": "VendorBills_OfficeID", "dataType": "int", "description": "An integer categorizing the office associated with the vendor bill, with specific office values.", "availableValues": ["419", "420", "421"], "is_unstructured": false}, {"name": "VendorBills_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field describing the office related to the vendor bill, potentially categorical.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "VendorBills_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the user or entity responsible for creating the vendor bill.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 25 more..."], "totalDistinctValueCount": 30, "is_unstructured": false}, {"name": "VendorBills_TaxAuthorityId", "dataType": "int", "description": "An integer identifier for the tax authority associated with the vendor bill, with some records missing this value.", "subsetOfAvailableValues": ["28037", "28039", "28041", "28042", "28043", "and 1 more..."], "is_unstructured": false}, {"name": "VendorBills_TaxAuthorityName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field specifying the name of the tax authority related to the vendor bill, with potential missing values.", "availableValues": ["GST 5%", "ON HST 13%", "MB PST 7%", "Tax Exempt", "SK PST 6%", "USD GST 5%"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "VendorBills_TaxScheduleName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field denoting the tax schedule related to the vendor bill, with some records having missing values.", "availableValues": ["GST Only", "Both taxes exempt", "MB - Sales Tax - GST 5% + PST 7%", "ON - Sales Tax - HST 13%", "MB - Sales Tax - GST 5%", "SK - Sales Tax - GST 5% + PST 6%", "SK - Sales Tax - GST 5%", "USD GST Only", "SK - Sales Tax - PST 6%", "MB - Sales Tax - PST 7%"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "VendorBills_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the state related to the vendor bill, with dominant values for specific states.", "availableValues": ["Ma", "SK"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "VendorBills_TaxPaid", "dataType": "decimal", "description": "A decimal field reflecting the tax amount paid associated with the vendor bill, with many records missing this value.", "is_unstructured": false}, {"name": "VendorBills_BillTypeID", "dataType": "int", "description": "An integer identifying the type of bill, with a majority of records falling under a few specific values.", "availableValues": ["1", "2"], "is_unstructured": false}, {"name": "VendorBills_InvType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field describing the type of invoice associated with the vendor bill, with many records missing values.", "availableValues": ["W", "I", "R"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "VendorBills_InvStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the status of the invoice related to the vendor bill, with many records having missing values.", "availableValues": ["X", "A"], "totalDistinctValueCount": 2, "is_unstructured": false}], "relationships": [{"child_table": "VendorBills", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[VendorBills.SupplierID]"}], "relationship_type": "one-to-many"}, {"child_table": "VendorBills", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[VendorBills.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "VendorBills", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.Name]", "child_column": "[VendorBills.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "VendorBills", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[VendorBills.Items.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "VendorBills", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[VendorBills.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "VendorBills", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[VendorBills.JobName]"}], "relationship_type": "one-to-many"}]}