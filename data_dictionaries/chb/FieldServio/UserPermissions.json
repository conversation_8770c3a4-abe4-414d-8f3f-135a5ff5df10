{"tableName": "UserPermissions", "description": "The UserPermissions table defines each employee’s access by mapping roles to specific permissions, with detailed descriptions for each permission granted. It includes the employee’s name and links to EmployeeDetail via EmployeeID, enabling precise management and auditing of user rights across functional areas such as service, inventory, and accounting within the system.", "fields": [{"name": "UserPermissions_Role_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The designated role assigned to each user, defining their level of access and responsibilities within the system, such as 'Service', 'Inventory Member', or 'Accounting Member'.", "subsetOfAvailableValues": ["Inventory Member", "Service", "Contracts", "Accounting Member", "Sales Member", "and 8 more..."], "is_unstructured": false}, {"name": "UserPermissions_Permission_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A specific permission that is granted to users based on their assigned role, detailing allowed actions like 'General Info tab of Asset' or 'Payments'.", "subsetOfAvailableValues": ["View Invoices", "Depreciation Tab of Asset", "General <PERSON><PERSON> tab of Asset", "Documents Tab", "Create Invoice", "and 263 more..."], "totalDistinctValueCount": 268, "is_unstructured": false}, {"name": "UserPermissions_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A comprehensive explanation of the actions permitted by the specific permission, clarifying what the user can access or perform within the application.", "subsetOfAvailableValues": ["Link to view invoices on Job Invoices tab, Contract Invoices tab, Invoices/Payments tab of customer", "Allows a user access to the Depreciation tab (Main Tab) of an asset.", "Allows a user access to the General Info tab (Main Tab) of an asset.", "", "Controls the create invoice button on Job/Contract Invoices tab, Send To Field on Job Invoices tab", "and 263 more..."], "totalDistinctValueCount": 268, "is_unstructured": false}, {"name": "UserPermissions_Employee_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The complete name of the employee associated with the permissions, facilitating identification of which user has specific access rights.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Dallas Skiftun", "<PERSON><PERSON>", "and 33 more..."], "totalDistinctValueCount": 38, "is_unstructured": false}, {"name": "UserPermissions_EmployeeID", "dataType": "int", "description": "A unique integer identifier for each employee within the system, serving as a foreign key that links to the EmployeeDetail table to maintain consistent access records.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 33 more..."], "is_unstructured": false}], "relationships": [{"child_table": "UserPermissions", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[UserPermissions.EmployeeID]"}], "relationship_type": "one-to-many"}]}