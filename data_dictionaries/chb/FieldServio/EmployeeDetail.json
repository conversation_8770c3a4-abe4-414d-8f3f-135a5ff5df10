{"tableName": "EmployeeDetail", "description": "The EmployeeDetail table contains comprehensive information on each employee, including unique identifiers, contact details, employment status, office location, and reporting relationships. It supports role differentiation with fields like IsSalesPerson and IsJobAssignee, facilitating assignment tracking and organizational management. Some contact and regional fields may be incomplete. The table is essential for managing employee data, internal communication, and the organizational structure of Air Unlimited.", "fields": [{"name": "EmployeeDetail_EmployeeID", "dataType": "int", "description": "A unique integer identifier for each employee, essential for referencing records and establishing relationships with other tables.", "subsetOfAvailableValues": ["107982", "107983", "107984", "107985", "107986", "and 73 more..."], "is_unstructured": false}, {"name": "EmployeeDetail_EmployeeNumber", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the employee's number, used for internal tracking; typically unique within small groups of records.", "subsetOfAvailableValues": ["100", "101", "102", "103", "104", "and 31 more..."], "is_unstructured": false}, {"name": "EmployeeDetail_EmployeeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The full name of the employee, which uniquely identifies individuals within the organization.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 71 more..."], "totalDistinctValueCount": 76, "is_unstructured": false}, {"name": "EmployeeDetail_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the employment status of the employee, either 'Active' or 'Inactive', used to track availability and engagement.", "availableValues": ["Inactive", "Active"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "EmployeeDetail_PrimaryOfficeID", "dataType": "int", "description": "An integer identifying the primary office location of the employee, linking them to their respective offices.", "availableValues": ["419", "421"], "is_unstructured": false}, {"name": "EmployeeDetail_PrimaryOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that specifies the primary office location, providing context for the employee's work base.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "EmployeeDetail_Address1", "dataType": "<PERSON><PERSON><PERSON>", "description": "The primary address of the employee, unique to each record, though it may be missing for some entries.", "subsetOfAvailableValues": ["45 Mohawk Bay", "436 2nd Street East", "412-917 Jefferson Ave", "4108-662 Kenaston Blvd", "343 Country Club Blvd", "and 37 more..."], "totalDistinctValueCount": 42, "is_unstructured": false}, {"name": "EmployeeDetail_Address2", "dataType": "<PERSON><PERSON><PERSON>", "description": "Supplementary address information, typically unique within small groups of records and available for a limited number of entries.", "availableValues": ["P.O. Box 407"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "EmployeeDetail_City", "dataType": "<PERSON><PERSON><PERSON>", "description": "The city where the employee resides, used for geographical analysis, though often missing from records.", "availableValues": ["Winnipeg", "Saskatoon", "Stonewall ", "West St. Paul ", "Regina ", "Tyndall ", "<PERSON><PERSON>", "", "<PERSON><PERSON><PERSON>", "Cooks Creek", "<PERSON><PERSON> "], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "EmployeeDetail_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "The state where the employee is located, either 'Manitoba' or 'Saskatchewan', important for regional management.", "availableValues": ["Manitoba", "Saskatchewan", ""], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "EmployeeDetail_Zip", "dataType": "<PERSON><PERSON><PERSON>", "description": "The postal code of the employee's address, unique for each record, but often missing, potentially impacting correspondence.", "subsetOfAvailableValues": ["R0C 2Z0", "R3W 0C6", "R3N 1K3", "R3M 2K3", "R3M 0Y3", "and 35 more..."], "totalDistinctValueCount": 40, "is_unstructured": false}, {"name": "EmployeeDetail_WorkPhone", "dataType": "<PERSON><PERSON><PERSON>", "description": "The employee's work phone number, available for less than half of the records, facilitating direct contact.", "availableValues": ["", "************", "************", "************"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "EmployeeDetail_CellPhone", "dataType": "<PERSON><PERSON><PERSON>", "description": "The employee's cell phone number, used for direct communication, though often missing from records.", "subsetOfAvailableValues": ["", "************", "************", "************", "************", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "EmployeeDetail_Title", "dataType": "<PERSON><PERSON><PERSON>", "description": "The job title of the employee, providing insight into their role, but frequently missing from the dataset.", "subsetOfAvailableValues": ["Service Technician ", "Customer Service Representative ", "Rental and Construction Sales Representative", "Technician ", "Receptionist ", "and 28 more..."], "totalDistinctValueCount": 33, "is_unstructured": false}, {"name": "EmployeeDetail_Email", "dataType": "<PERSON><PERSON><PERSON>", "description": "The employee's email address, unique to each record and essential for electronic communication.", "subsetOfAvailableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 72 more..."], "totalDistinctValueCount": 77, "is_unstructured": false}, {"name": "EmployeeDetail_IsSalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates if the employee is a salesperson, with values of 'Yes' or 'No', reflecting the sales structure.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "EmployeeDetail_IsJobAssignee", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates whether the employee is assigned to a job, aiding in project management and assignment tracking.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "EmployeeDetail_IsTechnician", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates if the employee is classified as a technician; currently, no records reflect this role.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "EmployeeDetail_ManagerID", "dataType": "int", "description": "An integer identifying the employee's manager, used for hierarchical structuring within the organization.", "availableValues": ["107988", "107994", "108001", "108014", "108041"], "is_unstructured": false}, {"name": "EmployeeDetail_ManagerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the employee's manager, critical for understanding reporting relationships within the company.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", ""], "totalDistinctValueCount": 5, "is_unstructured": false}]}