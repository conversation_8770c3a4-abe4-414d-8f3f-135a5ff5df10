{"tableName": "ServicePackageDetail", "description": "ServicePackageDetail stores itemized details for each service package, including item codes, names, quantities, and override prices, while linking each package to specific customers, equipment, and delivery locations. This table enables tracking, pricing, and analysis of service package contents, supporting detailed queries for service composition, inventory, billing, and customer relationships.", "fields": [{"name": "ServicePackage_ID", "dataType": "int", "description": "A unique integer identifier for each service package record, used to group related records that share common characteristics.", "subsetOfAvailableValues": ["67993", "68039", "68619", "68751", "68752", "and 1456 more..."], "is_unstructured": false}, {"name": "ServicePackage_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string that specifies the name of the service package, typically shared among related packages, indicating similarities in their offerings.", "subsetOfAvailableValues": ["LEVEL 3", "Annual", "LEVEL 1", "Level 2", "Inspection", "and 441 more..."], "is_unstructured": false}, {"name": "ServicePackage_Equipment_ID", "dataType": "int", "description": "An integer representing the unique identifier of the equipment associated with the service package, which may not be applicable for all packages.", "subsetOfAvailableValues": ["950458", "950577", "950603", "950834", "950835", "and 730 more..."], "is_unstructured": false}, {"name": "ServicePackage_Equipment_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that contains the name of the equipment linked to the service package, providing context for the equipment involved.", "subsetOfAvailableValues": ["SD100", "SD150", "ONAN 17.5RDJF-4R", "SD350", "GA7 VSD", "and 523 more..."], "totalDistinctValueCount": 528, "is_unstructured": false}, {"name": "ServicePackage_Equipment_SerialNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string holding the serial number of the associated equipment, which serves as a unique identifier for that specific piece of equipment.", "subsetOfAvailableValues": ["137937", "8477321", "9878409", "773671567", "3005141625", "and 729 more..."], "totalDistinctValueCount": 734, "is_unstructured": false}, {"name": "ServicePackage_Customer_ID", "dataType": "int", "description": "An integer denoting the unique identifier for the customer receiving the service package, essential for tracking customer-specific packages.", "subsetOfAvailableValues": ["380039", "380079", "380181", "380204", "380489", "and 337 more..."], "is_unstructured": false}, {"name": "ServicePackage_Customer_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the customer associated with the service package, providing clarity on the recipient of the service.", "subsetOfAvailableValues": ["Northern Health Region", "The Mosaic Company", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "Genrep Ltd.", "Canadian National Railway", "and 337 more..."], "totalDistinctValueCount": 342, "is_unstructured": false}, {"name": "ServicePackage_Location_ID", "dataType": "int", "description": "An integer that indicates the unique identifier for the delivery location of the service package, crucial for logistics and service fulfillment.", "subsetOfAvailableValues": ["659573", "659650", "659652", "659819", "660257", "and 509 more..."], "is_unstructured": false}, {"name": "ServicePackage_Location_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the location where the service package is to be delivered, aiding in geographical identification.", "subsetOfAvailableValues": ["The Mosaic Company", "Headingley Correctional Centre", "Future Transfer", "Milner Ridge Correctional Centre", "ARDENT MILLS, ULC.", "and 481 more..."], "totalDistinctValueCount": 486, "is_unstructured": false}, {"name": "ServicePackage_ItemID", "dataType": "int", "description": "An integer that uniquely identifies each item within the service package, ensuring distinct tracking of individual items.", "subsetOfAvailableValues": ["2825307", "2826815", "2827695", "2827709", "2827720", "and 871 more..."], "is_unstructured": false}, {"name": "ServicePackage_Item_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that represents the code assigned to each item in the service package, facilitating inventory management and identification.", "subsetOfAvailableValues": ["22421853", "24121212", "24900433", "38035531", "38459582", "and 871 more..."], "totalDistinctValueCount": 876, "is_unstructured": false}, {"name": "ServicePackage_Item_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string providing the name of each item included in the service package, enhancing clarity regarding the contents of the package.", "subsetOfAvailableValues": ["Labour per hour", "Environmental Consumables used", "Truck Charge", "Oil Filter", "Travel Time", "and 397 more..."], "is_unstructured": false}, {"name": "ServicePackage_Item_Quantity", "dataType": "decimal", "description": "A decimal indicating the quantity of each item included in the service package, essential for accurate inventory tracking.", "is_unstructured": false}, {"name": "ServicePackage_Item_OverridePrice", "dataType": "decimal", "description": "A decimal representing the overridden price for an item within the service package, allowing flexibility in pricing strategy.", "is_unstructured": false}], "relationships": [{"child_table": "ServicePackageDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[ServicePackage.Customer.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "ServicePackageDetail", "parent_table": "EquipmentDetail", "key_column_mapping": [{"parent_column": "[Equipment.ID]", "child_column": "[ServicePackage.Equipment.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "ServicePackageDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[ServicePackage.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "ServicePackageDetail", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[ServicePackage.Location.ID]"}], "relationship_type": "one-to-many"}]}