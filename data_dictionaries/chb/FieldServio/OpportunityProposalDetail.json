{"tableName": "OpportunityProposalDetail", "description": "OpportunityProposalDetail records comprehensive information for each sales opportunity proposal, including proposal details, customer, job, item, location, and associated financials. It tracks proposal progress, responsible salesperson, and links to related entities via foreign keys, supporting sales pipeline analysis and forecasting. Fields may be incomplete; queries should accommodate possible null values.", "fields": [{"name": "OpportunityProposalDetail_ID", "dataType": "int", "description": "A unique identifier for each record in the OpportunityProposalDetail table, ensuring distinct entries for tracking proposals.", "subsetOfAvailableValues": ["251981", "252823", "253052", "253692", "264154", "and 735 more..."], "is_unstructured": false}, {"name": "OpportunityProposalDetail_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The specific name assigned to the opportunity proposal, intended to provide a unique identifier for easy reference.", "subsetOfAvailableValues": ["PVWC Stephenfield ", "Exceldor- New Compressor PM", "NRC 2 Year PM", "NDL11472-PMA-GEN-11/24", "Propworks 2 Year PM", "and 708 more..."], "totalDistinctValueCount": 713, "is_unstructured": false}, {"name": "OpportunityProposalDetail_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "The office location associated with the opportunity proposal, indicating which regional office is handling the proposal.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "OpportunityProposalDetail_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed textual overview of the opportunity proposal, designed to provide context and specifics about the offering.", "subsetOfAvailableValues": ["", "Both Stephenfield and Morris 4 units 5 years ", "1-time annual service for North Caribou (Weagamow) Lake School's three Generacs.\n<PERSON> was recently there to commission transfer switch and recommended a repair for one of the units; ideally will plan all in single trip (one day travel, three days on site, one day return; quote assumes location is accessible by road and that there are accommodations available in community, or else additional charges may apply). Called <PERSON> to discuss, will quote him directly and he will confirm details and help coordinate with community. Followed up w/ <PERSON> via email 2024/01/03. May need to look at alternative travel options or wait until spring for work to be completed.", "5 Year PM on new unit ", "Can I get a level 3  service quote for <PERSON> with <PERSON><PERSON><PERSON>. For 1 year term.\n(updated)\n\nAir Dryer:\nModel number: HL1500IHGSALOX\nSerial number: 522101\n\nAir Compressor:\nMD- SSR-EPE250-2S\nSN- EE3274U12198\n\nAir Compressor:\nMD- SSR-EPE250-2S\nSN- EE3274U12194\n\nAir Compressor:\nMD- SSR-RPR250-25\nSN- EE3275U12199", "and 581 more..."], "totalDistinctValueCount": 586, "is_unstructured": false}, {"name": "OpportunityProposalDetail_NextSteps", "dataType": "<PERSON><PERSON><PERSON>", "description": "A summary of the planned actions to be taken for the opportunity proposal, guiding the follow-up process.", "subsetOfAvailableValues": ["", "A&B Mech awarded project. ", "Emailed DV for P&V", "Lost to <PERSON><PERSON><PERSON>", "PO provided to proceed", "and 155 more..."], "totalDistinctValueCount": 160, "is_unstructured": false}, {"name": "OpportunityProposalDetail_Salesperson", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the salesperson responsible for managing the opportunity proposal, indicating accountability and ownership.", "availableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON>"], "totalDistinctValueCount": 12, "is_unstructured": false}, {"name": "OpportunityProposalDetail_Stage", "dataType": "<PERSON><PERSON><PERSON>", "description": "The current status of the opportunity proposal, categorizing it into stages such as 'Opportunity won' or 'Opportunity lost'.", "availableValues": ["Opportunity won", "Opportunity-Abandoned", "Quoted", "Opportunity- Lost", "Investigating/data gathering"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "OpportunityProposalDetail_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "The classification of the opportunity proposal, helping to categorize different types of proposals for reporting and analysis.", "availableValues": ["PMA-New", "Compressors", "PMA-Rene<PERSON>", "Dryers", "Service repair", "Rentals", "Generators", "Audit", "Parts sale", "Remote monitoring", "Installation"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "OpportunityProposalDetail_ForecastCategory", "dataType": "<PERSON><PERSON><PERSON>", "description": "The expected closing timeline for the proposal, categorizing it based on urgency and expected completion timeframe.", "availableValues": ["1-30 day close", "31-45 day close", "46-60 day close", "61-90 day close", "Budgetary", "Emergency"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "OpportunityProposalDetail_LeadSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "The origin of the lead for the proposal, providing insights into the effectiveness of various marketing channels.", "availableValues": ["General Inquiry", "Service department identified lead", "Cold Call", "Bid and Spec (Tender)", "Service technician identified lead", "Referral from related companies", "Referral from supplier/contractor", "Referral from customer", "Website lead", "Marketing initiative"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "OpportunityProposalDetail_CloseDate", "dataType": "datetime", "description": "The date anticipated for the proposal to close, serving as a target for follow-up and revenue recognition.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_Probability", "dataType": "int", "description": "An integer value indicating the likelihood of the opportunity proposal closing successfully, expressed as a percentage.", "subsetOfAvailableValues": ["0", "5", "10", "15", "20", "and 15 more..."], "is_unstructured": false}, {"name": "OpportunityProposalDetail_Amount", "dataType": "decimal", "description": "The expected monetary value associated with the opportunity proposal, representing projected revenue.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_ExpectedRevenue", "dataType": "decimal", "description": "The estimated revenue expected from the opportunity proposal, aiding in financial forecasting.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the customer related to the proposal, helping to identify the client for whom the proposal is prepared.", "subsetOfAvailableValues": ["Pembina Valley Water Coop Inc.", "<PERSON><PERSON><PERSON>", "3728111 Manitoba Ltd O/A Eastside Industrial", "National Research Council", "Exceldor Cooperative", "and 450 more..."], "totalDistinctValueCount": 455, "is_unstructured": false}, {"name": "OpportunityProposalDetail_CustomerID", "dataType": "int", "description": "A unique identifier for the customer associated with the proposal, facilitating customer-specific tracking.", "subsetOfAvailableValues": ["380153", "380230", "380418", "380513", "380528", "and 146 more..."], "is_unstructured": false}, {"name": "OpportunityProposalDetail_LocationID", "dataType": "int", "description": "An identifier linking the opportunity proposal to a particular location, used for geographic data analysis.", "subsetOfAvailableValues": ["658877", "658880", "659041", "659240", "659349", "and 130 more..."], "is_unstructured": false}, {"name": "OpportunityProposalDetail_LocationName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the location associated with the opportunity proposal, providing context for site-specific proposals.", "subsetOfAvailableValues": ["<PERSON><PERSON><PERSON>", "North Caribou Lake First Nation School", "Propworks", "Calm Air International LP", "Manitoba Cooperative Honey Producers Ltd", "and 127 more..."], "totalDistinctValueCount": 132, "is_unstructured": false}, {"name": "OpportunityProposalDetail_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the job relevant to the proposal, indicating the specific work or project associated with it.", "subsetOfAvailableValues": ["SR-3894305", "ES-3860310", "ES-3689947", "ES-3723775", "ES-4128523", "and 89 more..."], "totalDistinctValueCount": 94, "is_unstructured": false}, {"name": "OpportunityProposalDetail_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A classification of the job related to the proposal, specifying its nature and category.", "availableValues": ["Equipment Sale-COM", "Specialty Services-COM", "Part Sale-COM", "Service Request - GEN", "Service Request-COM"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "OpportunityProposalDetail_JobStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "The current status of the job related to the proposal, indicating progress and actions needed.", "availableValues": ["Completed", "Waiting on Parts", "Ready to Schedule", "In Progress"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "OpportunityProposalDetail_JobDatePromised", "dataType": "datetime", "description": "The date by which the job is promised to be completed, serving as a commitment to the client.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_ProposalDate", "dataType": "datetime", "description": "The date the proposal was created or submitted, providing a timeline for proposal management.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A code that uniquely identifies the specific item linked to the proposal, facilitating tracking and reference.", "subsetOfAvailableValues": ["23973969", "24121212", "38035531", "38459582", "39329602", "and 381 more..."], "totalDistinctValueCount": 386, "is_unstructured": false}, {"name": "OpportunityProposalDetail_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The name of the item associated with the proposal, providing clarity on what is being offered.", "subsetOfAvailableValues": ["23973969", "24121212", "38035531", "38459582", "39329602", "and 380 more..."], "is_unstructured": false}, {"name": "OpportunityProposalDetail_ItemCategory", "dataType": "<PERSON><PERSON><PERSON>", "description": "The classification of the item within the proposal, helping to organize and analyze different types of items.", "availableValues": ["Parts", "Miscellaneous", "Labor", "Equipment", "Shipping", "Rental"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "OpportunityProposalDetail_ItemManufacturer", "dataType": "<PERSON><PERSON><PERSON>", "description": "The manufacturer of the item related to the proposal, allowing for analysis of supplier-related trends.", "availableValues": ["Ingersoll Rand", "N/A", "<PERSON>", "Generac", "DeVillbiss/DeVair", "CAG", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "SULLAIR", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "OpportunityProposalDetail_EquipItemNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "The item number for the equipment linked to the proposal, used for precise identification in inventory.", "subsetOfAvailableValues": ["AC - Sullair - Oil Flooded", "Generator - Generac - Diesel", "R45n", "AC - Ingersoll Rand - Oil Flooded", "IRN50H-OF AC", "and 61 more..."], "totalDistinctValueCount": 66, "is_unstructured": false}, {"name": "OpportunityProposalDetail_EquipName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the equipment associated with the proposal, providing details about the specific item being proposed.", "subsetOfAvailableValues": ["R45n", "1809EV/AC", "2209 AC", "IRN50H-OF AC", "SD400", "and 74 more..."], "totalDistinctValueCount": 79, "is_unstructured": false}, {"name": "OpportunityProposalDetail_EquipSerialNum", "dataType": "<PERSON><PERSON><PERSON>", "description": "The serial number of the equipment, crucial for tracking, warranty, and service purposes.", "subsetOfAvailableValues": ["1830", "3006967100", "202010230027", "201705120004", "MOX1004703", "and 83 more..."], "totalDistinctValueCount": 88, "is_unstructured": false}, {"name": "OpportunityProposalDetail_Quantity", "dataType": "decimal", "description": "The amount of items proposed in the opportunity, essential for inventory management and sales forecasting.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_UnitPrice", "dataType": "decimal", "description": "The price per unit of the item being proposed, critical for pricing strategy and revenue calculations.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_CurrencyPrice", "dataType": "decimal", "description": "The price of the item denoted in the relevant currency, necessary for financial reporting and analysis.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_Subtotal", "dataType": "decimal", "description": "The subtotal for all items in the proposal before taxes and discounts, fundamental for revenue calculations.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_Tax", "dataType": "decimal", "description": "The tax amount applied to the proposal's subtotal, essential for compliance and financial reporting.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_TotalPrice", "dataType": "decimal", "description": "The final price for the proposal, including all items, taxes, and discounts, crucial for customer billing.", "is_unstructured": false}, {"name": "OpportunityProposalDetail_DiscountAmt", "dataType": "decimal", "description": "The discount amount applied to the proposal's total price, impacting overall profitability and client negotiations.", "is_unstructured": false}], "relationships": [{"child_table": "OpportunityProposalDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[OpportunityProposalDetail.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "OpportunityProposalDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[OpportunityProposalDetail.ItemCode]"}], "relationship_type": "one-to-many"}, {"child_table": "OpportunityProposalDetail", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[OpportunityProposalDetail.LocationID]"}], "relationship_type": "one-to-many"}]}