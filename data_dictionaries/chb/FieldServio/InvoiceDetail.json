{"tableName": "InvoiceDetail", "description": "InvoiceDetail stores detailed line-item information for each invoice, including associated jobs, contracts, customers, and billed items. It tracks billing amounts, pricing adjustments, taxes, discounts, payment terms, key dates, and statuses. This table supports financial analysis, revenue recognition, and operational reporting across Air Unlimited’s service, rental, and maintenance activities, enabling comprehensive insight into invoicing, payment, and sales performance.", "fields": [{"name": "InvoiceDetail_InvoiceID", "dataType": "int", "description": "A unique identifier for each invoice record, ensuring distinct tracking across all invoices.", "subsetOfAvailableValues": ["2618461", "2629865", "2636766", "2636774", "2641839", "and 9989 more..."], "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The title or name of the invoice, which serves to describe the invoice's purpose or associated project.", "subsetOfAvailableValues": ["9862-1", "10342-1", "9863-1", "8795-1", "7077-1", "and 9956 more..."], "totalDistinctValueCount": 9961, "is_unstructured": false}, {"name": "InvoiceDetail_JobID", "dataType": "int", "description": "Links to the specific job related to the invoice, with some records potentially missing this reference.", "subsetOfAvailableValues": ["1821248", "1821278", "1827300", "1827949", "1828809", "and 4106 more..."], "is_unstructured": false}, {"name": "InvoiceDetail_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the job associated with the invoice, which may not always be present in the records.", "subsetOfAvailableValues": ["PFB-3727920", "PFB-3727919", "PS-4215606", "PS-3767417", "PFB-3679250", "and 4106 more..."], "totalDistinctValueCount": 4111, "is_unstructured": false}, {"name": "InvoiceDetail_ContractID", "dataType": "int", "description": "Identifies the contract linked to the invoice, but is not consistently available across all records.", "subsetOfAvailableValues": ["192363", "192392", "192395", "192401", "192901", "and 561 more..."], "is_unstructured": false}, {"name": "InvoiceDetail_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "The specific number of the contract associated with the invoice, with limited availability in the dataset.", "subsetOfAvailableValues": ["PFB194576", "PFB209178", "PFB75665", "PFB74994", "PMA192901", "and 561 more..."], "totalDistinctValueCount": 566, "is_unstructured": false}, {"name": "InvoiceDetail_ContractName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the contract associated with the invoice, often missing in a significant portion of records.", "subsetOfAvailableValues": ["AIM11504-PMA-GEN-08/24", "Safeway/Sobeys SK/MB/ON Annual Generator PM 2024", "NOR11055-PMA-GEN-05/24", "CIT10642-PMA-GEN-02/24", "GEN10792-PMA-GEN-07/23", "and 530 more..."], "totalDistinctValueCount": 535, "is_unstructured": false}, {"name": "InvoiceDetail_CustomerID", "dataType": "int", "description": "An identifier for the customer linked to the invoice, typically unique across records.", "subsetOfAvailableValues": ["379954", "380079", "380204", "380230", "380504", "and 1153 more..."], "is_unstructured": false}, {"name": "InvoiceDetail_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the customer associated with the invoice, generally unique when available.", "subsetOfAvailableValues": ["Genrep Ltd.", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "<PERSON> Agencies (USD)", "BPL Sales Ltd.", "Air Unlimited Inc.", "and 1148 more..."], "totalDistinctValueCount": 1153, "is_unstructured": false}, {"name": "InvoiceDetail_BillToID", "dataType": "<PERSON><PERSON><PERSON>", "description": "Identifies the billing entity for the invoice, usually unique within the context of the dataset.", "subsetOfAvailableValues": ["379954", "380079", "380204", "380230", "380504", "and 1153 more..."], "totalDistinctValueCount": 1158, "is_unstructured": false}, {"name": "InvoiceDetail_BillToName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the billing entity responsible for the invoice payment, usually unique.", "subsetOfAvailableValues": ["Genrep Ltd.", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "<PERSON> Agencies (USD)", "BPL Sales Ltd.", "Air Unlimited Inc.", "and 1148 more..."], "totalDistinctValueCount": 1153, "is_unstructured": false}, {"name": "InvoiceDetail_ShipToName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the shipping entity for the invoice, with infrequent missing values.", "subsetOfAvailableValues": ["BPL Sales Ltd.", "Winnipeg-Head Office", "TOM BEGGS (USD)", "The Mosaic Company", "<PERSON>", "and 1704 more..."], "totalDistinctValueCount": 1709, "is_unstructured": false}, {"name": "InvoiceDetail_ShipToCity", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the city for shipment related to the invoice, with some records not containing this information.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "<PERSON>", "Portage La Prairie", "and 445 more..."], "totalDistinctValueCount": 450, "is_unstructured": false}, {"name": "InvoiceDetail_ShipToState", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies the state for the invoice shipment, with occasional missing values.", "availableValues": ["MB", "SK", "ON", "", "AB", "YT", "TX", "BC", "KS", "IL", "NB", "NS", "ND", "WI", "QC", "NU", "--", "PA", "FL", "OH", "NY"], "totalDistinctValueCount": 21, "is_unstructured": false}, {"name": "InvoiceDetail_ShipToZip", "dataType": "<PERSON><PERSON><PERSON>", "description": "Represents the ZIP code of the shipping address, generally unique but with some missing data.", "subsetOfAvailableValues": ["R2R 0J2", "", "R2R 0J6", "R3L 2A2", "R2C 2Z2", "and 1224 more..."], "totalDistinctValueCount": 1229, "is_unstructured": false}, {"name": "InvoiceDetail_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the type of job related to the invoice, providing context for the billed work.", "subsetOfAvailableValues": ["Service Request-COM", "PM Fixed Bid - GEN", "PMA-COM", "Part Sale-COM", "PMA - GEN", "and 24 more..."], "totalDistinctValueCount": 29, "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the current status of the invoice, which helps in tracking payment and processing.", "availableValues": ["Paid in Full", "Voided", "<PERSON><PERSON>", "In Development", "Ready for Accounting Review", "Partial Payment Received", "Approved", "Waiting For Customer PO Number"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InvoiceDetail_SalesResource", "dataType": "<PERSON><PERSON><PERSON>", "description": "Identifies the sales resource associated with the invoice, though frequently missing.", "availableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 22, "is_unstructured": false}, {"name": "InvoiceDetail_VoidedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies who voided the invoice, if applicable, with limited availability in records.", "availableValues": ["<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "totalDistinctValueCount": 20, "is_unstructured": false}, {"name": "InvoiceDetail_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the creator of the invoice, with minimal missing values.", "subsetOfAvailableValues": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "and 22 more..."], "totalDistinctValueCount": 27, "is_unstructured": false}, {"name": "InvoiceDetail_SentBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "Identifies the individual who sent the invoice, with some records lacking this information.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "InvoiceDetail_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the office associated with the invoice, providing geographical context.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InvoiceDetail_Currency", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Specifies the currency used for the invoice, primarily Canadian or US dollars.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "InvoiceDetail_CurrencySubTotal", "dataType": "decimal", "description": "The subtotal amount of the invoice in the specified currency, excluding tax.", "is_unstructured": false}, {"name": "InvoiceDetail_CurrencyTax", "dataType": "decimal", "description": "The tax amount applied to the invoice in the specified currency.", "is_unstructured": false}, {"name": "InvoiceDetail_CurrencyTotalAmt", "dataType": "decimal", "description": "The total monetary amount for the invoice after all calculations and adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the invoice as either an Invoice, Credit, or Deposit for financial reporting.", "availableValues": ["Invoice", "Credit", "<PERSON><PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies the source of the invoice, aiding in tracking its origin.", "availableValues": ["Job", "Contract", "External", "Import", "Customer", "RMA", "Customer <PERSON><PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceAmount", "dataType": "decimal", "description": "The gross amount billed on the invoice prior to any adjustments or payments.", "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceAmountDue", "dataType": "decimal", "description": "The outstanding amount due for the invoice, indicating pending payments.", "is_unstructured": false}, {"name": "InvoiceDetail_PONumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "Contains the Purchase Order number associated with the invoice, facilitating procurement tracking.", "subsetOfAvailableValues": ["1817", "1819", "50155", "728389", "PM Service", "and 2997 more..."], "totalDistinctValueCount": 3002, "is_unstructured": false}, {"name": "InvoiceDetail_PaymentTerms", "dataType": "<PERSON><PERSON><PERSON>", "description": "Outlines the conditions under which payment is to be made, such as due dates and discounts.", "availableValues": ["Net 30 Days", "Payment required at time of pick up", "N30", "Payment required at time of order", "", "Net 60 Days", "Credit Card - Credit Card Payment", "N30 - Net 30 Days", "COD", "Net 45 Days", "Prepaid", "N45 - Net 45 Days", "COD - Payment required at time of pick up", "N90", "Prepaid - Payment required at time of order", "N60"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceDate", "dataType": "datetime", "description": "Records the date the invoice was issued, providing a timestamp for financial obligations.", "is_unstructured": false}, {"name": "InvoiceDetail_SentDate", "dataType": "datetime", "description": "Indicates the date the invoice was sent to the customer, with some records missing this detail.", "is_unstructured": false}, {"name": "InvoiceDetail_SentTransactionDate", "dataType": "datetime", "description": "Denotes the transaction date when the invoice was sent to the customer.", "is_unstructured": false}, {"name": "InvoiceDetail_VoidedDate", "dataType": "datetime", "description": "Shows the date when the invoice was voided, applicable only to certain records.", "is_unstructured": false}, {"name": "InvoiceDetail_VoidedTransactionDate", "dataType": "datetime", "description": "Indicates the transaction date when the invoice was voided, with limited availability.", "is_unstructured": false}, {"name": "InvoiceDetail_DueDate", "dataType": "datetime", "description": "Specifies the payment due date for the invoice, indicating expected payment timelines.", "is_unstructured": false}, {"name": "InvoiceDetail_PaidDate", "dataType": "datetime", "description": "Records the date when the invoice was paid, with some records missing this information.", "is_unstructured": false}, {"name": "InvoiceDetail_PaidStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates whether the invoice has been paid, essential for tracking payment status.", "availableValues": ["Paid", "Not Paid"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceDetail_DeferredRevenue", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies whether the revenue from the invoice is deferred, important for revenue recognition.", "availableValues": ["Not Deferred", "Deferred"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceDetail_Retainage", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the retainage status of the invoice, consistently showing that no retainage is held.", "availableValues": ["Not Retainage"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "InvoiceDetail_TaxSchedule", "dataType": "<PERSON><PERSON><PERSON>", "description": "Outlines the applicable tax schedule for the invoice, with occasional missing values.", "availableValues": ["MB - Sales Tax - GST 5% + PST 7%", "SK - Sales Tax - GST 5% + PST 6%", "MB - Sales Tax - GST 5%", "Both taxes exempt", "SK - Sales Tax - GST 5%", "MB - Sales Tax - PST 7%", "ON - Sales Tax - HST 13%", "USD MB - Sales Tax - GST 5%", "AB - Sales Tax - GST 5%", "SK - Sales Tax - PST 6%", "USD GST Only", "USD SK - Sales Tax - GST 5%", "USD MB - Sales Tax - GST 5% + PST 7%", "YT - Sales Tax - GST 5%", "GST Only", "BC - Sales Tax - GST 5%", "QC - Sales Tax - GST 5%", "USD ON - Sales Tax - HST 13%", "NB - Sales Tax - HST 15%", "BC - Sales Tax - GST 5% + PST 7%", "NU - Sales Tax - GST 5%", "USD SK - Sales Tax - GST 5% + PST 6%", "QC - Sales Tax - GST 5% + QST 9.98%"], "totalDistinctValueCount": 23, "is_unstructured": false}, {"name": "InvoiceDetail_ItemID", "dataType": "int", "description": "A unique identifier for the item associated with the invoice, linking to specific inventory items.", "subsetOfAvailableValues": ["2825776", "2825788", "2826815", "2827695", "2827709", "and 2754 more..."], "is_unstructured": false}, {"name": "InvoiceDetail_ItemCategory", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the item related to the invoice, aiding in classification and understanding.", "availableValues": ["Miscellaneous", "Parts", "Labor", "Shipping", "Equipment", "Rental"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "InvoiceDetail_ItemType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies the type of item billed on the invoice, providing clarity on its nature.", "availableValues": ["Parts", "Misc - taxed", "Labor", "Mileage", "Misc - Non Taxed", "Freight", "Equipment", "Expense"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InvoiceDetail_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The name of the item associated with the invoice, facilitating item identification.", "subsetOfAvailableValues": ["38035531", "38459582", "92692284", "Environmental Consumables", "Truck Charge", "and 2751 more..."], "is_unstructured": false}, {"name": "InvoiceDetail_ItemDescription", "dataType": "<PERSON><PERSON><PERSON>", "description": "Provides a description of the item on the invoice, enhancing understanding of the billed item.", "subsetOfAvailableValues": ["Environmental Consumables Environmental Consumables used", "Truck Charge Truck Charge", "PM Agreement", "Imported AR", "Customer Prepayment", "and 3932 more..."], "totalDistinctValueCount": 3937, "is_unstructured": false}, {"name": "InvoiceDetail_ItemQuantity", "dataType": "decimal", "description": "Indicates the quantity of the item listed on the invoice, reflecting how many units are billed.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemUnitCost", "dataType": "decimal", "description": "Represents the cost per unit of the item associated with the invoice.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemInvoicedCost", "dataType": "decimal", "description": "The total cost incurred for the item being invoiced, potentially including adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemUnitPrice", "dataType": "decimal", "description": "The price per unit of the item before taxes or discounts, contributing to the invoice total.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemCurrencyPrice", "dataType": "decimal", "description": "The price of the item in the specified currency, potentially including adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemInvoicedPrice", "dataType": "decimal", "description": "The total price of the item as it appears on the invoice, accounting for adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemPriceOverride", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates whether the item's price was overridden from standard pricing.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceDetail_ItemPriceOverrideAmount", "dataType": "decimal", "description": "The amount of the overridden price when applicable, reflecting pricing adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemTaxAmount", "dataType": "decimal", "description": "The tax amount applied to the item, critical for total invoice calculations.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemGrandTotalPrice", "dataType": "decimal", "description": "The final total price for the item, including all taxes and adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemDiscountPercent", "dataType": "decimal", "description": "The percentage discount applied to the item price, critical for invoice calculations.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemDiscountAmount", "dataType": "decimal", "description": "The total discount amount applied to the item, essential for determining the final invoice total.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemCurrencyAmount", "dataType": "decimal", "description": "The total amount for the item in the specified currency, reflecting potential adjustments.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemCurrencyTax", "dataType": "decimal", "description": "The tax amount applied to the item in the specified currency, important for reporting.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemCurrencyTotal", "dataType": "decimal", "description": "The total amount for the item, including tax in the specified currency.", "is_unstructured": false}, {"name": "InvoiceDetail_ItemLaborResource", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the labor resource associated with the item, with frequent missing values.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "InvoiceDetail_JobTechnicians", "dataType": "<PERSON><PERSON><PERSON>", "description": "Lists the technicians associated with the job for the invoice, with many records missing this detail.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 77 more..."], "totalDistinctValueCount": 82, "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceEmployeeID", "dataType": "<PERSON><PERSON><PERSON>", "description": "Represents the ID of the employee who processed the invoice, with notable missing values.", "subsetOfAvailableValues": ["103", "104", "107", "111", "114", "and 36 more..."], "totalDistinctValueCount": 41, "is_unstructured": false}, {"name": "InvoiceDetail_InvoiceEmployeeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Contains the name of the employee who processed the invoice, with some records lacking this information.", "subsetOfAvailableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "and 85 more..."], "totalDistinctValueCount": 90, "is_unstructured": false}], "relationships": [{"child_table": "InvoiceDetail", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[InvoiceDetail.ContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceDetail", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.Number]", "child_column": "[InvoiceDetail.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[InvoiceDetail.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceDetail", "parent_table": "InvoiceSummary", "key_column_mapping": [{"parent_column": "[InvoiceSummary.InvoiceID]", "child_column": "[InvoiceDetail.InvoiceID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceDetail", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InvoiceDetail.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[InvoiceDetail.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceDetail", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InvoiceDetail.JobID]"}], "relationship_type": "one-to-many"}]}