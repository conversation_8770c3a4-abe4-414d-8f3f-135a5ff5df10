{"tableName": "WarehouseInventory", "description": "The WarehouseInventory table records inventory details for each warehouse and office location, tracking item names, unique item IDs, current, minimum, and maximum quantities, and inventory status. Each record is uniquely identified by Warehouse.Inventory.ID. This table supports comprehensive inventory management, stock level monitoring, and replenishment decisions across all business locations.", "fields": [{"name": "Warehouse_Inventory_ID", "dataType": "int", "description": "A unique integer identifier for each inventory record, ensuring distinct reference across the WarehouseInventory table.", "subsetOfAvailableValues": ["13564234", "13564257", "13564357", "13564380", "13564403", "and 9613 more..."], "is_unstructured": false}, {"name": "Warehouse_Id", "dataType": "int", "description": "An integer serving as a secondary identifier for the warehouse, used to associate inventory records with specific warehouse entities.", "subsetOfAvailableValues": ["29913", "29914", "29916", "29917", "29918", "and 12 more..."], "is_unstructured": false}, {"name": "Warehouse_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field representing the name of the warehouse, aiding in the identification of the location or type related to the inventory records.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "~Regina Warehouse", "S/N 1143909 - <PERSON>", "S/N KB09767 - <PERSON>", "S/N 1249338 - <PERSON>", "S/N KB09748 - <PERSON>", "S/N 1296840 - <PERSON>", "S/N S545722 - <PERSON>", "S/N 1143253 - <PERSON>", "S/N 1143618 - <PERSON>", "Thermo King", "S/N 1196248 - <PERSON>", "S/N Z412284 - <PERSON>", "S/N G367219 - <PERSON>", "S/N FA50095 - <PERSON>", "S/N KE44716 - <PERSON>"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "Warehouse_Office_ID", "dataType": "int", "description": "An integer identifying the office associated with the warehouse, facilitating organizational tracking of inventory across different office locations.", "availableValues": ["419", "421"], "is_unstructured": false}, {"name": "Warehouse_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the name of the office linked to the warehouse, providing context for the inventory's administrative oversight.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Warehouse_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "An nvarchar field containing the name of the item stored in the warehouse, generally unique across related inventory records.", "subsetOfAvailableValues": ["22421853", "22463368", "23424922", "23973969", "23973977", "and 2847 more..."], "is_unstructured": false}, {"name": "Warehouse_ItemID", "dataType": "int", "description": "An integer uniquely identifying items in the inventory, providing a consistent reference for inventory management.", "subsetOfAvailableValues": ["2825057", "2825095", "2825307", "2826815", "2827199", "and 2848 more..."], "is_unstructured": false}, {"name": "Warehouse_MinQty", "dataType": "decimal", "description": "A decimal value representing the minimum quantity of an item required in inventory, used for stock management and replenishment decisions.", "is_unstructured": false}, {"name": "Warehouse_MaxQty", "dataType": "decimal", "description": "A decimal value indicating the maximum permissible quantity of an item in inventory, helping to prevent overstocking.", "is_unstructured": false}, {"name": "Warehouse_ItemQty", "dataType": "decimal", "description": "A decimal field representing the current quantity of the item in the warehouse, which may reflect real-time inventory adjustments.", "is_unstructured": false}, {"name": "Warehouse_Inventory_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the current status of the inventory item, crucial for tracking usability and managing inventory lifecycle.", "availableValues": ["Inactive", "Active"], "totalDistinctValueCount": 2, "is_unstructured": false}], "relationships": [{"child_table": "WarehouseInventory", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[Warehouse.ItemID]"}], "relationship_type": "one-to-many"}]}