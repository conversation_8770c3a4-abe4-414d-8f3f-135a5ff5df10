{"tableName": "PayrollReport", "description": "The PayrollReport table records comprehensive payroll data for each employee work entry, including dates, hours (regular and overtime), job details, company, location, and invoicing information. It links to employee, job, company, and location tables via foreign keys. Many columns may contain missing values, so queries should account for nulls when analyzing hours, billing, or assignment details.", "fields": [{"name": "PayrollReport_ID", "dataType": "int", "description": "A unique integer identifier for each payroll report record, serving as the primary key for distinct entry reference.", "subsetOfAvailableValues": ["23", "46", "215", "687", "710", "and 5815 more..."], "is_unstructured": false}, {"name": "PayrollReport_EmployeeID", "dataType": "int", "description": "An integer that associates the payroll record with a specific employee, linking to detailed employee information in the EmployeeDetail table.", "subsetOfAvailableValues": ["108014", "108015", "108016", "108017", "108018", "and 12 more..."], "is_unstructured": false}, {"name": "PayrollReport_EmployeeNumber", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the employee's unique organizational number, with a considerable percentage of records potentially lacking this information.", "subsetOfAvailableValues": ["103", "107", "109", "110", "111", "and 4 more..."], "is_unstructured": false}, {"name": "PayrollReport_EmployeeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the full name of the employee, used for identification and reporting within payroll records.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "PayrollReport_WeekDayName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the day of the week when work was performed, facilitating chronological payroll tracking.", "availableValues": ["Tuesday", "Thursday", "Wednesday", "Friday", "Monday", "Saturday", "Sunday"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "PayrollReport_WorkDate", "dataType": "date", "description": "A date indicating the specific day work was performed, essential for organizing payroll data chronologically.", "is_unstructured": false}, {"name": "PayrollReport_StartTimeTime", "dataType": "time", "description": "A time field marking the employee’s shift start time, crucial for calculating total hours worked.", "is_unstructured": false}, {"name": "PayrollReport_EndTimeTime", "dataType": "time", "description": "A time field indicating the end of the employee’s shift, used together with StartTimeTime to compute total hours worked.", "is_unstructured": false}, {"name": "PayrollReport_TimeTypeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the nature of time worked, with potential values including Onsite Time, Travel, and Meeting.", "availableValues": ["Onsite Time", "Travel", "Shop Time", "Other", "Meeting"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "PayrollReport_EntryHours", "dataType": "decimal", "description": "A decimal value representing the total hours logged for the payroll report, typically under 100 hours.", "is_unstructured": false}, {"name": "PayrollReport_RegularHours", "dataType": "decimal", "description": "A decimal value indicating the number of regular hours worked, generally under 100 hours.", "is_unstructured": false}, {"name": "PayrollReport_TimeAndHalfHours", "dataType": "decimal", "description": "A decimal indicating hours worked at time-and-a-half pay rate, typically under 100 hours.", "is_unstructured": false}, {"name": "PayrollReport_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string defining the type of job related to the payroll record, with a notable percentage of records potentially missing this information.", "availableValues": ["Service Request-COM", "PM Fixed Bid - GEN", "Service Request - GEN", "PMA-COM", "PMA - GEN", "PM Fixed Bid-COM", "Project - GEN", "Warranty Request-COM", "Warranty Request - GEN", "Asset maintenance", "Call Back-COM", "Rental asset maintenance", "Equipment Sale-COM", "Courtesy Call-COM", "Call Back - GEN", "Courtesy Call - GEN", "Specialty Services-COM", "Project - COM", "Rental Contract", "Rental Incidentals", "Remote Monitoring - GEN", "Equipment Sale - GEN", "Part Sale - GEN", "Part Sale-COM"], "totalDistinctValueCount": 24, "is_unstructured": false}, {"name": "PayrollReport_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string identifying the specific job associated with the payroll report, often unique within small groups of records.", "subsetOfAvailableValues": ["WR-3679665", "ESP-3703966", "SR-4029710", "PFB-3679984", "SS-3859200", "and 2493 more..."], "totalDistinctValueCount": 2498, "is_unstructured": false}, {"name": "PayrollReport_JobStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string denoting the current status of the job, with possible values including Completed, In Progress, and Cancelled.", "availableValues": ["Completed", "In Progress", "Cancelled", "Scheduled", "Waiting on Parts", "Ready to Schedule"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "PayrollReport_JobId", "dataType": "int", "description": "An integer linking the payroll report to a specific job in the JobsBacklog or JobsSummary tables, with possible missing values.", "subsetOfAvailableValues": ["1820877", "1821189", "1821278", "1821280", "1821344", "and 2493 more..."], "is_unstructured": false}, {"name": "PayrollReport_BilledHourSet", "dataType": "bit", "description": "A bit value indicating whether the billed hour set has been applied, important for tracking billing status.", "is_unstructured": false}, {"name": "PayrollReport_CompanyID", "dataType": "int", "description": "An integer identifying the associated company for the payroll record, linking to the CustomerInformation table with potential for missing values.", "subsetOfAvailableValues": ["379954", "380079", "380204", "380230", "380287", "and 583 more..."], "is_unstructured": false}, {"name": "PayrollReport_CompanyAccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the company's account number related to the payroll record, often unique but may have missing entries.", "subsetOfAvailableValues": ["GEN10792", "AIR00001", "CIT10642", "SOB11224", "PCS10285", "and 583 more..."], "totalDistinctValueCount": 588, "is_unstructured": false}, {"name": "PayrollReport_CompanyName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the company's name connected to the payroll record, with some records possibly lacking this information.", "subsetOfAvailableValues": ["Genrep Ltd.", "Air Unlimited Inc.", "City of Winnipeg", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "PCS Inc. (Nutrien)", "and 582 more..."], "totalDistinctValueCount": 587, "is_unstructured": false}, {"name": "PayrollReport_LocationID", "dataType": "int", "description": "An integer that identifies the location tied to the payroll record, linking to the LocationInformation table, with possible missing values.", "subsetOfAvailableValues": ["658913", "658951", "658979", "659189", "659190", "and 826 more..."], "is_unstructured": false}, {"name": "PayrollReport_LocationName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the name of the work location, with some records potentially lacking this information.", "subsetOfAvailableValues": ["Winnipeg-Head Office", "City of Winnipeg - Fleet Management Agency", "Anue Water / Saskatoon Lift Station", "GFL Environmental", "Superstore RCSS #1583", "and 782 more..."], "totalDistinctValueCount": 787, "is_unstructured": false}, {"name": "PayrollReport_LocationStreet", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the street address of the work location, which may have missing values in some records.", "subsetOfAvailableValues": ["2116 Logan Ave", "Jasper Rd", "", "335 Mazenod Rd", "30 <PERSON>", "and 783 more..."], "totalDistinctValueCount": 788, "is_unstructured": false}, {"name": "PayrollReport_LocationAdditionalAddressInfo", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string offering additional details for the location’s address, with a significant percentage of records possibly lacking this information.", "subsetOfAvailableValues": ["", "Dept. of Consumer Protection and Gov. Serv., Asset Management, Operations, D2", "PO Box 187", "Dept. of Consumer Protection and Gov. Services, Asset Management, Operations, D2", "20 km west of Carman, on the NE 1/4, Section 36", "and 126 more..."], "totalDistinctValueCount": 131, "is_unstructured": false}, {"name": "PayrollReport_LocationCity", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the city of the work location, with a chance of missing values in some records.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "Portage La Prairie", "<PERSON>", "and 200 more..."], "totalDistinctValueCount": 205, "is_unstructured": false}, {"name": "PayrollReport_LocationState", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical field indicating the state where the payroll report is generated, with potential missing values indicating incomplete data.", "availableValues": ["MB", "SK", "ON", "", "YT", "QC", "IL", "AB"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "PayrollReport_LocationZip", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the postal or zip code for the work location, with many records missing this information, serving as a potential geographic indicator.", "subsetOfAvailableValues": ["R2R 0J2", "", "R2J 3S8", "R0E 0C0", "S6H 7K8", "and 677 more..."], "totalDistinctValueCount": 682, "is_unstructured": false}, {"name": "PayrollReport_LastInvoiceDate", "dataType": "datetime", "description": "A datetime field recording the date and time of the last associated invoice, with some records potentially lacking this data.", "is_unstructured": false}, {"name": "PayrollReport_LastInvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar capturing the name or identifier of the last linked invoice, which may be unique within small groups of records and has potential for missing entries.", "subsetOfAvailableValues": ["4049-1", "8843-1", "8532-1", "3316-1", "6147-1", "and 2427 more..."], "totalDistinctValueCount": 2432, "is_unstructured": false}], "relationships": [{"child_table": "PayrollReport", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[PayrollReport.CompanyID]"}], "relationship_type": "one-to-many"}, {"child_table": "PayrollReport", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.Account<PERSON><PERSON>ber]", "child_column": "[PayrollReport.CompanyAccountNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "PayrollReport", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[PayrollReport.EmployeeID]"}], "relationship_type": "one-to-many"}, {"child_table": "PayrollReport", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[PayrollReport.JobId]"}], "relationship_type": "one-to-many"}, {"child_table": "PayrollReport", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[PayrollReport.JobId]"}], "relationship_type": "one-to-many"}, {"child_table": "PayrollReport", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[PayrollReport.LocationID]"}], "relationship_type": "one-to-many"}]}