{"tableName": "EquipmentAttributes", "description": "EquipmentAttributes contains detailed information for each equipment asset, including unique ID, name, serial number, and item number, along with compressor (CFM, HP, voltage) and generator (fuel type, kilowatt, voltage) specifications when available. Most equipment will have only relevant attributes populated; many fields may be null. Use this table to query equipment details for asset tracking, technical assessments, or inventory management.", "fields": [{"name": "Equipment_ID", "dataType": "int", "description": "A unique integer identifier for each piece of equipment, essential for record distinction and future data relationships.", "subsetOfAvailableValues": ["950464", "950470", "950539", "950544", "950549", "and 409 more..."], "is_unstructured": false}, {"name": "Equipment_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A unique string representing the equipment's name or model, providing a human-readable identifier, though may be missing in about 1% of records.", "subsetOfAvailableValues": ["2475N5-V", "30.0EK-15R/9336M", "D18IN", "SD100", "SD350", "and 324 more..."], "is_unstructured": false}, {"name": "Equipment_SerialNumber", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A unique string that serves as the equipment's serial number, critical for tracking and warranty, but may be absent for around 1% of entries.", "subsetOfAvailableValues": ["1003", "1005", "1029", "0.00", "Unknown", "and 400 more..."], "is_unstructured": false}, {"name": "Equipment_ItemNumber", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string that serves as an item number, providing categorization for equipment, though it averages unique identification for groups of 2 records and may be missing in about 1% of cases.", "subsetOfAvailableValues": ["23231806", "Generator - Generac - Gas", "Generator - Generac - Diesel", "Generator - Kohler - Diesel", "Generator - Cummins - Diesel", "and 193 more..."], "is_unstructured": false}, {"name": "Compressor_CFM", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string indicating the cubic feet per minute (CFM) airflow capacity of compressors, available for less than 50% of records and not strictly unique, averaging uniqueness for groups of 4 records.", "subsetOfAvailableValues": ["11", "14", "24", "25", "28", "and 92 more..."], "is_unstructured": false}, {"name": "Compressor_HP", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the horsepower (HP) rating of the compressor, present in less than 50% of records and averaging uniqueness for groups of 15 records.", "subsetOfAvailableValues": ["2", "3", "5", "10", "13", "and 20 more..."], "is_unstructured": false}, {"name": "Compressor_Voltage", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string denoting the voltage of the compressor's electrical supply, which may contain categorical values, and is present in less than 50% of records, averaging uniqueness for groups of 46 records.", "subsetOfAvailableValues": ["110", "115", "200", "230", "460", "and 3 more..."], "is_unstructured": false}, {"name": "Generator_Fuel", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string specifying the fuel type used by the generator, with categorical values and present in less than 50% of records.", "subsetOfAvailableValues": ["Diesel", "Gas", "Natural Gas", "NG", "Gas/Propane", "and 1 more..."], "is_unstructured": false}, {"name": "Generator_<PERSON>", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the kilowatt (KW) rating of the generator, indicating its power output capacity, available for less than 50% of records and averaging uniqueness for groups of 9 records.", "subsetOfAvailableValues": ["15", "20", "22", "25", "30", "and 40 more..."], "is_unstructured": false}, {"name": "Generator_Voltage", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string indicating the voltage of the generator's electrical supply, which may contain categorical values and is present in less than 50% of records.", "subsetOfAvailableValues": ["208", "240", "416", "480", "600", "and 1 more..."], "is_unstructured": false}]}