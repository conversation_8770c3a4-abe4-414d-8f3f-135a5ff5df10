{"tableName": "Opportunities", "description": "The Opportunities table records detailed information on individual sales opportunities, including identifying data, office location, stage, type, forecast category, lead source, and responsible salesperson. It tracks key financial metrics (amount, expected revenue, probability) and critical dates (creation, modification, close). Each record is uniquely identified and linked to a customer, supporting analysis of the sales pipeline, forecasting, lead effectiveness, and sales team performance for Air Unlimited’s operations.", "fields": [{"name": "Opportunity_ID", "dataType": "int", "description": "A unique integer identifier for each opportunity record, crucial for maintaining data integrity and referencing specific opportunities within the database.", "subsetOfAvailableValues": ["249183", "249186", "249197", "249403", "249483", "and 735 more..."], "is_unstructured": false}, {"name": "Opportunity_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A distinct variable character string that names each opportunity, essential for identifying and differentiating between various opportunities in the sales pipeline.", "subsetOfAvailableValues": ["<PERSON>'s Ice Age Mechanical", "Grass River Colony", "LabelX", "1 Year Trial PM", "Air Audit", "and 708 more..."], "totalDistinctValueCount": 713, "is_unstructured": false}, {"name": "Opportunity_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the office (e.g., 'MB Office' or 'SK Office') linked to the opportunity, aiding in geographical sales analysis.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Opportunity_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed variable character string that describes the specifics of the opportunity, providing context and insights unique to each record.", "subsetOfAvailableValues": ["", "2475N7.5-V", "TEST", "2475N7.5-V 230/1", "AHT 75UP & A3071X1", "and 581 more..."], "totalDistinctValueCount": 586, "is_unstructured": false}, {"name": "Opportunity_NextSteps", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string outlining the actionable steps planned for each opportunity, which may be absent in a small percentage of records.", "subsetOfAvailableValues": ["", "Follow up ", "A&B Mech awarded project. ", "Cust approved Comp and Dryer quotes. Please order when account has been opened.", "Follow up on quote", "and 155 more..."], "totalDistinctValueCount": 160, "is_unstructured": false}, {"name": "Opportunity_Salesperson", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string denoting the name of the salesperson responsible for the opportunity, facilitating accountability and performance tracking.", "availableValues": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun"], "totalDistinctValueCount": 12, "is_unstructured": false}, {"name": "Opportunity_Stage", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that indicates the current phase of the opportunity in the sales process, helping to assess progress and status.", "availableValues": ["Opportunity-Abandoned", "Opportunity won", "Quoted", "Opportunity- Lost", "Investigating/data gathering"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "Opportunity_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string categorizing the opportunity type, useful for analyzing sales strategies based on various opportunity classifications.", "availableValues": ["Compressors", "PMA-New", "PMA-Rene<PERSON>", "Dryers", "Rentals", "Generators", "Audit", "Service repair", "Parts sale", "Remote monitoring", "Installation"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "Opportunity_ForecastCategory", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that classifies the opportunity into defined forecast categories, assisting in sales forecasting and planning.", "availableValues": ["1-30 day close", "46-60 day close", "61-90 day close", "31-45 day close", "Budgetary", "Emergency"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "Opportunity_LeadSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string describing the origin of the lead for the opportunity, useful for evaluating the effectiveness of lead generation methods.", "availableValues": ["General Inquiry", "Service department identified lead", "Cold Call", "Bid and Spec (Tender)", "Service technician identified lead", "Referral from related companies", "Referral from supplier/contractor", "Referral from customer", "Website lead", "Marketing initiative"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "Opportunity_CreatedDate", "dataType": "datetime", "description": "A datetime field capturing the creation timestamp of the opportunity, important for monitoring the opportunity's age and lifecycle.", "is_unstructured": false}, {"name": "Opportunity_LastModifiedDate", "dataType": "datetime", "description": "A datetime field recording the last modification timestamp of the opportunity, providing insight into data recency and updates.", "is_unstructured": false}, {"name": "Opportunity_CloseDate", "dataType": "datetime", "description": "A datetime field that specifies the expected or actual closure date of the opportunity, crucial for sales cycle tracking and forecasting.", "is_unstructured": false}, {"name": "Opportunity_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the user or system responsible for creating the opportunity record, aiding in data ownership identification.", "availableValues": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun"], "totalDistinctValueCount": 15, "is_unstructured": false}, {"name": "Opportunity_Probability", "dataType": "int", "description": "An integer reflecting the likelihood of successfully closing the opportunity, typically represented as a percentage, which may be missing in some records.", "subsetOfAvailableValues": ["0", "5", "10", "15", "20", "and 15 more..."], "is_unstructured": false}, {"name": "Opportunity_Amount", "dataType": "decimal", "description": "A decimal field that denotes the total monetary value associated with the opportunity, critical for revenue forecasting and financial analysis.", "is_unstructured": false}, {"name": "Opportunity_ExpectedRevenue", "dataType": "decimal", "description": "A decimal field estimating the potential revenue from the opportunity, assisting in financial planning and revenue projections.", "is_unstructured": false}, {"name": "Opportunity_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string containing the customer's name associated with the opportunity, unique to each record for effective customer relationship management.", "subsetOfAvailableValues": ["PCS Inc. (Nutrien)", "Bourgault Industries Ltd.", "Calm Air International LP", "Cobra Construction (Enterprises)", "EDA Mechanical Services Ltd.", "and 450 more..."], "totalDistinctValueCount": 455, "is_unstructured": false}]}