{"tableName": "CustomerInformation", "description": "The CustomerInformation table contains detailed records for each customer, including identifiers, contact details, account status, billing preferences, balances, credit terms, and assigned salesperson. It supports customer management, billing, service scheduling, and segmentation for sales and financial processes. Most fields are unique or business-critical, with some optional or sparsely populated attributes. This standalone table is central to identifying and managing all customer-related activities.", "fields": [{"name": "Customer_ID", "dataType": "int", "description": "A unique integer identifier for each customer record, crucial for referencing and maintaining data integrity.", "subsetOfAvailableValues": ["379955", "379956", "379957", "379958", "379959", "and 2196 more..."], "is_unstructured": false}, {"name": "Customer_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string representing the customer's name, facilitating straightforward identification.", "subsetOfAvailableValues": ["<PERSON><PERSON><PERSON>", "Black & McDonald Ltd.", "D.A. Lincoln Pump Service", "Jubilee <PERSON>", "Masterfeeds", "and 2179 more..."], "totalDistinctValueCount": 2184, "is_unstructured": false}, {"name": "Customer_AccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string used for billing and account management, representing the customer's account.", "subsetOfAvailableValues": ["ACO00041", "ACM10497", "ACE10008", "ACC10496", "ACC10495", "and 2196 more..."], "totalDistinctValueCount": 2201, "is_unstructured": false}, {"name": "Customer_OfficeID", "dataType": "int", "description": "An integer identifier for the office linked to the customer, with a small percentage of records lacking a value.", "availableValues": ["419", "420", "421"], "is_unstructured": false}, {"name": "Customer_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string denoting the name of the office associated with the customer, with occasional missing values.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "Customer_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that categorizes the customer’s relationship status, with possible values like 'Customer', 'Prospect', and 'Internal'.", "availableValues": ["Customer", "Prospect", "Internal"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "Customer_CreditStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the customer's credit standing, with possible statuses such as 'Open - PO Required' and 'Credit Hold'.", "availableValues": ["Open - PO Required", "Prepaid", "Credit Hold", "Open Account", ""], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "Customer_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that describes the customer's classification, with less than 50% of records containing this information.", "availableValues": ["Standard", "COD"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Customer_Currency", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A variable character string specifying the currency for transactions, predominantly 'Canadian Dollars' for nearly all records.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "Customer_Reseller", "dataType": "<PERSON><PERSON><PERSON>", "description": "A constant variable character string indicating the customer's status as a non-reseller, typically recorded as 'No'.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Customer_WarrantyCustomer", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating warranty status, predominantly 'No' for the vast majority of records.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Customer_Active", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string that shows whether the customer is currently active, with 'Yes' for most records.", "availableValues": ["Yes", "No"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Customer_NextServiceDate", "dataType": "smalldatetime", "description": "A small datetime field indicating the next scheduled service date for the customer, available for less than 50% of records.", "is_unstructured": false}, {"name": "Customer_Phone", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string for the customer's phone number, with some records missing this information.", "subsetOfAvailableValues": ["", "************", "************", "************", "************", "and 1962 more..."], "totalDistinctValueCount": 1967, "is_unstructured": false}, {"name": "Customer_Email", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string for the customer's email address, with a significant number of records lacking this field.", "subsetOfAvailableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 1239 more..."], "totalDistinctValueCount": 1244, "is_unstructured": false}, {"name": "Customer_Website", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string providing the URL of the customer's website, with data present for a small fraction of records.", "availableValues": ["https://debden.ca/", "https://www.biochambers.com/", "https://www.flyingdust.net/", "http://norquaycabinets.com/", "<EMAIL>", "<EMAIL>", "https://www.transgas.com/", "https://www.supervaluehomeservices.com/contact/", "https://maplescc.ca/", "https://ndcconstruction.ca/", "https://www.backroadsspirits.ca/", "http://www.makkinga.ca/", "", "https://www.sherbrookecommunitycentre.ca/", "https://www.mfenterprises.ca/", "<EMAIL>", "<EMAIL>", "www.dspdiect.ca", "<EMAIL>"], "totalDistinctValueCount": 19, "is_unstructured": false}, {"name": "Customer_InvoicePreference", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the customer's preferred method for receiving invoices, with a majority favoring 'Email'.", "availableValues": ["Email", "Mail"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Customer_Fax", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for the customer's fax number, which is missing in a substantial number of records.", "subsetOfAvailableValues": ["", "204-945-7131", "204-785-5019", "(204) 325-8630", "(204) 832-4757", "and 855 more..."], "totalDistinctValueCount": 860, "is_unstructured": false}, {"name": "Customer_Address1", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string representing the primary address for the customer, with minimal missing values.", "subsetOfAvailableValues": ["", "2116 Logan Ave", "PO Box 100", "PO Box 10", "PO Box 400", "and 1969 more..."], "totalDistinctValueCount": 1974, "is_unstructured": false}, {"name": "Customer_Address2", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for additional address details, available for less than 50% of records.", "subsetOfAvailableValues": ["", "Attn: Accounts Payable", "Department of Central Services, Asset Management - Central Capital Division, Operations - District 1 Facilities", "74065 Blackdale Rd", "Bow Valley Square 2", "and 228 more..."], "totalDistinctValueCount": 233, "is_unstructured": false}, {"name": "Customer_City", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string indicating the city of the customer's address, with some records missing this information.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "BRANDON", "Calgary", "and 457 more..."], "totalDistinctValueCount": 462, "is_unstructured": false}, {"name": "Customer_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string for the state linked to the customer's address, with occasional missing values.", "subsetOfAvailableValues": ["MB", "SK", "ON", "AB", "BC", "and 32 more..."], "totalDistinctValueCount": 37, "is_unstructured": false}, {"name": "Customer_Zip", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique variable character string for the customer's zip code, with minimal missing values.", "subsetOfAvailableValues": ["", "R3C 2E6", "R2R 0J2", "R0G 0B0", "R0E 0C0", "and 1635 more..."], "totalDistinctValueCount": 1640, "is_unstructured": false}, {"name": "Customer_Country", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string representing the customer's country, with nearly all records showing 'US'.", "availableValues": ["US"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Customer_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "A variable character string categorizing the customer, available for a small percentage of records.", "availableValues": ["Standard", "COD"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Customer_DateCreated", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field representing the creation date of the customer record, available for less than half of the records.", "subsetOfAvailableValues": ["04/15/2024", "08/20/2024", "08/28/2024", "08/10/2024", "08/22/2024", "and 192 more..."], "totalDistinctValueCount": 197, "is_unstructured": false}, {"name": "Customer_Balance", "dataType": "decimal", "description": "A decimal field indicating the customer's balance, which can reflect both credits and debits.", "is_unstructured": false}, {"name": "Customer_Terms", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field defining the payment terms for the customer account, with various categorical options.", "availableValues": ["N30", "COD", "Prepaid", "N60", "N45", "N90"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "Customer_StatementCycle", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field specifying how often customer statements are generated, with some records missing this information.", "availableValues": ["Monthly", "Weekly", ""], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "Customer_TaxID", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field containing the customer's tax identification number, available for a small percentage of records.", "availableValues": ["", "PST", "MB PST # 077094-1", "0445460"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "Customer_TaxExemptCertificateNum", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field holding the tax exemption certificate number for eligible customers, present in less than half of records.", "subsetOfAvailableValues": ["1322759", "1462688", "1557081", "3905160", "5642343", "and 511 more..."], "totalDistinctValueCount": 516, "is_unstructured": false}, {"name": "Customer_SalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field that identifies the salesperson assigned to the customer account, with some records indicating 'Not Assigned'.", "availableValues": ["Not Assigned", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 20, "is_unstructured": false}, {"name": "Customer_SalesPersonId", "dataType": "int", "description": "An integer field storing the unique identifier for the assigned salesperson, which may include negative values.", "subsetOfAvailableValues": ["107985", "107994", "107996", "107998", "107999", "and 15 more..."], "is_unstructured": false}, {"name": "Customer_Contacts", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique varchar field listing the contact person for the customer account, ensuring distinct contact references.", "subsetOfAvailableValues": ["Accounts Payable", "General Inbox", "Unknown Unknown", "", "General Inquiries", "and 1500 more..."], "totalDistinctValueCount": 1505, "is_unstructured": false}, {"name": "Customer_AvgDaysToPay", "dataType": "decimal", "description": "A decimal field indicating the average days taken by the customer to pay invoices, with limited data availability.", "is_unstructured": false}, {"name": "Customer_CreditLimit", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field specifying the customer’s credit limit, which may not be present for all records.", "subsetOfAvailableValues": ["$30,000 Limit 30000.00", "Prepaid Limit 1000000.00", "$5,000 Limit 5000.00", "$10,000 Limit 10000.00", "$90,000 Limit 90000.00", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "Customer_RequiresPO", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating whether the customer mandates a purchase order for transactions, with a majority showing 'No'.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}]}