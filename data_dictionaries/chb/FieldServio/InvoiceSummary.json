{"tableName": "InvoiceSummary", "description": "The InvoiceSummary table contains detailed records of invoices, capturing identifiers, customer and contract associations, billing and shipping details, job information, financial amounts, payment statuses, and relevant dates. Each invoice is uniquely identified and may link to related jobs, contracts, and customers, supporting comprehensive financial, sales, service, and rental reporting for Air Unlimited’s business operations.", "fields": [{"name": "InvoiceSummary_InvoiceID", "dataType": "int", "description": "A unique integer identifier for each invoice record, ensuring distinct referencing within the database.", "subsetOfAvailableValues": ["2580584", "2583263", "2583286", "2583309", "2638709", "and 9989 more..."], "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A human-readable string that uniquely identifies each invoice for better tracking and management.", "subsetOfAvailableValues": ["EFT 09-22-2023", "EFT", "EFT 190025719", "EFT NO REMIT", "09-22-2023", "and 9956 more..."], "totalDistinctValueCount": 9961, "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the invoice as either 'Invoice', 'Deposit', or 'Credit', aiding in classification for reporting.", "availableValues": ["Invoice", "<PERSON><PERSON><PERSON><PERSON>", "Credit"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InvoiceSummary_JobID", "dataType": "int", "description": "An integer linking the invoice to a specific job, though often missing and not unique across all records.", "subsetOfAvailableValues": ["1821041", "1821292", "1822896", "1827708", "1830152", "and 4106 more..."], "is_unstructured": false}, {"name": "InvoiceSummary_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the job associated with the invoice, commonly shared among multiple records.", "subsetOfAvailableValues": ["WR-3679772", "ESP-3715712", "ESP-3704176", "ESP-3717535", "ESP-3699163", "and 4106 more..."], "totalDistinctValueCount": 4111, "is_unstructured": false}, {"name": "InvoiceSummary_ContractID", "dataType": "int", "description": "An integer representing the unique identifier of the contract linked to the invoice, with limited presence across records.", "subsetOfAvailableValues": ["192362", "192363", "192392", "192395", "192401", "and 561 more..."], "is_unstructured": false}, {"name": "InvoiceSummary_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the specific contract number associated with the invoice, often shared among several records.", "subsetOfAvailableValues": ["PMA207647", "PMA201721", "PMA220061", "PFB74994", "PMA75686", "and 561 more..."], "totalDistinctValueCount": 566, "is_unstructured": false}, {"name": "InvoiceSummary_ContractName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the contract linked to the invoice, typically not unique and present in fewer records.", "subsetOfAvailableValues": ["ASH10535-PMA-GEN-2024-2029", "EXC10810-PMA-COM-10/24", "The Colonnade (Towers) 200REOZJF Monthly PM 2025-2030", "CIT10642-PMA-GEN-02/24", "McKesson IDLC1250 Monthly PM + Connectivity", "and 530 more..."], "totalDistinctValueCount": 535, "is_unstructured": false}, {"name": "InvoiceSummary_CustomerID", "dataType": "int", "description": "An integer that identifies the customer related to the invoice, generally unique for groups of records.", "subsetOfAvailableValues": ["379954", "380124", "380204", "380230", "380460", "and 1153 more..."], "is_unstructured": false}, {"name": "InvoiceSummary_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the customer associated with the invoice, often not unique across records.", "subsetOfAvailableValues": ["BPL Sales Ltd.", "Genrep Ltd.", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "EDA Mechanical Services Ltd.", "City of Winnipeg", "and 1148 more..."], "totalDistinctValueCount": 1153, "is_unstructured": false}, {"name": "InvoiceSummary_BillToID", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string identifying the billing entity for the invoice, usually non-unique and commonly shared among records.", "subsetOfAvailableValues": ["379954", "380124", "380204", "380230", "380460", "and 1153 more..."], "totalDistinctValueCount": 1158, "is_unstructured": false}, {"name": "InvoiceSummary_BillToName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that provides the name of the entity billed for the invoice, offering context for billing.", "subsetOfAvailableValues": ["BPL Sales Ltd.", "Genrep Ltd.", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "EDA Mechanical Services Ltd.", "City of Winnipeg", "and 1148 more..."], "totalDistinctValueCount": 1153, "is_unstructured": false}, {"name": "InvoiceSummary_ShipToName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the entity receiving the shipment related to the invoice, generally unique.", "subsetOfAvailableValues": ["BPL Sales Ltd.", "<PERSON>", "The J. Hansen HVAC Group", "Winnipeg-Head Office", "G&K Electric Inc.", "and 1704 more..."], "totalDistinctValueCount": 1709, "is_unstructured": false}, {"name": "InvoiceSummary_ShipToCity", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the city for the shipment related to the invoice, with moderate presence across records.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "<PERSON>", "Portage La Prairie", "and 445 more..."], "totalDistinctValueCount": 450, "is_unstructured": false}, {"name": "InvoiceSummary_ShipToState", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string denoting the state where the invoice's shipment is directed, with some records missing this information.", "availableValues": ["MB", "SK", "ON", "", "AB", "TX", "YT", "KS", "NS", "NB", "BC", "QC", "NU", "ND", "WI", "FL", "OH", "IL", "--", "PA", "NY"], "totalDistinctValueCount": 21, "is_unstructured": false}, {"name": "InvoiceSummary_ShipToZip", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string for the ZIP code of the shipping address linked to the invoice, with some records lacking this data.", "subsetOfAvailableValues": ["R2R 0J2", "", "R2R 0J6", "R3C 4H8", "R0H 1E0", "and 1224 more..."], "totalDistinctValueCount": 1229, "is_unstructured": false}, {"name": "InvoiceSummary_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string describing the type of job associated with the invoice, with specific values unspecified.", "subsetOfAvailableValues": ["Service Request-COM", "PMA-COM", "Part Sale-COM", "PMA - GEN", "External", "and 24 more..."], "totalDistinctValueCount": 29, "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the current status of the invoice, helping to track its lifecycle through various stages.", "availableValues": ["Paid in Full", "In Development", "<PERSON><PERSON>", "Voided", "Ready for Accounting Review", "Partial Payment Received", "Approved", "Waiting For Customer PO Number"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InvoiceSummary_SalesResource", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string identifying the sales resource associated with the invoice, missing in a significant percentage of records.", "availableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 22, "is_unstructured": false}, {"name": "InvoiceSummary_VoidedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string recording the identity of the user who voided the invoice, applicable for a small number of records.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 20, "is_unstructured": false}, {"name": "InvoiceSummary_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the user who created the invoice record, with some records lacking this information.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 22 more..."], "totalDistinctValueCount": 27, "is_unstructured": false}, {"name": "InvoiceSummary_SentBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string identifying the user who sent the invoice, with a notable percentage of records missing this field.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "InvoiceSummary_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string denoting the office responsible for handling the invoice, aiding in operational tracking.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InvoiceSummary_Currency", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string specifying the currency of the invoice, predominantly Canadian Dollars, crucial for financial reporting.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "InvoiceSummary_CurrencySubTotal", "dataType": "decimal", "description": "A decimal field representing the subtotal amount of the invoice before any taxes or additional charges.", "is_unstructured": false}, {"name": "InvoiceSummary_CurrencyTax", "dataType": "decimal", "description": "A decimal value indicating the total tax applied to the invoice based on applicable tax rates.", "is_unstructured": false}, {"name": "InvoiceSummary_CurrencyTotalAmt", "dataType": "decimal", "description": "A decimal value representing the total amount of the invoice, including applicable taxes.", "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical field specifying the source of the invoice, aiding in tracking its origin.", "availableValues": ["Job", "Contract", "Import", "External", "Customer", "RMA", "Customer <PERSON><PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceAmount", "dataType": "decimal", "description": "A decimal value representing the base amount of the invoice before tax, reflecting services or products provided.", "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceAmountDue", "dataType": "decimal", "description": "A decimal indicating the amount still owed by the customer for the invoice, accounting for partial payments.", "is_unstructured": false}, {"name": "InvoiceSummary_PONumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the Purchase Order number associated with the invoice, with some records possibly missing this info.", "subsetOfAvailableValues": ["50155", "227133", "403699", "1030044", "8748867", "and 2997 more..."], "totalDistinctValueCount": 3002, "is_unstructured": false}, {"name": "InvoiceSummary_PaymentTerms", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string outlining the agreed terms of payment for the invoice, typically expressed in varchar format.", "availableValues": ["Net 30 Days", "Payment required at time of pick up", "N30", "Payment required at time of order", "", "Net 60 Days", "COD", "Prepaid", "Credit Card - Credit Card Payment", "N30 - Net 30 Days", "N45 - Net 45 Days", "Net 45 Days", "N90", "Prepaid - Payment required at time of order", "COD - Payment required at time of pick up", "N60"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceDate", "dataType": "datetime", "description": "A datetime field recording the date when the invoice was generated, aiding in lifecycle tracking.", "is_unstructured": false}, {"name": "InvoiceSummary_SentDate", "dataType": "datetime", "description": "A datetime indicating the date the invoice was sent to the customer, with some records missing this information.", "is_unstructured": false}, {"name": "InvoiceSummary_SentTransactionDate", "dataType": "datetime", "description": "A datetime field recording the date of the transaction when the invoice was sent, with potential missing values.", "is_unstructured": false}, {"name": "InvoiceSummary_VoidedDate", "dataType": "datetime", "description": "A datetime indicating the date the invoice was voided, applicable to a small percentage of records.", "is_unstructured": false}, {"name": "InvoiceSummary_VoidedTransactionDate", "dataType": "datetime", "description": "A datetime field noting the transaction date of the voided invoice, with limited records having this information.", "is_unstructured": false}, {"name": "InvoiceSummary_DueDate", "dataType": "datetime", "description": "A datetime field specifying the payment due date for the invoice amount.", "is_unstructured": false}, {"name": "InvoiceSummary_PaidDate", "dataType": "datetime", "description": "A datetime indicating the date when the invoice was paid, with some records missing this data.", "is_unstructured": false}, {"name": "InvoiceSummary_PaidStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the payment status of the invoice, with common values including 'Paid' and 'Not Paid'.", "availableValues": ["Paid", "Not Paid"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceSummary_DeferredRevenue", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating whether the revenue from the invoice is deferred, with a notable percentage showing 'Not Deferred'.", "availableValues": ["Not Deferred", "Deferred"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceSummary_Retainage", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field consistently indicating 'Not Retainage', signifying no retainage associated with the invoice.", "availableValues": ["Not Retainage"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "InvoiceSummary_TaxSchedule", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field outlining the tax schedule applied to the invoice, with some records potentially missing this field.", "availableValues": ["MB - Sales Tax - GST 5% + PST 7%", "SK - Sales Tax - GST 5% + PST 6%", "MB - Sales Tax - GST 5%", "Both taxes exempt", "SK - Sales Tax - GST 5%", "ON - Sales Tax - HST 13%", "MB - Sales Tax - PST 7%", "USD MB - Sales Tax - GST 5%", "AB - Sales Tax - GST 5%", "SK - Sales Tax - PST 6%", "USD SK - Sales Tax - GST 5%", "USD GST Only", "GST Only", "YT - Sales Tax - GST 5%", "USD ON - Sales Tax - HST 13%", "BC - Sales Tax - GST 5%", "USD MB - Sales Tax - GST 5% + PST 7%", "NB - Sales Tax - HST 15%", "NU - Sales Tax - GST 5%", "BC - Sales Tax - GST 5% + PST 7%", "QC - Sales Tax - GST 5% + QST 9.98%", "QC - Sales Tax - GST 5%", "USD SK - Sales Tax - GST 5% + PST 6%"], "totalDistinctValueCount": 23, "is_unstructured": false}, {"name": "InvoiceSummary_TaxExemptionCode", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A varchar field indicating any tax exemption status associated with the invoice, detailing relevant exemptions.", "availableValues": ["", "PST - PST Exempt", "GST - GST Exempt", "Tax Exempt - GST & PST Exempt"], "is_unstructured": false}, {"name": "InvoiceSummary_LastNote", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field containing the last note associated with the invoice, with a significant percentage of records lacking this information.", "subsetOfAvailableValues": ["Invoice Status Changed from Approved to Sent by <PERSON>", "Invoice Status Changed from Approved to Sen<PERSON> by <PERSON><PERSON>", "Invoice Status Changed from Approved to Sent by <PERSON><PERSON>", "Invoice Status Changed from Approved to Sent by <PERSON><PERSON>", "Invoice Status Changed from Ready for Accounting Review to <PERSON><PERSON> by <PERSON><PERSON>", "and 251 more..."], "totalDistinctValueCount": 256, "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceEmployeeID", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field representing the employee ID associated with the invoice, with limited records having this data.", "subsetOfAvailableValues": ["103", "104", "107", "111", "114", "and 36 more..."], "totalDistinctValueCount": 41, "is_unstructured": false}, {"name": "InvoiceSummary_InvoiceEmployeeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field storing the name of the employee associated with the invoice, with some records missing this information.", "subsetOfAvailableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "and 85 more..."], "totalDistinctValueCount": 90, "is_unstructured": false}], "relationships": [{"child_table": "InvoiceSummary", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[InvoiceSummary.ContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceSummary", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.Number]", "child_column": "[InvoiceSummary.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceSummary", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[InvoiceSummary.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceSummary", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InvoiceSummary.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceSummary", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.Name]", "child_column": "[InvoiceSummary.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceSummary", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InvoiceSummary.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceSummary", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[InvoiceSummary.JobName]"}], "relationship_type": "one-to-many"}]}