{"tableName": "CustomerPricing", "description": "The CustomerPricing table stores customer-specific pricing details, including pricing strategy (by product line or item type), item or service name, office location, pricing method (discount or markup), and amount. Each record is uniquely associated with a customer, enabling precise management of individualized pricing agreements across different offices, products, and services in Air Unlimited’s operations.", "fields": [{"name": "CustomerPricing_PricingType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the strategy for pricing tailored to the customer, with primary categories being 'Product Line Pricing' and 'Item Types'. Each record is associated with a unique pricing strategy.", "availableValues": ["Product Line Pricing", "Item Types"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "CustomerPricing_CustomerID", "dataType": "int", "description": "A unique integer identifier for each customer, ensuring distinct association between records and customers.", "availableValues": ["380230", "380702", "381022"], "is_unstructured": false}, {"name": "CustomerPricing_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the customer, represented as a unique varchar entry, facilitating clear identification of the customer associated with the pricing.", "availableValues": ["G&K Electric Inc.", "PCS Inc. (Nutrien)", "Pritchard Industrial"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "CustomerPricing_CustomerOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies the office location of the customer, with common entries being 'MB Office' and 'SK Office', ensuring distinct categorization of customer locations.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "CustomerPricing_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Describes the specific item or service related to the pricing, maintaining uniqueness for each record, with frequent examples including 'Compressor Oil' and 'Labor'.", "availableValues": ["Compressor Oil ", "Labor"], "is_unstructured": false}, {"name": "CustomerPricing_Description", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Provides context about the pricing, with options typically being 'Cost' or 'Retail', and noting that 34% of records may have missing values.", "availableValues": ["Cost", "Retail"], "is_unstructured": false}, {"name": "CustomerPricing_Discount_Markup", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates whether the pricing is derived from a 'Discount' or 'Markup', with clear categorization ensuring each record is uniquely defined by its pricing structure.", "availableValues": ["Discount", "<PERSON><PERSON>"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "CustomerPricing_Amount", "dataType": "decimal", "description": "Represents the numerical pricing value associated with each customer and pricing type, restricted to positive decimal values under 100 for clarity in pricing.", "is_unstructured": false}]}