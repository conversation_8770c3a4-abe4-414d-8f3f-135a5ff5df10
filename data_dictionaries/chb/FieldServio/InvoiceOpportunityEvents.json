{"tableName": "InvoiceOpportunityEvents", "description": "The InvoiceOpportunityEvents table integrates sales opportunity, job, customer, and invoice data, with each row representing a unique invoice linked to its associated sales cycle. It captures opportunity and job specifics, customer information, invoice amounts and statuses, and detailed event tracking for communications and follow-ups. This table supports comprehensive analysis of revenue, sales performance, customer engagement, and workflow from lead generation through invoice payment.", "fields": [{"name": "InvoiceOpportunityEvents_BidID", "dataType": "int", "description": "A unique integer identifier for each bid, ensuring distinct reference for every entry in the InvoiceOpportunityEvents table.", "subsetOfAvailableValues": ["3690672", "3708181", "3711291", "3711432", "3716936", "and 82 more..."], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityID", "dataType": "int", "description": "A unique integer identifier for each sales opportunity linked to a bid, facilitating tracking throughout the sales cycle.", "subsetOfAvailableValues": ["249149", "249183", "249696", "250700", "251356", "and 82 more..."], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the unique name of the sales opportunity, clarifying the nature of the associated bid.", "subsetOfAvailableValues": ["Fairmont - 2545 Bare", "Feitsma Dairies 10HP Recip", "Oil-Free Air Compressor", "<PERSON> SS5L5", "Central Botanical 2475N7.5", "and 81 more..."], "totalDistinctValueCount": 86, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityAmount", "dataType": "decimal", "description": "A decimal value indicating the total expected revenue from the opportunity associated with the bid.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityExpRevenue", "dataType": "decimal", "description": "A decimal value representing the forecasted revenue from the opportunity, aiding in financial impact assessment.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the opportunity type, such as 'Compressors' or 'Service repair', for better sales analysis.", "availableValues": ["Compressors", "Dryers", "Audit", "Service repair", "Parts sale", "Installation"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_LeadSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the origin of the sales lead, helping evaluate the effectiveness of different lead generation methods.", "availableValues": ["General Inquiry", "Cold Call", "Service technician identified lead", "Referral from supplier/contractor", "Service department identified lead", "Marketing initiative", "Referral from related companies", "Bid and Spec (Tender)"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityCreationDate", "dataType": "datetime", "description": "A datetime value marking when the opportunity was created, crucial for tracking its age and progress.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityCloseDate", "dataType": "datetime", "description": "A datetime value denoting when the sales opportunity was closed, providing insights for performance analysis.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_OpportunityStage", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string describing the current stage in the sales process, indicating success rates in closing opportunities.", "availableValues": ["Opportunity won", "Quoted"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_JobID", "dataType": "int", "description": "A unique integer identifier for each job linked to the opportunity, enabling specific job tracking.", "subsetOfAvailableValues": ["1827751", "1835337", "1836171", "1836213", "1838841", "and 83 more..."], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that provides the unique name of the job associated with the opportunity for easy identification.", "subsetOfAvailableValues": ["ES-3999240", "ES-3833127", "ES-4261189", "ES-3774853", "ES-3711291", "and 83 more..."], "totalDistinctValueCount": 88, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_ProjectName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the project name related to the job, aiding in organization and tracking.", "subsetOfAvailableValues": ["2545V Bare Pump", "Feitsma Dairies 10HP Recip", "OTS1101 Powerex Air Compressor", "SS5L5 Air Compressor", "Central Botanical - 2475N7.5-V", "and 82 more..."], "totalDistinctValueCount": 87, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string classifying the type of job, such as 'Equipment Sale-COM', enhancing reporting capabilities.", "availableValues": ["Equipment Sale-COM", "Specialty Services-COM", "Part Sale-COM", "Service Request-COM", "Service Request - GEN"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_JobStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the current status of the job, vital for managing workflows and job progress.", "availableValues": ["Completed", "Ready to Schedule", "Cancelled", "Waiting on Parts"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_CustomerID", "dataType": "int", "description": "A unique integer identifier for each customer associated with the opportunity, linking to customer records.", "subsetOfAvailableValues": ["379992", "380005", "380039", "380230", "380292", "and 75 more..."], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the unique name of the customer related to the opportunity for effective client management.", "subsetOfAvailableValues": ["Fairmont Winnipeg", "Feitsma Dairies", "Foampak", "Keller Industrial Inc.", "<PERSON>", "and 75 more..."], "totalDistinctValueCount": 80, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_JobOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the office location managing the job, assisting in regional reporting and management.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceID", "dataType": "int", "description": "A unique integer identifier for each invoice linked to the opportunity, allowing precise tracking of billing processes.", "subsetOfAvailableValues": ["2605811", "2607314", "2607497", "2609966", "2612944", "and 141 more..."], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceBillToID", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the identifier for the billing entity associated with the invoice, aiding billing management.", "subsetOfAvailableValues": ["379992", "380005", "380039", "380230", "380292", "and 75 more..."], "totalDistinctValueCount": 80, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_TaxSchedule", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string detailing the applicable tax schedule for the invoice, essential for accurate financial reporting.", "availableValues": ["SK - Sales Tax - GST 5% + PST 6%", "MB - Sales Tax - GST 5% + PST 7%", "MB - Sales Tax - GST 5%", "SK - Sales Tax - GST 5%", "ON - Sales Tax - HST 13%", "AB - Sales Tax - GST 5%", "Both taxes exempt", "MB - Sales Tax - PST 7%"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the unique name of the invoice, simplifying identification and reference.", "subsetOfAvailableValues": ["3019-1", "2983-1", "2979-1", "2941-1", "2644-1", "and 141 more..."], "totalDistinctValueCount": 146, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceQuoteAmt", "dataType": "decimal", "description": "A decimal value representing the quoted amount on the invoice for comparison against final amounts.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceAmount", "dataType": "decimal", "description": "A decimal value representing the total billed amount on the invoice, important for revenue tracking.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the current status of the invoice, crucial for tracking payment progress.", "availableValues": ["Paid in Full", "Voided", "<PERSON><PERSON>", "Ready for Accounting Review", "Partial Payment Received"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_InvoiceDate", "dataType": "datetime", "description": "The date and time when the invoice was generated, essential for tracking timelines.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_SentDate", "dataType": "datetime", "description": "The date and time when the invoice was sent to the customer, with some records missing this value.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_VoidedDate", "dataType": "datetime", "description": "The date and time when the invoice was voided, with values recorded for less than half of the records.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_DueDate", "dataType": "datetime", "description": "The date by which payment for the invoice is expected, critical for cash flow management.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_PaidDate", "dataType": "datetime", "description": "The date and time when the invoice was paid, with around 21% of records lacking this information.", "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_EventCount", "dataType": "int", "description": "An integer representing the count of events related to the invoice, with values potentially indicating interaction frequency.", "availableValues": ["1", "2", "3", "4"], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_EmailCount", "dataType": "int", "description": "An integer indicating the number of emails sent regarding the invoice, consistently showing a value of 0.", "availableValues": ["0"], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_FaceToFaceCount", "dataType": "int", "description": "An integer representing the number of face-to-face interactions related to the invoice, showing engagement levels.", "availableValues": ["0", "1", "2"], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_FollowUpCount", "dataType": "int", "description": "An integer indicating the number of follow-up actions taken regarding the invoice, reflecting follow-up efforts.", "availableValues": ["0", "1", "2"], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_ColdCallCount", "dataType": "int", "description": "An integer indicating the number of cold calls made regarding the invoice, suggesting infrequent use of this method.", "availableValues": ["0", "1"], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_PreventiveMaintenanceCount", "dataType": "int", "description": "An integer representing the count of preventive maintenance actions associated with the invoice, consistently showing a value of 0.", "availableValues": ["0"], "is_unstructured": false}, {"name": "InvoiceOpportunityEvents_LastEventDate", "dataType": "datetime", "description": "The date and time of the last event related to the invoice, recorded for less than half of the invoices.", "is_unstructured": false}], "relationships": [{"child_table": "InvoiceOpportunityEvents", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[InvoiceOpportunityEvents.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceOpportunityEvents", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InvoiceOpportunityEvents.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceOpportunityEvents", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.Name]", "child_column": "[InvoiceOpportunityEvents.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceOpportunityEvents", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InvoiceOpportunityEvents.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceOpportunityEvents", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[InvoiceOpportunityEvents.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoiceOpportunityEvents", "parent_table": "Opportunities", "key_column_mapping": [{"parent_column": "[Opportunity.ID]", "child_column": "[InvoiceOpportunityEvents.OpportunityID]"}], "relationship_type": "one-to-many"}]}