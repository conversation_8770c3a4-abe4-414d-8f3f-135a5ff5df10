{"tableName": "EquipmentHours", "description": "The EquipmentHours table records operational hours, status, serial number, customer associations (end user and purchaser), and detailed location information for each equipment item, uniquely identified by EquipmentID. It supports monitoring equipment usage, warranty status, and job completion history. Some fields, such as warranty expiration, start date, and sold customer details, are often incomplete, so users should consider data availability when querying this table.", "fields": [{"name": "EquipmentHours_EquipmentID", "dataType": "int", "description": "A unique integer identifier for each equipment record, serving as the primary key for the EquipmentHours table.", "subsetOfAvailableValues": ["950376", "950631", "950739", "951385", "951797", "and 196 more..."], "is_unstructured": false}, {"name": "EquipmentHours_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the unique name of the equipment, identifying its type or model.", "subsetOfAvailableValues": ["Sullivan Palatek DC185P3IZ-C", "SG060", "60GS60", "<PERSON>an 30EK H37866S", "OPTCA-1708531", "and 156 more..."], "totalDistinctValueCount": 161, "is_unstructured": false}, {"name": "EquipmentHours_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing details about the equipment, though approximately 1% of records may lack this information.", "subsetOfAvailableValues": ["Gas", "Oil Flooded", "", "Sullivan-<PERSON><PERSON><PERSON> Trailer Mt Compressor", "Sullivan-Palatek 185CFM Trailer Mt Compressor", "and 133 more..."], "totalDistinctValueCount": 138, "is_unstructured": false}, {"name": "EquipmentHours_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the operational status of the equipment, consistently marked as 'Active' for equipment in use or available.", "availableValues": ["Active"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "EquipmentHours_SerialNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string that identifies the equipment's serial number, crucial for tracking and inventory management.", "subsetOfAvailableValues": ["6610", "35475", "73804", "286763", "352468", "and 196 more..."], "totalDistinctValueCount": 201, "is_unstructured": false}, {"name": "EquipmentHours_StartDate", "dataType": "smalldatetime", "description": "A smalldatetime indicating when the equipment was first operational, with values present in less than 50% of records.", "is_unstructured": false}, {"name": "EquipmentHours_WarrantyExpiration", "dataType": "smalldatetime", "description": "A smalldatetime noting the expiration date of the equipment's warranty, present in fewer than 10% of records, indicating limited warranty tracking.", "is_unstructured": false}, {"name": "EquipmentHours_EndCustomerID", "dataType": "int", "description": "An integer referencing the unique identifier of the end customer using the equipment, typically shared across an average of 2 records.", "subsetOfAvailableValues": ["379954", "379987", "379992", "380016", "380026", "and 122 more..."], "is_unstructured": false}, {"name": "EquipmentHours_EndCustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the name of the end customer associated with the equipment, generally unique for a group of 2 records.", "subsetOfAvailableValues": ["Air Unlimited Inc.", "Genrep Ltd.", "The Mosaic Company", "Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "Winnipeg Regional Health", "and 122 more..."], "totalDistinctValueCount": 127, "is_unstructured": false}, {"name": "EquipmentHours_SoldCustomerID", "dataType": "int", "description": "An integer denoting the unique identifier of the customer who purchased the equipment, found in less than 50% of records.", "subsetOfAvailableValues": ["379954", "380138", "380202", "380288", "380330", "and 11 more..."], "is_unstructured": false}, {"name": "EquipmentHours_SoldCustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the name of the customer who bought the equipment, available in fewer than 50% of records.", "availableValues": ["Air Unlimited Inc.", "McCaine Electric", "Morris Industries Ltd.", "Nortec Electric", "PennLite Electrical & Mechanical", "Randolph Feed Mill Ltd.", "Resolute Forest Products", "SaskPower", "Sunrise Health Region", "Sutherland Electric", "Triad Power (2004) Ltd.", "Western Sales Ltd.", "Diefenbaker Seed Processors Ltd. O/A Diefenbaker Spice & Pulse", "EDA Mechanical Services Ltd.", "ICR Commercial Real Estate", "Linde Canada Inc."], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "EquipmentHours_LocationID", "dataType": "int", "description": "An integer uniquely identifying the location where the equipment is stored or used, with values missing in approximately 2% of records.", "subsetOfAvailableValues": ["659012", "659186", "659187", "659188", "659189", "and 155 more..."], "is_unstructured": false}, {"name": "EquipmentHours_LocationName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string specifying the name of the location associated with the equipment, typically unique for each record, with 2% missing data.", "subsetOfAvailableValues": ["Winnipeg-Head Office", "The Mosaic Company", "FARM CREDIT CANADA", "Superstore RCSS #1533", "Superstore RCSS #1535", "and 153 more..."], "totalDistinctValueCount": 158, "is_unstructured": false}, {"name": "EquipmentHours_LocationAddress", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the address of the equipment's location, available in less than 50% of records, unique for about 26 records.", "availableValues": ["2116 Logan Avenue ", "3806 Albert Street Unit# 1200", "1519 Henderson Highway See <PERSON> (************) keys for building", "P.O. Box 187 40117 Road 65N", "2223 Victoria Avenue Victoria Square Shopping Centre", "2296 Springfield Road Sunnyside", "3015 Gordon Road Safeway Southland Mall", "355 Alberta Street North Northgate Mall", "100 Irene Street Unit# 1"], "totalDistinctValueCount": 9, "is_unstructured": false}, {"name": "EquipmentHours_LocationCity", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the city of the equipment's location, generally unique for a group of 4 records, with 2% missing values.", "subsetOfAvailableValues": ["Winnipeg", "Regina", "Saskatoon", "<PERSON>", "<PERSON>", "and 53 more..."], "totalDistinctValueCount": 58, "is_unstructured": false}, {"name": "EquipmentHours_LocationState", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string identifying the state of the location where the equipment is used, with categorical values such as SK, MB, and ON, and 2% missing data.", "availableValues": ["SK", "MB", "ON", ""], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "EquipmentHours_LocationZip", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the postal or ZIP code for the equipment's location, with unique values for each record and less than 2% missing information.", "subsetOfAvailableValues": ["R2R 0J2", "", "S0G 0G0", "S4M 0A1", "S0A 0X0", "and 141 more..."], "totalDistinctValueCount": 146, "is_unstructured": false}, {"name": "EquipmentHours_LocationDefaultJobOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the default job office for the location, with less than 50% of records populated, showing common values like 'MB Office'.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "EquipmentHours_Hours", "dataType": "decimal", "description": "A decimal field recording the total operational hours the equipment has been used, with no additional information provided.", "is_unstructured": false}, {"name": "EquipmentHours_HoursUpdatedDate", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the date when the operational hours were last updated, uniquely identifying each record's last modification.", "subsetOfAvailableValues": ["01/30/2025", "01/10/2025", "08/26/2024", "01/09/2025", "02/10/2025", "and 147 more..."], "totalDistinctValueCount": 152, "is_unstructured": false}, {"name": "EquipmentHours_JobCompletedDate", "dataType": "smalldatetime", "description": "A smalldatetime showing the date when the job associated with the equipment was completed, with approximately 27% of records lacking values.", "is_unstructured": false}], "relationships": [{"child_table": "EquipmentHours", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[EquipmentHours.EndCustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentHours", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[EquipmentHours.SoldCustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentHours", "parent_table": "EquipmentDetail", "key_column_mapping": [{"parent_column": "[Equipment.ID]", "child_column": "[EquipmentHours.EquipmentID]"}], "relationship_type": "one-to-many"}, {"child_table": "EquipmentHours", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[EquipmentHours.LocationID]"}], "relationship_type": "one-to-many"}]}