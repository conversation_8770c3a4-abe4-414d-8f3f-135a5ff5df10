{"tableName": "InvoicesNotSent", "description": "The InvoicesNotSent table contains detailed records of invoices that have not yet been sent to customers, including invoice type, source, creation date, total amount, and related customer, job, and contract information. It enables tracking and management of pending billing across Air Unlimited’s services, supporting reporting and operational workflows. Each record is uniquely identified and may reference external tables such as CustomerInformation, JobsBacklog, and JobsSummary.", "fields": [{"name": "InvoicesNotSent_InvoiceID", "dataType": "int", "description": "A unique numerical identifier for each invoice, essential for tracking and managing invoice records.", "subsetOfAvailableValues": ["2579745", "2579764", "2579772", "2579795", "2579796", "and 128 more..."], "is_unstructured": false}, {"name": "InvoicesNotSent_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string representing the name or identification number of the invoice, used for reference in billing processes.", "subsetOfAvailableValues": ["0000142020 PST", "0000142012", "0000141999 PST", "0000140183", "0000137529 PST", "and 128 more..."], "totalDistinctValueCount": 133, "is_unstructured": false}, {"name": "InvoicesNotSent_InvoiceType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical string indicating the nature of the invoice, primarily distinguishing between 'Invoice' and 'Deposit' types.", "availableValues": ["Invoice", "<PERSON><PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InvoicesNotSent_InvoiceSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the origin of the invoice, with values such as 'Import', 'Contract', 'External', 'Job', and 'Customer' to understand the generation context.", "availableValues": ["Import", "Job", "Contract", "External", "Customer"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "InvoicesNotSent_InvoiceDate", "dataType": "datetime", "description": "A timestamp recording when the invoice was created, critical for tracking billing timelines and payment schedules.", "is_unstructured": false}, {"name": "InvoicesNotSent_CustomerID", "dataType": "int", "description": "An integer linking to the customer associated with the invoice, allowing for grouping of invoices by customer while connecting to the CustomerInformation table.", "subsetOfAvailableValues": ["380079", "380204", "380576", "380596", "380627", "and 41 more..."], "is_unstructured": false}, {"name": "InvoicesNotSent_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the name of the customer to whom the invoice is issued, aiding in customer identification for billing.", "subsetOfAvailableValues": ["Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "<PERSON> Agencies (USD)", "Generac Mobile Products LLC", "Motion Industries (Canada) Inc.", "Canadian National Railway", "and 41 more..."], "totalDistinctValueCount": 46, "is_unstructured": false}, {"name": "InvoicesNotSent_InvoiceTotal", "dataType": "decimal", "description": "A decimal value representing the total charge on the invoice, crucial for financial reporting and payment processing.", "is_unstructured": false}, {"name": "InvoicesNotSent_CreatedByID", "dataType": "int", "description": "An identifier for the user or system that generated the invoice, facilitating tracking of the invoice creation source.", "subsetOfAvailableValues": ["0", "107985", "107989", "107992", "107998", "and 9 more..."], "is_unstructured": false}, {"name": "InvoicesNotSent_CreatedByName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string with the name of the individual or system responsible for creating the invoice, with partial record availability.", "availableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "totalDistinctValueCount": 13, "is_unstructured": false}, {"name": "InvoicesNotSent_JobID", "dataType": "int", "description": "An integer potentially linking to a job related to the invoice, with limited availability and connections to the JobsBacklog and JobsSummary tables.", "subsetOfAvailableValues": ["1824110", "1849822", "1915709", "1931795", "1947462", "and 24 more..."], "is_unstructured": false}, {"name": "InvoicesNotSent_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the job associated with the invoice, also with limited data availability.", "subsetOfAvailableValues": ["PS-4113259", "PS-4204386", "PS-4146053", "PS-3985049", "PS-4058635", "and 24 more..."], "totalDistinctValueCount": 29, "is_unstructured": false}, {"name": "InvoicesNotSent_ContractID", "dataType": "int", "description": "An integer linking to the contract related to the invoice, with restricted data availability.", "subsetOfAvailableValues": ["192401", "192761", "193041", "194762", "195269", "and 4 more..."], "is_unstructured": false}, {"name": "InvoicesNotSent_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string denoting the contract number associated with the invoice, with limited data availability.", "availableValues": ["ESP194762", "ESP195269", "ESP213715", "ESP213728", "PFB75665", "PMA192761", "PMA193041", "PMA195412", "RC201794"], "totalDistinctValueCount": 9, "is_unstructured": false}, {"name": "InvoicesNotSent_PONumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that may include the purchase order number related to the invoice, with less than 50% data availability.", "subsetOfAvailableValues": ["339906", "9306789", "IS-4112986-1 (SO627694)", "IS-4204244-1 (SO627745)", "PM Service", "and 38 more..."], "totalDistinctValueCount": 43, "is_unstructured": false}, {"name": "InvoicesNotSent_ShipToName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the recipient location or entity for the shipment of goods or services, aiding in delivery identification.", "subsetOfAvailableValues": ["Sobeys Group Inc./ Sobeys Capital Inc. (304368)", "TOM BEGGS (USD)", "Generac Mobile Products LLC", "Canadian National Railway", "FieldServio Canada", "and 46 more..."], "totalDistinctValueCount": 51, "is_unstructured": false}], "relationships": [{"child_table": "InvoicesNotSent", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[InvoicesNotSent.ContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoicesNotSent", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.Number]", "child_column": "[InvoicesNotSent.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoicesNotSent", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[InvoicesNotSent.CustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoicesNotSent", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InvoicesNotSent.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InvoicesNotSent", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InvoicesNotSent.JobID]"}], "relationship_type": "one-to-many"}]}