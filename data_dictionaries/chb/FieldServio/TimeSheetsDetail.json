{"tableName": "TimeSheetsDetail", "description": "TimeSheetsDetail stores daily timesheet entries for employees, capturing work dates, start/end times, job and non-job hours, time type, and descriptive details. Each record links to an employee and may reference job assignments, job location, and office. Approval information and billing labor type are included, supporting tracking of labor allocation, technician utilization, and payroll or performance analysis across multiple locations and job types.", "fields": [{"name": "TimeSheetsDetail_EmployeeId", "dataType": "int", "description": "A unique integer identifier for each employee, linking timesheet entries to the corresponding employee record in the EmployeeDetail table.", "subsetOfAvailableValues": ["108014", "108015", "108016", "108017", "108018", "and 12 more..."], "is_unstructured": false}, {"name": "TimeSheetsDetail_EmployeeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The full name of the employee, provided as a variable character string (varchar) for easy human identification.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "TimeSheetsDetail_EmployeeNumber", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A unique alphanumeric employee number assigned to each employee, used for internal tracking and identification purposes.", "subsetOfAvailableValues": ["103", "107", "109", "110", "111", "and 5 more..."], "is_unstructured": false}, {"name": "TimeSheetsDetail_ReportMonthStart", "dataType": "<PERSON><PERSON><PERSON>", "description": "The starting date of the reporting month for the timesheet, formatted as a varchar, indicating the period for which hours are recorded.", "availableValues": ["09/01/2024", "10/01/2024", "01/01/2025", "11/01/2024", "05/01/2025", "03/01/2025", "04/01/2025", "12/01/2024", "02/01/2025", "08/01/2024", "06/01/2025"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "TimeSheetsDetail_ReportMonthEnd", "dataType": "<PERSON><PERSON><PERSON>", "description": "The ending date of the reporting month for the timesheet, formatted as a varchar, marking the conclusion of the recording period.", "availableValues": ["09/30/2024", "10/31/2024", "01/31/2025", "11/30/2024", "05/31/2025", "03/31/2025", "04/30/2025", "12/31/2024", "02/28/2025", "08/31/2024", "06/30/2025"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "TimeSheetsDetail_WorkDate", "dataType": "<PERSON><PERSON><PERSON>", "description": "The specific date of work performed, formatted as a varchar, generally unique within a group of records for accurate time tracking.", "subsetOfAvailableValues": ["09/18/2024", "08/26/2024", "09/17/2024", "09/12/2024", "09/04/2024", "and 230 more..."], "totalDistinctValueCount": 235, "is_unstructured": false}, {"name": "TimeSheetsDetail_StartTime", "dataType": "<PERSON><PERSON><PERSON>", "description": "The exact time when the employee began work on a particular day, formatted as a varchar, facilitating detailed time tracking.", "subsetOfAvailableValues": ["08:00", "12:00", "09:00", "11:00", "14:00", "and 219 more..."], "totalDistinctValueCount": 224, "is_unstructured": false}, {"name": "TimeSheetsDetail_EndTime", "dataType": "<PERSON><PERSON><PERSON>", "description": "The exact time when the employee completed work for the day, formatted as a varchar, facilitating detailed time tracking.", "subsetOfAvailableValues": ["16:00", "12:00", "14:00", "15:00", "09:00", "and 221 more..."], "totalDistinctValueCount": 226, "is_unstructured": false}, {"name": "TimeSheetsDetail_TimeType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical varchar field indicating the type of time recorded, such as 'Onsite Time', 'Travel', or 'Meeting'.", "availableValues": ["Onsite Time", "Travel", "Shop Time", "Other", "Meeting"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "TimeSheetsDetail_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A textual description of the specific work performed during recorded hours, provided as a varchar for clarity.", "subsetOfAvailableValues": ["", "Tech entry check-in", "Training", "IR Training", "School", "and 460 more..."], "totalDistinctValueCount": 465, "is_unstructured": false}, {"name": "TimeSheetsDetail_JobHours", "dataType": "decimal", "description": "The total number of hours worked on job-related tasks, represented as a positive decimal value under 100.", "is_unstructured": false}, {"name": "TimeSheetsDetail_NonJobHours", "dataType": "decimal", "description": "The total number of hours allocated to non-job-related activities, represented as a positive decimal value under 100.", "is_unstructured": false}, {"name": "TimeSheetsDetail_JobID", "dataType": "int", "description": "A unique integer identifier for the job associated with the timesheet entry, linking to detailed job information.", "subsetOfAvailableValues": ["0", "1820877", "1821189", "1821278", "1821280", "and 2494 more..."], "is_unstructured": false}, {"name": "TimeSheetsDetail_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the job related to the timesheet entry, formatted as a varchar, with potential missing values for some records.", "subsetOfAvailableValues": ["WR-3679665", "ESP-3703966", "SR-4029710", "PFB-3679984", "SS-3859200", "and 2493 more..."], "totalDistinctValueCount": 2498, "is_unstructured": false}, {"name": "TimeSheetsDetail_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field categorizing the type of job performed, which may be missing in a subset of records.", "availableValues": ["Service Request-COM", "PM Fixed Bid - GEN", "Service Request - GEN", "PMA-COM", "PMA - GEN", "PM Fixed Bid-COM", "Project - GEN", "Warranty Request-COM", "Warranty Request - GEN", "Asset maintenance", "Call Back-COM", "Rental asset maintenance", "Equipment Sale-COM", "Courtesy Call-COM", "Call Back - GEN", "Courtesy Call - GEN", "Specialty Services-COM", "Project - COM", "Rental Contract", "Rental Incidentals", "Remote Monitoring - GEN", "Equipment Sale - GEN", "Part Sale - GEN", "Part Sale-COM"], "totalDistinctValueCount": 24, "is_unstructured": false}, {"name": "TimeSheetsDetail_JobCity", "dataType": "<PERSON><PERSON><PERSON>", "description": "The city where the job was performed, formatted as a varchar, with some records potentially missing this information.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "Portage La Prairie", "<PERSON>", "and 200 more..."], "totalDistinctValueCount": 205, "is_unstructured": false}, {"name": "TimeSheetsDetail_JobState", "dataType": "<PERSON><PERSON><PERSON>", "description": "The state of the job location, represented as a varchar, with possible values including 'MB', 'SK', 'ON', and others.", "availableValues": ["MB", "SK", "ON", "", "YT", "QC", "IL", "AB"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "TimeSheetsDetail_JobPostalCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "The postal code for the job location, formatted as a varchar, with potential missing values for some records.", "subsetOfAvailableValues": ["R2R 0J2", "", "R2J 3S8", "R0E 0C0", "S6H 7K8", "and 677 more..."], "totalDistinctValueCount": 682, "is_unstructured": false}, {"name": "TimeSheetsDetail_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the office responsible for the timesheet entry, formatted as a varchar, with most records indicating 'MB Office' or 'SK Office'.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "TimeSheetsDetail_ApprovedDate", "dataType": "datetime", "description": "The datetime when the timesheet entry was officially approved, capturing the validation timestamp.", "is_unstructured": false}, {"name": "TimeSheetsDetail_ApprovedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the user who approved the timesheet entry, consistently recorded as 'Support User'.", "availableValues": ["Support User"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "TimeSheetsDetail_BilledLaborType", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The type of labor being billed for the timesheet entry, represented as a nvarchar, with some records potentially lacking this information.", "subsetOfAvailableValues": [" Labour-Spot Compressor", "Labour-PMA Generator", "Travel Time", "Labour-Spot Generator", "Labour-PMA Compressor", "and 9 more..."], "is_unstructured": false}], "relationships": [{"child_table": "TimeSheetsDetail", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[TimeSheetsDetail.EmployeeId]"}], "relationship_type": "one-to-many"}]}