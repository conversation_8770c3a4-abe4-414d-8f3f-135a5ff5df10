{"tableName": "JobsBacklog", "description": "The JobsBacklog table records service jobs pending completion, detailing each job’s unique ID, name, status, type, office location, assigned salesperson (if available), promised completion date, expected revenue, and estimated cost. This table supports tracking job progress, managing resource allocation, and forecasting revenue and costs by office location.", "fields": [{"name": "JobsBacklog_ID", "dataType": "int", "description": "A unique integer identifier for each job in the backlog, serving as the primary key for the table.", "subsetOfAvailableValues": ["1820803", "1820816", "1820820", "1820833", "1820841", "and 5059 more..."], "is_unstructured": false}, {"name": "JobsBacklog_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string representing the name of the job, used to identify specific jobs within the backlog.", "subsetOfAvailableValues": ["ESP-3681758-C", "PFB-3703521-C", "PMA-3678911-C", "PMA-3678927-C", "PMA-3678972-C", "and 5004 more..."], "totalDistinctValueCount": 5009, "is_unstructured": false}, {"name": "JobsBacklog_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical field indicating the current progress of the job, with possible values including 'Cancelled', 'Completed', 'Waiting on Parts', 'Ready to Schedule', 'Scheduled', 'In Progress', and 'On Hold'.", "availableValues": ["Completed", "Cancelled", "Waiting on Parts", "Ready to Schedule", "Scheduled", "In Progress", "On Hold"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "JobsBacklog_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that categorizes the job based on its nature, such as 'PMA - GEN' or 'PM Fixed Bid - GEN', aiding in job classification.", "subsetOfAvailableValues": ["Part Sale-COM", "Service Request-COM", "PM Fixed Bid - GEN", "Service Request - GEN", "PMA-COM", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "JobsBacklog_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the office linked to the job, primarily 'MB Office' or 'SK Office', providing insights into geographical job distribution.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsBacklog_SalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the salesperson assigned to the job, with a significant percentage of records lacking this information.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 18, "is_unstructured": false}, {"name": "JobsBacklog_PromisedDate", "dataType": "date", "description": "A date indicating the deadline for job completion, essential for managing customer expectations and project timelines.", "is_unstructured": false}, {"name": "JobsBacklog_Revenue", "dataType": "decimal", "description": "A decimal value representing the anticipated revenue from the job, with incomplete data for many records affecting financial projections.", "is_unstructured": false}, {"name": "JobsBacklog_Cost", "dataType": "decimal", "description": "A decimal value reflecting the estimated cost for job completion, with less than half of the records providing this information, impacting cost analysis.", "is_unstructured": false}], "relationships": [{"child_table": "JobsBacklog", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[JobsBacklog.ID]"}], "relationship_type": "one-to-one"}, {"child_table": "JobsBacklog", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[JobsBacklog.Name]"}], "relationship_type": "one-to-one"}]}