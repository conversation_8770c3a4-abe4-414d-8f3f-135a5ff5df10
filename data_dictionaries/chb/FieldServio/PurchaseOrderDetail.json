{"tableName": "PurchaseOrderDetail", "description": "The PurchaseOrderDetail table captures comprehensive line item details for each purchase order, including item, job, customer, supplier, and warehouse information. It tracks item quantities, costs, pricing, fulfillment, delivery, and status, with each record uniquely identified by POLineID. This table enables detailed procurement, inventory, and job allocation analysis, supporting Air Unlimited’s order processing and operational management across its air and power solutions services.", "fields": [{"name": "PurchaseOrderDetail_ID", "dataType": "int", "description": "A unique integer identifier for each record in the PurchaseOrderDetail table, ensuring distinct identification across line items.", "subsetOfAvailableValues": ["2053810", "2053957", "2061163", "2061352", "2061806", "and 5197 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the purchase order detail, which may not always serve as a standalone unique identifier.", "subsetOfAvailableValues": ["SO2116", "SO1117", "SO1591", "SO2260", "SO1239", "and 5174 more..."], "totalDistinctValueCount": 5179, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_ID", "dataType": "int", "description": "An integer representing the unique identifier of the associated job, allowing linkage to multiple purchase order details.", "subsetOfAvailableValues": ["1821217", "1821278", "1821556", "1827955", "1842402", "and 3522 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the job associated with the purchase order detail, which may have multiple records for the same job.", "subsetOfAvailableValues": ["PFB-3727918", "PS-4215606", "PFB-3727921", "PS-4058635", "ESP-3703966", "and 3516 more..."], "totalDistinctValueCount": 3521, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the category of the job associated with the purchase order detail.", "subsetOfAvailableValues": ["Part Sale-COM", "Service Request-COM", "PM Fixed Bid - GEN", "PMA-COM", "PM Fixed Bid-COM", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "The current status of the job, with potential values indicating its progress in the workflow.", "availableValues": ["Completed", "Waiting on Parts", "Cancelled", "Ready to Schedule", "In Progress", "Scheduled"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "The office associated with the job, specifying the location responsible for managing the purchase order.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_AssignedResource", "dataType": "<PERSON><PERSON><PERSON>", "description": "The resource assigned to the job, which may not be consistently provided across records.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"], "totalDistinctValueCount": 18, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Urgency", "dataType": "<PERSON><PERSON><PERSON>", "description": "The urgency level of the job, indicating its priority status in the order processing sequence.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_PriorityNotes", "dataType": "<PERSON><PERSON><PERSON>", "description": "Any additional priority notes related to the job, capturing specific details that may affect its handling.", "availableValues": ["Call <PERSON> when you get to site 306-260-0625", "<PERSON> will pay the labour when the parts arrive in", "Level 1 - 4 hours not 3 hours ", "To enter, a key box has been installed near the main store entrance.\r\nThen, It will be necessary to deactivate the alarm and reactivate it at the end of the inspection In case of problem, I indicated the number of the monitoring vendor.\r\nLock box code: 4127\r\nAlarm code: 2020\r\nMonitoring: Vector / +1 (703) 468 6100", "Login Info \r\nWKO-00505061\r\nPin# 22854", "Login and out Information\r\nWKO-00504383\r\nPin# 22854", "Login and out Information\r\nWKO-00504382\r\nPin# 22854", "Login Information \r\nWO-00505062\r\nPin# 22854\r\n", "Login and out Information \r\nWKO-00504384\r\nPin# 22854\r\n", "Login Infor\r\nWKO-00505063\r\nPin# 22854"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_CustomerPO", "dataType": "<PERSON><PERSON><PERSON>", "description": "The customer purchase order number, which helps in tracking the customer's request and linking it to the order.", "subsetOfAvailableValues": ["728389", "305247422", "PM Service", "SS-FM-0006-WR", "", "and 2202 more..."], "totalDistinctValueCount": 2207, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed description of the job, which provides context for the associated purchase order detail.", "subsetOfAvailableValues": ["", "Annual + Quinquennial (ensure to update service stickers and receive signoff from on-site staff) - SD0200LG178.7D18HPNN3 - 8624578", "Pick up", "Rosenau Transport", "Annual + Quinquennial (ensure to update service stickers and receive signoff from on-site staff) - 125REOZJB - 776960", "and 2473 more..."], "totalDistinctValueCount": 2478, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_EstimatedDuration", "dataType": "<PERSON><PERSON><PERSON>", "description": "The anticipated duration of the job, offering insight into the expected time frame for completion.", "subsetOfAvailableValues": ["0", "1", "2", "3", "4", "and 35 more..."], "totalDistinctValueCount": 40, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_EstimatedDays", "dataType": "<PERSON><PERSON><PERSON>", "description": "The estimated number of days required to complete the job, aiding in scheduling and planning.", "availableValues": ["0", "1", "2", "3", "4", "5", "6", "8", "2.5", "", "2.25"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_NumberTechs", "dataType": "<PERSON><PERSON><PERSON>", "description": "The number of technicians needed for the job, which helps in resource allocation and planning.", "availableValues": ["0", "1", "2", ""], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_ShipToOverride", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Any shipping instructions that override standard procedures, important for ensuring proper delivery.", "subsetOfAvailableValues": ["", "PCS Potash Lanigan\r\n12 KM West of Lanigan, HWY 16\r\nLanigan, SK S0K 2M0", "\r\n", "Shamattawa Nursing Station\r\nShamattawa, Manitoba\r\nR0B 1K0", "725 Black Diamond Blvd", "and 87 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the user who created the job record, providing accountability and tracking of job entries.", "subsetOfAvailableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 23 more..."], "totalDistinctValueCount": 28, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_CreatedDate", "dataType": "smalldatetime", "description": "The date the job was created, essential for tracking job timelines and history.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_ModifiedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the user who last modified the job record, indicating who made recent changes.", "subsetOfAvailableValues": ["107985", "108013", "108035", "109566", "109943", "and 49 more..."], "totalDistinctValueCount": 54, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_ModifiedDate", "dataType": "datetime", "description": "The date and time when the job was last modified, critical for understanding the job's history.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Bid_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the bid related to the purchase order detail, identifying the competitive aspect of the order.", "subsetOfAvailableValues": ["PM Fixed Bid - GEN - 3727918 (Pending)", "Part Sale-COM - 4215606 (Pending)", "PM Fixed Bid - GEN - 3727921 (Pending)", "Part Sale-COM - 4058635 (Pending)", "Equipment Sale Project - GEN - 3703966 (Pending)", "and 3434 more..."], "totalDistinctValueCount": 3439, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "The status of the purchase order detail, providing an overview of its current processing stage.", "availableValues": ["All Arrived", "Partial Arrived", "Ordered", "In Development", "Waiting on Parts", "Shipped"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "The type of purchase order detail, categorizing the nature of the ordered items.", "availableValues": ["Pick List", "Stock Order", "Job Order", "Miscellaneous"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Terms", "dataType": "<PERSON><PERSON><PERSON>", "description": "The terms associated with the purchase order, outlining conditions for payment and delivery.", "availableValues": ["Standard", "None", "Over Night", "<PERSON>", "Overnight", "", "See Notes"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Notes", "dataType": "<PERSON><PERSON><PERSON>", "description": "Additional notes regarding the purchase order detail, allowing for further context or instructions.", "subsetOfAvailableValues": ["26528015", "Ship Complete", "", "PO 144062", "Quote # 2733174.  Submitted as quote as ccn 48660336 was missing the discount.", "and 574 more..."], "totalDistinctValueCount": 579, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "The office location responsible for the purchase order processing, indicating where the order is managed.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "PurchaseOrderDetail_PO_ShipToOverride", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Indicates whether there is an override for the shipping address specified in the purchase order.", "subsetOfAvailableValues": ["Air Unlimited Inc.\r2116 Logan Ave\nWinnipeg, MB R2R 0J2", "Air Unlimited Inc.\r1-3247 Millar Ave\nSaskatoon, SK S7K 5Y3", "Air Unlimited Inc.\nWinnipeg, Manitoba R2R 0J2", "Air Unlimited Inc.\r1-3247 Millar Avenue\nSaskatoon, SK S7K 5Y3", "PCS Potash Lanigan\r\n12 KM West of Lanigan, HWY 16\r\nLanigan, SK S0K 2M0", "and 64 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Customer_ID", "dataType": "int", "description": "An integer uniquely identifying the customer associated with the purchase order.", "subsetOfAvailableValues": ["379954", "380039", "380053", "380079", "380204", "and 985 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Customer_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the customer linked to the purchase order, providing a reference for customer relations.", "subsetOfAvailableValues": ["<PERSON> Agencies (USD)", "Air Unlimited Inc.", "The Mosaic Company", "PCS Inc. (Nutrien)", "City of Winnipeg", "and 983 more..."], "totalDistinctValueCount": 988, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Supplier_ID", "dataType": "int", "description": "An integer uniquely identifying the supplier associated with the purchase order.", "subsetOfAvailableValues": ["81061", "81073", "81079", "81111", "81113", "and 275 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Supplier_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the supplier for the purchase order, important for tracking vendor relationships.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "<PERSON>", "Cleanair Filter Service", "Oil Mart Ltd", "and 272 more..."], "totalDistinctValueCount": 277, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Supplier_AccountNo", "dataType": "<PERSON><PERSON><PERSON>", "description": "The account number assigned to the supplier, facilitating billing and payment processes.", "subsetOfAvailableValues": ["5038", "5044", "5047", "5066", "5067", "and 275 more..."], "totalDistinctValueCount": 280, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Supplier_Phone", "dataType": "<PERSON><PERSON><PERSON>", "description": "The phone number of the supplier, essential for communication regarding the order.", "subsetOfAvailableValues": ["************", "************", "************", "************", "************", "and 256 more..."], "totalDistinctValueCount": 261, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Warehouse_ID", "dataType": "int", "description": "An integer uniquely identifying the warehouse associated with the purchase order.", "subsetOfAvailableValues": ["29913", "29914", "29915", "29916", "29917", "and 11 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Warehouse_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the warehouse where the items related to the purchase order are stored.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "Thermo King", "Shared Services", "S/N 1143909 - <PERSON>", "~Regina Warehouse", "S/N 1249338 - <PERSON>", "S/N 1296840 - <PERSON>", "S/N Z412284 - <PERSON>", "S/N KB09748 - <PERSON>", "S/N 1143253 - <PERSON>", "S/N S545722 - <PERSON>", "S/N KE44716 - <PERSON>", "S/N G367219 - <PERSON>", "S/N 1196248 - <PERSON>", "S/N 1143618 - <PERSON>"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Warehouse_Address1", "dataType": "<PERSON><PERSON><PERSON>", "description": "The primary address of the warehouse, crucial for logistics and delivery management.", "availableValues": ["2116 Logan Ave", "1-3247 Millar Ave"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Warehouse_City", "dataType": "<PERSON><PERSON><PERSON>", "description": "The city where the warehouse is located, providing geographic context for the order fulfillment.", "availableValues": ["Winnipeg", "Saskatoon", "Regina"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Warehouse_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "The state abbreviation where the warehouse is situated, indicating its regional location.", "availableValues": ["MB", "SK"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Warehouse_Zip", "dataType": "<PERSON><PERSON><PERSON>", "description": "The postal code for the warehouse location, necessary for accurate shipping and handling.", "availableValues": ["R2R 0J2", "S7K 5Y3"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Date", "dataType": "datetime", "description": "The date when the purchase order was created, establishing a timeline for order processing.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_OrderedBy_ID", "dataType": "int", "description": "An integer identifying the employee who placed the order, providing accountability.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 29 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_OrderedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the employee who placed the order, allowing for tracking of order requests.", "subsetOfAvailableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "PurchaseOrderDetail_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the user who created the purchase order, ensuring accountability for the order entry.", "subsetOfAvailableValues": ["Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_POFufilledDate", "dataType": "smalldatetime", "description": "The date when the purchase order was fulfilled, marking the completion of the order process.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_PODeliveryDate", "dataType": "smalldatetime", "description": "The date when the purchase order is scheduled to be delivered, essential for planning logistics.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Job_Location", "dataType": "<PERSON><PERSON><PERSON>", "description": "The job location associated with the purchase order, providing context for logistical arrangements.", "subsetOfAvailableValues": ["TOM BEGGS (USD), 2116 Logan Ave, Winnipeg, MB R2R 0J2", "Winnipeg-Head Office, 2116 Logan Ave, Winnipeg, MB R2R 0J2", "The Mosaic Company, Kalium Road, , Belle Plain, SK S0G 0G0", "GFL Environmental, 335 Mazenod Rd, , Winnipeg, MB R2J 3S8", "ARDENT MILLS, ULC., 95 33 St E, , Saskatoon, SK S7K 0R8", "and 1628 more..."], "totalDistinctValueCount": 1633, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Via", "dataType": "<PERSON><PERSON><PERSON>", "description": "The method of delivery for the purchase order, indicating how the items will be transported.", "subsetOfAvailableValues": ["Prepaid No Charge", "Prepaid & Add - Ground", "Purolator Ground 9211283", "Bestway", "Pick Up ", "and 116 more..."], "totalDistinctValueCount": 121, "is_unstructured": false}, {"name": "PurchaseOrderDetail_DateRequired", "dataType": "datetime", "description": "The date by which the order is required, important for scheduling and delivery commitments.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_PromiseDate", "dataType": "datetime", "description": "The promise date for the delivery of the purchase order, indicating the expected delivery commitment.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_ID", "dataType": "int", "description": "An integer uniquely identifying the item in the purchase order, crucial for tracking inventory.", "subsetOfAvailableValues": ["2825307", "2826815", "2827695", "2827709", "2827719", "and 3309 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The name of the item associated with the purchase order, providing clarity on the ordered goods.", "subsetOfAvailableValues": ["22421853", "24121212", "24900433", "38035531", "38436721", "and 3306 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Code", "dataType": "<PERSON><PERSON><PERSON>", "description": "The unique code assigned to the item, used for identification and categorization.", "subsetOfAvailableValues": ["24121212", "24900433", "38035531", "38436721", "38459582", "and 3308 more..."], "totalDistinctValueCount": 3313, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Manufacturer", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the manufacturer of the item, providing information on the source of the products.", "availableValues": ["Ingersoll Rand", "Generac", "N/A", "CUMMINS", "Toromont Cat", "CAG", "<PERSON><PERSON><PERSON>", "SULLAIR", "Atlas Copco", "KOHLER", "DeVillbiss/DeVair", "<PERSON>", "Chicago Pneumatic", "<PERSON>", "Detroit Diesel", "Asco", "Omega", "MTU", "<PERSON><PERSON>", "<PERSON><PERSON>", "Trystar", "Schneider Electric", "<PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 23, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Description", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A detailed description of the item, offering insights into its specifications and use.", "subsetOfAvailableValues": ["", "Oil Filter", "Air Filter", "Separator", "Fluid Analysis Kit", "and 1752 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "The current status of the item in the order process, indicating its progress.", "availableValues": ["Arrived", "Picked", "Returned", "Ordered", "Deleted", "In Stock"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Quantity", "dataType": "decimal", "description": "The quantity of the item being ordered, representing the number of units in the purchase order.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_OrderQty", "dataType": "decimal", "description": "The quantity of items ordered, which may reflect adjustments or returns.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Cost", "dataType": "decimal", "description": "The cost price of the item, indicating the expense incurred for its purchase.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_ExtendedCost", "dataType": "decimal", "description": "The total cost for the ordered items, calculated by multiplying quantity by cost.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Price", "dataType": "decimal", "description": "The selling price of the item, indicating the retail value assigned to it.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_ExtendedPrice", "dataType": "decimal", "description": "The total price for the sold quantity of items, calculated by multiplying quantity by price.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_ItemLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "An identifier for the line item in the purchase order, facilitating tracking of specific items.", "subsetOfAvailableValues": ["Parts-Compressor", "Parts-Generator", "Generator Oil", "Compressor Oil ", "Compressor Filtration", "and 49 more..."], "totalDistinctValueCount": 54, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_JobRevenue", "dataType": "decimal", "description": "The revenue generated from the job associated with this item, reflecting its financial impact.", "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "The category of the item, helping classify it within the context of the order.", "availableValues": ["Parts", "Miscellaneous", "Equipment", "Shipping", "Rental"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "The type of item, which may vary in classification such as parts, equipment, or freight.", "availableValues": ["Parts", "Expense", "Equipment", "Misc - taxed", "Freight"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_ReceivedByID", "dataType": "int", "description": "An integer identifier for the employee who received the item, aiding accountability in receipt processing.", "subsetOfAvailableValues": ["107985", "107989", "107994", "107998", "107999", "and 25 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_Item_ReceivedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the employee who received the item, providing clarity on the person responsible for receipt.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "and 25 more..."], "totalDistinctValueCount": 30, "is_unstructured": false}, {"name": "PurchaseOrderDetail_JobItemID", "dataType": "int", "description": "A unique identifier for the job item, ensuring distinct identification of job-related items.", "subsetOfAvailableValues": ["9538753", "9540335", "9599795", "9622241", "9655501", "and 10905 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_POLineID", "dataType": "int", "description": "A unique identifier for the purchase order line, allowing for precise tracking within the table.", "subsetOfAvailableValues": ["5400523", "5450135", "5478522", "5501121", "5511177", "and 14303 more..."], "is_unstructured": false}, {"name": "PurchaseOrderDetail_POModifiedDate", "dataType": "datetime", "description": "The date and time when the purchase order was last modified, critical for version control.", "is_unstructured": false}], "relationships": [{"child_table": "PurchaseOrderDetail", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[PurchaseOrderDetail.Supplier.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.AccountNumber]", "child_column": "[PurchaseOrderDetail.Supplier.AccountNo]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[PurchaseOrderDetail.Customer.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[PurchaseOrderDetail.OrderedBy.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[PurchaseOrderDetail.Item.ReceivedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[PurchaseOrderDetail.Job.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.Name]", "child_column": "[PurchaseOrderDetail.Job.Name]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[PurchaseOrderDetail.Item.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[PurchaseOrderDetail.Item.Code]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[PurchaseOrderDetail.Job.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "PurchaseOrderDetail", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[PurchaseOrderDetail.Job.Name]"}], "relationship_type": "one-to-many"}]}