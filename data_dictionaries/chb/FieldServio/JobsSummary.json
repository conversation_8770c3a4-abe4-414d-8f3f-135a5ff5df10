{"tableName": "JobsSummary", "description": "JobsSummary consolidates key information for each job at Air Unlimited, capturing job status, scheduling, financials, customer, contract, location, and departmental details. It links jobs to associated customers, employees, and contracts, and tracks proposals, bids, invoicing, and assigned resources. This table enables comprehensive reporting, operational tracking, and analysis of sales, service, and rental activities, supporting decision-making and performance management.", "fields": [{"name": "JobsSummary_ID", "dataType": "int", "description": "Unique integer identifier for each job record, serving as the primary key.", "subsetOfAvailableValues": ["1820803", "1820816", "1820820", "1820833", "1820841", "and 5059 more..."], "is_unstructured": false}, {"name": "JobsSummary_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "Unique varchar representing the job's name for identification and filtering.", "subsetOfAvailableValues": ["ESP-3681758-C", "PFB-3703521-C", "PMA-3678911-C", "PMA-3678927-C", "PMA-3678972-C", "and 5004 more..."], "totalDistinctValueCount": 5009, "is_unstructured": false}, {"name": "JobsSummary_ProjectName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the project related to the job; may be absent for some records.", "subsetOfAvailableValues": ["Spot Call", "Level 3", "Monthly Inspection", "Annual ", "Inspection", "and 2524 more..."], "totalDistinctValueCount": 2529, "is_unstructured": false}, {"name": "JobsSummary_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "Textual description of the job, aiding in understanding job specifics.", "subsetOfAvailableValues": ["", "Pick up", "Pickup", "SD300 - 3003544218", "Monthly (2 hours) - SD250 - 3011658702", "and 3561 more..."], "totalDistinctValueCount": 3566, "is_unstructured": false}, {"name": "JobsSummary_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "Current job status, indicating progress with categories like 'Completed' or 'In Progress'.", "availableValues": ["Completed", "Cancelled", "Waiting on Parts", "Ready to Schedule", "Scheduled", "In Progress", "On Hold"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "JobsSummary_InvoiceStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Reflects the invoicing state of the job, important for billing management.", "availableValues": ["Invoiced", "Not Invoiced", "Partially Invoiced"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "JobsSummary_InvoicesAllSent", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates whether all related invoices have been sent, with values 'Yes', 'No', or 'No Invoices'.", "availableValues": ["Yes", "No Invoices", "No"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "JobsSummary_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the job type, crucial for classification and reporting.", "subsetOfAvailableValues": ["Part Sale-COM", "Service Request-COM", "PM Fixed Bid - GEN", "Service Request - GEN", "PMA-COM", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "JobsSummary_DepartmentID", "dataType": "int", "description": "Integer categorizing the job into a specific department for responsibility tracking.", "availableValues": ["496", "497", "498"], "is_unstructured": false}, {"name": "JobsSummary_DepartmentName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Name of the department responsible for the job, aiding in identification.", "availableValues": ["Compressor", "Generator", "Rental "], "is_unstructured": false}, {"name": "JobsSummary_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the office associated with the job, useful for regional analysis.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsSummary_ContractID", "dataType": "int", "description": "Reference to the contract linked to the job, with less than 50% coverage.", "subsetOfAvailableValues": ["192343", "192351", "192361", "192362", "192363", "and 534 more..."], "is_unstructured": false}, {"name": "JobsSummary_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "Contract number associated with the job, typically unique for clusters of records.", "subsetOfAvailableValues": ["PFB74994", "PMA74990", "PFB209178", "PMA193579", "PFB75665", "and 534 more..."], "totalDistinctValueCount": 539, "is_unstructured": false}, {"name": "JobsSummary_Customer_ID", "dataType": "int", "description": "Integer referencing the ID of the customer linked to the job.", "subsetOfAvailableValues": ["379954", "380053", "380079", "380204", "380230", "and 1088 more..."], "is_unstructured": false}, {"name": "JobsSummary_Customer", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the customer associated with the job, important for identification.", "subsetOfAvailableValues": ["City of Winnipeg", "Air Unlimited Inc.", "Genrep Ltd.", "PCS Inc. (Nutrien)", "The Mosaic Company", "and 1086 more..."], "totalDistinctValueCount": 1091, "is_unstructured": false}, {"name": "JobsSummary_CustomerAccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "Account number of the customer, essential for financial tracking.", "subsetOfAvailableValues": ["CIT10642", "AIR00001", "GEN10792", "PCS10285", "THE10255", "and 1088 more..."], "totalDistinctValueCount": 1093, "is_unstructured": false}, {"name": "JobsSummary_JobLocation", "dataType": "<PERSON><PERSON><PERSON>", "description": "Location where the job is performed, aiding in logistical planning.", "subsetOfAvailableValues": ["Winnipeg-Head Office, 2116 Logan Ave, Winnipeg, MB R2R 0J2", "ARDENT MILLS, ULC., 95 33 St E, , Saskatoon, SK S7K 0R8", "GFL Environmental, 335 Mazenod Rd, , Winnipeg, MB R2J 3S8", "JCV Mechanical Inc, 1369 Border St, Winnipeg, MB R3H 0N1", "TOM BEGGS (USD), 2116 Logan Ave, Winnipeg, MB R2R 0J2", "and 1894 more..."], "totalDistinctValueCount": 1899, "is_unstructured": false}, {"name": "JobsSummary_Location_ID", "dataType": "int", "description": "Integer referencing the location ID associated with the job.", "subsetOfAvailableValues": ["658898", "658913", "658979", "659229", "659325", "and 1504 more..."], "is_unstructured": false}, {"name": "JobsSummary_Location_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the location related to the job, aiding in identification.", "subsetOfAvailableValues": ["Winnipeg-Head Office", "City of Winnipeg - Fleet Management Agency", "ARDENT MILLS, ULC.", "MacDon Industries Ltd.", "GFL Environmental", "and 1409 more..."], "totalDistinctValueCount": 1414, "is_unstructured": false}, {"name": "JobsSummary_CreatedByID", "dataType": "int", "description": "ID of the employee who created the job record, facilitating audits.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 29 more..."], "is_unstructured": false}, {"name": "JobsSummary_CreatedByName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the employee who created the job record, important for accountability.", "subsetOfAvailableValues": ["Dallas Skiftun", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "JobsSummary_ModifiedByID", "dataType": "int", "description": "ID of the employee who last modified the job record, tracking changes.", "subsetOfAvailableValues": ["106106", "107985", "107989", "107992", "107994", "and 23 more..."], "is_unstructured": false}, {"name": "JobsSummary_ModifiedByName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the employee who last modified the job record, crucial for auditing.", "subsetOfAvailableValues": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "and 23 more..."], "totalDistinctValueCount": 28, "is_unstructured": false}, {"name": "JobsSummary_ModifiedDate", "dataType": "datetime", "description": "Date and time when the job record was last modified, essential for tracking updates.", "is_unstructured": false}, {"name": "JobsSummary_SalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the salesperson associated with the job, important for performance analysis.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 18, "is_unstructured": false}, {"name": "JobsSummary_AssignedResourceID", "dataType": "int", "description": "Integer identifier for the resource assigned to the job, linking to employee details.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 31 more..."], "is_unstructured": false}, {"name": "JobsSummary_AssignedResourceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the employee assigned to the job, providing a human-readable reference.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "JobsSummary_Priority_Notes", "dataType": "<PERSON><PERSON><PERSON>", "description": "Optional notes regarding job priority, available for a small percentage of records.", "subsetOfAvailableValues": ["To enter, a key box has been installed near the main store entrance.\r\nThen, It will be necessary to deactivate the alarm and reactivate it at the end of the inspection In case of problem, I indicated the number of the monitoring vendor.\r\nLock box code: 4127\r\nAlarm code: 2020\r\nMonitoring: Vector / +1 (703) 468 6100", "Log in and out Information\r\nWKO-00426919\r\nPin- 22854\r\n", "Login and out information\r\nWKO-543492\r\nPin# 22854", "Login and out Information \r\nWKO-00454760\r\nPin# 22854", "Login and out Information\r\nWKO-00458732\r\nPin# 22854", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "JobsSummary_ScheduledDate", "dataType": "smalldatetime", "description": "Scheduled start date of the job, missing for nearly half of the records.", "is_unstructured": false}, {"name": "JobsSummary_PromisedDate", "dataType": "datetime", "description": "Date by which the job is promised to be completed, critical for customer expectations.", "is_unstructured": false}, {"name": "JobsSummary_CompletedDate", "dataType": "smalldatetime", "description": "Date when the job was completed, indicating the actual timeline.", "is_unstructured": false}, {"name": "JobsSummary_DateEntered", "dataType": "smalldatetime", "description": "Date when the job record was created, vital for tracking the job's lifecycle.", "is_unstructured": false}, {"name": "JobsSummary_QuoteAmount", "dataType": "decimal", "description": "Monetary amount quoted for the job, providing a financial estimate.", "is_unstructured": false}, {"name": "JobsSummary_Urgency", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the urgency level of the job, with most records categorized as 'Normal'.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsSummary_Priority", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the job's priority level, reflecting urgency distribution.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsSumary_CreditOverrideNote", "dataType": "<PERSON><PERSON><PERSON>", "description": "Notes related to any credit overrides for the job, available for a small percentage.", "subsetOfAvailableValues": ["Sep  4 2024 Credit Limit/Status <NAME_EMAIL> - SR approved - over limit due to pending Jobs", "Aug 29 2024 Credit Limit/Status <NAME_EMAIL> - Oct PM", "May  5 2025 Credit Limit/Status <NAME_EMAIL> - Override - CT", "Sep 14 2024 Credit Limit/Status <NAME_EMAIL> - SR approved - over limit due to project", "Sep 16 2024 Credit Limit/Status <NAME_EMAIL> - SR approved - over limit due to pending jobs", "and 237 more..."], "totalDistinctValueCount": 242, "is_unstructured": false}, {"name": "JobsSummary_FirstProposalSentDate", "dataType": "datetime", "description": "Date when the first proposal was sent for the job, aiding in tracking timelines.", "is_unstructured": false}, {"name": "JobsSummary_CurrentProposalSentDate", "dataType": "datetime", "description": "Date of the current proposal sent, similar to FirstProposalSentDate.", "is_unstructured": false}, {"name": "JobsSummary_ProposalSentQty", "dataType": "int", "description": "Indicates the quantity of proposals sent for the job, with values under 100.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 1 more..."], "is_unstructured": false}, {"name": "JobsSummary_BidCreatedDate", "dataType": "smalldatetime", "description": "Date when the bid for the job was created, serving as a timestamp.", "is_unstructured": false}, {"name": "JobsSummary_BidCreatedByID", "dataType": "int", "description": "Identifier for the employee who created the bid, linking to employee details.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 29 more..."], "is_unstructured": false}, {"name": "JobsSummary_BidCreatedByName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the employee who created the bid, providing clarity on the initiator.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "JobsSummary_BidModifiedDate", "dataType": "datetime", "description": "Date when the bid was last modified, tracking changes post-creation.", "is_unstructured": false}, {"name": "JobsSummary_BidModifiedByID", "dataType": "int", "description": "Identifier for the employee who modified the bid, linking to employee details.", "subsetOfAvailableValues": ["106106", "107985", "107989", "107992", "107994", "and 38 more..."], "is_unstructured": false}, {"name": "JobsSummary_BidModifiedByName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the employee who modified the bid, providing accountability.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "and 36 more..."], "totalDistinctValueCount": 41, "is_unstructured": false}, {"name": "JobsSummary_BidAssignedResourceID", "dataType": "int", "description": "Identifier for the resource assigned to the bid, linking to employee details.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 31 more..."], "is_unstructured": false}, {"name": "JobsSummary_BidAssignedResourceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the employee assigned to the bid, providing a reference for accountability.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "JobsSummary_BidOrigDescription", "dataType": "<PERSON><PERSON><PERSON>", "description": "Unique description of the bid for each record, crucial for differentiation.", "subsetOfAvailableValues": ["", "Pick up", "1.6 MW - **********", "125ROZP-91 - 391086", "2.0 MW - **********", "and 2809 more..."], "totalDistinctValueCount": 2814, "is_unstructured": false}], "relationships": [{"child_table": "JobsSummary", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[JobsSummary.ContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.Number]", "child_column": "[JobsSummary.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[JobsSummary.Customer.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.Name]", "child_column": "[JobsSummary.Customer]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.Account<PERSON><PERSON>ber]", "child_column": "[JobsSummary.CustomerAccountNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsSummary.CreatedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsSummary.ModifiedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsSummary.AssignedResourceID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsSummary.BidCreatedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsSummary.BidModifiedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsSummary.BidAssignedResourceID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsSummary", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[JobsSummary.ID]"}], "relationship_type": "one-to-one"}, {"child_table": "JobsSummary", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.Name]", "child_column": "[JobsSummary.Name]"}], "relationship_type": "one-to-one"}, {"child_table": "JobsSummary", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[JobsSummary.Location.ID]"}], "relationship_type": "one-to-many"}]}