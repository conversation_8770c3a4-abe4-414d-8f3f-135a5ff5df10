{"tableName": "GeneralLedgerDetail", "description": "GeneralLedgerDetail stores comprehensive general ledger transaction details, capturing account classifications, amounts, debits, credits, and related context such as office, department, customer, invoice, job, and bill information. This table enables granular financial tracking, supports multi-dimensional reporting and analysis by business segment or region, and serves as the primary source for audits, financial statements, and operational performance evaluation across Air Unlimited.", "fields": [{"name": "GeneralLedger_ChartType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the account type in the general ledger, with possible values including Revenues, Assets, Liabilities, Expenses, and Shareholder Equity.", "availableValues": ["Assets", "Liabilities", "Expenses", "Revenues", "Shareholder Equity"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "GeneralLedger_ChartName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Descriptive label for the account as it appears in the general ledger, providing clarity on the account's purpose.", "subsetOfAvailableValues": ["Accounts payable and accrued liabilities", "Inventory", "Cash", "Accounts receivable", "Revenue", "and 37 more..."], "totalDistinctValueCount": 42, "is_unstructured": false}, {"name": "GeneralLedger_SubTypeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Further classifies the account to give specific insights into the nature of transactions recorded within it.", "subsetOfAvailableValues": ["Accounts Payable", "Accounts receivable", "Inventory - Work-in-Progress", "Inventory Clearing", "A/R - Clearing", "and 160 more..."], "totalDistinctValueCount": 165, "is_unstructured": false}, {"name": "GeneralLedger_Code", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique identifier for each account in the general ledger, essential for transaction categorization.", "subsetOfAvailableValues": ["1020", "1025", "1100", "1300", "1710", "and 160 more..."], "totalDistinctValueCount": 165, "is_unstructured": false}, {"name": "GeneralLedger_Amount", "dataType": "decimal", "description": "Represents the total monetary value of the transaction, accommodating both positive and negative values for credits and debits.", "is_unstructured": false}, {"name": "GeneralLedger_Debit", "dataType": "decimal", "description": "The amount debited in the transaction, indicating increases in assets or expenses, or decreases in liabilities or equity.", "is_unstructured": false}, {"name": "GeneralLedger_Credit", "dataType": "decimal", "description": "The amount credited in the transaction, reflecting decreases in assets or expenses and increases in liabilities or equity.", "is_unstructured": false}, {"name": "GeneralLedger_TransactionID", "dataType": "int", "description": "A unique identifier for the transaction, typically shared among three related records, aiding in tracking entries.", "subsetOfAvailableValues": ["33280705", "33343220", "33762442", "34110228", "34261956", "and 34631 more..."], "is_unstructured": false}, {"name": "GeneralLedger_TransactionDate", "dataType": "datetime", "description": "Records the date and time of the transaction, critical for tracking financial activity timelines.", "is_unstructured": false}, {"name": "GeneralLedger_TransactionType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the nature of the transaction, including various processes like invoices, payments, and adjustments.", "subsetOfAvailableValues": ["Process Invoice - A/R", "<PERSON> Approved", "Bill - Add Payment", "Invoice - Add Payment", "Bill Added - Bill Date Change", "and 61 more..."], "totalDistinctValueCount": 66, "is_unstructured": false}, {"name": "GeneralLedger_CreatedByName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the individual who created the transaction entry, ensuring accountability and traceability.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "and 39 more..."], "totalDistinctValueCount": 44, "is_unstructured": false}, {"name": "GeneralLedger_OfficeID", "dataType": "int", "description": "Identifier for the office linked to the transaction, aiding in the categorization of transactions by location.", "availableValues": ["419", "420", "421"], "is_unstructured": false}, {"name": "GeneralLedger_OfficeAcctCode", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Code representing the financial account associated with the office, utilized for reporting and analysis.", "availableValues": ["100", "200", "300"], "is_unstructured": false}, {"name": "GeneralLedger_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the office related to the transaction, providing context for the transaction's origin.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "GeneralLedger_DepartmentID", "dataType": "int", "description": "Identifier for the department associated with the transaction, available for less than half of the records.", "availableValues": ["496", "497", "498"], "is_unstructured": false}, {"name": "GeneralLedger_DepartmentName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Name of the department linked to the transaction, available for less than half of the records.", "availableValues": ["Compressor", "Generator", "Rental "], "is_unstructured": false}, {"name": "GeneralLedger_DepartmentAcctCode", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Code representing the financial account of the department, available for less than half of the records.", "availableValues": ["10", "20", "30"], "is_unstructured": false}, {"name": "GeneralLedger_CustomerId", "dataType": "int", "description": "Identifier for the customer associated with the transaction, connecting specific clients to their transactions.", "subsetOfAvailableValues": ["0", "380079", "380204", "380230", "380504", "and 1115 more..."], "is_unstructured": false}, {"name": "GeneralLedger_CustomerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the customer involved in the transaction, providing clarity on the financial interaction.", "subsetOfAvailableValues": ["None", "Genrep Ltd.", "City of Winnipeg", "BPL Sales Ltd.", "PCS Inc. (Nutrien)", "and 1111 more..."], "totalDistinctValueCount": 1116, "is_unstructured": false}, {"name": "GeneralLedger_CustomerAccountNum", "dataType": "<PERSON><PERSON><PERSON>", "description": "Account number for the customer related to the transaction, essential for financial tracking.", "subsetOfAvailableValues": ["None", "GEN10792", "CIT10642", "BPL10579", "PCS10285", "and 1115 more..."], "totalDistinctValueCount": 1120, "is_unstructured": false}, {"name": "GeneralLedger_LocationID", "dataType": "int", "description": "Identifier for the location associated with the transaction, available for less than half of the records.", "subsetOfAvailableValues": ["658879", "658884", "658898", "658900", "658913", "and 1366 more..."], "is_unstructured": false}, {"name": "GeneralLedger_LocationName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the location linked to the transaction, available for less than half of the records.", "subsetOfAvailableValues": ["City of Winnipeg - Fleet Management Agency", "TOM BEGGS (USD)", "MacDon Industries Ltd.", "GFL Environmental", "Winnipeg Transit", "and 1290 more..."], "totalDistinctValueCount": 1295, "is_unstructured": false}, {"name": "GeneralLedger_InvoiceID", "dataType": "int", "description": "Identifier for the invoice associated with the transaction, typically unique for a group of 15 related records.", "subsetOfAvailableValues": ["2611890", "2616687", "2618075", "2619167", "2635537", "and 7269 more..."], "is_unstructured": false}, {"name": "GeneralLedger_InvoiceVoidedDate", "dataType": "datetime", "description": "Date when the invoice was voided, available for a small percentage of records.", "is_unstructured": false}, {"name": "GeneralLedger_InvoiceDate", "dataType": "datetime", "description": "Date when the invoice was issued, providing context for billing timelines.", "is_unstructured": false}, {"name": "GeneralLedger_InvoiceSentDate", "dataType": "datetime", "description": "Records the date and time when the invoice was sent, available for less than half of the records.", "is_unstructured": false}, {"name": "GeneralLedger_InvoiceSentTransactionDate", "dataType": "datetime", "description": "Transaction date associated with the invoice being sent, recorded as datetime.", "is_unstructured": false}, {"name": "GeneralLedger_InvoiceDueDate", "dataType": "datetime", "description": "Due date for the invoice, indicating when payment is expected, recorded as datetime.", "is_unstructured": false}, {"name": "GeneralLedger_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name or identifier of the invoice, typically unique for a group of 15 records, available for less than half of the records.", "subsetOfAvailableValues": ["8488-1", "8637-1", "4474-1", "9819-1", "5666-1", "and 7268 more..."], "totalDistinctValueCount": 7273, "is_unstructured": false}, {"name": "GeneralLedger_InvoiceNotes", "dataType": "<PERSON><PERSON><PERSON>", "description": "Additional notes related to the invoice, available for less than half of the records.", "subsetOfAvailableValues": ["\n\rProposal Terms and Conditions\r\n\r\nGST#100095942RT0001\r\n\r\nThe presented proposal is preliminary; additional components may be necessary and are extra.\r\n\r\nApplicable shipping fees may be incurred, with charges payable based on the actual freight cost.\r\n\r\nAir Unlimited does not assume liability for delays in freight and transit\r\n\r\nThis proposal remains valid for 15 days from proposal date and it subject to potential alterations.\r\n\r\nFor clients with an established account, our standard payment terms apply. If you do not have an established account, prepayment will be required prior to parts ordered and/or the commencement of services.\r\n\r\nNOTES: All credit card payments are subject to a 3% processing fee\r\n\r\nUnits Return Policy - No returns on units allowed.\r\n\r\nParts Return Policy - Please refer to below webpage.\r\n\r\nFor all other details please refer to Terms & Conditions | Air Unlimited Inc. ", "\n\rTerms and Conditions\r\n \r\nTERMS: \r\nPayment terms net 30 days unless otherwise noted above. Overdue accounts charged interest of 2% per month (24% per annum) on unpaid balances. \r\n\r\nNOTES:  \r\nAll credit card payments are subject to a 3% processing fee. \r\n\r\nUnits Return Policy - No returns on units allowed. \r\n\r\nParts Return Policy - Please refer to below webpage.  \r\n\r\nFor all other details please refer to www.airunlimited.ca/termsandconditions  ", "", "\n\rGST#100095942RT0001\r\n\r\nThe presented estimation is preliminary; additional components might be necessary.\r\n\r\nApplicable shipping fees may be incurred, with charges payable based on actual freight cost.\r\n\r\nAir Unlimited does not assume liability for delays in freight and transit.\r\n\r\nThis estimate remains valid for 15 days and is subject to potential alterations. \r\n\r\nTERMS: For clients with an established account, our standard payment terms apply. If you do not have an established account, prepayment will be required prior to the commencement of services.\r\n\r\nAll credit card payments are subject to a 3% processing fee.\r\n\r\nUnits Return Policy - No returns on units allowed.\r\n\r\nParts Return Policy - Please refer to below webpage.\r\n\r\nFor all other details please refer to https://www.airunlimited.ca/terms-and-conditions", "\n\rGST# 100095942RT0001\r\n\r\nTerms and Conditions\r\n \r\nTERMS: \r\nPayment terms net 30 days unless otherwise noted above. Overdue accounts charged interest of 2% per month (24% per annum) on unpaid balances. \r\n\r\nNOTES:  \r\nAll credit card payments are subject to a 3% processing fee. \r\n\r\nUnits Return Policy - No returns on units allowed. \r\n\r\nParts Return Policy - Please refer to below webpage.  \r\n\r\nFor all other details please refer to www.airunlimited.ca/termsandconditions  ", "and 1182 more..."], "totalDistinctValueCount": 1187, "is_unstructured": false}, {"name": "GeneralLedger_JobID", "dataType": "int", "description": "Unique identifier for a job linked to the invoice, available for less than half of the records.", "subsetOfAvailableValues": ["1826573", "1827300", "1827353", "1829579", "1830152", "and 3881 more..."], "is_unstructured": false}, {"name": "GeneralLedger_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Name of the job connected to the invoice, with limited availability of data.", "subsetOfAvailableValues": ["ES-3883061", "ESP-3704176", "SR-3759380", "ESP-3699163", "ESP-3717535", "and 3881 more..."], "totalDistinctValueCount": 3886, "is_unstructured": false}, {"name": "GeneralLedger_JobType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies the type of job related to the invoice, with limited data availability.", "subsetOfAvailableValues": ["Part Sale-COM", "Service Request-COM", "PMA-COM", "PM Fixed Bid - GEN", "Service Request - GEN", "and 23 more..."], "totalDistinctValueCount": 28, "is_unstructured": false}, {"name": "GeneralLedger_JobDescription", "dataType": "<PERSON><PERSON><PERSON>", "description": "Description of the job associated with the invoice, typically unique for a group of 40 records.", "subsetOfAvailableValues": ["", "Pick up", "Start up and commissioning - 8:30am", "Northern Lights Personal Care Home - <PERSON><PERSON> Flon, MB\r\nSD0200GG178.7D18HPNN3 / 3014205600\r\nÃ‚H07ATBA30800C5XM / 2483808-001WE\r\n", "Pickup", "and 2829 more..."], "totalDistinctValueCount": 2834, "is_unstructured": false}, {"name": "GeneralLedger_InvoiceSalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the salesperson responsible for the invoice, with limited data availability.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 21, "is_unstructured": false}, {"name": "GeneralLedger_CustomerAssignedSalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "Identifies the salesperson assigned to the customer, with very limited data availability.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 15, "is_unstructured": false}, {"name": "GeneralLedger_BillID", "dataType": "int", "description": "Unique identifier for the bill related to the invoice, average uniqueness for a group of 18 records.", "subsetOfAvailableValues": ["1488079", "1488970", "1492960", "1520617", "1523810", "and 6045 more..."], "is_unstructured": false}, {"name": "GeneralLedger_BillRefNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "Reference number for the bill, typically unique for a group of 21 records.", "subsetOfAvailableValues": ["106324", "107200", "802673", "1631941", "1632800", "and 5249 more..."], "totalDistinctValueCount": 5254, "is_unstructured": false}, {"name": "GeneralLedger_BillMemo", "dataType": "<PERSON><PERSON><PERSON>", "description": "Allows for additional memos relating to the bill, with limited data availability.", "subsetOfAvailableValues": ["Items Returned to supplier", "", "Import", "Bill Created for PO: SO1059", "Bill Created for PO: SO2190", "and 4165 more..."], "totalDistinctValueCount": 4170, "is_unstructured": false}, {"name": "GeneralLedger_BillStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the current status of the bill, with possible values such as 'Paid in Full', 'Voided', or 'Pending Approval'.", "availableValues": ["Paid in Full", "", "Voided", "Pending Approval"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "GeneralLedger_PaymentNotes", "dataType": "<PERSON><PERSON><PERSON>", "description": "Notes related to payments associated with the bill, with data available for less than half of the records.", "subsetOfAvailableValues": ["", "Auto Payment for Process Internal Job Type", "EFT", "Spot Rate Change", "AP Batch: 12329", "and 2619 more..."], "totalDistinctValueCount": 2624, "is_unstructured": false}, {"name": "GeneralLedger_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "Description associated with the general ledger entry, typically unique for a group of 3 records.", "subsetOfAvailableValues": ["Average Costs", "Inventory Adjustment", "Trial Balance Import - AirUnlimited", "Bill 313088 & 316439 for Ingersoll-Rand Canada Sales & Service ULC", "Bill 796849&797042 for Oil Mart Ltd", "and 31307 more..."], "totalDistinctValueCount": 31312, "is_unstructured": false}], "relationships": [{"child_table": "GeneralLedgerDetail", "parent_table": "InvoiceSummary", "key_column_mapping": [{"parent_column": "[InvoiceSummary.InvoiceID]", "child_column": "[GeneralLedger.InvoiceID]"}], "relationship_type": "one-to-many"}, {"child_table": "GeneralLedgerDetail", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[GeneralLedger.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "GeneralLedgerDetail", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[GeneralLedger.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "GeneralLedgerDetail", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[GeneralLedger.LocationID]"}], "relationship_type": "one-to-many"}]}