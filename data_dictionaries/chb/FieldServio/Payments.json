{"tableName": "Payments", "description": "The Payments table captures all financial transactions involving customer invoices and vendor bills, supporting diverse payment types, methods, and currencies. Each record tracks payment amounts, currency details, key dates (creation, payment, processing, updates), and status. Flexible links to invoices, bills, customers, or vendors enable detailed cash flow tracking and reconciliation. This table underpins financial, audit, and operational reporting for Air Unlimited, supporting comprehensive analysis of payment activity across its air and power solutions business.", "fields": [{"name": "Payment_ID", "dataType": "int", "description": "A unique identifier for each payment entry, ensuring distinct reference for every transaction within the Payments table.", "subsetOfAvailableValues": ["3073833", "3123445", "3149130", "3167630", "3228503", "and 14086 more..."], "is_unstructured": false}, {"name": "Payment_EntityType", "dataType": "<PERSON><PERSON><PERSON>", "description": "Categorizes the payment type, with possible values including 'Bill', 'Invoice', 'Customer Credit', 'Other', and 'A/R Deposit', clarifying the nature of the payment.", "availableValues": ["Bill", "Invoice", "Customer Credit", "Other", "A/R Deposit"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "Payment_BankingTransactionID", "dataType": "int", "description": "Links to a specific banking transaction; this field is absent in about 26% of records and may be shared among multiple payments.", "subsetOfAvailableValues": ["1479375", "1503653", "1526219", "1566962", "1574349", "and 5261 more..."], "is_unstructured": false}, {"name": "Payment_InvoiceId", "dataType": "int", "description": "Connects the payment to a specific invoice; present in only 52% of records, suggesting that multiple payments can be associated with one invoice.", "subsetOfAvailableValues": ["2579638", "2579639", "2579640", "2579642", "2579945", "and 6325 more..."], "is_unstructured": false}, {"name": "Payment_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Holds the name associated with the invoice linked to the payment, missing in about 48% of records and often shared among related payments.", "subsetOfAvailableValues": ["CHQ 2422", "**********", "EFT", "8380-1", "10364-1", "and 6316 more..."], "totalDistinctValueCount": 6321, "is_unstructured": false}, {"name": "Payment_InvoiceCustomerID", "dataType": "int", "description": "Identifies the customer related to the invoice for the payment; this field is absent in 48% of records, indicating multiple payments may link to the same customer.", "subsetOfAvailableValues": ["380124", "380204", "380230", "380356", "380428", "and 1079 more..."], "is_unstructured": false}, {"name": "Payment_BillID", "dataType": "int", "description": "Indicates the ID of the corresponding bill for the payment; populated for less than half of the records, typically unique for pairs of records.", "subsetOfAvailableValues": ["1529163", "1540163", "1541672", "1556201", "1557223", "and 5517 more..."], "is_unstructured": false}, {"name": "Payment_BillReference", "dataType": "<PERSON><PERSON><PERSON>", "description": "Contains a reference name for the bill associated with the payment, available for fewer than 50% of records and usually shared among related payments.", "subsetOfAvailableValues": ["6916424", "6917614", "6922873", "6943680", "6955498", "and 4847 more..."], "totalDistinctValueCount": 4852, "is_unstructured": false}, {"name": "Payment_BillVendorID", "dataType": "int", "description": "Identifies the vendor linked to the bill; this field is present in less than 50% of records and typically unique across groups of records.", "subsetOfAvailableValues": ["81061", "81111", "81233", "81247", "81297", "and 358 more..."], "is_unstructured": false}, {"name": "Payment_PaymentAmount", "dataType": "money", "description": "Records the total amount of the payment as a monetary value, which may include both positive and negative figures reflecting adjustments.", "is_unstructured": false}, {"name": "Payment_CurrencyName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "Specifies the currency in which the payment is made, with the majority in Canadian Dollars and a smaller portion in US Dollars.", "availableValues": ["Canadian Dollars", "US Dollars"], "is_unstructured": false}, {"name": "Payment_CurrencyRate", "dataType": "decimal", "description": "Represents the applicable exchange rate for the payment, always a positive decimal value typically below 100.", "is_unstructured": false}, {"name": "Payment_CurrencyAmount", "dataType": "decimal", "description": "Shows the payment amount in an alternative currency, which may be positive or negative, indicating conversion or adjustments.", "is_unstructured": false}, {"name": "Payment_CreatedDate", "dataType": "datetime", "description": "Records the date and time when the payment entry was created, aiding the tracking of payment history.", "is_unstructured": false}, {"name": "Payment_PaymentDate", "dataType": "datetime", "description": "Denotes the actual date on which the payment was processed, essential for financial tracking and reporting.", "is_unstructured": false}, {"name": "Payment_ProcessedDate", "dataType": "datetime", "description": "Captures the date when the payment was fully processed, critical for auditing purposes and payment completion tracking.", "is_unstructured": false}, {"name": "Payment_UpdatedDate", "dataType": "datetime", "description": "Indicates the last date when the payment record was modified, facilitating version control and change tracking.", "is_unstructured": false}, {"name": "Payment_PaymentRecordSource", "dataType": "<PERSON><PERSON><PERSON>", "description": "Provides context on the source or method through which the payment record was generated.", "availableValues": ["Paid a bill.", "Paid an invoice.", "Migrated from Invoices_Credits", "Exchange Rate Adjustment", "Other Deposit Record.", "Payment from Account.", "Credit Offset Applied", "Credit for Cash Deposit.", "Customer Credit - Refund Credit", "Auto Payment to RMA"], "totalDistinctValueCount": 10, "is_unstructured": false}, {"name": "Payment_PaymentNotes", "dataType": "<PERSON><PERSON><PERSON>", "description": "Allows for additional notes or comments related to the payment; typically contains unique information relevant to grouped records.", "subsetOfAvailableValues": ["", "Auto Payment for Process Internal Job Type", "EFT", "Spot Rate Change", "AP Batch: 12329", "and 4365 more..."], "totalDistinctValueCount": 4370, "is_unstructured": false}, {"name": "Payment_CreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the user who created the payment record, bolstering accountability and tracking of user actions.", "availableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Support User", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 13, "is_unstructured": false}, {"name": "Payment_TypeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "Classifies the payment method, with options including 'Check', 'Manual', 'EFT', 'Visa', 'MasterCard', and more, reflecting the various payment processing methods.", "availableValues": ["Manual", "EFT", "Credit Offset", "Check", "Exchange Rate Adjustment", "Visa", "MasterCard", "Cash"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "Payment_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the current state of the payment, with most records marked as 'Processed' and a small percentage as 'Voided', offering insight into payment completion.", "availableValues": ["Processed", "Voided"], "totalDistinctValueCount": 2, "is_unstructured": false}], "relationships": [{"child_table": "Payments", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[Payment.BillVendorID]"}], "relationship_type": "one-to-many"}, {"child_table": "Payments", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[Payment.InvoiceCustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "Payments", "parent_table": "InvoiceSummary", "key_column_mapping": [{"parent_column": "[InvoiceSummary.InvoiceID]", "child_column": "[Payment.InvoiceId]"}], "relationship_type": "one-to-many"}, {"child_table": "Payments", "parent_table": "InvoiceSummary", "key_column_mapping": [{"parent_column": "[InvoiceSummary.InvoiceName]", "child_column": "[Payment.InvoiceName]"}], "relationship_type": "one-to-many"}]}