{"tableName": "InventoryArchive", "description": "InventoryArchive records historical inventory data by office, including item identifiers, names, types (Inventory, WIP, Returns), quantities, and costs as of each archive date. It enables tracking inventory changes over time, supporting financial analysis and audit requirements. ItemID and ItemCode reference the master Items table but may not be unique. This table is vital for analyzing past inventory levels, valuation, and office-level inventory trends.", "fields": [{"name": "InventoryArchive_ArchiveDate", "dataType": "date", "description": "The date when the inventory record was archived, serving as a reference point for tracking historical inventory changes.", "is_unstructured": false}, {"name": "InventoryArchive_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the office managing the inventory record, indicating the specific location of inventory handling.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryArchive_ItemID", "dataType": "int", "description": "An integer identifier for each inventory item, primarily used for linking to the parent 'Items' table, though it may not be unique across all records.", "subsetOfAvailableValues": ["2826815", "2827391", "2827693", "2827695", "2827707", "and 3857 more..."], "is_unstructured": false}, {"name": "InventoryArchive_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar code uniquely assigned to each inventory item, facilitating item reference within the parent 'Items' table, with some non-uniqueness across records.", "subsetOfAvailableValues": ["22421853", "22806889", "23564446", "23973969", "24121212", "and 3856 more..."], "totalDistinctValueCount": 3861, "is_unstructured": false}, {"name": "InventoryArchive_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "The human-readable name of the inventory item, aiding in identification and displayed in a user-friendly format.", "subsetOfAvailableValues": ["22421853", "22806889", "23564446", "23973969", "24121212", "and 3855 more..."], "is_unstructured": false}, {"name": "InventoryArchive_InventoryType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field categorizing the inventory as either 'Inventory', 'WIP' (Work in Progress), or 'Returns', useful for inventory reporting and management.", "availableValues": ["Inventory", "WIP", "Returns"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InventoryArchive_Quantity", "dataType": "decimal", "description": "A decimal representing the exact count of the inventory item available, crucial for inventory management and reporting accuracy.", "is_unstructured": false}, {"name": "InventoryArchive_Cost", "dataType": "decimal", "description": "A decimal indicating the financial cost associated with each inventory item, essential for financial analysis and accounting purposes.", "is_unstructured": false}], "relationships": [{"child_table": "InventoryArchive", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[InventoryArchive.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryArchive", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryArchive.ItemCode]"}], "relationship_type": "one-to-many"}]}