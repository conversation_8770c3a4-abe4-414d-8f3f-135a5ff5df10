{"tableName": "JobsDetail", "description": "JobsDetail stores detailed records for each job, including project, customer, location, item, contract, and contact information. It tracks job status, scheduling, assigned personnel, resource allocation, and financial metrics such as costs, quantities, and revenue. This table supports comprehensive tracking, analysis, and reporting of service, sales, and rental jobs across Air Unlimited’s operations, enabling effective management of projects, resources, and customer relationships.", "fields": [{"name": "JobsDetail_ID", "dataType": "int", "description": "A unique integer identifier for each job detail entry, ensuring distinct identification of records within the database.", "subsetOfAvailableValues": ["1821278", "1821388", "1821552", "1821556", "1827300", "and 5059 more..."], "is_unstructured": false}, {"name": "JobsDetail_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string that specifies the name of the job, aiding in identification and grouping of related records.", "subsetOfAvailableValues": ["PS-3678884-C", "ESP-3703966", "PMA-3679078-C", "PMA-3679079-C", "PMA-3678927-C", "and 5004 more..."], "totalDistinctValueCount": 5009, "is_unstructured": false}, {"name": "JobsDetail_ProjectName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string denoting the name of the project associated with the job, which may be absent in some records and is used for project categorization.", "subsetOfAvailableValues": ["Level 3", "Annual", "Spot call", "Level 1", "Level 2", "and 2524 more..."], "totalDistinctValueCount": 2529, "is_unstructured": false}, {"name": "JobsDetail_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed string description of the job being performed, providing context and specifics about the job's scope.", "subsetOfAvailableValues": ["", "SD130 - 9560105", "Pick up", "UP6-7.5-150 - CBV579328", "<PERSON> ", "and 3561 more..."], "totalDistinctValueCount": 3566, "is_unstructured": false}, {"name": "JobsDetail_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical string that reflects the current status of the job, with predefined values indicating its progress.", "availableValues": ["Completed", "Cancelled", "Waiting on Parts", "Ready to Schedule", "Scheduled", "In Progress", "On Hold"], "totalDistinctValueCount": 7, "is_unstructured": false}, {"name": "JobsDetail_CustomerPO", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the customer purchase order linked to the job, essential for tracking customer agreements.", "subsetOfAvailableValues": ["728389", "305247422", "PM Service", "", "TBD", "and 2706 more..."], "totalDistinctValueCount": 2711, "is_unstructured": false}, {"name": "JobsDetail_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that classifies the type of job being performed, used for categorization and reporting purposes.", "subsetOfAvailableValues": ["Service Request-COM", "PM Fixed Bid - GEN", "Part Sale-COM", "PMA-COM", "Service Request - GEN", "and 21 more..."], "totalDistinctValueCount": 26, "is_unstructured": false}, {"name": "JobsDetail_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the office associated with the job, helping to identify the geographical responsibility for job execution.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsDetail_DepartmentID", "dataType": "int", "description": "An integer that identifies the department responsible for handling the job, facilitating departmental tracking.", "availableValues": ["496", "497", "498"], "is_unstructured": false}, {"name": "JobsDetail_DepartmentName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the name of the department managing the job, clarifying the organizational structure.", "availableValues": ["Compressor", "Generator", "Rental "], "is_unstructured": false}, {"name": "JobsDetail_EnteredBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the user who originally entered the job details, important for audit trails.", "subsetOfAvailableValues": ["Dallas Skiftun", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}, {"name": "JobsDetail_ModifiedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that identifies the user who last modified the job details, enhancing accountability for changes.", "subsetOfAvailableValues": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "and 23 more..."], "totalDistinctValueCount": 28, "is_unstructured": false}, {"name": "JobsDetail_SalesPerson", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string denoting the sales person assigned to the job, relevant for sales tracking and accountability.", "availableValues": ["<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 18, "is_unstructured": false}, {"name": "JobsDetail_Technician", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the technician assigned to the job, crucial for resource allocation and tracking.", "subsetOfAvailableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "JobsDetail_AssignedResource", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string listing the resources allocated to the job, used for managing job execution effectively.", "subsetOfAvailableValues": ["<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "JobsDetail_EstimatedDuration", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that provides an estimate of the job's duration, guiding scheduling and planning efforts.", "subsetOfAvailableValues": ["0", "1", "2", "3", "4", "and 39 more..."], "totalDistinctValueCount": 44, "is_unstructured": false}, {"name": "JobsDetail_EstimatedDays", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string estimating the number of days required to complete the job, serving as a planning metric.", "availableValues": ["0", "1", "2", "3", "4", "5", "6", "8", "2.5", "", "2.25"], "totalDistinctValueCount": 11, "is_unstructured": false}, {"name": "JobsDetail_ScheduledDate", "dataType": "smalldatetime", "description": "A datetime field specifying when the job is scheduled to take place, important for resource planning.", "is_unstructured": false}, {"name": "JobsDetail_PromisedDate", "dataType": "datetime", "description": "A datetime field indicating the promised completion date for the job, essential for customer expectations.", "is_unstructured": false}, {"name": "JobsDetail_CompletedDate", "dataType": "smalldatetime", "description": "A datetime field representing when the job was finished, critical for performance tracking.", "is_unstructured": false}, {"name": "JobsDetail_POFufilledDate", "dataType": "smalldatetime", "description": "A datetime field showing when the purchase order related to the job was fulfilled, important for procurement tracking.", "is_unstructured": false}, {"name": "JobsDetail_PODeliveryDate", "dataType": "smalldatetime", "description": "A datetime field indicating when the purchase order was delivered, relevant for tracking delivery timelines.", "is_unstructured": false}, {"name": "JobsDetail_POPromiseDate", "dataType": "datetime", "description": "A datetime field that captures when the purchase order is promised to be fulfilled, significant for managing customer expectations.", "is_unstructured": false}, {"name": "JobsDetail_ModifiedDate", "dataType": "datetime", "description": "A datetime field that records the last modification date of the job details, aiding in tracking changes over time.", "is_unstructured": false}, {"name": "JobsDetail_InvoiceDate", "dataType": "datetime", "description": "A datetime field indicating when the job invoice was generated, important for financial tracking.", "is_unstructured": false}, {"name": "JobsDetail_DateEntered", "dataType": "smalldatetime", "description": "The datetime when the job detail record was created in the system, useful for historical tracking.", "is_unstructured": false}, {"name": "JobsDetail_ContractID", "dataType": "int", "description": "An integer identifier for the contract associated with the job detail, aiding in contract management.", "subsetOfAvailableValues": ["192363", "192395", "192401", "192901", "193384", "and 534 more..."], "is_unstructured": false}, {"name": "JobsDetail_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representation of the contract number linked to the job detail, essential for contract tracking.", "subsetOfAvailableValues": ["PFB209178", "PFB74994", "PMA193811", "PMA74990", "PFB75665", "and 534 more..."], "totalDistinctValueCount": 539, "is_unstructured": false}, {"name": "JobsDetail_Urgency", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the urgency level of the job, helping prioritize jobs based on urgency.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsDetail_Priority", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string reflecting the priority level of the job, used for task prioritization and management.", "availableValues": ["2 - Normal", "1 - High/Rush"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsDetail_PriorityNotes", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing additional notes regarding the job's priority, providing context for prioritization decisions.", "subsetOfAvailableValues": ["To enter, a key box has been installed near the main store entrance.\r\nThen, It will be necessary to deactivate the alarm and reactivate it at the end of the inspection In case of problem, I indicated the number of the monitoring vendor.\r\nLock box code: 4127\r\nAlarm code: 2020\r\nMonitoring: Vector / +1 (703) 468 6100", "<PERSON> will pay the labour when the parts arrive in", "Level 1 - 4 hours not 3 hours ", "Login and out Information \r\nWKO-00504384\r\nPin# 22854\r\n", "Login and out Information\r\nWKO-00504382\r\nPin# 22854", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "JobsDetail_JobItemLineID", "dataType": "int", "description": "An integer uniquely identifying each job item line within the job detail records, ensuring distinct tracking of item entries.", "subsetOfAvailableValues": ["9539045", "9543436", "9625726", "9707924", "9707970", "and 23152 more..."], "is_unstructured": false}, {"name": "JobsDetail_Item", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the item linked to the job detail, crucial for inventory and resource management.", "subsetOfAvailableValues": ["24121212", "38035531", "38459582", "39329602", "88171913", "and 3240 more..."], "is_unstructured": false}, {"name": "JobsDetail_ItemId", "dataType": "int", "description": "An integer serving as a unique identifier for the item related to the job detail, facilitating distinct item tracking.", "subsetOfAvailableValues": ["2826815", "2827695", "2827709", "2827720", "2827724", "and 3244 more..."], "is_unstructured": false}, {"name": "JobsDetail_ItemDescription", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string providing a description of the item, enhancing clarity regarding what resources are involved.", "subsetOfAvailableValues": ["Labour per hour", "Environmental Consumables used", "Truck Charge", "Oil Filter", "Travel Time", "and 2474 more..."], "is_unstructured": false}, {"name": "JobsDetail_ItemType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the type of item, crucial for understanding resource classifications and reporting.", "availableValues": ["Parts", "Labor", "Misc - taxed", "Mileage", "Freight", "Equipment", "Expense", "Misc - Non Taxed"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "JobsDetail_ItemCategory", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the category of the item, aiding in inventory and item management.", "availableValues": ["Parts", "Miscellaneous", "Labor", "Shipping", "Equipment", "Rental"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "JobsDetail_ItemProductLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that may indicate the product line of the item, relevant for product categorization.", "subsetOfAvailableValues": ["Parts-Compressor", "Labour", "Parts-Generator", "Shop Supplies", "Vehicle Charge", "and 34 more..."], "totalDistinctValueCount": 39, "is_unstructured": false}, {"name": "JobsDetail_ItemStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the status of the item, essential for tracking item progress and availability.", "availableValues": ["All Arrived", "Partial Arrived", "Ordered", "In Development", "Waiting on Parts", "Shipped"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "JobsDetail_Item_Technician", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string specifying the technician associated with the item, crucial for resource accountability.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "JobsDetail_Item_TechnicianID", "dataType": "int", "description": "An integer identifier for the technician linked to the item, aiding in technician tracking.", "subsetOfAvailableValues": ["108014", "108015", "108016", "108017", "108018", "and 12 more..."], "is_unstructured": false}, {"name": "JobsDetail_Item_TechnicianStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the status of the technician, relevant for workforce management.", "availableValues": ["Active", "Inactive"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "JobsDetail_ItemLaborResourceID", "dataType": "int", "description": "An integer identifier for the labor resource related to the item, facilitating resource management.", "subsetOfAvailableValues": ["108014", "108015", "108016", "108017", "108018", "and 12 more..."], "is_unstructured": false}, {"name": "JobsDetail_Item_EquipmentID", "dataType": "int", "description": "An integer uniquely identifying the equipment linked to the item, important for equipment tracking.", "subsetOfAvailableValues": ["950576", "950584", "950619", "950682", "950694", "and 1471 more..."], "is_unstructured": false}, {"name": "JobsDetail_Item_Equipment", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string specifying the equipment associated with the item, relevant for resource management.", "subsetOfAvailableValues": ["R160IE-145", "SD100", "SD350", "60ENA", "SD600", "and 1010 more..."], "totalDistinctValueCount": 1015, "is_unstructured": false}, {"name": "JobsDetail_Item_EquipmentSerial", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the serial number of the equipment linked to the item, crucial for equipment identification.", "subsetOfAvailableValues": ["352468", "2098914", "2117123", "9560105", "9943213", "and 1462 more..."], "totalDistinctValueCount": 1467, "is_unstructured": false}, {"name": "JobsDetail_Item_ActualQuantity", "dataType": "decimal", "description": "A decimal representing the actual quantity of the item involved in the job, important for inventory tracking.", "is_unstructured": false}, {"name": "JobsDetail_Item_OrderedQuantity", "dataType": "decimal", "description": "A decimal indicating the quantity of the item that was ordered, essential for procurement management.", "is_unstructured": false}, {"name": "JobsDetail_Item_EstimatedQuantity", "dataType": "decimal", "description": "A decimal that indicates the estimated quantity of the item for the job, aiding in resource planning.", "is_unstructured": false}, {"name": "JobsDetail_Item_InvoicedQuantity", "dataType": "decimal", "description": "A decimal field indicating the quantity of the item that has been invoiced, relevant for financial tracking.", "is_unstructured": false}, {"name": "JobsDetail_Item_UsedQuantity", "dataType": "decimal", "description": "A decimal indicating the quantity of items used in the job, crucial for resource utilization tracking.", "is_unstructured": false}, {"name": "JobsDetail_Item_PickedQuantity", "dataType": "decimal", "description": "A decimal reflecting the quantity of items selected for a job, important for inventory management.", "is_unstructured": false}, {"name": "JobsDetail_Item_WIPQuantity", "dataType": "decimal", "description": "A decimal showing the amount of items currently in progress, crucial for workflow management.", "is_unstructured": false}, {"name": "JobsDetail_Item_WIPCost", "dataType": "decimal", "description": "A decimal indicating the cost associated with Work in Progress items, important for cost tracking.", "is_unstructured": false}, {"name": "JobsDetail_Item_WIPExtendedCost", "dataType": "decimal", "description": "A decimal representing the total extended cost of Work in Progress items, essential for budgeting.", "is_unstructured": false}, {"name": "JobsDetail_Item_MaterialCost", "dataType": "decimal", "description": "A decimal reflecting the total cost of materials required for the job, critical for financial management.", "is_unstructured": false}, {"name": "JobsDetail_Item_POCost", "dataType": "decimal", "description": "A decimal indicating the cost associated with purchase orders related to the job, relevant for procurement tracking.", "is_unstructured": false}, {"name": "JobsDetail_Item_JobRevenue", "dataType": "decimal", "description": "A decimal recording the revenue generated from the job, crucial for financial performance assessment.", "is_unstructured": false}, {"name": "JobsDetail_Item_JobCost", "dataType": "decimal", "description": "A decimal representing the total cost of the job, important for profitability analysis.", "is_unstructured": false}, {"name": "JobsDetail_Item_InvoiceName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string capturing the name of the invoice related to the job, aiding in invoice management.", "subsetOfAvailableValues": ["10342-1", "9863-1", "8795-1", "7077-1", "10345-1", "and 4025 more..."], "totalDistinctValueCount": 4030, "is_unstructured": false}, {"name": "JobsDetail_Item_InvoicedRevenue", "dataType": "decimal", "description": "A decimal reflecting the revenue that has been invoiced related to the job, important for revenue tracking.", "is_unstructured": false}, {"name": "JobsDetail_Item_InvoicedCost", "dataType": "decimal", "description": "A decimal recording the cost that has been invoiced for the job, essential for financial reconciliation.", "is_unstructured": false}, {"name": "JobsDetail_Item_UnitPrice", "dataType": "decimal", "description": "A decimal indicating the price per unit of the items involved in the job, relevant for pricing analysis.", "is_unstructured": false}, {"name": "JobsDetail_Item_CurrencyPrice", "dataType": "decimal", "description": "A decimal representing the job price in a specific currency, important for financial reporting.", "is_unstructured": false}, {"name": "JobsDetail_Item_Total", "dataType": "decimal", "description": "A decimal indicating the total calculated amount for the job, encompassing all related costs and revenues.", "is_unstructured": false}, {"name": "JobsDetail_Item_TotalDiscountAmt", "dataType": "decimal", "description": "A decimal reflecting the total amount of discounts applied to the job, aiding in financial analysis.", "is_unstructured": false}, {"name": "JobsDetail_Item_DiscountPercent", "dataType": "decimal", "description": "A decimal indicating the percentage of discount applied to the job, significant for pricing strategies.", "is_unstructured": false}, {"name": "JobsDetail_Item_ItemNotes", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing additional notes regarding the items in the job, providing context for item management.", "subsetOfAvailableValues": ["", "In stock", "Not in stock", "Estimated 2-3 weeks lead time ", "Estimated 2-3 weeks to arrive. ", "and 402 more..."], "totalDistinctValueCount": 407, "is_unstructured": false}, {"name": "JobsDetail_Customer_ID", "dataType": "int", "description": "An integer uniquely identifying the customer associated with the job, essential for customer management.", "subsetOfAvailableValues": ["379954", "380039", "380079", "380204", "380230", "and 1088 more..."], "is_unstructured": false}, {"name": "JobsDetail_Customer_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the customer for whom the job is being performed, crucial for identification.", "subsetOfAvailableValues": ["The Mosaic Company", "Genrep Ltd.", "City of Winnipeg", "Air Unlimited Inc.", "<PERSON> Agencies (USD)", "and 1086 more..."], "totalDistinctValueCount": 1091, "is_unstructured": false}, {"name": "JobsDetail_Customer_AccountNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the account number assigned to the customer, important for account management.", "subsetOfAvailableValues": ["THE10255", "GEN10792", "CIT10642", "AIR00001", "TOM11442", "and 1088 more..."], "totalDistinctValueCount": 1093, "is_unstructured": false}, {"name": "JobsDetail_Location_ID", "dataType": "int", "description": "An integer uniquely identifying the location associated with the job, crucial for location tracking.", "subsetOfAvailableValues": ["658879", "658913", "658951", "658979", "659191", "and 1504 more..."], "is_unstructured": false}, {"name": "JobsDetail_Location_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string capturing the name of the job location, important for geographical identification.", "subsetOfAvailableValues": ["Winnipeg-Head Office", "TOM BEGGS (USD)", "The Mosaic Company - K3", "GFL Environmental", "The Mosaic Company", "and 1409 more..."], "totalDistinctValueCount": 1414, "is_unstructured": false}, {"name": "JobsDetail_Location_Address1", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the primary address of the job location, essential for logistical management.", "subsetOfAvailableValues": ["2116 Logan Ave", "", "General Delivery Highway 22 E", "335 Mazenod Rd", "95 33 St E", "and 1389 more..."], "totalDistinctValueCount": 1394, "is_unstructured": false}, {"name": "JobsDetail_Location_Address2", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing secondary address information for the job location, relevant for detailed location tracking.", "subsetOfAvailableValues": ["", "Dept. of Consumer Protection and Gov. Serv., Asset Management, Operations, D2", "PO Box 187", "PO Box 28", "20 km west of Carman, on the NE 1/4, Section 36", "and 222 more..."], "totalDistinctValueCount": 227, "is_unstructured": false}, {"name": "JobsDetail_Location_City", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string storing the city name associated with the job location, important for geographical categorization.", "subsetOfAvailableValues": ["Winnipeg", "Saskatoon", "Regina", "Portage La Prairie", "<PERSON>", "and 332 more..."], "totalDistinctValueCount": 337, "is_unstructured": false}, {"name": "JobsDetail_Location_State", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string recording the state abbreviation where the job is located, essential for regional categorization.", "availableValues": ["MB", "SK", "ON", "AB", "", "YT", "IL", "QC", "BC", "NB", "NU", "WI", "ND", "OH"], "totalDistinctValueCount": 14, "is_unstructured": false}, {"name": "JobsDetail_Location_Zip", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the postal or ZIP code of the job location, important for detailed geographical tracking.", "subsetOfAvailableValues": ["R2R 0J2", "", "S0A 0X0", "R2J 3S8", "S7K 0R8", "and 1113 more..."], "totalDistinctValueCount": 1118, "is_unstructured": false}, {"name": "JobsDetail_ShipToOverride", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string indicating any special shipping instructions related to the job, relevant for logistical management.", "subsetOfAvailableValues": ["", "\r\n", "PCS Potash Lanigan\r\n12 KM West of Lanigan, HWY 16\r\nLanigan, SK S0K 2M0", "Shamattawa Nursing Station\r\nShamattawa, Manitoba\r\nR0B 1K0", "24183 SaskPower Phase 2\r\n12305 Rotary Ave\r\nRegina, SK S4M 0A5", "and 122 more..."], "is_unstructured": false}, {"name": "JobsDetail_Contact_ID", "dataType": "int", "description": "An integer uniquely identifying the contact person associated with the job, crucial for accountability.", "subsetOfAvailableValues": ["904289", "905036", "905037", "905052", "905317", "and 1778 more..."], "is_unstructured": false}, {"name": "JobsDetail_Contact_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the name of the contact person for the job, important for communication management.", "subsetOfAvailableValues": ["Accounts Payable", "General Inbox", "<PERSON>", "<PERSON> switzer", "<PERSON>", "and 1488 more..."], "totalDistinctValueCount": 1493, "is_unstructured": false}, {"name": "JobsDetail_Contact_Title", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string holding the title of the contact person, providing context regarding their role.", "subsetOfAvailableValues": ["Site Contact", "Accounts Payable", "", "General Inbox", "Senior Service Coordinator", "and 398 more..."], "totalDistinctValueCount": 403, "is_unstructured": false}, {"name": "JobsDetail_Contact_Email", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string storing the email address of the contact person, essential for direct communication.", "subsetOfAvailableValues": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "and 1593 more..."], "totalDistinctValueCount": 1598, "is_unstructured": false}, {"name": "JobsDetail_Contact_Cell", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string recording the cell phone number of the contact person, important for quick communication.", "subsetOfAvailableValues": ["", "(*************", "************", "************", "************", "and 630 more..."], "totalDistinctValueCount": 635, "is_unstructured": false}, {"name": "JobsDetail_Contact_WorkPhone", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the work phone number of the contact person, essential for official communication.", "subsetOfAvailableValues": ["3065438749", "", "************ ext:334", "(************* Ext. 1903      ", "(************* Ext.", "and 1150 more..."], "totalDistinctValueCount": 1155, "is_unstructured": false}], "relationships": [{"child_table": "JobsDetail", "parent_table": "ContactInformation", "key_column_mapping": [{"parent_column": "[Contact.ID]", "child_column": "[JobsDetail.Contact.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[JobsDetail.ContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.Number]", "child_column": "[JobsDetail.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[JobsDetail.Customer.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.Account<PERSON><PERSON>ber]", "child_column": "[JobsDetail.Customer.AccountNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[JobsDetail.Item.TechnicianID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "EquipmentDetail", "key_column_mapping": [{"parent_column": "[Equipment.ID]", "child_column": "[JobsDetail.Item.EquipmentID]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "InvoiceSummary", "key_column_mapping": [{"parent_column": "[InvoiceSummary.InvoiceName]", "child_column": "[JobsDetail.Item.InvoiceName]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[JobsDetail.ItemId]"}], "relationship_type": "one-to-many"}, {"child_table": "JobsDetail", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[JobsDetail.Location.ID]"}], "relationship_type": "one-to-many"}]}