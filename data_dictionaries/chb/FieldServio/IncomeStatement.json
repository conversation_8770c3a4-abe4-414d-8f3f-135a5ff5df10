{"tableName": "IncomeStatement", "description": "The IncomeStatement table captures individual financial transactions—classified as revenues or expenses—for each company, office, and department. It stores account codes, names, groupings, office and department identifiers, and transaction dates, with amounts indicating positive (revenue) or negative (expense) values. Each record is uniquely identified by GLID, supporting detailed financial reporting and analysis by company, office, department, account type, and group.", "fields": [{"name": "IncomeStatement_GLID", "dataType": "int", "description": "A unique integer identifier for each record in the Income Statement table, serving as the primary key to distinctly reference each entry.", "subsetOfAvailableValues": ["********", "********", "********", "********", "********", "and 28685 more..."], "is_unstructured": false}, {"name": "IncomeStatement_CompanyName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the company linked to the income statement entry, primarily 'Winnipeg-Head Office' with a smaller portion for 'CHB Group', stored as a varchar.", "availableValues": ["Winnipeg-Head Office", "CHB Group"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "IncomeStatement_AccountType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating whether the entry is classified as 'Expenses' or 'Revenues', with a majority of records categorized as Expenses.", "availableValues": ["Expenses", "Revenues"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "IncomeStatement_SubTypeID", "dataType": "int", "description": "An integer field that may provide further categorization of account types, though specific subtype details are not defined.", "subsetOfAvailableValues": ["30259", "30260", "30261", "30262", "30264", "and 83 more..."], "is_unstructured": false}, {"name": "IncomeStatement_BaseAccountName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field that describes the financial category at a high level, associated with the entry.", "subsetOfAvailableValues": ["Standard labour expense", "Cost of sales - Parts", "Revenue - Parts", "Revenue - Service", "Shop supplies recovery", "and 83 more..."], "totalDistinctValueCount": 88, "is_unstructured": false}, {"name": "IncomeStatement_AccountCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field that assigns a specific code to the account for accounting purposes, facilitating transaction identification and categorization.", "subsetOfAvailableValues": ["4000", "4010", "4020", "4030", "4050", "and 83 more..."], "totalDistinctValueCount": 88, "is_unstructured": false}, {"name": "IncomeStatement_OfficeID", "dataType": "int", "description": "An integer field representing the ID of the office linked to the record, with known values corresponding to different operational locations.", "availableValues": ["419", "420", "421"], "is_unstructured": false}, {"name": "IncomeStatement_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field naming the office related to the income statement entry, with values such as 'MB Office', 'Shared Services', and 'SK Office'.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "IncomeStatement_DepartmentName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "An nvarchar field indicating the department relevant to the entry, noting that around 34% of records are missing this value.", "availableValues": ["Compressor", "Generator", "Rental "], "is_unstructured": false}, {"name": "IncomeStatement_OfficeAccountingCode", "dataType": "n<PERSON><PERSON><PERSON>", "description": "An nvarchar field providing accounting codes specific to the office, with possible values like 100, 300, and 200.", "availableValues": ["100", "200", "300"], "is_unstructured": false}, {"name": "IncomeStatement_DepartmentAccountingCode", "dataType": "n<PERSON><PERSON><PERSON>", "description": "An nvarchar field providing accounting codes specific to the department, with about 34% of records missing this information.", "availableValues": ["10", "20", "30"], "is_unstructured": false}, {"name": "IncomeStatement_AccountName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field specifying the name of the account associated with the entry, offering additional context for the financial transaction.", "subsetOfAvailableValues": ["5200-10-100 Standard labour expense", "5200-20-100 Standard labour expense", "5010-20-100 Cost of sales - Parts", "4010-20-100 Revenue - Parts", "5070-000-100 Inventory variance", "and 314 more..."], "totalDistinctValueCount": 319, "is_unstructured": false}, {"name": "IncomeStatement_GroupName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field that categorizes the entry into broader financial groups, enhancing financial analysis and reporting.", "availableValues": ["Revenue", "Cost of sales", "Standard labour", "Foreign exchange gain/loss", "Wages and subcontractors", "Management fee", "Rent and common area", "Travel", "IT and Software", "Automotive", "Office supplies", "Direct labour", "Interest and bank charges", "Meals and entertainment", "Telephone", "Bad debt expense", "Advertising and Promotion", "Safety", "Dues and subscriptions", "Professional fees", "Insurance", "Interest revenue", "Licenses and taxes", "Gain/loss on disposal of assets", "Amortization"], "totalDistinctValueCount": 25, "is_unstructured": false}, {"name": "IncomeStatement_Amount", "dataType": "decimal", "description": "A decimal field representing the financial amount of the entry, which can be positive or negative, indicating revenues and expenses respectively.", "is_unstructured": false}, {"name": "IncomeStatement_TransactionDate", "dataType": "datetime", "description": "A datetime field documenting the date and time of the financial transaction, providing temporal context for the entry.", "is_unstructured": false}]}