{"tableName": "InventoryDetail", "description": "InventoryDetail captures comprehensive inventory activity, tracking each item’s location, status, cost, price, and quantity, along with optional links to jobs, equipment, suppliers, and customers. It supports detailed stock management, job costing, and financial reporting for Air Unlimited’s service, sales, and rental operations. Each record is uniquely identified and may include warehouse, bin, and job details, enabling analysis of inventory movement and valuation across multiple business processes.", "fields": [{"name": "InventoryDetail_ID", "dataType": "int", "description": "A unique integer identifier assigned to each record in the InventoryDetail table for distinct identification.", "subsetOfAvailableValues": ["13563190", "13563202", "13563206", "13563207", "13563219", "and 5001 more..."], "is_unstructured": false}, {"name": "InventoryDetail_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field categorizing the status of the inventory item, with possible values such as 'Inventory', 'Work In Progress', or 'Return'.", "availableValues": ["Inventory", "Work In Progress", "Return"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InventoryDetail_CreatedDate", "dataType": "date", "description": "The date on which the inventory entry was created, formatted as a date, aiding in the management timeline.", "is_unstructured": false}, {"name": "InventoryDetail_WarehouseID", "dataType": "int", "description": "An integer identifier for the warehouse housing the inventory item; approximately 13% of records may lack this value.", "subsetOfAvailableValues": ["29913", "29914", "29916", "29917", "29918", "and 12 more..."], "is_unstructured": false}, {"name": "InventoryDetail_Warehouse", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the warehouse where the item is stored, with around 13% of records potentially missing this information.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "~Regina Warehouse", "S/N 1143909 - <PERSON>", "S/N KB09767 - <PERSON>", "S/N 1249338 - <PERSON>", "S/N S545722 - <PERSON>", "S/N 1296840 - <PERSON>", "S/N 1143618 - <PERSON>", "S/N KB09748 - <PERSON>", "S/N 1143253 - <PERSON>", "S/N 1196248 - <PERSON>", "S/N Z412284 - <PERSON>", "S/N G367219 - <PERSON>", "S/N FA50095 - <PERSON>", "S/N KE44716 - <PERSON>", "Thermo King"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "InventoryDetail_BinID", "dataType": "int", "description": "A unique integer identifier for the specific bin within the warehouse, with approximately 19% of records missing values.", "subsetOfAvailableValues": ["0", "54459", "54464", "54482", "54501", "and 374 more..."], "is_unstructured": false}, {"name": "InventoryDetail_BinName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the bin containing the item, with about 33% of records missing this data, typically unique for groups of 13 records.", "subsetOfAvailableValues": ["Pressure Relief Val<PERSON>", "W-06-01-04", "W-06-02-04", "<PERSON>", "First Storage Shelter", "and 373 more..."], "totalDistinctValueCount": 378, "is_unstructured": false}, {"name": "InventoryDetail_ItemID", "dataType": "int", "description": "A unique integer assigned to each item in the inventory, facilitating tracking and referencing.", "subsetOfAvailableValues": ["2825095", "2825307", "2826815", "2827693", "2827695", "and 2621 more..."], "is_unstructured": false}, {"name": "InventoryDetail_Item_ItemLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field that provides additional context regarding the item line associated with the inventory item.", "availableValues": ["Parts-Compressor", "Parts-Generator", "Compressor Oil ", "Compressor Filtration", "Generator Oil", "Low Pressure (<600 PSI)  compressor", "Dryers ", "Transfer Switch", "Gas Generator", "Diesel Generator", "Air Ends - New", "Custom build", "Portable Equipment-Generator"], "totalDistinctValueCount": 13, "is_unstructured": false}, {"name": "InventoryDetail_Item", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique varchar description or identifier for each inventory item, ensuring distinctiveness.", "subsetOfAvailableValues": ["23424922", "23973969", "24121212", "24335028", "37949542", "and 2621 more..."], "totalDistinctValueCount": 2626, "is_unstructured": false}, {"name": "InventoryDetail_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique varchar code assigned to the item for quick identification and retrieval.", "subsetOfAvailableValues": ["23424922", "23973969", "24121212", "24335028", "37949542", "and 2621 more..."], "totalDistinctValueCount": 2626, "is_unstructured": false}, {"name": "InventoryDetail_ItemInventoryCosting", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar indicating the inventory costing method used, primarily 'AVCO' or 'SPID', reflecting the costing strategy.", "availableValues": ["AVCO", "SPID"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetail_Item_ItemType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the type of item, with the majority categorized as 'Parts' or 'Equipment'.", "availableValues": ["Parts", "Equipment"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetail_Item_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar categorizing items into groups such as 'Parts', 'Equipment', or 'Miscellaneous'.", "availableValues": ["Parts", "Equipment", "Miscellaneous"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InventoryDetail_Item_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar providing a brief, generally unique description of the item for better understanding.", "subsetOfAvailableValues": ["Oil Filter", "Air Filter", "Separator", "Fuel Filter", "Fluid Analysis Kit", "and 2083 more..."], "totalDistinctValueCount": 2088, "is_unstructured": false}, {"name": "InventoryDetail_Item_SupplierID", "dataType": "int", "description": "An integer identifier for the supplier of the item, with approximately 18% of records missing this information.", "subsetOfAvailableValues": ["81049", "81061", "81073", "81111", "81113", "and 87 more..."], "is_unstructured": false}, {"name": "InventoryDetail_Item_SupplierName", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the supplier associated with the item; about 18% of records may lack this data.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Green Line Hose & Fittings Ltd", "Royal Fluid Power Ltd.", "Ingersoll-Rand Canada Sales & Service ULC", "and 86 more..."], "totalDistinctValueCount": 91, "is_unstructured": false}, {"name": "InventoryDetail_Item_Price", "dataType": "decimal", "description": "A decimal representing the price of the item, essential for pricing and sales analysis.", "is_unstructured": false}, {"name": "InventoryDetail_Item_PriceExtended", "dataType": "decimal", "description": "A decimal field showing the total extended price based on quantity and unit price for financial reporting.", "is_unstructured": false}, {"name": "InventoryDetail_Quantity", "dataType": "decimal", "description": "A decimal indicating the available quantity of the item in inventory, capable of reflecting both incoming and outgoing transactions.", "is_unstructured": false}, {"name": "InventoryDetail_Cost", "dataType": "decimal", "description": "A decimal representing the cost associated with the item, crucial for profitability and valuation assessments.", "is_unstructured": false}, {"name": "InventoryDetail_Item_CostExtended", "dataType": "decimal", "description": "A decimal showing the total extended cost based on quantity, important for inventory management.", "is_unstructured": false}, {"name": "InventoryDetail_WarehouseMaxQty", "dataType": "decimal", "description": "A decimal specifying the maximum quantity the warehouse can hold for the item, with data available for less than 50% of records.", "is_unstructured": false}, {"name": "InventoryDetail_WarehouseMinQty", "dataType": "decimal", "description": "A decimal indicating the minimum quantity required to be maintained in the warehouse for the item, generally under 100.", "is_unstructured": false}, {"name": "InventoryDetail_WarehouseOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "The name of the office related to the warehouse, with about 13% of records missing this information.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetail_Warehouse_ItemQty", "dataType": "decimal", "description": "A decimal representing the quantity of items in the warehouse, with approximately 13% of records missing this value.", "is_unstructured": false}, {"name": "InventoryDetail_EquipmentID", "dataType": "int", "description": "An integer holding the ID of the equipment linked to the inventory detail, with about 47% of records missing this data.", "subsetOfAvailableValues": ["0", "961807", "961813", "961850", "961859", "and 143 more..."], "is_unstructured": false}, {"name": "InventoryDetail_LocationID", "dataType": "int", "description": "An integer indicating the location of the inventory item, with values available for less than 10% of records.", "subsetOfAvailableValues": ["658934", "659512", "659642", "659971", "660463", "and 14 more..."], "is_unstructured": false}, {"name": "InventoryDetail_Equipment_SerialNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar containing the equipment's serial number, unique for groups of 33 records, but with low data availability.", "subsetOfAvailableValues": ["997419", "2620924", "10998440", "3008130568", "3012149980", "and 142 more..."], "totalDistinctValueCount": 147, "is_unstructured": false}, {"name": "InventoryDetail_Equipment_Make", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar specifying the manufacturer of the equipment, with less than 10% of records populated.", "availableValues": ["Generac", "Ingersoll Rand"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetail_Equipment_Model", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar identifying the model of the equipment, with data for less than 10% of records.", "subsetOfAvailableValues": ["8251", "18002386", "20103180", "22235923", "23231624", "and 103 more..."], "totalDistinctValueCount": 108, "is_unstructured": false}, {"name": "InventoryDetail_Equipment_SoldCondition", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar describing the condition of sold equipment, with very few records marked as 'New'.", "availableValues": ["Not Set", "New"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryDetail_JobID", "dataType": "int", "description": "An integer representing the job associated with the inventory detail, with less than 50% of records populated.", "subsetOfAvailableValues": ["1821278", "1824524", "1929373", "1983308", "1984117", "and 446 more..."], "is_unstructured": false}, {"name": "InventoryDetail_JobItemId", "dataType": "int", "description": "An integer identifying the specific job item, with values available for less than 50% of records.", "subsetOfAvailableValues": ["9570321", "9684309", "9759163", "10158374", "10356138", "and 1385 more..."], "is_unstructured": false}, {"name": "InventoryDetail_JobDateEntered", "dataType": "datetime", "description": "A datetime column recording when the job was entered, with data available for less than 50% of records.", "is_unstructured": false}, {"name": "InventoryDetail_JobCustomerID", "dataType": "int", "description": "An integer containing the ID of the job's customer, with less than 50% of records populated.", "subsetOfAvailableValues": ["379954", "380039", "380079", "380204", "380230", "and 230 more..."], "is_unstructured": false}, {"name": "InventoryDetail_JobCustomer", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar providing the name of the customer related to the job, available for less than 50% of records.", "subsetOfAvailableValues": ["Air Unlimited Inc.", "Sachigo Lake First Nation", "The Mosaic Company", "Canadian National Railway", "Black & McDonald Ltd.", "and 229 more..."], "totalDistinctValueCount": 234, "is_unstructured": false}, {"name": "InventoryDetail_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar describing the name of the job, populated in less than 50% of records.", "subsetOfAvailableValues": ["PMA-4193982", "PS-4250121", "PMA-4142976", "PMA-4194128", "PFB-3978771", "and 445 more..."], "totalDistinctValueCount": 450, "is_unstructured": false}, {"name": "InventoryDetail_JobStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar indicating the current status of the job, with less than 50% of records having this information.", "availableValues": ["Completed", "Ready to Schedule", "Waiting on Parts", "In Progress", "Scheduled", "Cancelled"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "InventoryDetail_LastInvoiceDate", "dataType": "date", "description": "The date of the last invoice related to the inventory item, with about 40% of records missing this information.", "is_unstructured": false}, {"name": "InventoryDetail_InvoiceCount", "dataType": "int", "description": "An integer indicating the count of invoices related to the item, with 40% of records missing data.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 50 more..."], "is_unstructured": false}, {"name": "InventoryDetail_ReportRunDate", "dataType": "datetime", "description": "A datetime capturing when the report was last generated, crucial for tracking report timelines.", "is_unstructured": false}], "relationships": [{"child_table": "InventoryDetail", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[InventoryDetail.Item.SupplierID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[InventoryDetail.JobCustomerID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InventoryDetail.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[InventoryDetail.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryDetail.Item]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryDetail.ItemCode]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InventoryDetail.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryDetail", "parent_table": "LocationInformation", "key_column_mapping": [{"parent_column": "[Location.ID]", "child_column": "[InventoryDetail.LocationID]"}], "relationship_type": "one-to-many"}]}