{"tableName": "Items", "description": "The Items table contains master data for all inventory and non-inventory items, including parts, equipment, rentals, and labor. Each item has unique identifiers and includes fields for categorization, pricing, supplier and manufacturer details, inventory status, and audit information. The table supports inventory tracking, costing methods, and item classification, making it essential for procurement, inventory management, and sales operations at Air Unlimited.", "fields": [{"name": "Items_ID", "dataType": "int", "description": "A unique integer identifier for each item record, ensuring no two records share the same ID.", "subsetOfAvailableValues": ["2825750", "2825773", "2825896", "2825919", "2827101", "and 9362 more..."], "is_unstructured": false}, {"name": "Items_Name", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A unique string representing the name of the item, essential for distinguishing items within the inventory.", "subsetOfAvailableValues": ["23002", "22856991", "23702053", "30221170", "31385693", "and 9284 more..."], "is_unstructured": false}, {"name": "Items_Description", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A detailed string description of the item, ideally unique to each record, providing more context about the item.", "subsetOfAvailableValues": ["Oil Filter", "Air Filter", "Separator", "Fuel Filter", "Element", "and 7101 more..."], "is_unstructured": false}, {"name": "Items_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the item into specific groups such as 'Parts', 'Equipment', or 'Rental', aiding in inventory organization.", "availableValues": ["Parts", "Equipment", "Miscellaneous", "Rental", "Labor", "Shipping"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "Items_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique alphanumeric code assigned to each item for efficient identification and quick reference.", "subsetOfAvailableValues": ["23002", "22856991", "23702053", "30221170", "31385693", "and 9338 more..."], "totalDistinctValueCount": 9343, "is_unstructured": false}, {"name": "Items_ProductLine", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the product line associated with the item, facilitating organization within product categories.", "subsetOfAvailableValues": ["Parts-Compressor", "Parts-Generator", "Low Pressure (<600 PSI)  compressor", "Dryers ", "Compressor Filtration", "and 68 more..."], "totalDistinctValueCount": 73, "is_unstructured": false}, {"name": "Items_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that classifies the type of item, with options like 'Parts' or 'Labor', which is useful for financial tracking and inventory management.", "availableValues": ["Parts", "Equipment", "Misc - taxed", "Labor", "Expense", "Misc - Non Taxed", "Freight", "Mileage"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "Items_InventoryType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the item's status as either 'Inventory' or 'Non-Inventory', with most records classified as 'Inventory'.", "availableValues": ["Inventory", "Non-Inventory"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Items_SupplierID", "dataType": "int", "description": "An integer referencing the unique identifier of the associated supplier, with many records potentially lacking this information.", "subsetOfAvailableValues": ["81061", "81073", "81079", "81091", "81111", "and 163 more..."], "is_unstructured": false}, {"name": "Items_SupplierName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the name of the supplier linked to the item, which may be missing for a significant number of records.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Green Line Hose & Fittings Ltd", "CAG Technologies Inc.", "Compresseurs D'air Express Inc.", "and 161 more..."], "totalDistinctValueCount": 166, "is_unstructured": false}, {"name": "Items_ManufacturerID", "dataType": "int", "description": "An integer referencing the unique identifier of the item's manufacturer, available for less than half of the records.", "subsetOfAvailableValues": ["7442", "7443", "7444", "7445", "7446", "and 34 more..."], "is_unstructured": false}, {"name": "Items_ManufacturerName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the manufacturer of the item, which may not be present for many records.", "subsetOfAvailableValues": ["Generac", "Ingersoll Rand", "N/A", "CUMMINS", "Atlas Copco", "and 34 more..."], "totalDistinctValueCount": 39, "is_unstructured": false}, {"name": "Items_RetailPrice", "dataType": "decimal", "description": "A decimal value representing the retail price of the item, critical for sales transactions and inventory assessments.", "is_unstructured": false}, {"name": "Items_CostDate", "dataType": "datetime", "description": "A timestamp indicating the last update date of the item's cost, with some records potentially lacking this information.", "is_unstructured": false}, {"name": "Items_MaterialCost", "dataType": "decimal", "description": "A decimal value indicating the cost of materials used to create the item, essential for calculating profit margins.", "is_unstructured": false}, {"name": "Items_MaterialCostDate", "dataType": "datetime", "description": "A timestamp indicating when the material cost was last updated, with some records possibly missing this detail.", "is_unstructured": false}, {"name": "Items_MFGPrice", "dataType": "decimal", "description": "A decimal representing the manufacturer's price for the item, which may be missing in a small percentage of records.", "is_unstructured": false}, {"name": "Items_MFGPriceDate", "dataType": "datetime", "description": "A timestamp indicating when the manufacturer's price was last updated, with some records lacking this data.", "is_unstructured": false}, {"name": "Items_MinAmount", "dataType": "decimal", "description": "A decimal indicating the minimum order or stock quantity for the item, typically positive and under 100, with some records possibly missing this value.", "is_unstructured": false}, {"name": "Items_MaxAmount", "dataType": "decimal", "description": "A decimal indicating the maximum order or stock quantity for the item, usually positive and under 100, with potential missing values.", "is_unstructured": false}, {"name": "Items_DateCreated", "dataType": "smalldatetime", "description": "A timestamp representing when the item record was initially created, important for tracking inventory age.", "is_unstructured": false}, {"name": "Items_Createdby", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string capturing the name of the user who created the record, with many records lacking this information.", "availableValues": ["<PERSON>", "Dallas Skiftun", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Jay<PERSON><PERSON>rence"], "totalDistinctValueCount": 20, "is_unstructured": false}, {"name": "Items_LastModified", "dataType": "smalldatetime", "description": "A timestamp indicating when the item record was last modified, with some records possibly missing this detail.", "is_unstructured": false}, {"name": "Items_ModifiedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string capturing the name of the user who last modified the record, with many records lacking this information.", "availableValues": ["<PERSON>", "Dallas Skiftun", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>"], "totalDistinctValueCount": 19, "is_unstructured": false}, {"name": "Items_Status", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the current status of the item, primarily 'Active' or 'Inactive', crucial for inventory management.", "availableValues": ["Active", "Inactive"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Items_InventoryCosting", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the inventory costing method used, accepting values like 'SPID' and 'AVCO', with few records potentially lacking this value.", "availableValues": ["AVCO", "SPID", ""], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "Items_AllowTechAdd", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating whether technicians can add items, which consistently holds the value 'No'.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Items_RequiresPO", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating if a purchase order is necessary for the item, with 99% of records indicating 'No'.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Items_AcceptsPayment", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that specifies if the item can accept payment transactions, which consistently holds the value 'No'.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Items_LinkedItemsMethod", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string defining the method for linking items, predominantly using 'By Equipment' in most records.", "availableValues": ["By Equipment", "By Specific Equipment"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Items_HasDynamicPricing", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating whether the item has dynamic pricing capabilities, consistently holding the value 'No'.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Items_AllowFractionalInvoicing", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string determining if fractional invoicing is permitted for the item, which is predominantly 'No'.", "availableValues": ["No", "Yes"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Items_AvalaraInSyc", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating if the item integrates with Avalara for tax compliance, which consistently holds the value 'No'.", "availableValues": ["No"], "totalDistinctValueCount": 1, "is_unstructured": false}], "relationships": [{"child_table": "Items", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[Items.SupplierID]"}], "relationship_type": "one-to-many"}]}