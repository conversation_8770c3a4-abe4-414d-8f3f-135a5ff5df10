{"tableName": "InventoryReturns", "description": "The InventoryReturns table records comprehensive details of inventory items returned from various sources, including jobs and purchase orders. It captures item identifiers, quantities, costs, prices, reasons for return, processing personnel, and associated locations. This table supports inventory reconciliation, financial reporting, and process accountability for Air Unlimited’s multi-office operations, enabling effective management of returns across warehouses and service activities.", "fields": [{"name": "InventoryReturns_ReturnID", "dataType": "int", "description": "A unique integer identifier for each return transaction, essential for tracking individual return records within the InventoryReturns table.", "subsetOfAvailableValues": ["5904500", "5904501", "5904502", "5904503", "5904504", "and 2422 more..."], "is_unstructured": false}, {"name": "InventoryReturns_SourceID", "dataType": "int", "description": "An integer identifier that specifies the origin of the inventory item being returned, enabling effective tracking of where returned items came from.", "subsetOfAvailableValues": ["********", "********", "********", "********", "********", "and 2419 more..."], "is_unstructured": false}, {"name": "InventoryReturns_DestinationID", "dataType": "int", "description": "An integer identifier representing the intended destination for the returned item, though missing for about 8% of records, which may limit destination tracking accuracy.", "subsetOfAvailableValues": ["13570460", "13570468", "13570479", "13570484", "13570528", "and 2229 more..."], "is_unstructured": false}, {"name": "InventoryReturns_ItemID", "dataType": "int", "description": "An integer identifier for the specific item being returned, with unique values typically appearing in groups of three records, indicating possible multiple returns of the same item.", "subsetOfAvailableValues": ["2825057", "2825307", "2826056", "2826327", "2826815", "and 621 more..."], "is_unstructured": false}, {"name": "InventoryReturns_ActionTaken", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical string field that defines the specific action taken regarding the return, such as 'Returned to Supplier' or 'In Return Queue', aiding in process categorization.", "availableValues": ["In Return Queue from Job WIP", "Returned to Stock", "Returned to Supplier", "In Return Queue from Stock PO"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "InventoryReturns_JobID", "dataType": "int", "description": "An integer identifier for the job linked to the return, present in less than 50% of records and usually unique for groups of four records, indicating job-specific returns.", "subsetOfAvailableValues": ["1821104", "1821333", "1821359", "1827300", "1833335", "and 535 more..."], "is_unstructured": false}, {"name": "InventoryReturns_JobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string field representing the name of the job associated with the return, similarly present in less than 50% of records and often unique for groups of four records.", "subsetOfAvailableValues": ["PFB-3727921", "PS-3678884-C", "SR-4032901-C", "PFB-3727918", "PS-3852857", "and 527 more..."], "totalDistinctValueCount": 532, "is_unstructured": false}, {"name": "InventoryReturns_POName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string field providing the name of the purchase order tied to the return, with 21% of records missing this information, potentially impacting purchase order tracking.", "subsetOfAvailableValues": ["SO2133", "PS-3852857-1", "SO1452", "PS-3983540-1", "SR-3731830-1", "and 582 more..."], "totalDistinctValueCount": 587, "is_unstructured": false}, {"name": "InventoryReturns_ReturnQueueType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical string field that specifies the type of return queue for the item, with values such as 'R', 'W', and 'I', helping to categorize return workflows.", "availableValues": ["R", "W", "I"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "InventoryReturns_ReturnDestinationType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A categorical string field indicating the type of return destination, with approximately 50% of records marked as 'R' and 41% as 'I', and some missing values.", "availableValues": ["R", "I"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryReturns_ItemName", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string field representing the name of the item being returned, typically unique for groups of three records, highlighting the potential for multiple returns of similar items.", "subsetOfAvailableValues": ["22421853", "22463368", "23973977", "24121212", "24900433", "and 621 more..."], "is_unstructured": false}, {"name": "InventoryReturns_ItemCode", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string field that provides the code for the item being returned, with uniqueness typically found in groups of three records, reflecting similarities in returned items.", "subsetOfAvailableValues": ["22421853", "22463368", "23973977", "24121212", "24900433", "and 621 more..."], "totalDistinctValueCount": 626, "is_unstructured": false}, {"name": "InventoryReturns_ItemDescription", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string field offering a description of the returned item, usually unique for groups of four records, giving insight into the nature of the items returned.", "subsetOfAvailableValues": ["Oil Filter", "Air Filter", "Separator", "Antifreeze, Diesel/Gas Universal, 50/50 Premix, 3.78L (Yellow)", "Fluid Analysis Kit", "and 487 more..."], "is_unstructured": false}, {"name": "InventoryReturns_ReturnQuantity", "dataType": "decimal", "description": "A decimal field indicating the total quantity of the item being returned, with positive values usually under 100, crucial for effective inventory management.", "is_unstructured": false}, {"name": "InventoryReturns_UnitCost", "dataType": "decimal", "description": "A decimal field that denotes the cost per unit of the returned item, important for calculating the total costs associated with the returns.", "is_unstructured": false}, {"name": "InventoryReturns_ExtendedCost", "dataType": "decimal", "description": "A decimal field representing the total cost of the returned items, derived from multiplying the unit cost by the return quantity, though missing for 21% of records.", "is_unstructured": false}, {"name": "InventoryReturns_UnitPrice", "dataType": "decimal", "description": "A decimal field indicating the sale price per unit of the returned item, essential for financial analysis and reporting related to returns.", "is_unstructured": false}, {"name": "InventoryReturns_ExtendedPrice", "dataType": "decimal", "description": "A decimal field showing the total price of the returned items, calculated as the unit price times the return quantity, with missing values in 21% of records.", "is_unstructured": false}, {"name": "InventoryReturns_ReturnReason", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string field that provides the reason for the return, typically missing for 42% of records and usually unique for groups of three records, useful for analyzing return trends.", "subsetOfAvailableValues": ["not needed", "Wrong Part", "Cancelled Job", "incorrect account", "<PERSON>", "and 624 more..."], "is_unstructured": false}, {"name": "InventoryReturns_ReturnDate", "dataType": "datetime", "description": "A datetime field that records the date and time when the return was processed, critical for tracking timelines and managing inventory adjustments.", "is_unstructured": false}, {"name": "InventoryReturns_OfficeName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string field indicating the office responsible for processing the return, with a significant majority associated with 'MB Office' and a smaller portion with 'SK Office'.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "InventoryReturns_WarehouseName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string field specifying the warehouse where the returned item is stored, providing insight into the inventory location associated with the return.", "availableValues": ["~MB Warehouse", "~SK Warehouse", "Thermo King", "S/N 1143909 - <PERSON>", "S/N 1296840 - <PERSON>", "~Regina Warehouse", "S/N KB09748 - <PERSON>", "S/N 1249338 - <PERSON>"], "totalDistinctValueCount": 8, "is_unstructured": false}, {"name": "InventoryReturns_ReturnedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string field indicating the name of the individual who processed the return, with 1% of records missing this information, important for accountability.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Dallas Skiftun", "and 29 more..."], "totalDistinctValueCount": 34, "is_unstructured": false}], "relationships": [{"child_table": "InventoryReturns", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[InventoryReturns.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryReturns", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.Name]", "child_column": "[InventoryReturns.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryReturns", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ID]", "child_column": "[InventoryReturns.ItemID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryReturns", "parent_table": "Items", "key_column_mapping": [{"parent_column": "[Items.ItemCode]", "child_column": "[InventoryReturns.ItemCode]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryReturns", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[InventoryReturns.JobID]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryReturns", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.Name]", "child_column": "[InventoryReturns.JobName]"}], "relationship_type": "one-to-many"}, {"child_table": "InventoryReturns", "parent_table": "PurchaseOrderSummary", "key_column_mapping": [{"parent_column": "[PurchaseOrderSummary.Name]", "child_column": "[InventoryReturns.POName]"}], "relationship_type": "one-to-many"}]}