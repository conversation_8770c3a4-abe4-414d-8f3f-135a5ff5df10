{"tableName": "Rentals", "description": "The Rentals table stores comprehensive information about each rental asset, including identifiers, serial numbers, values, group/type, office location, and related contract details such as status, dates, unit count, and financials. It enables management and tracking of equipment rentals and contracts, supporting operational oversight and financial analysis for Air Unlimited’s rental services.", "fields": [{"name": "Rental_Asset_ID", "dataType": "int", "description": "A unique integer identifier for each rental asset, facilitating specific asset identification.", "subsetOfAvailableValues": ["33911", "33931", "33956", "33960", "33965", "and 17 more..."], "is_unstructured": false}, {"name": "Rental_Equipment_ID", "dataType": "int", "description": "An integer identifier for equipment linked to the rental, ensuring traceability to equipment details.", "subsetOfAvailableValues": ["950376", "951797", "951996", "952000", "952665", "and 17 more..."], "is_unstructured": false}, {"name": "Rental_Asset_Name", "dataType": "<PERSON><PERSON><PERSON>", "description": "A human-readable string representing the name of the rental asset, typically unique for identification purposes.", "availableValues": ["Sullivan Palatek DC185P3IZ-C", "Sullivan <PERSON>latek DC375PDJD", "Generac MMG150CAN6", "Generac MMG35CN", "Generac MMG45FHKCAN", "ZEKS HCP1202Z JM", "Generac MMG120", "Ingersoll Rand SSR-EP50SE", "Sullair LS-20S", "Sullivan-Palatek DC260PDDZ", "Generac MMG75CAN6", "Ingersoll Rand EH800", "Ingersoll Rand IRN100H-CC", "Generac MMG405", "DC375PDJD", "DV Air SC10-04-41", "Generac MMG100CAN6"], "totalDistinctValueCount": 17, "is_unstructured": false}, {"name": "Rental_SerialNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string that contains the serial number of the rental asset, essential for tracking and warranty management.", "availableValues": ["35475", "35497", "35498", "73310", "73804", "73805", "1202097", "1508921", "1804003", "1804013", "1806007", "3001558556", "3002359278", "3002400113", "3002400116", "3003333339", "90971-1", "G2358U94070", "36173UJ", "003-100187", "NV8667U09317", "EH800"], "totalDistinctValueCount": 22, "is_unstructured": false}, {"name": "Rental_Group_RequiresSerialForCheckout", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates whether the asset requires a serial number for the checkout process, with a fixed value of 'Yes'.", "availableValues": ["Yes"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "Rental_Status", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A fixed string representing the current status of the rental, which is always 'Active' in this context.", "availableValues": ["Active"], "is_unstructured": false}, {"name": "Rental_Group", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string categorizing the rental asset into specific groups, aiding in equipment classification.", "availableValues": ["Diesel Compressor - 185 CFM", "Diesel Compressor - 375 CFM", "Generator - 150 KVA - 600V", "Generator - 35 KVA - 480V", "Generator - 45 KVA - 480V", "Air Dryer - 120 CFM - 115V - Desiccant", "Generator - 120 KVA - 480V", "Electrical Compressor - 50 HP - 575V - FS", "Generator - 100 KVA - XXXV", "Generator - 405 KVA - 480V", "Generator - 75 KVA - 600V", "Air Dryer - 800 CFM - XXXV - Desiccant", "Diesel Compressor - 260 CFM", "Electrical Compressor - 10 HP - 208V - FS", "Electrical Compressor - 100 HP - 575 - VSD", "Electrical Compressor - 150 HP - 575V - FS"], "totalDistinctValueCount": 16, "is_unstructured": false}, {"name": "Rental_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "Specifies the nature of the rental, with the majority being 'Equipment', providing context on the rental type.", "availableValues": ["Equipment", "Rental"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Rental_Office", "dataType": "<PERSON><PERSON><PERSON>", "description": "Indicates the office location associated with the rental, helping identify the geographical context of the asset.", "availableValues": ["MB Office", "SK Office"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Rental_InitialValue", "dataType": "decimal", "description": "A decimal representing the rental asset's initial cost, offering insight into its worth at the time of rental.", "is_unstructured": false}, {"name": "Rental_CurrentValue", "dataType": "decimal", "description": "A decimal indicating the current estimated worth of the rental asset, accounting for depreciation or market changes.", "is_unstructured": false}, {"name": "Rental_Contract_ID", "dataType": "int", "description": "A unique integer identifier for the rental contract, allowing for straightforward association with contract details.", "subsetOfAvailableValues": ["193886", "193890", "193891", "193894", "193895", "and 43 more..."], "is_unstructured": false}, {"name": "Rental_ContractStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "Denotes the status of the rental contract, with possible values including 'Expired', 'Pending', 'Active', and 'Canceled'.", "availableValues": ["Expired", "Active", "Canceled", "Pending"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "Rental_ContractNumber", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string containing the contract number associated with the rental, essential for contract management.", "subsetOfAvailableValues": ["RC200885-1", "RC201308-1", "RC201308", "RC201257-1", "RC201257", "and 43 more..."], "totalDistinctValueCount": 48, "is_unstructured": false}, {"name": "Rental_ContractStartDate", "dataType": "datetime", "description": "A datetime field marking the start date of the rental contract, crucial for managing contract duration.", "is_unstructured": false}, {"name": "Rental_EndDate", "dataType": "datetime", "description": "A datetime field indicating the end date of the rental contract, important for renewal and rental period calculations.", "is_unstructured": false}, {"name": "Rental_Contract_Unit", "dataType": "int", "description": "An integer representing the count of units associated with the rental contract, with positive values under 100.", "subsetOfAvailableValues": ["1", "2", "3", "4", "5", "and 7 more..."], "is_unstructured": false}, {"name": "Rental_Contract_Cost", "dataType": "decimal", "description": "A decimal outlining the cost associated with the rental contract, significant for financial assessments.", "is_unstructured": false}, {"name": "Rental_Contract_Tax", "dataType": "decimal", "description": "A decimal specifying the tax applicable to the rental contract, important for total cost calculations.", "is_unstructured": false}, {"name": "Rental_Contract_Total", "dataType": "decimal", "description": "A decimal representing the total amount due for the rental contract, inclusive of costs and taxes.", "is_unstructured": false}], "relationships": [{"child_table": "Rentals", "parent_table": "AssetRevenue", "key_column_mapping": [{"parent_column": "[AssetRevenue.ContractID]", "child_column": "[Rental.Contract.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "Rentals", "parent_table": "AssetRevenue", "key_column_mapping": [{"parent_column": "[AssetRevenue.ContractNumber]", "child_column": "[Rental.ContractNumber]"}], "relationship_type": "one-to-many"}, {"child_table": "Rentals", "parent_table": "Assets", "key_column_mapping": [{"parent_column": "[Asset.ID]", "child_column": "[Rental.Asset.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "Rentals", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[Rental.Contract.ID]"}], "relationship_type": "one-to-many"}, {"child_table": "Rentals", "parent_table": "EquipmentDetail", "key_column_mapping": [{"parent_column": "[Equipment.ID]", "child_column": "[Rental.Equipment.ID]"}], "relationship_type": "one-to-many"}]}