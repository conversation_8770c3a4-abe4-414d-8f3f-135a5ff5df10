{"tableName": "Tasks", "description": "The Tasks table records individual work items—actions or meetings—each uniquely identified by TaskID. It includes task details, status, type, due and creation dates, creators, modifiers, and assigned members. Tasks may be linked to jobs, sales opportunities, or contracts, allowing integration across business functions. Nullable fields enable flexible associations. This table facilitates operational tracking, accountability, and project management for Air Unlimited’s service, sales, and rental operations.", "fields": [{"name": "Tasks_TaskID", "dataType": "int", "description": "A unique integer identifier for each task, ensuring distinct reference and integrity in task management.", "subsetOfAvailableValues": ["199571", "199583", "199585", "199687", "199949", "and 556 more..."], "is_unstructured": false}, {"name": "Tasks_TaskType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that categorizes the task as either 'Action' or 'Meeting', with the majority being 'Action' for effective task management.", "availableValues": ["Action", "Meeting"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Tasks_TaskStatus", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the current progress of the task, predominantly marked as 'Complete' to facilitate tracking.", "availableValues": ["Complete", "New"], "totalDistinctValueCount": 2, "is_unstructured": false}, {"name": "Tasks_TaskTitle", "dataType": "<PERSON><PERSON><PERSON>", "description": "A unique string representing the title of the task, providing a quick reference to its main objective.", "subsetOfAvailableValues": ["Quote Follow-Up", "Pick Parts ", "Follow-Up", "Update", "Add parts", "and 460 more..."], "totalDistinctValueCount": 465, "is_unstructured": false}, {"name": "Tasks_TaskDescription", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed string description of the task, offering specific insights into its requirements and expectations.", "subsetOfAvailableValues": ["", "non-inventory items to be ordered by Dallas", "Carry out Warranty Registration Process after customer pick up", "Please look into this and see if it is still active, has been completed or can be marked abandoned.\nTask can be marked complete once status has been changed.", "Carry out Warranty Registration Process after customer pick up\n", "and 402 more..."], "totalDistinctValueCount": 407, "is_unstructured": false}, {"name": "Tasks_TaskDueDate", "dataType": "smalldatetime", "description": "A datetime field indicating the deadline for task completion, with some entries lacking a due date, potentially influencing prioritization.", "is_unstructured": false}, {"name": "Tasks_TaskCreatedDate", "dataType": "datetime", "description": "A datetime field that logs when the task was created, crucial for understanding task timelines.", "is_unstructured": false}, {"name": "Tasks_TaskCreatedByID", "dataType": "int", "description": "An integer referencing the ID of the employee who created the task, aiding in accountability and tracking.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 16 more..."], "is_unstructured": false}, {"name": "Tasks_TaskCreatedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the name of the employee who created the task for improved communication.", "availableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Dallas Skiftun", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>"], "totalDistinctValueCount": 21, "is_unstructured": false}, {"name": "Tasks_ModifiedDate", "dataType": "datetime", "description": "A datetime field capturing when the task was last modified, important for tracking updates.", "is_unstructured": false}, {"name": "Tasks_TaskModifiedByID", "dataType": "int", "description": "An integer referring to the ID of the employee who last modified the task, similar in purpose to TaskCreatedByID.", "subsetOfAvailableValues": ["107985", "107989", "107992", "107994", "107996", "and 16 more..."], "is_unstructured": false}, {"name": "Tasks_TaskModifiedBy", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the name of the employee who last modified the task, assisting in tracing changes.", "availableValues": ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Dallas Skiftun", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 21, "is_unstructured": false}, {"name": "Tasks_TaskMembers", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string listing members assigned to the task, with about 4% of entries potentially lacking assigned members.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Dallas Skiftun", "and 56 more..."], "totalDistinctValueCount": 61, "is_unstructured": false}, {"name": "Tasks_TaskSubscribers", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating stakeholders associated with the task, often missing for many records, indicating interest in task updates.", "subsetOfAvailableValues": ["<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>,\r<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "and 31 more..."], "totalDistinctValueCount": 36, "is_unstructured": false}, {"name": "Tasks_TaskPendingJobID", "dataType": "int", "description": "An integer referencing the ID of a job related to the task, available for less than half of the records.", "subsetOfAvailableValues": ["3685748", "3686644", "3687692", "3687775", "3689890", "and 112 more..."], "is_unstructured": false}, {"name": "Tasks_TaskPendingJobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the name of the job associated with the task, correlating with TaskPendingJobID.", "subsetOfAvailableValues": ["Service Request - GEN - 3886900 (Pending)", "Service Request - GEN - 3900600 (Pending)", "Service Request-COM - 3693526 (Pending)", "Service Request-COM - 4025920 (Pending)", "Service Request-COM - 4034950 (Pending)", "and 112 more..."], "totalDistinctValueCount": 117, "is_unstructured": false}, {"name": "Tasks_TaskJobID", "dataType": "int", "description": "An integer linking the task to a specific job, missing for nearly half of the records, indicating a lack of job association.", "subsetOfAvailableValues": ["1821269", "1826236", "1832156", "1843364", "1859105", "and 250 more..."], "is_unstructured": false}, {"name": "Tasks_TaskJobName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the name of the job associated with the task, similarly missing for about half of the records.", "subsetOfAvailableValues": ["PFB-3741380", "ES-3683928", "PFB-3842160", "PFB-3906112", "RAM-3889380", "and 250 more..."], "totalDistinctValueCount": 255, "is_unstructured": false}, {"name": "Tasks_TaskOpportunityID", "dataType": "int", "description": "An integer referencing a sales opportunity related to the task, present in less than half of the records.", "subsetOfAvailableValues": ["251180", "251356", "252334", "252352", "252355", "and 114 more..."], "is_unstructured": false}, {"name": "Tasks_TaskOpportunityName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the name of the sales opportunity related to the task, available for less than half of the records.", "subsetOfAvailableValues": ["<PERSON><PERSON><PERSON>", "Callian - Flow Controller", "Cobra Mechanical ", "Crossmount Cider PMA", "Golden West, Weyburn Studio - PM <PERSON><PERSON><PERSON> ", "and 113 more..."], "totalDistinctValueCount": 118, "is_unstructured": false}, {"name": "Tasks_TaskContractID", "dataType": "int", "description": "An integer referencing the ID of a contract associated with the task, found in less than 10% of records.", "subsetOfAvailableValues": ["193667", "193865", "193885", "194576", "195747", "and 4 more..."], "is_unstructured": false}, {"name": "Tasks_TaskContractName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing the name of the contract associated with the task, similarly available for less than 10% of records.", "availableValues": ["PFB193667", "PFB194576", "PFB209178", "PFB217506", "PFB218201", "RC193885", "RC195747", "RC195828", "RM-G193865"], "totalDistinctValueCount": 9, "is_unstructured": false}], "relationships": [{"child_table": "Tasks", "parent_table": "AssetRevenue", "key_column_mapping": [{"parent_column": "[AssetRevenue.ContractID]", "child_column": "[Tasks.TaskContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "Tasks", "parent_table": "Contracts", "key_column_mapping": [{"parent_column": "[Contract.ContractID]", "child_column": "[Tasks.TaskContractID]"}], "relationship_type": "one-to-many"}, {"child_table": "Tasks", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[Tasks.TaskCreatedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "Tasks", "parent_table": "EmployeeDetail", "key_column_mapping": [{"parent_column": "[EmployeeDetail.EmployeeID]", "child_column": "[Tasks.TaskModifiedByID]"}], "relationship_type": "one-to-many"}, {"child_table": "Tasks", "parent_table": "JobsBacklog", "key_column_mapping": [{"parent_column": "[JobsBacklog.ID]", "child_column": "[Tasks.TaskJobID]"}], "relationship_type": "one-to-many"}, {"child_table": "Tasks", "parent_table": "JobsSummary", "key_column_mapping": [{"parent_column": "[JobsSummary.ID]", "child_column": "[Tasks.TaskJobID]"}], "relationship_type": "one-to-many"}, {"child_table": "Tasks", "parent_table": "Opportunities", "key_column_mapping": [{"parent_column": "[Opportunity.ID]", "child_column": "[Tasks.TaskOpportunityID]"}], "relationship_type": "one-to-many"}]}