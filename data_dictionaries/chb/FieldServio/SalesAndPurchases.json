{"tableName": "SalesAndPurchases", "description": "The SalesAndPurchases table records detailed information on inventory items involved in sales or purchase transactions, including item attributes, categorization, supplier details, pricing, and transaction quantities. Each entry is uniquely identified by ID and may reference suppliers via SupplierID. The table supports comprehensive analysis of parts, equipment, rentals, and miscellaneous transactions, enabling effective financial tracking and inventory management for Air Unlimited’s air and power solutions business.", "fields": [{"name": "SalesAndPurchases_ID", "dataType": "int", "description": "A unique integer identifier for each transaction record in the SalesAndPurchases table, serving as the primary key to distinguish individual entries.", "subsetOfAvailableValues": ["2825896", "2825919", "2827101", "2827247", "2827316", "and 9212 more..."], "is_unstructured": false}, {"name": "SalesAndPurchases_ItemName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the item involved in the transaction, essential for identifying specific products within sales and purchases.", "subsetOfAvailableValues": ["23002", "22856991", "23702053", "30221170", "31385693", "and 9135 more..."], "totalDistinctValueCount": 9140, "is_unstructured": false}, {"name": "SalesAndPurchases_Description", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string providing detailed information about the item, including specifications and features, to enhance understanding and distinguish it from other items.", "subsetOfAvailableValues": ["Oil Filter", "Air Filter", "Separator", "Fuel Filter", "Element", "and 7010 more..."], "totalDistinctValueCount": 7015, "is_unstructured": false}, {"name": "SalesAndPurchases_Category", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that categorizes the item into predefined groups such as Parts, Equipment, Rental, Miscellaneous, and Shipping for better organization and filtering.", "availableValues": ["Parts", "Equipment", "Rental", "Miscellaneous", "Shipping"], "totalDistinctValueCount": 5, "is_unstructured": false}, {"name": "SalesAndPurchases_Type", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string indicating the nature of the transaction related to the item, with possible values including Parts, Equipment, Misc - taxed, and Freight for accounting purposes.", "availableValues": ["Parts", "Equipment", "Misc - taxed", "Freight"], "totalDistinctValueCount": 4, "is_unstructured": false}, {"name": "SalesAndPurchases_InventoryType", "dataType": "<PERSON><PERSON><PERSON>", "description": "A constant string value of 'Inventory' that signifies all items listed are part of the managed inventory of the business.", "availableValues": ["Inventory"], "totalDistinctValueCount": 1, "is_unstructured": false}, {"name": "SalesAndPurchases_Manufacturer_Code", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the manufacturer code of the item, if available, which may include values like N/A or specific manufacturer names.", "availableValues": ["", "N/A", "Baldor"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "SalesAndPurchases_SupplierID", "dataType": "int", "description": "An integer serving as a foreign key that links the item to its supplier in the SupplierInformation table, which may be absent for some records.", "subsetOfAvailableValues": ["81061", "81073", "81079", "81091", "81111", "and 163 more..."], "is_unstructured": false}, {"name": "SalesAndPurchases_SupplierAccount", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that denotes the account number associated with the supplier, used for financial transactions and may be missing for some records.", "subsetOfAvailableValues": ["5038", "5044", "5047", "5053", "5066", "and 163 more..."], "totalDistinctValueCount": 168, "is_unstructured": false}, {"name": "SalesAndPurchases_SupplierName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string containing the name of the supplier providing the item, which may be absent for some records, impacting supplier identification.", "subsetOfAvailableValues": ["Ingersoll-Rand (USD)", "Generac Power Systems, Inc.", "Green Line Hose & Fittings Ltd", "CAG Technologies Inc.", "Compresseurs D'air Express Inc.", "and 161 more..."], "totalDistinctValueCount": 166, "is_unstructured": false}, {"name": "SalesAndPurchases_ProductLineName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string that indicates the product line to which the item belongs, offering additional categorization within the company's offerings.", "subsetOfAvailableValues": ["Parts-Compressor", "Parts-Generator", "Low Pressure (<600 PSI)  compressor", "Dryers ", "Compressor Filtration", "and 25 more..."], "totalDistinctValueCount": 30, "is_unstructured": false}, {"name": "SalesAndPurchases_Price", "dataType": "decimal", "description": "A decimal value representing the selling price of the item, crucial for tracking revenue and conducting inventory calculations.", "is_unstructured": false}, {"name": "SalesAndPurchases_InvoiceSalesQty", "dataType": "decimal", "description": "A decimal value indicating the quantity sold of the item as recorded on the invoice, essential for tracking sales volume.", "is_unstructured": false}, {"name": "SalesAndPurchases_InvoiceSalesTotal", "dataType": "decimal", "description": "A decimal value representing the total sales revenue for the item on the invoice, calculated by multiplying the price by the quantity sold.", "is_unstructured": false}, {"name": "SalesAndPurchases_PurchaseOrderQty", "dataType": "decimal", "description": "A decimal value indicating the quantity of the item ordered via a purchase order, important for managing inventory levels.", "is_unstructured": false}, {"name": "SalesAndPurchases_PurchaseOrderTotal", "dataType": "decimal", "description": "A decimal value representing the total cost associated with the purchase order for the item, critical for budgeting and financial analysis.", "is_unstructured": false}], "relationships": [{"child_table": "SalesAndPurchases", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.ID]", "child_column": "[SalesAndPurchases.SupplierID]"}], "relationship_type": "one-to-many"}, {"child_table": "SalesAndPurchases", "parent_table": "SupplierInformation", "key_column_mapping": [{"parent_column": "[Supplier.AccountNumber]", "child_column": "[SalesAndPurchases.SupplierAccount]"}], "relationship_type": "one-to-many"}]}