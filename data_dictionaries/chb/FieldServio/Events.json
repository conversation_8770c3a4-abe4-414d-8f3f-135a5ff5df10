{"tableName": "Events", "description": "The Events table stores records of scheduled business activities—including meetings, calls, and follow-ups—with details such as date, subject, status, priority, type, assigned employee, and company information. Events may also link to specific business opportunities. This table supports tracking, prioritization, and follow-up of customer and sales interactions, facilitating effective management of service delivery and business development processes.", "fields": [{"name": "Event_ID", "dataType": "int", "description": "A unique integer identifier for each event record, serving as the primary key to distinctly reference events.", "subsetOfAvailableValues": ["645831", "646371", "646816", "646818", "646856", "and 259 more..."], "is_unstructured": false}, {"name": "Event_Date", "dataType": "datetime", "description": "The scheduled date of the event, stored in datetime format, essential for tracking event timelines.", "is_unstructured": false}, {"name": "Event_Subject", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A brief text description summarizing the event's topic, providing quick reference for identification.", "subsetOfAvailableValues": ["Site Visit", "Follow up ", "Quote", "Discuss Preventative Maintenace Agreemet ", "Cold Call", "and 139 more..."], "is_unstructured": false}, {"name": "Event_Text", "dataType": "<PERSON><PERSON><PERSON>", "description": "A detailed text description offering additional context about the event, including specifics not captured in the subject.", "subsetOfAvailableValues": ["", "Follow-up", "follow up and inform of price increase", "Follow up ", "Contact <PERSON><PERSON><PERSON> to push his cust for PO", "and 193 more..."], "totalDistinctValueCount": 198, "is_unstructured": false}, {"name": "Event_Status", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A categorical indicator of the event's progress, with values such as 'Complete', 'Canceled', and 'Scheduled'.", "availableValues": ["Complete", "Scheduled", "Canceled"], "is_unstructured": false}, {"name": "Event_Priority", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A categorical field representing the urgency of the event, aiding in task prioritization with values like 'High', 'Med', and 'Low'.", "availableValues": ["Med", "High", "Low"], "is_unstructured": false}, {"name": "Event_EventType", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A categorical field defining the nature of the event, with types such as 'Face-to-Face', 'Follow-up', and 'Cold Call'.", "availableValues": ["Face-to-Face", "Follow-up", "Cold Call", "Email", "Preventative Maintenance "], "is_unstructured": false}, {"name": "Event_EventStartTime", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string indicating the starting time of the event, typically following a pattern for similar events.", "subsetOfAvailableValues": ["10:00", "09:00", "13:00", "11:00", "14:00", "and 85 more..."], "is_unstructured": false}, {"name": "Event_EventEndTime", "dataType": "n<PERSON><PERSON><PERSON>", "description": "A string representing the ending time of the event, providing essential scheduling information.", "subsetOfAvailableValues": ["11:00", "10:00", "12:00", "14:00", "14:30", "and 104 more..."], "is_unstructured": false}, {"name": "Event_NextCallDate", "dataType": "smalldatetime", "description": "A smalldatetime field indicating when the next follow-up call is planned, often missing for many records.", "is_unstructured": false}, {"name": "Event_AssignedEmployee", "dataType": "<PERSON><PERSON><PERSON>", "description": "A string representing the name of the employee responsible for managing the event, useful for record grouping.", "availableValues": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"], "totalDistinctValueCount": 6, "is_unstructured": false}, {"name": "Event_CompanyID", "dataType": "int", "description": "An integer identifier for the company associated with the event, potentially missing for some records.", "subsetOfAvailableValues": ["380499", "380518", "380528", "380553", "380647", "and 117 more..."], "is_unstructured": false}, {"name": "Event_CompanyName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field containing the name of the company linked to the event, aiding in identification.", "subsetOfAvailableValues": ["Kleysen Group Ltd.", "TransX Group of Companies", "Bison Fire Protection", "Pembina Valley Water Coop Inc.", "Structural Composite Technologies", "and 117 more..."], "totalDistinctValueCount": 122, "is_unstructured": false}, {"name": "Event_CompanyOffice", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field indicating the office location handling the event, with values like 'MB Office' and 'SK Office'.", "availableValues": ["MB Office", "SK Office", "Shared Services"], "totalDistinctValueCount": 3, "is_unstructured": false}, {"name": "Event_Attendees", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field listing individuals involved in the event, often containing unique combinations for identification.", "availableValues": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>,\r<PERSON>", "<PERSON><PERSON>", "<PERSON>,\r<PERSON><PERSON><PERSON>,\r<PERSON><PERSON>", "<PERSON><PERSON>,\r<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>,\r<PERSON><PERSON>", "<PERSON><PERSON>,\r<PERSON><PERSON><PERSON>", "<PERSON><PERSON>,\r<PERSON>", "<PERSON>,\r<PERSON><PERSON>", "<PERSON><PERSON><PERSON>,\r<PERSON>,\r<PERSON>"], "totalDistinctValueCount": 13, "is_unstructured": false}, {"name": "Event_OpportunityID", "dataType": "int", "description": "An integer reference to an associated business opportunity, often missing for a significant number of records.", "subsetOfAvailableValues": ["249846", "250409", "251741", "253692", "253912", "and 98 more..."], "is_unstructured": false}, {"name": "Event_OpportunityName", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field providing the name of the opportunity linked to the event, relevant for tracking sales.", "subsetOfAvailableValues": ["Kleysen Group", "TransX", "Cascades - Wall St.", "Structural Composite", "Bison Fire ", "and 98 more..."], "totalDistinctValueCount": 103, "is_unstructured": false}, {"name": "Event_OpportunityNextSteps", "dataType": "<PERSON><PERSON><PERSON>", "description": "A varchar field detailing the next steps related to the opportunity, which may be missing in many records.", "subsetOfAvailableValues": ["", "Update pricing with incentive amounts. Complete site visit with trades for installation costs\n\n", "Waiting on CN for approval ", "Logging equipment on site. ", "Met with <PERSON><PERSON><PERSON> who provided specs for new Kaeser comp to be installed - electrical/mechanical and ducting along with servicing unit and testing. Cust will also require a dryer and filtration. \n\nRevised quote sent to include relocation of customer supplied scale and air receiver tank. \nAdditional Revised quote sent to include repiping the compressor room. March 18/25", "and 36 more..."], "totalDistinctValueCount": 41, "is_unstructured": false}], "relationships": [{"child_table": "Events", "parent_table": "CustomerInformation", "key_column_mapping": [{"parent_column": "[Customer.ID]", "child_column": "[Event.CompanyID]"}], "relationship_type": "one-to-many"}, {"child_table": "Events", "parent_table": "Opportunities", "key_column_mapping": [{"parent_column": "[Opportunity.ID]", "child_column": "[Event.OpportunityID]"}], "relationship_type": "one-to-many"}]}