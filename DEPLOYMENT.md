# Deployment Notes

## LN-V3.20.4

1. Need to add visibility level to the existing data blocks.

   - db.getCollection('DataBlocks').updateMany({"user_visibility":{$exists:false}},{$set:{"user_visibility" : 10}})
   - db.DataBlocks.createIndex({ "user_visibility": 1 })

## LN-V3.20.3

1. Add following environment variables
   - SES_AWS_REGION
   - SES_AWS_ACCESS_KEY_ID
   - SES_AWS_SECRET_ACCESS_KEY
2. update below env variables in chat .env
   MODEL_VISUAL_RENDER=gpt-4.1

3. Add .env variable called ANALYSIS_EMAIL_TIME_THRESHOLD in seconds to control the email sending threshold. (default value is 300s)

## LN-V3.20.2

1. Add new environment variable to chat .env
   MODEL_KNOWLEDGE_GENERATION=o3

2. check db indexes

// Primary indexes for DataBlocks collection
db.DataBlocks.createIndex({ "session_id": 1 });
db.DataBlocks.createIndex({ "conversation_id": 1 });
db.DataBlocks.createIndex({ "session_id": 1, "conversation_id": 1 });

// Status and type indexes for filtering
db.DataBlocks.createIndex({ "status": 1 });
db.DataBlocks.createIndex({ "session_type": 1 });
db.DataBlocks.createIndex({ "final": 1 });

// Timestamp indexes for sorting and time-based queries
db.DataBlocks.createIndex({ "created_at": -1 });
db.DataBlocks.createIndex({ "updated_at": -1 });

// Compound indexes for common query patterns
db.DataBlocks.createIndex({ "session_id": 1, "status": 1 });
db.DataBlocks.createIndex({ "conversation_id": 1, "session_id": 1, "created_at": -1 });
db.DataBlocks.createIndex({ "session_id": 1, "final": 1 });

// Index for blocks array queries (if you query by block properties)
db.DataBlocks.createIndex({ "blocks.block_id": 1 });
db.DataBlocks.createIndex({ "blocks.block_type": 1 });
db.DataBlocks.createIndex({ "blocks.status": 1 });

// Primary indexes for Sessions collection
db.Sessions.createIndex({ "conversation_id": 1 });
db.Sessions.createIndex({ "user_id": 1 });
db.Sessions.createIndex({ "status": 1 });
db.Sessions.createIndex({ "type": 1 });

// Timestamp indexes for sorting and time-based queries
db.Sessions.createIndex({ "created_at": -1 });
db.Sessions.createIndex({ "updated_at": -1 });

// Compound indexes for common query patterns
db.Sessions.createIndex({ "conversation_id": 1, "status": 1 });
db.Sessions.createIndex({ "user_id": 1, "status": 1 });
db.Sessions.createIndex({ "conversation_id": 1, "created_at": -1 });
db.Sessions.createIndex({ "user_id": 1, "created_at": -1 });
db.Sessions.createIndex({ "conversation_id": 1, "type": 1 });

// Primary key and user-based indexes
db.Conversations.createIndex({ "userId": 1 });
db.Conversations.createIndex({ "status": 1 });
db.Conversations.createIndex({ "type": 1 });
db.Conversations.createIndex({ "isInternal": 1 });
db.Conversations.createIndex({ "isViewed": 1 });
db.Conversations.createIndex({ "isFavourite": 1 });

// Timestamp indexes for sorting and time-based queries
db.Conversations.createIndex({ "created_at": -1 });
db.Conversations.createIndex({ "updated_at": -1 });

// User conversations with status filtering
db.Conversations.createIndex({ "userId": 1, "status": 1 });
db.Conversations.createIndex({ "userId": 1, "type": 1 });
db.Conversations.createIndex({ "userId": 1, "isInternal": 1 });
db.Conversations.createIndex({ "userId": 1, "isViewed": 1 });
db.Conversations.createIndex({ "userId": 1, "isFavourite": 1 });

// User conversations with time sorting
db.Conversations.createIndex({ "userId": 1, "createdAt": -1 });
db.Conversations.createIndex({ "userId": 1, "updatedAt": -1 });

// Status-based queries with time sorting
db.Conversations.createIndex({ "status": 1, "createdAt": -1 });
db.Conversations.createIndex({ "status": 1, "updatedAt": -1 });

// Type-based queries with time sorting
db.Conversations.createIndex({ "type": 1, "createdAt": -1 });
db.Conversations.createIndex({ "type": 1, "updatedAt": -1 });

// Internal conversations filtering
db.Conversations.createIndex({ "isInternal": 1, "createdAt": -1 });
db.Conversations.createIndex({ "isInternal": 1, "updatedAt": -1 });

// Favourite conversations
db.Conversations.createIndex({ "userId": 1, "isFavourite": 1, "createdAt": -1 });
db.Conversations.createIndex({ "userId": 1, "isFavourite": 1, "updatedAt": -1 });

// Text search on conversation name
db.Conversations.createIndex({ "name": "text" });
db.Conversations.createIndex({ "userName": "text" });

## LN-V3.20.1

1. run below db scripts in chat db

   - create backups from below collections

     - Conversations
     - Messages
     - Sessions (if any)
     - DataBlocks

   - create backups manually or use below script

     - chat/scripts/3.20.1/mongo_migration_backup.js

   - run below js scripts in mongo shell using load command

     - chat/scripts/3.20.1/mongo_fix_conversation_ids.js
     - chat/scripts/3.20.1/mongo_migrate_sessions.js
     - chat/scripts/3.20.1/mongo_post_process_migration.js

## LN-V3.20.0

Environment variables to add:

1. Add below env variable to chat .env
   MODEL_COMPLEX_ANALYSIS=o3
   MODEL_EXCEL_UPDATER=o3

## LN-v3.19.7

1. Make Sure old users env file have below variable with proper value to send insight and analysis questions count to layernext-cms daily
   INSTALLATION_UUID
2. Make sure token counting is disabled in the .env file because now tiktoken is not compatible with the latest openai models.
   COUNT_TOKENS=False

## LN-V3.19.3

1. add belows to .env file
   SSO_INTERNAL_SERVER=http://sso_node_backend:8888

## LN-V3.19.2

1. add MODEL_VISUAL_RENDER=gpt-4o-mini-2024-07-18 to chat .env

## LN-V3.19.0

1. add belows to .env file
   DAILY_TRIGGER_HOUR=04
   DAILY_TRIGGER_MINUTE=10
   TIME_ZONE=America/Winnipeg

2. Environment variables to add:
   INSIGHT_PARALLEL_THREAD_COUNT --> To specify the thread count of insight parallel execution (default = 5)

## LN-V3.18.0

1. run below db query in chat db
   db.getCollection('Messages').updateMany({"is_llm_agent_input": true}, {$set:{"is_required_for_follow_ups":true}})

2. DB indexes added for unstructured data processing label cache:
   db.getCollection("LabelCacheMaster").createIndex({field_id:1, data_item_id:1})
   db.getCollection("LabelCacheMaster").createIndex({field_id:1,data_item_id:1, primary_label:1})

## LN-V3.16.5

### Environment variables to add:

MODEL_UNSTRUCTURED_PROCESSING=gpt-4o-mini-2024-07-18

MODEL_UNSTRUCTURED_LABEL_IDENTIFICATION --> gpt-4o-mini-2024-07-18 for azure, o3-mini for openai

GENERATE_QUESTION_SUGGESTIONS_FOR_USER=enabled/disabled

### DB changes:

- run chat/scripts/3.16.5/chat_list_filtering_index.js

## LN-V3.16.4

### Environment variables to add:

- SIMULTANEOUS_INSIGHT_INVOKES_LIMIT=<int> #default has set to 2

## LN-V3.16.3

### Environment variables to add:

MODEL_VISUAL_REVIEWER=gpt-4o-2024-08-06

## LN-V3.16.2

## Environment variables to update:

- TEMPERATURE = 0.7
- AZURE_OPENAI_API_VERSION = 2024-08-01-preview
- OPENAI_API_KEY
- DEEPSEEK_API_KEY
- LLM_API_KEY - for azure this is already there

- To send emails ensure that following env variables are there:

  - CLIENT_EMAILS - comma separated list of emails
  - SENDGRID_API_KEY
  - SUPPORT_EMAIL

- Added the variable to for unstructured data processing model
  MODEL_UNSTRUCTURED_PROCESSING=gpt-4o-mini-2024-07-18

### To Change the Provider

### 1. Update the `LLM_API_PROVIDER`

Set the `LLM_API_PROVIDER` to one of the following values:

- `azure-openai`
- `openai`
- `deep-seek`

### 2. Update the Models

Ensure the models are compatible with the selected provider.  
**Note:** Currently, the models are not tested for `deep-seek`.

---

### **Provider-Specific Configuration**

#### **Azure**

- `LLM_API_KEY=`
- `MODEL=gpt-4o-2024-08-06`
- `MODEL_INSIGHT=gpt-4o-2024-08-06`
- `CODE_REVIEW_MODEL=gpt-4-0125-preview`
- `MODEL_DATA_REVIEWER=gpt-4o-2024-08-06`
- `MODEL_HYPOTHESIS=gpt-4o-2024-08-06`
- `MODEL_REPORT_GENERATOR=gpt-4o-2024-08-06`
- `MODEL_JSON_OUTPUT=gpt-4o-mini-2024-07-18`

#### **OpenAI**

- `OPENAI_API_KEY=`
- `MODEL=gpt-4o`
- `MODEL_INSIGHT=gpt-4o`
- `CODE_REVIEW_MODEL=gpt-4-0125-preview`
- `MODEL_DATA_REVIEWER=gpt-4o`
- `MODEL_REPORT_GENERATOR=gpt-4o`
- `MODEL_HYPOTHESIS=gpt-4o`
- `MODEL_JSON_OUTPUT=gpt-4o-mini`

#### **Deep Seek**

- `DEEPSEEK_API_KEY=`
- `MODEL=deepseek-chat`
- `MODEL_INSIGHT=deepseek-reasoner`
- `CODE_REVIEW_MODEL=deepseek-reasoner`
- `MODEL_DATA_REVIEWER=deepseek-reasoner`
- `MODEL_REPORT_GENERATOR=deepseek-chat`
- `MODEL_HYPOTHESIS=deepseek-reasoner`

## Database Updates

- Run the script in `layernext-docker-image-deploy/chat/scripts/3.16.2/chat_database_retrieval_indexes.js` to add new indexes on chatDB

Database Changes :

1. Add field for initial question in messages collection
   isInitialQuestion : True -> for initial question otherwise False
2. Add a separate collection named "Users"

## LN-V3.15.1

Environment variables to update:S

1. Change the model of these in LLM backend as below:
   MODEL=gpt-4o-2024-08-06
   MODEL_INSIGHT=gpt-4o-2024-08-06

## LN-V3.15.0

Environment variables to update:

1. Data reviewer model - update to the latest (08-06) - to support json schema for reviewer feedback
   MODEL_DATA_REVIEWER=gpt-4o-2024-08-06

Database updates to apply:

1. Added default value (False) for the new flag "isChild" added to Messages collection.
   script path: layernext-docker-image-deploy/blob/3.15.0/chat/scripts/3.15.0/add_is_child_flag_to_message.js

## LN-V3.14.1

## LN-V3.14.0

## LN-V3.12.1

Environment variables added:

1. Data reviewer AI model name (default "gpt-4o")
   MODEL_DATA_REVIEWER

2. Update the sdk version to 3.12.1b1

## LN-V3.12.0

1. Add following env variable
   - CLIENT_EMAILS : comma separated emails to which the insight feed is sent (eg: <EMAIL>,<EMAIL>,<EMAIL>)
   - Insight feature is not actively using in customer setups. So delete the old insight chats. Because old insight chats aren't supported in new insight view.
     Fix:
     1.Delete conversations with conversation type 3

## LN-V3.11.1

## LN-V3.11.0

1. Update the sdk version to 3.11.0b1
2. Add missing document section "proposal" to data dict mapping
   script path: layernext-docker-image-deploy/blob/3.11.0/datalake/scripts/3.11.0/data-dict-mapping-add-proposal.js

## LN-V3.10.1

### Database Scripts

1. Hotfix: Add the missing document sections to data dictionary mapping in system data of MetaLake MongoDB.
   bill, certificate, email, memo, notice, schedule, transcript
   script path: layernext-docker-image-deploy/datalake/scripts/3.10.1/3.10.1-add-new-element-to-document-array.js

2. Hotfix : Add new environment variable for CODE REVIEW process.
   Add below env variable when deploying
   CODE_REVIEW_MODEL=gpt-4-0125-preview

3. Change the following model names
   gpt-4o-mini changed to gpt-4o-mini-2024-07-18
   gpt-4o changed to gpt-4o-2024-05-13

## LN-V3.10.0

1. Add new environment variable to chat .env
   LOG_BACKUP_COUNT - by default this is 90. This describes, size of the window which keeps logs files.

### Database Scripts

1. Before deployed to ensure our chatDB database -> conversations collection-> each document's status are in "completed". (run layernext-docker-image-deploy/chat/scripts/3.10.0/3.10.0-make-all-chat-status-completed.js)

2. Add searchKey field to the Conversation Documents in ChatDB->Conversations (run : chat/scripts/3.10.0/3.10.0-add-searchString-to-conversation-documents.js)

## LN-V3.9.2

1. Add new environment variable to chat .env

DOCUMENT_PROCESS_PAGE_SIZE=10
