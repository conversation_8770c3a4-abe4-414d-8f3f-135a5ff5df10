"""
 * Copyright (c) 2024 LayerNext Inc.
 *
 * all rights reserved.
 *
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
 *
Miscellaneous utility functions
"""

import math
import platform
from typing import List
from bson import ObjectId
from datetime import datetime

from utils.constant import DataBlockVisibility, UserType


def remove_fields_from_dict(data, fields_to_remove):
    """
    Recursively remove specified fields from a dictionary or list of dictionaries.

    Args:
        data (dict or list): Input dictionary or list of dictionaries.
        fields_to_remove (list): List of field names to remove.

    Returns:
        dict or list: Cleaned dictionary or list of dictionaries.
    """
    if isinstance(data, dict):
        # Remove specified fields from dictionary
        for field in fields_to_remove:
            if field in data:
                del data[field]

        # Recursively check nested dictionaries or lists
        for key, value in list(data.items()):
            if isinstance(value, (dict, list)):
                data[key] = remove_fields_from_dict(value, fields_to_remove)

    elif isinstance(data, list):
        # Apply the cleaning function to each element in the list
        for index, item in enumerate(data):
            if isinstance(item, (dict, list)):
                data[index] = remove_fields_from_dict(item, fields_to_remove)

    return data


def make_json_compatible(data):
    """
    Recursively makes data JSON-compatible by:
    - Converting MongoDB ObjectId to string
    - Replacing NaN, Infinity, and -Infinity with None
    """
    if isinstance(data, ObjectId):  # Handle MongoDB ObjectId
        return str(data)
    elif isinstance(data, float):  # Handle invalid floats
        if math.isinf(data) or math.isnan(data):
            return None  # Replace with None or any other placeholder
        return data
    elif isinstance(data, list):  # Handle lists
        return [make_json_compatible(item) for item in data]
    elif isinstance(data, dict):  # Handle dictionaries
        return {key: make_json_compatible(value) for key, value in data.items()}
    return data  # Return other data types unchanged


def get_table_headers_and_rows_from_csv(csv_file_name, chat_id, chat_log):
    import pandas as pd

    csv_path = f"./storage/public/{chat_id}/files/{csv_file_name}"
    try:
        df = pd.read_csv(csv_path)
        headers = list(df.columns)
        rows = df.values.tolist()
        return headers, rows
    except Exception as e:
        chat_log.info(f"Error reading CSV file {csv_path}: {e}")
        return [], []


def csv_to_markdown(file_path, max_rows):
    """
    Convert a CSV file to a markdown string.
    Note: If the no. of records goes over (Max rows), the data is truncated.
    Args:
    - file_path (str): The path to the CSV file to convert.
    - max_rows (int): The maximum number of rows to include in the markdown string.

    Returns:
    - (str, bool, int): The markdown string representation of the CSV file, in case of error, returns empty string + boolean flag indicating whether data was truncated or not, and row count

    """
    import pandas as pd

    row_count = 0
    try:
        # Load the CSV file into a DataFrame
        df = pd.read_csv(file_path)
        row_count = df.shape[0]
        # Check row count
        if df.shape[0] > max_rows:
            df = df.head(max_rows)
            truncated = True
        else:
            truncated = False
        # Convert DataFrame to markdown format
        markdown_string = df.to_markdown(index=False)
        markdown_string = markdown_string.encode("utf-8").decode("unicode_escape")
        return markdown_string, truncated, row_count
    except:
        return "", False, row_count


def list_to_markdown(dict_list: list):
    """
    Convert a list of dictionaries to a markdown table.
    Args:
    - dict_list (list): The list of dictionaries to convert.

    Returns:
    - str: The markdown table representation of the list of dictionaries.
    """
    import pandas as pd

    if not dict_list:
        return ""
    df = pd.DataFrame(dict_list)
    str_table = df.to_markdown(index=False)
    return str_table


def batch_generator(data_list, batch_size=20):
    batches = []
    for i in range(0, len(data_list), batch_size):
        batches.append(data_list[i : i + batch_size])
    return batches


def get_month_number(month_name):
    try:
        # Convert input to lowercase
        month_name = month_name.lower()
        # Get index (0-11) and add 1 to get month number (1-12)
        return [
            "january",
            "february",
            "march",
            "april",
            "may",
            "june",
            "july",
            "august",
            "september",
            "october",
            "november",
            "december",
        ].index(month_name) + 1
    except ValueError:
        return -1


def format_number(value):
    """Format a number into human-readable string with K, M, B abbreviations"""
    if value is None:
        return None

    # Convert to float and handle very small/large numbers
    value = float(value)
    abs_value = abs(value)

    if abs_value >= 1_000_000_000:
        formatted = f"{value/1_000_000_000:.2f}B"
    elif abs_value >= 1_000_000:
        formatted = f"{value/1_000_000:.2f}M"
    elif abs_value >= 1_000:
        formatted = f"{value/1_000:.2f}K"
    else:
        formatted = f"{value:.2f}"

    # Remove trailing zeros and decimal point if unnecessary
    formatted = formatted.rstrip("0").rstrip(".") if "." in formatted else formatted
    return formatted


def format_human_readable_date(dt: datetime) -> str:
    if not isinstance(dt, datetime):
        return ""

    try:
        day_format = "%-d" if platform.system() != "Windows" else "%#d"
        return dt.strftime(f"%B {day_format}, %Y")
    except Exception:
        return ""


def get_data_block_visibility_list(user_type: str) -> List[DataBlockVisibility]:
    if user_type == UserType.USER_TYPE_SUPER_ADMIN.value:
        return [DataBlockVisibility.ALL_USERS.value, DataBlockVisibility.SUPER_ADMIN_ONLY.value]
    else:
        return [DataBlockVisibility.ALL_USERS.value, DataBlockVisibility.ALL_EXCLUDE_SUPER_ADMIN.value]
