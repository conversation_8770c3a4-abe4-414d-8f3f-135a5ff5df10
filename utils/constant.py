"""
* Copyright (c) 2024 ZOOMi Technologies Inc.
*
* all rights reserved.
*
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
*
"""

from enum import Enum

MAIN_DATA_DICTIONARY = "overview.json"
MAIN_INSIGHT_DICTIONARY = "overview_insight.txt"
GLOBAL_BUSINESS_RULE_TABLE_IDENTIFIER = "-"

INSTRUCTIONS = {
    "chat_agent": {
        "initial_instruction": "instruction_initial.txt",
        "additional_initial_instructions_on_file_upload": "instruction_initial_additional_on_file_upload.txt",
        "instruction": "action_agent/python_code_instruct.txt",
    },
    "master_agent": {
        "initial_instruction": "agent_instruction_initial.txt",
        "instruction": "agent_instructions.txt",
    },
    "insight_agent": {
        "initial_instruction": "instruction_initial_insight.txt",
        "initial_instruction_data": "instruction_initial_data.txt",
        "instruction": "single_code_instruct.txt",
    },
    "insight_action_agent": {
        "initial_instruction": "instruction_initial.txt",
        "additional_initial_instructions_on_file_upload": "instruction_initial_additional_on_file_upload.txt",
        "instruction": "react/action.txt",
    },
    "pre_data_agent": {
        "initial_instruction": "instruction_initial.txt",
        "additional_initial_instructions_on_file_upload": "instruction_initial_additional_on_file_upload.txt",
        "instruction": "pre_data_query_instruction.txt",
    },
    "goal_tracker_agent": {
        "initial_instruction": "instruction_initial.txt",
        "instruction": "insight_app/single_code_instruct_goal.txt",
        "special_sources": ["goal-tracker"],
    },
    "insight_follow_up_agent": {
        "initial_instruction": "instruction_initial.txt",
        "additional_initial_instructions_on_file_upload": "instruction_initial_additional_on_file_upload.txt",
        "instruction": "single_code_instruct.txt",
    },
}

DATA_REVIEW_FEEDBACK_IDENTIFIER = "Reviewer feedback:"


# Agent types
class LLMAgentType(Enum):
    AGENT_TYPE_MASTER = "master_agent"
    AGENT_TYPE_CHAT = "chat_agent"
    AGENT_TYPE_INSIGHT = "insight_agent"
    AGENT_TYPE_GOAL_TRACKER = "goal_tracker_agent"
    AGENT_TYPE_ACTION = "insight_action_agent"
    AGENT_TYPE_PRE_DATA = "pre_data_agent"
    AGENT_TYPE_CODE_EXECUTION = "code_execution_agent"
    AGENT_TYPE_INSIGHT_FOLLOW_UP = "insight_follow_up_agent"
    AGENT_TYPE_COMPLEX_ANALYSIS = "complex_analysis_agent"
    AGENT_TYPE_USER_REACTION = "user_reaction_agent"
    AGENT_TYPE_COMPLEX_INSIGHT = "complex_insight_agent"
    AGENT_TYPE_COMPLEX_INSIGHT_REPORT_GENERATION = "complex_insight_report_generation_agent"


class ConversationType(Enum):  # this Enum should be removed and need to be replaced with LLMAgentType
    CHAT_BOT_CONVERSATION = 1
    AGENT_SETUP_CONVERSATION = 2
    INSIGHT_GEN_CONVERSATION = 3
    INSIGHT_FOLLOW_UP_CONVERSATION = 4


# Data Reviewer Stages
class DataReviewStage(Enum):
    TEXTUAL_REVIEW_STAGE = "textual_review"
    VISUAL_REVIEW_STAGE = "visual_review"


# Limits
LLM_MAX_LIMIT_INVOKES_PER_SESSION_CHAT_AGENT = 15
LLM_MAX_LIMIT_INVOKES_PER_SESSION_INSIGHT_AGENT = 25
LLM_MAX_LIMIT_REACT_CYCLES = 10
MAX_SESSIONS_PER_CONVERSATION = 20
SUB_LLM_MAX_ATTEMPTS_PER_SESSION = 2
SUB_LLM_MAX_CSV_FILE_LENGTH = 5000  # 5kB
LLM_INVOKE_NEWEST_PROMPT_MAX_LENGTH = 1024000  # 100kb - limit for LLM input from prev. result
MAX_REACT_ANALYSE_DATA_SIZE = 102400  # 100kb - limit for data size to be sent to react analyzer
SESSION_MAX_DATA_REVIEW_FAIL_COUNT = (
    4  # Maximum corrections can be attempted for failed data retrievals in the session (including re-starts)
)
MAX_DATA_AVAILABILITY_CHECK_COUNT = 3  # Maximum data availability checks allowed per session
MAX_VISUAL_REVIEW_FAIL_COUNT = 2
DEFAULT_SIMULTANEOUS_INSIGHT_INVOKES_LIMIT = 2
DEFAULT_INSIGHT_PARALLEL_THREAD_COUNT = 5  # Number of threads to be used for parallel execution of insight generation - Default value unless the env variable INSIGHT_PARALLEL_THREAD_COUNT is set
MAX_DATA_RETRIEVAL_FAILURES = (
    3  # Maximum number of consecutive failures allowed for data retrievals before re-starting the process
)
MAX_DATA_LOCATE_FAIL_LIMIT = (
    1  # Maximum number of consecutive failures allowed for data locate before re-starting the process
)

# supported image files
IMAGE_EXTENSIONS = [".png", ".jpg", ".jpeg"]
# supported other files
FILE_EXTENSIONS = [".csv", ".pdf", ".txt"]
# Agent types
AGENT_TYPES = ["mailBot", "uploadBot"]

# LLM providers names
LLM_PROVIDER_OPENAI = "openai"  # The default provider
LLM_PROVIDER_ANTHROPIC = "anthropic"
LLM_PROVIDER_AZURE_OPENAI = "azure-openai"
LLM_PROVIDER_DEEP_SEEK = "deep-seek"

LLM_OUTPUT_PYTHON_CODE_IDENTIFIERS = ["```python", "```\npython", "```\r\npython"]


class ConversationStatus(Enum):
    QUEUED = "queued"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    DELETED = "deleted"
    STOPPED = "stopped"
    FAILED = "failed"


class SessionStatus(Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    STOPPED = "stopped"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SectionType(Enum):
    DEFAULT = "default"
    QUESTION = "question"
    ACTION = "action"
    ANSWER = "answer"
    TITLE = "title"
    DATA_SOURCE = "data_source"
    QUERY_PLAN = "query_plan"


class SubSectionType(Enum):
    STEP_BAR = "step_bar"


class AnalysisStep(Enum):
    NOT_STARTED = ("not_started", "Not Started")
    METADATA_LOOKUP = ("metadata_lookup", "Looking up Metadata")
    DATA_SECTION_LOCATE = ("data_locate", "Finding Metadata")
    TABLE_ANALYSE = ("table_analyse", "Planning SQL Query")
    COLUMN_FINDING = ("column_finding", "Re-checking the Columns")
    SQL_GENERATION = ("sql_generation", "Generating SQL")
    SQL_REVIEW = ("sql_review", "Reviewing SQL")
    CODE = ("code", "Writing Code")
    CODE_REVIEW = ("code_review", "Reviewing Code")
    EXECUTE = ("execute", "Executing Code")
    UNSTRUCTURED_DATA_PROCESS = (
        "unstructured_data_process",
        "Unstructured data processing",
    )
    AGENTIC_FLOW = ("agentic_flow", "Running Deep Data Analysis")
    DATA_REVIEW = ("data_review", "Reviewing the Data")
    ANSWER_PREPARE = ("answer_prepare", "Preparing the Answer")
    COMPLETED = ("completed", "Analysis completed")
    REASONING_AND_PLANNING = ("reasoning_and_planning", "Reasoning and Planning")
    PREVIOUS_STEP = ("previous_step", "Previous Step")
    DATA_PROCESSING = ("data_processing", "Processing Data")
    EXCEL_UPDATE = ("excel_update", "Updating Excel Sheet")
    ARCHIVE_USE = ("archive_use", "Recalling from the Memory")
    KNOWLEDGE_FIND = ("knowledge_find", "Finding Knowledge")
    KNOWLEDGE_TREE_BUILDER = ("knowledge_tree_builder", "Updating Knowledge")

    @property
    def identifier(self):
        return self.value[0]

    @property
    def description(self):
        return self.value[1]


class InsightStep(Enum):
    HYPO_GEN = ("hypo_gen", "Generating hypotheses")
    HYPO_RUN = ("hypo_run", "Running hypothesis")
    REACT_RUN = ("react_run", "Reasoning and Acting")
    HYPO_REPORT_GEN = ("hypo_report_gen", "Generating hypothesis report")
    INSIGHT_REPORT_GEN = ("insight_report_gen", "Generating insight report")

    @property
    def identifier(self):
        return self.value[0]

    @property
    def description(self):
        return self.value[1]


class SectionStatus(Enum):
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    STOPPED = "stopped"
    FAILED = "failed"


class AgentTypes(Enum):
    MAIL_BOT = "mailBot"
    UPLOAD_BOT = "uploadBot"


class ExecutionMode(Enum):
    TEST = "test"
    LIVE = "live"


class AgentStatus(Enum):
    INITIALIZED = 1
    CONFIGURED = 2
    ACTIVE = 3
    PAUSE = 4
    ERRORED = 10


# Shared enums to hold the state of any agent
class AgentState(Enum):
    QUERY_INTENT_ROUTING = "query_intent_routing"
    FOLLOWUP_QUESTION_COMBINE = "followup_question_combine"
    DATA_RETRIEVAL = "data_retrieval"
    DATA_FINDING = "data_finding"
    TABLE_FINDING = "table_finding"
    COLUMN_FINDING = "column_finding"
    QUERY_PLANNING = "query_planning"
    UNCERTAINTY_DIAGNOSIS = "uncertainty_diagnosis"
    SQL_CONSTRUCTION = "sql_construction"
    SQL_REVIEW = "sql_review"
    PYTHON_CODE_GENERATION = "python_code_generation"
    CODE_REVIEW = "code_review"
    PYTHON_CODE_EXECUTION = "python_code_execution"
    TEXTUAL_REVIEW = "data_review"
    VISUAL_REVIEW = "visual_review"
    ANSWER_PREPARATION = "answer_preparation"
    METADATA_QUERY = "metadata_query"
    COMPLETED = "completed"
    FAILED = "failed"
    COMPLEX_REASONING = "complex_problem_reasoning"
    EXCEL_UPDATER = "excel_updater"
    VERIFY_CODE_GENERATION = "update_verifier"
    EXCEL_UPDATE_CODE_EXECUTION = "verification_code_execution"
    MEMORY_RECALL = "memory_recall"
    KNOWLEDGE_MANAGER = "knowledge_manager"
    KNOWLEDGE_TREE_BUILDER = "knowledge_tree_builder"
    COMPLEX_INSIGHT = "complex_insight"


class ConversationLLMStatus(Enum):
    # General chat
    LLM_DATA_SECTION_LOCATING = 1  # Initial phase of data section finding (default)
    LLM_DATA_RETRIEVAL = 2  # After the data section found - finnding required data for answer
    LLM_EXECUTE = 3  # Execute an action (eg: Python code) to get the anseer
    LLM_ANSWER_PROCESSING = 4  # Checking the answer phase - not used now
    LLM_FINISHED = 5  # Completed the flow (whether fail or success)
    LLM_POST_PROCESS = 6  # Sending of other details to perform task (In case of Agent - package installations)
    LLM_EXECUTION_FAILED = 7  # Execution failed
    # Insight generation
    LLM_INSIGHT_CATEGORY_LOCATING = 8  # Identifying the insight category
    LLM_INSIGHT_GENERATION = 9  # Generating insight report from insight data


class ConversationConclusion(Enum):
    # Final outcome of the conversation categorized
    OUTSIDE_SCOPE = "outside_scope"  # Eg: User asked to summarize the conversation
    GENERAL_MESSAGE = "general_message"  # Eg: Greeting, farewell, Thank you, etc.
    SYSTEM_FAILURE = "system_error"  # Eg: OpenAI error, database connectivity error
    DATA_RETRIEVAL_FAILURE = "data_retrieval_error"  # Eg: Python code errors, SQL errors, LLM invoke limit hit
    CANT_FIND_DATA = "relevant_data_not_found"  # Eg: couldn't locate section, out of scope
    NO_RECORDS = "no_records_returned"  # Eg: no matching records in DB
    TRUNCATED_RECORDS = "data_records_returned_but_truncated"  # Records truncated due to limit
    DATA_AVAILABLE = (
        "data_records_returned"  # Eg: Table with one or more records - all records present without truncation
    )
    NO_TABLE_ROWS = "no_table_rows"  # Eg: single answer
    USER_STOPPED = "stopped_by_user"  # User stopped the conversation by pressing the stop button


# Initial LLM response status
class DataLocateStatus(Enum):
    DATA_LOCATE_SUCCESS = 0
    SECTIONS_UNAVAILABLE = 1
    INVALID_SECTIONS_FORMAT = 2
    INVALID_SECTION_NAME = 3
    INVALID_DATASOURCE_NAME = 4
    NO_NEW_SECTIONS = 5


class MessageContentType(Enum):
    TEXT = "text"
    MEDIA = "media"
    DATA_SOURCE = "data_source"


class ChatAppUserMessages(Enum):
    TEAM_NOT_EXIST = "Couldn't find the team of the user"
    FAILED_TO_GET_CONVERSATION_ID = "Failed to get conversation id"
    CONVERSATION_ID_REQUIRED = "Conversation identifier is required"
    CHAT_CAN_NOT_FIND = "Unable to find the conversation. Please check the conversation ID and try again."
    CHAT_CAN_NOT_FIND_SESSIONS = "No session data available to view. The conversation may not have any sessions."
    CHAT_CAN_NOT_RECONNECT_ID_REQUIRED = (
        "Unable to reconnect to the conversation. Please provide a valid conversation ID to continue."
    )
    CHAT_CAN_NOT_RECONNECT_CONVERSATION_NOT_IN_MEMORY = (
        "Unable to reconnect to the conversation. The conversation session has expired or is no longer active."
    )
    FAILED_TO_RETRIEVE_CONVERSATION_HISTORY_BLOCKS = "Failed to retrieve conversation history"
    CHAT_DELETED = "Conversation has been deleted"
    FAILED_TO_INSERT_DOCUMENT = "Failed to insert document"
    FAILED_TO_AGGREGATE_MONGO_QUERY_FOR_GET_CONVERSATION_LIST = (
        "Failed to aggregate mongodb query when getting conversation list"
    )
    FAILED_TO_AGGREGATE_MONGO_QUERY_FOR_GET_MESSAGE_LIST = (
        "Failed to aggregate mongodb query when getting message list"
    )
    FAILED_TO_UPDATE_CONVERSATION = "Failed to update conversation detail"
    FAILED_TO_CREATE_CONVERSATION = "Failed to create new conversation"
    ERROR_OCCUR_WHILE_BULK_WRITE = "Error occur while bulk write messages to db"
    AGENT_NAME_ALREADY_EXIST = "Agent name already exist"
    INVALID_CONVERSATION_TYPE = "Invalid conversation type"
    INVALID_CONVERSATION_ID = "Invalid conversation id"
    INVALID_AGENT_CONFIGURATIONS = "Invalid agent configurations"
    FAILED_TO_FIND_EXECUTION_LOG = "Failed to find execution log"
    FAILED_TO_FIND_EXECUTION_SUMMARY = "Test Run Completed. Failed to find execution summary"
    AGENT_NOT_FOUND = "Agent not found"
    PACKAGE_INSTALLATION_FAILED = "Package installation failed"
    EXCEPTION_DURING_EXECUTION = (
        "An exception occurred during execution. Please check the execution logs for more details."
    )
    SESSION_LIMIT_EXCEEDED = "You have exceeded the maximum no of inputs for this conversation."
    NOT_CHAT_OWNER = "Not allowed. This chat is not associated with your account."
    FILE_NOT_FOUND_TO_DOWNLOAD = "Download failed. File not found"
    FORBIDDEN = "Sorry, access to the requested resource is forbidden."
    INSUFFICIENT_CONTEXT = "The current data dictionary does not provide enough context for the LLM agent to respond."


class ResponseKeywords(Enum):
    FINISH = "FINAL:"
    QUESTION = "QUESTION: "
    SECTIONS = "SECTIONS:"
    SECTIONS_FEEDBACK = "SECTIONS_FEEDBACK:"
    INSIGHT_CATEGORIES = "INSIGHT_CATEGORIES:"
    UNABLE = "UNABLE TO ANSWER"
    UNRELVENT = "UN_RELEVENT"
    IMAGE = "GRAPH_RESPONSE"
    CHAT_DELETE_ERROR = "Failed to delete the chat"
    CHAT_CAN_NOT_FIND = "Chat not found"
    CHAT_DELETED = "Chat has deleted"
    MEDIA = "MEDIA"
    FILE = "FILE:"
    RE_GENERATE = "RE_GENERATE"
    TASK = "TASK:"


CONNECTOR_ICON = "/api/public/file/download?file_path=storage/icons/connector.png"
LOGIC_ICON = "/api/public/file/download?file_path=storage/icons/logic.png"
TOOLS_ICON_MAP: dict = {"email_sender": "/api/public/file/download?file_path=storage/icons/Mail.png"}


class ConnectionSourceType(Enum):
    QUICK_BOOK = "qb"
    MONGO_DB = "mongodb"
    AWS_S3 = "aws_s3"
    GCS = "gcs"
    AZURE_BLOB = "azure_blob"
    MYSQL_DB = "mysql"
    MSSQL_DB = "mssql"


CONNECTIONS_ICON_MAP: dict = {
    ConnectionSourceType.QUICK_BOOK.value: "/api/public/file/download?file_path=storage/icons/quickbooks.png",
    ConnectionSourceType.MONGO_DB.value: "/api/public/file/download?file_path=storage/icons/mongodb.png",
    ConnectionSourceType.AWS_S3.value: "/api/public/file/download?file_path=storage/icons/s3.png",
    ConnectionSourceType.GCS.value: "/api/public/file/download?file_path=storage/icons/gcloud.png",
    ConnectionSourceType.AZURE_BLOB.value: "/api/public/file/download?file_path=storage/icons/azure.png",
    ConnectionSourceType.MYSQL_DB.value: "/api/public/file/download?file_path=storage/icons/mysql.png",
    ConnectionSourceType.MSSQL_DB.value: "/api/public/file/download?file_path=storage/icons/mssql.png",
}


class ObjectType(Enum):
    VIDEO = 1
    IMAGE = 2
    DATASET = 3
    VIDEO_COLLECTION = 4
    IMAGE_COLLECTION = 5
    OTHER = 6
    OTHER_COLLECTION = 7


# Sub LLM operation modes


class SubLLMOperationMode(Enum):
    SUB_LLM_EXTRACTOR = "Extractor"
    SUB_LLM_ANALYSIS = "Analysis"


class GoalInsightGenerationStatus(Enum):
    GOAL_INSIGHT_NOT_GENERATED = 0
    GOAL_INSIGHT_IN_PROGRESS = 1
    GOAL_INSIGHT_GENERATE_FINISHED = 2
    GOAL_INSIGHT_GENERATE_FAILED = 3
    GOAL_INSIGHT_ARCHIVED = 4


class InsightAppSection(Enum):
    SECTION_GOAL_TRACKING = 1
    SECTION_INSIGHT_OBSERVATIONS = 2


class GoalTrackingWithInsightStatus(Enum):
    GOAL_TRACKING_AND_INSIGHT_DATA_PENDING = 1
    GOAL_TRACKING_READY_INSIGHT_DATA_PENDING = 2
    DATA_GENERATION_COMPLETED = 3
    DATA_GENERATION_FAILED = 4


class InsightReportStatus(Enum):
    DRAFT = "drafted"  # just received insight question
    QUEUED = "queued"  # put to queue since ongoing insight generation has reached limit
    HYPO_GENERATING = "starting"  # Hypothesis generating
    HYPO_RUNNING = "running"  # Hypothesis generated and running
    HYPO_COMPLETED = "completed"  # Hypothesis running completed (inter reports available)
    FINALIZED = "finalized"  # Master report generated
    REGENERATE_REQUESTED = "regenerate"  # When user feedback given to some point in the chain of thought of a hypothesis, but queued for regeneration
    FAILED = "failed"  # Unexpected termination of the process
    STOPPED = "stopped"  # User stopped the process


class HypothesisReportStatus(Enum):
    DRAFT = "drafted"  # just hypothesis created
    REACT_RUNNING = "running"  # running react cycles
    REACT_COMPLETED = "completed"  # react cycles completed
    FINALIZED = "finalized"  # intern report generated
    REGENERATE_REQUESTED = (
        "regenerate"  # When user feedback given to some point in the chain of thought, but queued for regeneration
    )
    FAILED = "failed"
    STOPPED = "stopped"  # User stopped the process


class HypothesisStatus(Enum):  # proved, disproved, no_data
    PROVED = "proved"  # A LLM decision according to observations
    DISPROVED = "disproved"  # A LLM decision according to observations
    INCONCLUSIVE = "inconclusive"  # A LLM decision according to observations
    PENDING = "pending"  # React cycles not started or running
    ERRORED = "error"  # process failed


class UserType(Enum):
    USER_TYPE_ANNOTATOR = 0
    USER_TYPE_AUDITOR = 1
    USER_TYPE_SUPER_ADMIN = 2
    USER_TYPE_TEAM_ADMIN = 3
    USER_TYPE_QA = 4
    USER_TYPE_COLLABORATOR = 5


class DashboardInsightAudience(Enum):
    PRIVATE = "private"
    RESTRICTED = "restricted"
    PUBLIC = "public"


class SchedulerFrequency(Enum):
    NONE = "none"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    QUARTERLY = "quarterly"


# Report section IDs
class ReportSectionId:
    KEY_FINDINGS = "keyFindings"
    ACTIONABLE_RECOMMENDATIONS = "actionableRecommendations"
    OTHER_FACTS = "otherFacts"
    APPENDIX = "appendix"
    DIAGNOSTIC_ANALYSIS = "diagnosticAnalysis"


# Report subsection IDs
class ReportSubSectionId:
    SHORT_DESCRIPTION = "shortDescription"
    MAIN_FACTS = "mainFacts"
    VISUALIZATIONS = "visualizations"
    BULLET_POINTS = "bulletPoints"
    INSIGHTS = "insights"
    FURTHER_OBSERVATIONS = "furtherObservations"


MASTER_REPORT_JSON_TEMPLATE = {
    "keyFindings": {
        "shortDescription": "",
        "mainFacts": [],
        "visualizations": [],
    },
    "actionableRecommendations": {
        "shortDescription": "",
        "bulletPoints": [],
    },
    "otherFacts": {
        "bulletPoints": [],
    },
    "appendix": {
        "visualizations": [],
    },
    "diagnosticAnalysis": {
        "insights": [],
        "furtherObservations": [],
    },
}


class ModelProvider(Enum):
    OPENAI = "openai"


class DataSourceCategory(Enum):
    DOCUMENT = "doc"
    DATABASE = "db"


class StreamChunkOperation(Enum):
    ADD = "add"
    REPLACE = "replace"
    REMOVE = "remove"


class FrontendBlockType(Enum):
    USER_QUESTION = ("USER_QUESTION", 1000)
    MARKDOWN = ("MARKDOWN", 2000)  # tab type required
    AGENT_QUESTION = ("AGENT_QUESTION", 3000)
    MEDIA = ("MEDIA", 4000)  # tab type required
    DATA_SOURCES = ("DATA_SOURCES", 5000)  # tab type required
    TAB_CONTENT = ("TAB_CONTENT", 6000)
    ANALYSIS_STEPS = ("ANALYSIS_STEPS", 7000)
    TITLE = ("TITLE", 8000)
    USER_REACTION = ("USER_REACTION", 9000)

    @property
    def type_value(self):
        return self.value[0]

    @property
    def suffix_value(self):
        return self.value[1]


class FrontendTabContentType(Enum):
    ANSWER = (
        "answer",
        "Answer",
    )  # this tab act as two types in frontend: 1. answer tab (when stream is in progress) 2. final conclusion tab (when loading from history)
    DEEP_RESEARCH = ("deep_research", "Deep Research")
    SOURCES = ("sources", "Data Sources")
    TASKS = ("tasks", "Tasks")
    QUERY = ("query", "Query")  # need to remove
    TABS = ("tabs", "Tabs")  # need to remove
    FINAL_CONCLUSION = (
        "final_conclusion",
        "Final Conclusion",
    )  # this tab doesn't exist in frontend, only keep in backend to differentiate answer tab double behavior
    ANALYSIS_STEPS = ("analysis_steps", "Analysis Steps")  # need to remove
    TITLE = ("title", "Title")  # need to remove

    @property
    def tab_type_value(self):
        return self.value[0]

    @property
    def tab_title_value(self):
        return self.value[1]


class SessionType(Enum):
    ANALYSIS = "analysis"
    DEEP_RESEARCH = "deep_research"
    COMPLEX_ANALYSIS = "complex_analysis"
    USER_REACTION = "user_reaction"
    ADD_TO_DASHBOARD = "add_to_dashboard"


class DataBlockHandlerType(Enum):
    STREAM = "stream"
    PERSIST = "persist"
    PERSIST_AND_STREAM = "persist_and_stream"


class FrontendBlockStatus(Enum):
    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    STOPPED = "stopped"
    FAILED = "failed"


class FrontendBlockDataMessages(Enum):
    PRE_PROCESSING_USER_PROMPT = "\n\n🔄 Processing your request...\n\n"
    INTENT_CLASSIFICATION = "\n\n🎯 Understanding what you need...\n\n"
    GENERATING_PYTHON_CODE = "\n\n💻 Writing Python code to process data...\n\n"
    PYTHON_CODE_GENERATED = "\n\n✅ Python code created successfully\n\n"
    EXECUTING_PYTHON_CODE = "\n\n⚡ Executing Python code...\n\n"
    PYTHON_CODE_EXECUTED = "\n\n✅ Analysis completed successfully\n\n"
    REVIEWING_CODE = "\n\n🔍 Checking code quality and safety...\n\n"
    CODE_REVIEW_PASSED = "\n\n✅ Code review passed - everything looks good!\n\n"
    CODE_REVIEW_FAILED = "\n\n❌ Code needs improvement - fixing it now...\n\n"
    REVIEWING_TEXTUAL_DATA = "\n\n📝 Reviewing the data for accuracy...\n\n"
    TEXTUAL_DATA_REVIEW_PASSED = "\n\n✅ Data review passed - results are reliable\n\n"
    TEXTUAL_DATA_REVIEW_FAILED = "\n\n❌ Data review failed - need to improve the analysis\n\n"
    REVIEWING_VISUAL_DATA = "\n\n📊 Reviewing charts and visualizations...\n\n"
    VISUAL_DATA_REVIEW_PASSED = "\n\n✅ Visualizations look great!\n\n"
    VISUAL_DATA_REVIEW_FAILED = "\n\n❌ Visualizations need improvement - working on it...\n\n"
    METADATA_LOOKUP_IN_PROGRESS = "\n\n🔍 Exploring your data structure...\n\n"
    METADATA_LOOKUP_PASSED = "\n\n✅ Found the right data sources\n\n"
    METADATA_LOOKUP_FAILED = "\n\n❌ Having trouble finding the right data\n\n"
    SQL_QUERY_GENERATED = "\n\n✅ Database query created successfully\n\n"
    SQL_REVISION_REQUIRED = "\n\n⚠️ Query needs adjustment - improving it now...\n\n"
    SQL_REVIEW_SUCCESS = "\n\n✅ Query looks perfect!\n\n"
    SQL_REVIEW_FAILED = "\n\n❌ Query needs fixing - working on it...\n\n"
    SQLDataFinderAgent_TASK = "\n\n🔍 Finding the right tables and columns...\n\n"
    SQLMissedDataFinderAgent_TASK = "\n\n🔍 Finding the missing columns...\n\n"
    SQLQueryPlannerAgent_TASK = "\n\n📋 Planning the query to retrieve data...\n\n"
    SQLConstructionAgent_TASK = "\n\n🔧 Building the database query...\n\n"
    SQLReviewAgent_TASK = "\n\n🔍 Double-checking the query for accuracy...\n\n"
    ANSWER_PREPARE_TASK = "\n\n📝 Preparing your final answer...\n\n"
    ANSWER_PREPARE_SUCCESS = "\n\n✅ Your answer is ready!\n\n"
    CHECKING_FILE_UPLOADS = "\n\n🔍 Checking your uploaded files...\n\n"
    CHECKING_FILE_UPLOADS_SUCCESS = "\n\n✅ Files processed successfully\n\n"
    CHECKING_FILE_UPLOADS_FAILED = "\n\n❌ Having trouble with the uploaded files\n\n"
    REASONING_AND_PLANNING = "\n\n🧠 Reasoning and Planning...\n\n"
    INSTRUCTIONS_TO_SQL_DATA_RETRIEVAL_TOOL = "\n\n🔍 Running SQL query to retrieve data...\n\n"
    INSTRUCTIONS_TO_DATA_PROCESSING_TOOL = "\n\n⚙️ Processing your data...\n\n"
    INSTRUCTIONS_TO_METADATA_FINDER_TOOL = "\n\n📋 Finding relevant data...\n\n"
    INSTRUCTIONS_TO_EXCEL_UPDATER_TOOL = "\n\n📊 Updating excel sheet...\n\n"
    INSTRUCTIONS_TO_MEMORY_RECALL_TOOL = "\n\n💭 Recalling the previous results...\n\n"
    INSTRUCTIONS_TO_KNOWLEDGE_FINDER_TOOL = "\n\n📖 Querying the knowledge base...\n\n"
    INSTRUCTIONS_TO_USER_INPUT_TOOL = "\n\n🙋 Seeking your input to clarify...\n\n"
    PROCESS_STOPPED_BY_USER = "\n\n⏹️ Analysis stopped\n\n"
    ADD_TO_DASHBOARD_TASK = "\n\n📊 Adding your analysis to the dashboard...\n\n"
    ADD_TO_DASHBOARD_SUCCESS = "\n\n✅ Analysis added to the dashboard successfully\n\n"
    ADD_TO_DASHBOARD_FAILED = "\n\n❌ Having trouble adding your analysis to the dashboard\n\n"
    REFRESH_DASHBOARD_TASK = "\n\n🔄 Refreshing the dashboard with latest data...\n\n"
    FURTHER_ANALYSIS_IN_PROGRESS = "\n\n🔍Starting further analysis of KPI ...\n\n"
    FURTHER_ANALYSIS_COMPLETED = "\n\n✅ Further analysis of KPI completed\n\n"


class AgentToolName(Enum):
    KNOWLEDGE_FINDER = "Knowledge_Finder"
    SQL_DATA_RETRIEVAL_TOOL = "SQL_Data_Retrieval_Tool"
    DATA_PROCESSING_TOOL = "Data_Processing_Tool"
    METADATA_FINDER = "Metadata_Finder"
    EXCEL_UPDATER = "Excel_Updater"
    MEMORY_RECALL = "Memory_Recall"
    USER_INPUT = "User_Input_Tool"


class UserReaction(Enum):
    THUMBS_UP = "thumbs_up"
    THUMBS_DOWN = "thumbs_down"
    PENDING = "pending"


class DataBlockVisibility(Enum):
    ALL_USERS = 10
    SUPER_ADMIN_ONLY = 20
    ALL_EXCLUDE_SUPER_ADMIN = 30
