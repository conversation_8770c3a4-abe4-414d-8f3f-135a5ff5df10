Python code:

```python
# invoices_monthly_2025_export.py
"""
Loads 2025 monthly invoice revenue data, saves as CSV, and creates a line chart PNG.
"""
import pandas as pd
import matplotlib; matplotlib.use("Agg")
import matplotlib.pyplot as plt
import seaborn as sns
from metalake import load_data


def main():
    chat_id = "688425b004c36975c3192bfc"
    data_file_name = "invoices_monthly_2025.dat"

    # Load the full, pre-filtered dataset
    df = load_data(chat_id, data_file_name)

    if df.empty:
        print("No data returned for the specified parameters.")
        return

    # Save the full DataFrame as CSV
    csv_filename = "invoices_monthly_2025.csv"
    df.to_csv(csv_filename, index=False)

    # For plotting, ensure correct types and ordering
    df_plot = df.copy()
    df_plot["Month"] = pd.to_datetime(df_plot["Month"], format="%Y-%m")
    df_plot = df_plot.sort_values("Month")
    df_plot["Total_Revenue"] = pd.to_numeric(df_plot["Total_Revenue"], errors="coerce")

    # Create line chart
    plt.figure(figsize=(10, 5))
    sns.lineplot(data=df_plot, x="Month", y="Total_Revenue", marker="o")
    plt.title("Monthly Revenue Trend – 2025")
    plt.xlabel("Month")
    plt.ylabel("Total Revenue")
    plt.gca().yaxis.set_major_formatter(
        plt.matplotlib.ticker.StrMethodFormatter("{x:,.2f}")
    )
    plt.xticks(rotation=45)
    plt.tight_layout()
    png_filename = "invoices_monthly_2025.png"
    plt.savefig(png_filename, dpi=300)
    plt.close()

    # Show up to 50 rows as markdown preview
    preview = df.head(50)
    print(preview.to_markdown(index=False))
    if len(df) > 50:
        print("\nNote: output truncated to first 50 rows.")


if __name__ == "__main__":
    main()

````

Output:

| Month   |   Total_Revenue |
|:--------|----------------:|
| 2025-01 |          289592 |
| 2025-02 |          262110 |
| 2025-03 |          287058 |
| 2025-04 |          283328 |
| 2025-05 |          293763 |
| 2025-06 |          271276 |
| 2025-07 |          223458 |

