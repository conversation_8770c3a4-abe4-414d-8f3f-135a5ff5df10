SQL Query:

````
SELECT
  week_start,
  ST.Description AS service_type,
  SUM(I.Total_Charge) AS revenue
FROM (
  SELECT
    SR.Service_ID,
    SR.Service_Type_ID,
    SR.Employee_ID,
    SR.Service_Date,
    DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) AS week_start
  FROM ServiceRecords SR
  INNER JOIN EmployeeData ED ON SR.Employee_ID = ED.Employee_ID
  WHERE
    LOWER(ED.Role) LIKE 'technician'
    AND (ED.Leave_Date IS NULL OR ED.Leave_Date >= SR.Service_Date)
    AND DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) IN (
      DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 2 WEEK),
      DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 1 WEEK)
    )
) AS sub
INNER JOIN Invoices I ON sub.Service_ID = I.Service_ID
INNER JOIN ServiceTypes ST ON sub.Service_Type_ID = ST.Service_Type_ID
GROUP BY week_start, ST.Description
ORDER BY week_start ASC, revenue DESC;
````

Data retrieved successfully:

| week_start               | service_type   |   revenue |
|:-------------------------|:---------------|----------:|
| 2025-07-07T00:00:00.000Z | Brake Repair   |   27494.9 |
| 2025-07-07T00:00:00.000Z | Oil Change     |   14036.5 |
| 2025-07-07T00:00:00.000Z | Car Wash       |   12390.1 |
| 2025-07-07T00:00:00.000Z | Tyre Change    |   10772.9 |
| 2025-07-14T00:00:00.000Z | Brake Repair   |   27343.2 |
| 2025-07-14T00:00:00.000Z | Car Wash       |   13024.8 |
| 2025-07-14T00:00:00.000Z | Tyre Change    |   11657   |
| 2025-07-14T00:00:00.000Z | Oil Change     |   11351.5 |