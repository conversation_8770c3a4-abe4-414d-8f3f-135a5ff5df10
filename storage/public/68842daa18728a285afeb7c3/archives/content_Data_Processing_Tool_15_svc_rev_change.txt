Python code:

```python
import pandas as pd
from metalake import load_data

chat_id = "68842e6718728a285afeb875"
data_file = "revenue_by_service_type_2weeks.dat"

# Load the full, pre-filtered dataset
revenue_df = load_data(chat_id, data_file)

# Check for empty DataFrame
if revenue_df.empty:
    print("No data available for revenue by service type for the last two weeks.")
else:
    # Ensure ascending order for week_start and service_type
    df_sorted = revenue_df.sort_values(['week_start', 'service_type'], ascending=[True, True])
    # Identify the two weeks
    week_labels = df_sorted['week_start'].unique()
    if len(week_labels) != 2:
        print(f"Expected data for 2 weeks, found {len(week_labels)}. Unable to process.")
    else:
        prev_week, latest_week = week_labels[0], week_labels[1]
        # Pivot: rows = service_type, columns = prev_revenue, latest_revenue
        pivot = df_sorted.pivot(index='service_type', columns='week_start', values='revenue')
        # Rename columns for clarity
        pivot = pivot.rename(columns={prev_week: 'prev_revenue', latest_week: 'latest_revenue'})
        # Fill missing with 0
        pivot = pivot.fillna(0)
        # Calculate change columns
        pivot['abs_change'] = pivot['latest_revenue'] - pivot['prev_revenue']
        pivot['pct_change'] = pivot.apply(
            lambda row: round(100 * row['abs_change'] / row['prev_revenue'], 2) if row['prev_revenue'] != 0 else None,
            axis=1
        )
        # Format revenue columns to two decimals
        for col in ['prev_revenue', 'latest_revenue', 'abs_change']:
            pivot[col] = pivot[col].apply(lambda x: f"{x:.2f}")
        pivot['pct_change'] = pivot['pct_change'].apply(lambda x: f"{x:.2f}" if x is not None else 'N/A')
        # Reset index for output
        result = pivot.reset_index()[['service_type', 'prev_revenue', 'latest_revenue', 'abs_change', 'pct_change']]
        # Save to CSV and DAT
        result.to_csv("files/service_type_revenue_change.csv", index=False)
        result.to_csv("files/service_type_revenue_change.dat", index=False)
        # Display up to 50 rows as Markdown
        display_rows = min(len(result), 50)
        print(result.head(display_rows).to_markdown(index=False))
        if len(result) > 50:
            print("Note: Table truncated to first 50 rows.")

````

Output:

| service_type   |   prev_revenue |   latest_revenue |   abs_change |   pct_change |
|:---------------|---------------:|-----------------:|-------------:|-------------:|
| Brake Repair   |        27494.9 |          27343.2 |      -151.7  |        -0.55 |
| Car Wash       |        12390.1 |          13024.8 |       634.64 |         5.12 |
| Oil Change     |        14036.5 |          11351.5 |     -2684.97 |       -19.13 |
| Tyre Change    |        10772.9 |          11657   |       884.13 |         8.21 |

