Python code:

```python
import pandas as pd
from metalake import load_data

chat_id = "68842e5418728a285afeb855"

def round2(val):
    return round(val, 2) if pd.notnull(val) else None

# Step 1: Load both datasets
revenue_df = load_data(chat_id, "weekly_revenue_metrics_last2weeks.dat")
tech_df = load_data(chat_id, "weekly_tech_productivity_12w.dat")

# Check for empty data
if revenue_df.empty or tech_df.empty:
    print("Required data for revenue change summary is missing.")
else:
    # Step 2: Filter tech_df to only week_start in revenue_df (should be last two weeks)
    weeks = revenue_df['week_start'].unique()
    tech2 = tech_df[tech_df['week_start'].isin(weeks)].copy()

    # Step 3: Merge on week_start
    merged = pd.merge(revenue_df, tech2, on='week_start', how='inner')
    if merged.shape[0] != 2:
        print(f"Unable to match both weeks between datasets. Found {merged.shape[0]} matches.")
    else:
        # Step 4: Compute revenue_per_tech
        merged['revenue_per_tech'] = merged['total_revenue'] / merged['tech_count']
        merged['revenue_per_tech'] = merged['revenue_per_tech'].apply(round2)
        # Make sure values are float for calculation
        metrics = {
            'total_revenue': merged['total_revenue'].values,
            'job_count': merged['job_count'].values,
            'tech_count': merged['tech_count'].values,
            'avg_rev_per_job': merged['avg_revenue_per_job'].values,
            'revenue_per_tech': merged['revenue_per_tech'].values
        }
        
        # Step 5: Reshape into change table
        rows = []
        for metric, vals in metrics.items():
            prev_value, latest_value = vals[0], vals[1]
            abs_change = latest_value - prev_value
            pct_change = (abs_change / prev_value * 100) if prev_value != 0 else None
            # Format values: two decimals for all
            row = {
                'metric': metric,
                'prev_value': f"{prev_value:.2f}",
                'latest_value': f"{latest_value:.2f}",
                'abs_change': f"{abs_change:.2f}",
                'pct_change': f"{pct_change:.2f}" if pct_change is not None else 'N/A'
            }
            rows.append(row)
        summary_df = pd.DataFrame(rows, columns=['metric', 'prev_value', 'latest_value', 'abs_change', 'pct_change'])
        # Step 6: Save as CSV and DAT
        summary_df.to_csv("files/revenue_kpi_change_summary.csv", index=False)
        summary_df.to_csv("files/revenue_kpi_change_summary.dat", index=False)
        # Print Markdown Table (≤50 rows)
        display_rows = min(len(summary_df), 50)
        print(summary_df.head(display_rows).to_markdown(index=False))
        if len(summary_df) > 50:
            print("Note: Table truncated to first 50 rows.")

````

Output:

| metric           |   prev_value |   latest_value |   abs_change |   pct_change |
|:-----------------|-------------:|---------------:|-------------:|-------------:|
| total_revenue    |     64694.4  |       63376.5  |     -1317.9  |        -2.04 |
| job_count        |      1021    |        1011    |       -10    |        -0.98 |
| tech_count       |        45    |          44    |        -1    |        -2.22 |
| avg_rev_per_job  |        63.36 |          62.69 |        -0.67 |        -1.06 |
| revenue_per_tech |      1437.65 |        1440.37 |         2.72 |         0.19 |

