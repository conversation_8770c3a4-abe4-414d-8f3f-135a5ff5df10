SQL Query:

````
SELECT
  week_start,
  COUNT(*) AS total_jobs,
  COUNT(DISTINCT sub.Employee_ID) AS tech_count,
  ROUND(COUNT(*) / NULLIF(COUNT(DISTINCT sub.Employee_ID), 0), 2) AS avg_jobs_per_tech
FROM (
  SELECT
    SR.Service_ID,
    SR.Employee_ID,
    SR.Service_Date,
    DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) AS week_start
  FROM ServiceRecords SR
  INNER JOIN EmployeeData ED ON SR.Employee_ID = ED.Employee_ID
  WHERE
    LOWER(ED.Role) LIKE 'technician'
    AND (ED.Leave_Date IS NULL OR ED.Leave_Date >= SR.Service_Date)
    AND SR.Service_Date >= DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 12 WEEK)
    AND SR.Service_Date < DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY)
) AS sub
GROUP BY week_start
ORDER BY week_start ASC;
````

Data retrieved successfully:

| week_start               |   total_jobs |   tech_count |   avg_jobs_per_tech |
|:-------------------------|-------------:|-------------:|--------------------:|
| 2025-04-28T00:00:00.000Z |         1046 |           43 |               24.33 |
| 2025-05-05T00:00:00.000Z |         1028 |           43 |               23.91 |
| 2025-05-12T00:00:00.000Z |         1068 |           43 |               24.84 |
| 2025-05-19T00:00:00.000Z |         1037 |           43 |               24.12 |
| 2025-05-26T00:00:00.000Z |         1031 |           43 |               23.98 |
| 2025-06-02T00:00:00.000Z |         1028 |           45 |               22.84 |
| 2025-06-09T00:00:00.000Z |         1021 |           45 |               22.69 |
| 2025-06-16T00:00:00.000Z |         1007 |           45 |               22.38 |
| 2025-06-23T00:00:00.000Z |         1013 |           45 |               22.51 |
| 2025-06-30T00:00:00.000Z |         1033 |           45 |               22.96 |
| 2025-07-07T00:00:00.000Z |         1021 |           45 |               22.69 |
| 2025-07-14T00:00:00.000Z |         1011 |           44 |               22.98 |