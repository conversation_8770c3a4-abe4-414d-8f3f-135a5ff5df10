Python code:

```python
import pandas as pd
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
from metalake import load_data

# Load full dataset
chat_id = "688302d3742e7c197211cf3d"
data_file_name = "vendorbills_overdue_by_month.dat"
df = load_data(chat_id, data_file_name)

# Handle empty DataFrame
if df.empty:
    print("No overdue vendor bill data available for the selected months.")
else:
    # Sort by DueMonth ascending
    df_sorted = df.sort_values(by="DueMonth", ascending=True)
    # Save full DataFrame to CSV
    csv_path = "files/vendorbills_overdue_by_month.csv"
    df_sorted.to_csv(csv_path, index=False)
    # Display first 50 rows as markdown (note if truncated)
    display_rows = min(50, len(df_sorted))
    print(df_sorted.head(display_rows).to_markdown(index=False))
    if len(df_sorted) > 50:
        print(f"\nNote: Only first 50 of {len(df_sorted)} rows shown above.")
    # Plot bar chart
    plt.figure(figsize=(10,6))
    plt.bar(df_sorted['DueMonth'], df_sorted['OverdueAmount'], color='skyblue')
    plt.title("Monthly Overdue Vendor Bill Amount (Last 6 Months)")
    plt.xlabel("Due Month")
    plt.ylabel("Overdue Amount (USD)")
    plt.xticks(rotation=45)
    plt.tight_layout()
    image_path = "files/vendorbills_overdue_by_month.png"
    plt.savefig(image_path)

````

Output:

| DueMonth   |   OverdueAmount |
|:-----------|----------------:|
| 2025-02    |  34244.2        |
| 2025-03    | 102102          |
| 2025-04    | 136336          |
| 2025-05    | 164483          |
| 2025-06    |      1.5854e+06 |
| 2025-07    | 539256          |

