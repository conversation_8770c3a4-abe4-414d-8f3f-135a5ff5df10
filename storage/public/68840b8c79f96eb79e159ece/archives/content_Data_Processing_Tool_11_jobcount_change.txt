Python code:

```python
import pandas as pd
from metalake import load_data

# Load the full, pre-filtered dataset
chat_id = "68842a4e97c928a587dae97a"
data_file = "service_jobs_by_type_last2weeks.dat"
df = load_data(chat_id, data_file)

# Check for empty DataFrame
if df.empty:
    print("No data available for service job counts by type for the last two weeks.")
else:
    # Ensure week_start is sorted ascending (oldest to newest)
    df_sorted = df.sort_values(['week_start', 'service_type'], ascending=[True, True])
    # Get unique week_start values (should be two)
    week_labels = df_sorted['week_start'].unique()
    if len(week_labels) != 2:
        print(f"Expected data for 2 weeks, found {len(week_labels)}: {week_labels}. Unable to process.")
    else:
        prev_week, latest_week = week_labels[0], week_labels[1]
        # Pivot for prev_jobs and latest_jobs
        pivot = df_sorted.pivot(index='service_type', columns='week_start', values='jobs')
        # Rename columns for clarity
        pivot = pivot.rename(columns={prev_week: 'prev_jobs', latest_week: 'latest_jobs'})
        # Fill missing jobs with zero (if any service_type doesn't appear in both weeks)
        pivot = pivot.fillna(0)
        # Calculate absolute and percent change
        pivot['abs_change'] = pivot['latest_jobs'] - pivot['prev_jobs']
        pivot['pct_change'] = pivot.apply(
            lambda row: (100 * row['abs_change'] / row['prev_jobs']) if row['prev_jobs'] != 0 else None, axis=1)
        # Reset index for output
        result = pivot.reset_index()[['service_type', 'prev_jobs', 'latest_jobs', 'abs_change', 'pct_change']]
        # Format values
        for col in ['prev_jobs', 'latest_jobs', 'abs_change']:
            result[col] = result[col].astype(int)
        result['pct_change'] = result['pct_change'].apply(lambda x: f"{x:.2f}" if x is not None else 'N/A')
        # Save to CSV and DAT
        result.to_csv("files/service_type_change.csv", index=False)
        result.to_csv("files/service_type_change.dat", index=False)
        # Show up to 50 rows as Markdown
        display_rows = min(len(result), 50)
        print(result.head(display_rows).to_markdown(index=False))
        if len(result) > 50:
            print("Note: Table truncated to first 50 rows.")

````

Output:

| service_type   |   prev_jobs |   latest_jobs |   abs_change |   pct_change |
|:---------------|------------:|--------------:|-------------:|-------------:|
| Brake Repair   |         182 |           183 |            1 |         0.55 |
| Car Wash       |         495 |           521 |           26 |         5.25 |
| Oil Change     |         236 |           191 |          -45 |       -19.07 |
| Tyre Change    |         108 |           116 |            8 |         7.41 |

