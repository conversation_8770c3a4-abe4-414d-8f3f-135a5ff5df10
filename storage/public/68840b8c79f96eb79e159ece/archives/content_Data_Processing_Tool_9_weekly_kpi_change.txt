Python code:

```python
import pandas as pd
from metalake import load_data

# Load full, pre-filtered dataset
df = load_data("688429c997c928a587dae8fa", "weekly_tech_productivity_12w.dat")

# Check for empty DataFrame or insufficient rows
if df.empty or len(df) < 2:
    print("Not enough data available to compare the latest two completed weeks.")
else:
    # Sort by week_start ascending
    df_sorted = df.sort_values('week_start', ascending=True)
    # Select the last two rows (latest two weeks)
    last_two = df_sorted.tail(2).reset_index(drop=True)
    # Assign prev_week and latest_week
    prev_week = last_two.iloc[0]
    latest_week = last_two.iloc[1]
    # Metrics to compare
    metrics = ['total_jobs', 'tech_count', 'avg_jobs_per_tech']
    data = []
    for metric in metrics:
        prev_value = float(prev_week[metric])
        latest_value = float(latest_week[metric])
        abs_change = latest_value - prev_value
        pct_change = (abs_change / prev_value * 100) if prev_value != 0 else None
        # Format values: two decimals for all metrics
        row = {
            'metric': metric,
            'prev_value': f"{prev_value:.2f}",
            'latest_value': f"{latest_value:.2f}",
            'abs_change': f"{abs_change:.2f}",
            'pct_change': f"{pct_change:.2f}" if pct_change is not None else 'N/A'
        }
        data.append(row)
    # Create summary DataFrame
    summary_df = pd.DataFrame(data, columns=['metric', 'prev_value', 'latest_value', 'abs_change', 'pct_change'])
    # Save to CSV and DAT files
    summary_df.to_csv("files/weekly_kpi_change_summary.csv", index=False)
    summary_df.to_csv("files/weekly_kpi_change_summary.dat", index=False)
    # Display first up to 50 rows as Markdown table
    display_rows = min(len(summary_df), 50)
    print(summary_df.head(display_rows).to_markdown(index=False))
    if len(summary_df) > 50:
        print("Note: Table truncated to first 50 rows.")

````

Output:

| metric            |   prev_value |   latest_value |   abs_change |   pct_change |
|:------------------|-------------:|---------------:|-------------:|-------------:|
| total_jobs        |      1021    |        1011    |       -10    |        -0.98 |
| tech_count        |        45    |          44    |        -1    |        -2.22 |
| avg_jobs_per_tech |        22.69 |          22.98 |         0.29 |         1.28 |

