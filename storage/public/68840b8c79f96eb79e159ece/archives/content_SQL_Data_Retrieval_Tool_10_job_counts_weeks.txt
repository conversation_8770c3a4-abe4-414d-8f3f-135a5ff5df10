SQL Query:

````
SELECT
  week_start,
  ST.Description AS service_type,
  COUNT(*) AS jobs
FROM (
  SELECT
    SR.Service_ID,
    SR.Service_Type_ID,
    SR.Employee_ID,
    DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) AS week_start,
    SR.Service_Date
  FROM ServiceRecords SR
  INNER JOIN EmployeeData ED ON SR.Employee_ID = ED.Employee_ID
  WHERE
    LOWER(ED.Role) LIKE 'technician'
    AND (ED.Leave_Date IS NULL OR ED.Leave_Date >= SR.Service_Date)
    AND DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) IN (
      DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 2 WEEK),
      DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 1 WEEK)
    )
) AS sub
INNER JOIN ServiceTypes ST ON sub.Service_Type_ID = ST.Service_Type_ID
GROUP BY week_start, ST.Description
ORDER BY week_start ASC, jobs DESC;
````

Data retrieved successfully:

| week_start               | service_type   |   jobs |
|:-------------------------|:---------------|-------:|
| 2025-07-07T00:00:00.000Z | Car Wash       |    495 |
| 2025-07-07T00:00:00.000Z | Oil Change     |    236 |
| 2025-07-07T00:00:00.000Z | Brake Repair   |    182 |
| 2025-07-07T00:00:00.000Z | Tyre Change    |    108 |
| 2025-07-14T00:00:00.000Z | Car Wash       |    521 |
| 2025-07-14T00:00:00.000Z | Oil Change     |    191 |
| 2025-07-14T00:00:00.000Z | Brake Repair   |    183 |
| 2025-07-14T00:00:00.000Z | Tyre Change    |    116 |