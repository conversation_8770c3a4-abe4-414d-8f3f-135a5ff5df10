SQL Query:

````
SELECT
  SR.Employee_ID,
  SUM(CASE WHEN DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) = DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 2 WEEK) THEN 1 ELSE 0 END) AS jobs_prev_week,
  SUM(CASE WHEN DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) = DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 1 WEEK) THEN 1 ELSE 0 END) AS jobs_latest_week
FROM ServiceRecords SR
INNER JOIN EmployeeData ED ON SR.Employee_ID = ED.Employee_ID
WHERE LOWER(ED.Role) LIKE 'technician'
  AND (ED.Leave_Date IS NULL OR ED.Leave_Date >= SR.Service_Date)
  AND DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) IN (
    DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 2 WEEK),
    DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 1 WEEK)
  )
GROUP BY SR.Employee_ID
ORDER BY SR.Employee_ID ASC;
````

Data retrieved successfully:

|   Employee_ID |   jobs_prev_week |   jobs_latest_week |
|--------------:|-----------------:|-------------------:|
|             1 |               12 |                  9 |
|             2 |               38 |                 29 |
|             3 |               33 |                 25 |
|             4 |               19 |                 20 |
|             7 |               28 |                 37 |
|             9 |               30 |                 39 |
|            11 |               17 |                 10 |
|            12 |               30 |                 26 |
|            13 |               16 |                 15 |
|            15 |               18 |                 15 |
|            18 |               38 |                 31 |
|            19 |               14 |                 16 |
|            23 |               11 |                  5 |
|            25 |               11 |                 18 |
|            27 |               29 |                 35 |
|            31 |               29 |                 43 |
|            32 |               32 |                 33 |
|            33 |               34 |                 37 |
|            34 |               27 |                 27 |
|            36 |               27 |                 24 |
|            40 |               29 |                 31 |
|            44 |               21 |                 21 |
|            45 |               13 |                 21 |
|            47 |               13 |                 22 |
|            48 |               14 |                 14 |
|            49 |               26 |                 34 |
|            51 |               39 |                 33 |
|            52 |               14 |                  7 |
|            54 |               38 |                 31 |
|            55 |               22 |                 31 |
|            58 |               30 |                 27 |
|            60 |               12 |                 17 |
|            63 |               25 |                 37 |
|            65 |               23 |                 16 |
|            66 |               13 |                  6 |
|            68 |               34 |                 34 |
|            70 |                8 |                  0 |
|            71 |               16 |                 17 |
|            72 |               19 |                 13 |
|            73 |               30 |                 28 |
|            75 |               29 |                 32 |
|            77 |               10 |                 10 |
|            78 |               15 |                  8 |
|            79 |               16 |                 16 |
|            80 |               19 |                 11 |