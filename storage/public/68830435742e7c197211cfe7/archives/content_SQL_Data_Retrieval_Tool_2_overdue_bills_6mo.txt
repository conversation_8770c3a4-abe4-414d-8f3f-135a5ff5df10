SQL Query:

````
WITH BillSummary AS (
    SELECT 
        VendorBills_BillId AS BillId,
        MIN(VendorBills_DueDate) AS DueDate,
        MIN(VendorBills_Balance) AS Balance
    FROM VendorBills
    GROUP BY VendorBills_BillId
)
SELECT 
    FORMAT(DATEFROMPARTS(YEAR(DueDate), MONTH(DueDate), 1), 'yyyy-MM') AS DueMonth,
    SUM(Balance) AS OverdueAmount
FROM BillSummary
WHERE 
    Balance > 0
    AND DueDate < CAST(GETDATE() AS DATE)
    AND DueDate >= DATEFROMPARTS(YEAR(DATEADD(month, -5, GETDATE())), MONTH(DATEADD(month, -5, GETDATE())), 1)
GROUP BY FORMAT(DATEFROMPARTS(YEAR(DueDate), MONTH(DueDate), 1), 'yyyy-MM')
ORDER BY DueMonth;
````

Data retrieved successfully:

| DueMonth   |   OverdueAmount |
|:-----------|----------------:|
| 2025-02    |  34244.2        |
| 2025-03    | 102102          |
| 2025-04    | 136336          |
| 2025-05    | 164483          |
| 2025-06    |      1.5854e+06 |
| 2025-07    | 539256          |