Python code:

```python
import pandas as pd
from metalake import load_data

# Set identifiers
chat_id = "6883030c742e7c197211cf6b"
data_file_name = "vendorbills_overdue_by_month.dat"

# Load full, filtered dataset
df = load_data(chat_id, data_file_name)

# Handle empty DataFrame
if df.empty:
    print("No overdue vendor bill data available for the selected months.")
else:
    # Sort by DueMonth ascending
    df_sorted = df.sort_values(by="DueMonth", ascending=True)
    # Save full result to CSV
    csv_path = "files/vendorbills_overdue_by_month.csv"
    df_sorted.to_csv(csv_path, index=False)
    # Display first up to 50 rows as markdown
    display_rows = min(50, len(df_sorted))
    print(df_sorted.head(display_rows).to_markdown(index=False))
    if len(df_sorted) > 50:
        print(f"\nNote: Only first 50 of {len(df_sorted)} rows shown above.")

````

Output:

| DueMonth   |   OverdueAmount |
|:-----------|----------------:|
| 2025-02    |  34244.2        |
| 2025-03    | 102102          |
| 2025-04    | 136336          |
| 2025-05    | 164483          |
| 2025-06    |      1.5854e+06 |
| 2025-07    | 539256          |

