SQL Query:

````
SELECT
  sub.year,
  sub.month_number,
  sub.month_name,
  COUNT(*) AS first_time_tire_customers
FROM (
  SELECT 
    EXTRACT(YEAR FROM first_tire_invoice_date) AS year,
    EXTRACT(MONTH FROM first_tire_invoice_date) AS month_number,
    MONTHNAME(first_tire_invoice_date) AS month_name
  FROM (
    SELECT 
      Customer_ID, 
      MIN(Invoice_Date) AS first_tire_invoice_date
    FROM Invoices
    WHERE Service_Type_ID = 1
    GROUP BY Customer_ID
  ) AS t1
  WHERE first_tire_invoice_date >= '2023-01-01'
    AND first_tire_invoice_date <= '2025-12-31'
) AS sub
GROUP BY sub.year, sub.month_number, sub.month_name
ORDER BY sub.year ASC, sub.month_number ASC;
````

Data retrieved successfully:

|   year |   month_number | month_name   |   first_time_tire_customers |
|-------:|---------------:|:-------------|----------------------------:|
|   2023 |              1 | January      |                         110 |
|   2023 |              2 | February     |                          66 |
|   2023 |              3 | March        |                         100 |
|   2023 |              4 | April        |                          86 |
|   2023 |              5 | May          |                          93 |
|   2023 |              6 | June         |                          92 |
|   2023 |              7 | July         |                         118 |
|   2023 |              8 | August       |                         107 |
|   2023 |              9 | September    |                         117 |
|   2023 |             10 | October      |                         104 |
|   2023 |             11 | November     |                         115 |
|   2023 |             12 | December     |                         105 |
|   2024 |              1 | January      |                         113 |
|   2024 |              2 | February     |                         129 |
|   2024 |              3 | March        |                         125 |
|   2024 |              4 | April        |                         132 |
|   2024 |              5 | May          |                         117 |
|   2024 |              6 | June         |                         135 |
|   2024 |              7 | July         |                         121 |
|   2024 |              8 | August       |                         131 |
|   2024 |              9 | September    |                         142 |
|   2024 |             10 | October      |                         138 |
|   2024 |             11 | November     |                         132 |
|   2024 |             12 | December     |                         158 |
|   2025 |              1 | January      |                         138 |
|   2025 |              2 | February     |                         144 |
|   2025 |              3 | March        |                         146 |
|   2025 |              4 | April        |                         161 |
|   2025 |              5 | May          |                         167 |
|   2025 |              6 | June         |                         144 |
|   2025 |              7 | July         |                         119 |