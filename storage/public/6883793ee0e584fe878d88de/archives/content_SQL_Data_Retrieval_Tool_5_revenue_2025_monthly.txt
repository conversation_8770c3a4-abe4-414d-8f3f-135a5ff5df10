SQL Query:

````
SELECT
  month_start,
  SUM(Total_Charge) AS monthly_revenue
FROM (
  SELECT
    STR_TO_DATE(CONCAT(YEAR(Invoice_Date), '-', LPAD(MONTH(Invoice_Date),2,'0'), '-01'), '%Y-%m-%d') AS month_start,
    Total_Charge
  FROM Invoices
  WHERE Invoice_Date >= '2025-01-01'
    AND Invoice_Date <= '2025-12-31'
) AS subq
GROUP BY month_start
ORDER BY month_start ASC;
````

Data retrieved successfully:

| month_start              |   monthly_revenue |
|:-------------------------|------------------:|
| 2025-01-01T00:00:00.000Z |            289592 |
| 2025-02-01T00:00:00.000Z |            262110 |
| 2025-03-01T00:00:00.000Z |            287058 |
| 2025-04-01T00:00:00.000Z |            283328 |
| 2025-05-01T00:00:00.000Z |            293763 |
| 2025-06-01T00:00:00.000Z |            271276 |
| 2025-07-01T00:00:00.000Z |            223458 |