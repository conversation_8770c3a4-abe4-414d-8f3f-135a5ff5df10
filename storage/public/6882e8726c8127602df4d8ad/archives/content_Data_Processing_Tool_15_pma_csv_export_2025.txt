Python code:

```python
import pandas as pd
from metalake import load_data
import os

chat_id = "6882ff75b9d899f80948fd73"

files_info = [
    ("pma_invoice_metrics_jun_jul_2025.dat", "files/pma_invoice_metrics_jun_jul_2025.csv", "files/pma_invoice_metrics_jun_jul_2025.dat"),
    ("revenue_by_month_status_jun_jul2025.dat", "files/pma_revenue_by_status_jun_jul_2025.csv", "files/pma_revenue_by_status_jun_jul_2025.dat")
]

for data_file, csv_out, dat_out in files_info:
    df = load_data(chat_id, data_file)
    if df.empty:
        print(f"No data found in {data_file}.")
        continue
    df.to_csv(csv_out, index=False)
    df.to_csv(dat_out, index=False)
    print(f"Data from {data_file} saved to {csv_out} and {dat_out}.")
    display_rows = min(50, len(df))
    print(df.head(display_rows).to_markdown(index=False))
    if len(df) > 50:
        print(f"\nNote: Table truncated to first 50 of {len(df)} rows.")
````

Output:

Data from pma_invoice_metrics_jun_jul_2025.dat saved to files/pma_invoice_metrics_jun_jul_2025.csv and files/pma_invoice_metrics_jun_jul_2025.dat.
| MonthStart               | ContractType   |   InvoiceCount |   TotalRevenue |   AvgInvoice |
|:-------------------------|:---------------|---------------:|---------------:|-------------:|
| 2025-06-01T00:00:00.000Z | PMA - GEN      |             81 |       101159   |      1248.88 |
| 2025-06-01T00:00:00.000Z | PMA-COM        |             70 |       147998   |      2114.25 |
| 2025-07-01T00:00:00.000Z | PMA - GEN      |             37 |        80266.8 |      2169.37 |
| 2025-07-01T00:00:00.000Z | PMA-COM        |             56 |       148193   |      2646.31 |
Data from revenue_by_month_status_jun_jul2025.dat saved to files/pma_revenue_by_status_jun_jul_2025.csv and files/pma_revenue_by_status_jun_jul_2025.dat.
| MonthStart               | InvoiceStatus   |   TotalRevenue |
|:-------------------------|:----------------|---------------:|
| 2025-06-01T00:00:00.000Z | Paid in Full    |      215767    |
| 2025-06-01T00:00:00.000Z | Sent            |       33389.6  |
| 2025-07-01T00:00:00.000Z | Approved        |        3056.08 |
| 2025-07-01T00:00:00.000Z | Paid in Full    |      111414    |
| 2025-07-01T00:00:00.000Z | Sent            |      113990    |

