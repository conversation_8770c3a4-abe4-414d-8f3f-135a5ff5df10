Python code:

```python
import pandas as pd
from metalake import load_data
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import seaborn as sns

chat_id = "6882ebc909c0d4f583be554b"
data_file_name = "pma_revenue_last6_months.dat"
output_img = "files/pma_revenue_last6_months.png"

# 1. Load the data (already filtered to 6 months)
df = load_data(chat_id, data_file_name)

if df.empty:
    print("No recognised PMA contract revenue data is available for the last six months.")
else:
    # 2. Parse MonthStart as datetime and sort ascending
    df["MonthStart"] = pd.to_datetime(df["MonthStart"])
    df = df.sort_values("MonthStart", ascending=True)
    # 3. Remove commas and convert TotalRevenue to float (if needed)
    df["TotalRevenue"] = df["TotalRevenue"].astype(str).str.replace(",", "").astype(float)
    # 4. X-axis labels as YYYY-MM
    df["MonthLabel"] = df["MonthStart"].dt.strftime("%Y-%m")
    # 5. Plot
    plt.figure(figsize=(10, 6))
    sns.barplot(x="MonthLabel", y="TotalRevenue", data=df, color="#69b3a2")
    plt.title("Monthly PMA Contract Revenue – Last 6 Months", fontsize=16)
    plt.xlabel("Month", fontsize=12)
    plt.ylabel("Total Revenue (USD)", fontsize=12)
    # Format y-axis as currency
    plt.gca().yaxis.set_major_formatter(
        plt.matplotlib.ticker.StrMethodFormatter("${x:,.2f}")
    )
    plt.tight_layout()
    plt.savefig(output_img)
    plt.close()
    print(f"Bar chart saved as {output_img}.")
````

Output:

Bar chart saved as files/pma_revenue_last6_months.png.

