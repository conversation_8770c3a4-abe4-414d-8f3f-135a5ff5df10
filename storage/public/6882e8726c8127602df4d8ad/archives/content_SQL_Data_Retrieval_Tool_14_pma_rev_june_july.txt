SQL Query:

````
SELECT
  DATEFROMPARTS(YEAR(I.InvoiceSummary_InvoiceDate), MONTH(I.InvoiceSummary_InvoiceDate), 1) AS MonthStart,
  CAST(I.InvoiceSummary_InvoiceStatus AS VARCHAR(MAX)) AS InvoiceStatus,
  SUM(I.InvoiceSummary_InvoiceAmount) AS TotalRevenue
FROM Contracts AS C
INNER JOIN InvoiceSummary AS I ON C.Contract_Number = I.InvoiceSummary_ContractNumber
WHERE CAST(C.Contract_Type AS VARCHAR(MAX)) IN ('PMA - GEN', 'PMA-COM')
  AND I.InvoiceSummary_InvoiceType = 'Invoice'
  AND CAST(I.InvoiceSummary_InvoiceStatus AS VARCHAR(MAX)) IN ('Sent', 'Paid in Full', 'Partial Payment Received', 'Approved', 'Ready for Accounting Review')
  AND I.InvoiceSummary_InvoiceDate >= '2025-06-01'
  AND I.InvoiceSummary_InvoiceDate < '2025-08-01'
GROUP BY DATEFROMPARTS(YEAR(I.InvoiceSummary_InvoiceDate), MONTH(I.InvoiceSummary_InvoiceDate), 1), CAST(I.InvoiceSummary_InvoiceStatus AS VARCHAR(MAX))
ORDER BY MonthStart, InvoiceStatus;
````

Data retrieved successfully:

| MonthStart               | InvoiceStatus   |   TotalRevenue |
|:-------------------------|:----------------|---------------:|
| 2025-06-01T00:00:00.000Z | Paid in Full    |      215767    |
| 2025-06-01T00:00:00.000Z | Sent            |       33389.6  |
| 2025-07-01T00:00:00.000Z | Approved        |        3056.08 |
| 2025-07-01T00:00:00.000Z | Paid in Full    |      111414    |
| 2025-07-01T00:00:00.000Z | Sent            |      113990    |