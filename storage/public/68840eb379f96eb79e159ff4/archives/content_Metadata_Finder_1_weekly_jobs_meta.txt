Metadata of all data sources:

````
[
  {
    "data_source_name": "superlube",
    "selected_table_column_metadata": {
      "ServiceRecords": {
        "table_name": "ServiceRecords",
        "fields": {
          "Service_ID": {
            "name": "Service_ID",
            "description": "A unique integer that identifies each service record, ensuring distinct tracking and referencing of individual service entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 270722 more..."
            ]
          },
          "Employee_ID": {
            "name": "Employee_ID",
            "description": "An integer identifier for the employee who performed the service, enabling tracking of employee performance and service responsibilities.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "6",
              "and 52 more..."
            ]
          },
          "Service_Date": {
            "name": "Service_Date",
            "description": "The date when the service was performed, recorded in a date format to track service history and assist in scheduling.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2019-01-06",
              "2019-01-09",
              "2019-01-10",
              "2019-01-08",
              "2019-01-13",
              "and 2350 more..."
            ]
          }
        }
      },
      "EmployeeData": {
        "table_name": "EmployeeData",
        "fields": {
          "Employee_ID": {
            "name": "Employee_ID",
            "description": "A unique integer identifier for each employee, ensuring that no two records share the same ID, critical for referencing individual employee records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "6",
              "and 66 more..."
            ]
          },
          "Role": {
            "name": "Role",
            "description": "A variable character string representing the employee's job role, which can include 'Technician', 'Customer Service', or 'Manager', providing insight into role distribution.",
            "dataType": "varchar(255)",
            "is_unstructured": false,
            "All distinct values": [
              "Technician",
              "Manager",
              "Customer Service"
            ]
          },
          "Leave_Date": {
            "name": "Leave_Date",
            "description": "The date an employee leaves the company, applicable to less than half of the records, indicating the employee's departure status and tenure.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2025-02-13",
              "2024-07-28",
              "2023-06-23",
              "2022-11-05",
              "2025-04-21",
              "and 7 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n\n**Reasoning:**  \n\n\nTo calculate weekly average jobs per technician, we need job records with technician IDs, job dates, and job status. The ServiceRecords table holds job completions and technician (Employee_ID), with Service_Date as the job date. The EmployeeData table contains technician master data, including Employee_ID and Role (\"Technician\"). For business rules, examine ServiceRecords for job validity, use Service_Date for week grouping, and infer technician inclusion from EmployeeData.Role and Leave_Date (active status).  \n\n\n**Table Columns:**  \n\n\n- table: ServiceRecords  \n  columns: Service_ID, Employee_ID, Service_Date  \n\n- table: EmployeeData  \n  columns: Employee_ID, Role, Leave_Date  \n\n",
    "other_table_columns": {
      "ServiceTypes": [
        "Service_Type_ID",
        "Description",
        "base_charge",
        "Average_Time_Taken",
        "Time_Range"
      ],
      "CustomerFeedbacks": [
        "Feedback_ID",
        "Service_ID",
        "Rating",
        "Comments",
        "Review_Date"
      ],
      "Invoices": [
        "Invoice_ID",
        "Service_Type_ID",
        "Customer_ID",
        "Service_ID",
        "Invoice_Date",
        "Total_Charge"
      ],
      "Appointments": [
        "Appointment_ID",
        "Customer_ID",
        "Vehicle_ID",
        "Service_Type_ID",
        "Appointment_Date",
        "Schedule_Date"
      ],
      "ServiceRecords": [
        "Customer_ID",
        "Service_Type_ID",
        "Time_Taken"
      ],
      "EmployeeData": [
        "Name",
        "Service_Type_ID",
        "Hire_Date"
      ],
      "Salaries": [
        "Salary_ID",
        "Employee_ID",
        "Pay_Date",
        "Amount"
      ],
      "CustomerData": [
        "Customer_ID",
        "Name",
        "Contact_Information",
        "Join_Date",
        "Vehicle_ID",
        "Vehicle_Make",
        "Vehicle_Model"
      ]
    }
  }
]
````