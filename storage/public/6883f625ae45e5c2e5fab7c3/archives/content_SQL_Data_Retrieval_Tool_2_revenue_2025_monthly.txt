SQL Query:

````
SELECT
  Month,
  SUM(Total_Charge) AS Total_Revenue
FROM (
  SELECT
    EXTRACT(MONTH FROM Invoice_Date) AS Month,
    Total_Charge
  FROM Invoices
  WHERE Invoice_Date BETWEEN '2025-01-01' AND '2025-12-31'
) AS sub
GROUP BY Month
ORDER BY Month ASC;
````

Data retrieved successfully:

|   Month |   Total_Revenue |
|--------:|----------------:|
|       1 |          289592 |
|       2 |          262110 |
|       3 |          287058 |
|       4 |          283328 |
|       5 |          293763 |
|       6 |          271276 |
|       7 |          223458 |