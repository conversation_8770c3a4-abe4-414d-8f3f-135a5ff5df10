Python code:

```python
import pandas as pd
from metalake import load_data
import os

# Ensure output directory exists
os.makedirs('files', exist_ok=True)

chat_id = "687ff705b3150354fe142bfd"

data_file = "gl_detail_2025_06_visa_3774.dat"
# 1. Load data
df = load_data(chat_id, data_file)

# Handle empty DataFrame
def empty_report():
    print("No data found in gl_detail_2025_06_visa_3774.dat. No further processing possible.")

if df.empty:
    empty_report()
else:
    # 2. Group by GL_Code and count rows
    result = df.groupby('GL_Code').size().reset_index(name='Transaction_Count')
    # 3. Output markdown preview and save files
    result.to_csv('files/visa_3774_glcodes_counts.csv', index=False)
    result.to_csv('files/visa_3774_glcodes_counts.dat', index=False, sep='|')
    n_preview = min(50, len(result))
    print(result.head(n_preview).to_markdown(index=False))
    if len(result) > 50:
        print(f"\nNote: Showing first 50 of {len(result)} rows.")

````

Output:

|   GL_Code |   Transaction_Count |
|----------:|--------------------:|
|      1025 |                   2 |
|      2000 |                  31 |
|      2021 |                  28 |
|      2030 |                   1 |

