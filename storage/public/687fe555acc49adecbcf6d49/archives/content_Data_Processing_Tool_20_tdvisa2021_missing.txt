Python code:

```python
import pandas as pd
import os
from metalake import load_data

# Ensure output directory exists
os.makedirs('files', exist_ok=True)

chat_id = "687ff8d2b3150354fe142cab"

# 1. Load ledger and normalise GL_Code to string digits only, keep only 2021
ledger = load_data(chat_id, 'generalledger_june2025.dat')
if ledger.empty:
    print("No data found in generalledger_june2025.dat. No further processing possible.")
else:
    ledger = ledger.copy()
    # Normalise GL_Code as string digits only
    ledger['GL_Code_norm'] = ledger['GL_Code'].astype(str).str.strip().str.replace(r'[^0-9]', '', regex=True)
    ledger_2021 = ledger[ledger['GL_Code_norm'] == '2021'].copy()
    if ledger_2021.empty:
        print("No rows found in generalledger_june2025.dat for GL_Code 2021. No further processing possible.")
    else:
        ledger_2021['Date'] = pd.to_datetime(ledger_2021['TransactionDate'], errors='coerce').dt.date.astype(str)
        ledger_2021['SignedAmount'] = pd.to_numeric(ledger_2021['Amount'], errors='coerce').round(2)
        df_ledger = ledger_2021[['Date', 'SignedAmount']].dropna()

        # 2. Load worksheet and prepare Date and SignedAmount
        df_ws = load_data(chat_id, 'RECONCILIATION.dat')
        if df_ws.empty:
            print("No data found in RECONCILIATION.dat. No further processing possible.")
        else:
            # Find first column containing 'date' (case-insensitive)
            date_col = next((col for col in df_ws.columns if 'date' in col.lower()), None)
            if not date_col:
                print("No date column found in RECONCILIATION.dat. Cannot continue.")
            else:
                df_ws = df_ws.copy()
                df_ws['Date'] = pd.to_datetime(df_ws[date_col], errors='coerce').dt.date.astype(str)
                # Build SignedAmount
                signed_amount = None
                if 'Value' in df_ws.columns and not df_ws['Value'].isna().all():
                    signed_amount = pd.to_numeric(df_ws['Value'], errors='coerce')
                elif 'Charges' in df_ws.columns and 'Credit' in df_ws.columns:
                    charges = pd.to_numeric(df_ws['Charges'], errors='coerce').fillna(0)
                    credit = pd.to_numeric(df_ws['Credit'], errors='coerce').fillna(0)
                    signed_amount = (charges - credit)
                elif 'Amount' in df_ws.columns:
                    signed_amount = pd.to_numeric(df_ws['Amount'], errors='coerce')
                else:
                    print("No usable Value, Charges+Credit, or Amount column in RECONCILIATION.dat. Cannot continue.")
                    signed_amount = pd.Series([pd.NA] * len(df_ws))
                df_ws['SignedAmount'] = signed_amount.round(2)
                df_ws = df_ws[['Date', 'SignedAmount']].dropna()

                # 3. Create join keys
                df_ledger['Key'] = df_ledger['Date'] + '_' + df_ledger['SignedAmount'].map(lambda x: f"{x:.2f}")
                df_ws['Key'] = df_ws['Date'] + '_' + df_ws['SignedAmount'].map(lambda x: f"{x:.2f}")

                # 4. Left-anti join
                missing = df_ws[~df_ws['Key'].isin(df_ledger['Key'])].copy().drop(columns=['Key'])

                # 5. Output files and markdown preview
                missing.to_csv('files/missing_transactions_v2.csv', index=False)
                missing.to_csv('files/missing_transactions_v2.dat', index=False, sep='|')
                n_preview = min(20, len(missing))
                if n_preview == 0:
                    print("No missing worksheet transactions found for GL_Code 2021. All are present in the ledger.")
                else:
                    print(missing.head(n_preview).to_markdown(index=False))
                    if len(missing) > 20:
                        print(f"\nNote: Showing first 20 of {len(missing)} rows.")

````

Output:

| Date       |   SignedAmount |
|:-----------|---------------:|
| 2025-06-30 |         134.4  |
| 2025-06-29 |          63.48 |
| 2025-06-27 |         886.06 |
| 2025-06-26 |          96.73 |
| 2025-06-26 |         287.84 |
| 2025-06-25 |         960.78 |
| 2025-06-25 |         180.88 |
| 2025-06-24 |         -18.47 |
| 2025-06-23 |         810.39 |
| 2025-06-21 |          93.74 |
| 2025-06-21 |          14.94 |
| 2025-06-21 |          74.61 |
| 2025-06-21 |         120.58 |
| 2025-06-20 |         122.04 |
| 2025-06-20 |          23.55 |
| 2025-06-20 |          72.18 |
| 2025-06-20 |          16.15 |
| 2025-06-20 |          28.96 |
| 2025-06-20 |         383.58 |
| 2025-06-20 |         107.95 |

Note: Showing first 20 of 104 rows.

