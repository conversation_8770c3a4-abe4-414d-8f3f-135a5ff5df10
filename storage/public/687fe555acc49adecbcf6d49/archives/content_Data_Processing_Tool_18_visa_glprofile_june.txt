Python code:

```python
import pandas as pd
import os
from metalake import load_data

# Ensure output directory exists
os.makedirs('files', exist_ok=True)

chat_id = "687ff82db3150354fe142c63"
data_file = "generalledger_june2025.dat"

# 1. Load data
df = load_data(chat_id, data_file)

if df.empty:
    print("No data found in generalledger_june2025.dat. No further processing possible.")
else:
    # 2. Create summary1: count of rows per GL_Code
    summary1 = df.groupby('GL_Code').size().reset_index(name='Total_Rows')
    # 3. Create summary2: count of rows with 'visa' in Description (case-insensitive)
    has_visa = df['Description'].astype(str).str.lower().str.contains('visa', na=False)
    summary2 = df[has_visa].groupby('GL_Code').size().reset_index(name='Visa_Desc_Rows')
    # 4. Merge summaries
    merged = pd.merge(summary1, summary2, on='GL_Code', how='left')
    merged['Visa_Desc_Rows'] = merged['Visa_Desc_Rows'].fillna(0).astype(int)
    # 5. Output preview and save files
    merged.to_csv('files/glcode_visa_profile.csv', index=False)
    merged.to_csv('files/glcode_visa_profile.dat', index=False, sep='|')
    print(merged.to_markdown(index=False))
````

Output:

|   GL_Code |   Total_Rows |   Visa_Desc_Rows |
|----------:|-------------:|-----------------:|
|      1025 |          122 |                2 |
|      2000 |         1094 |               53 |
|      2021 |           30 |               28 |
|      2030 |           24 |                3 |

