Extracted formulas and data from Excel file 06-2025 - AU VISA Reconciliation Acct 3774 <PERSON>.xlsx:



## Sheet: Instructions
### Data:
| TD Visa Reconciliation Process                                                                                                                                                                                                                                                                                                                                   |
|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
|                                                                                                                                                                                                                                                                                                                                                                  |
|                                                                                                                                                                                                                                                                                                                                                                  |
| Prep work                                                                                                                                                                                                                                                                                                                                                        |
| Every month, wait until TD processes the preauthorized credit card payments from the TD bank account (usually mid-month), the bookkeep would then record this payment through the TD Visa clearing account (Dr. Clearing, Cr. TD Bank)                                                                                                                           |
| We now have a debit balance sitting in the Visa clearing account to allocate, which is when you would use this file                                                                                                                                                                                                                                              |
| Receive the Visa statement (PDF) for the period and the related excel account activity listing file (same transactions, different formats), these should be saved in the monthly VISA folder                                                                                                                                                                     |
| Verify that all the transaction in the Excel file add up to the Statement balance (PDF), and also agree to the pre-authorized payment that TD pulls from the bank account                                                                                                                                                                                        |
|                                                                                                                                                                                                                                                                                                                                                                  |
| Reconciliation tab                                                                                                                                                                                                                                                                                                                                               |
| Update Cell B3, N5                                                                                                                                                                                                                                                                                                                                               |
| Go to the Excel account activity listing file, COPY and PASTE VALUE data for column A-D, into column A-D on the reconciliation tab starting at line 12 (the grey area)                                                                                                                                                                                           |
| Ensure the formulas in column E, J, K, N, O, P, Q are copied and dragged down all the way to the end of the activity list, or delete formulas in those column that go over the activity list (ie, if the activity list in column A-C only goes to row 150, formula cells should end there, wipe out everything in row 151 and below or you will get #N/A errors) |
| Review the statement, supporting receipts, go through and fill out (using the dropdown) all the cells in column F-I, L-M, S-AB for each transaction                                                                                                                                                                                                              |
| Ensure there is no #N/A value in column J, K, N-Q                                                                                                                                                                                                                                                                                                                |
| Once done, check the check box to ensure M3-M5 are all the same number                                                                                                                                                                                                                                                                                           |
|                                                                                                                                                                                                                                                                                                                                                                  |
| Allocation Tab                                                                                                                                                                                                                                                                                                                                                   |
| Go to the DR cell (D7), refilter to only show appropriate numbers                                                                                                                                                                                                                                                                                                |
| ensure your entry balance                                                                                                                                                                                                                                                                                                                                        |
| go to BV, G/L entry, date the entry the same date as the day the bank pulls the Preauth payment, enter the BV transaction number in cell B5                                                                                                                                                                                                                      |
| Post the entry                                                                                                                                                                                                                                                                                                                                                   |
| VOILA!!!                                                                                                                                                                                                                                                                                                                                                         |
### Formulas:
_No formulas found_
Data for sheet saved as: 'Instructions.dat'

---


## Sheet: RECONCILIATION
### Data:
| Date                | Description               | Charges   | Credit   |   Value | Receipt provided   | Province   | GST/HST   | PST   |   GST/HST% | PST%   | Is this related to an AP vendor account/ PO   | COGS or General   | Expense Description       | GL Account   |  GST/HST-Input Tax Credit    |  Pre-Tax Expense    | Div   | Status            | Links to Field Servio         |    |
|:--------------------|:--------------------------|:----------|:---------|--------:|:-------------------|:-----------|:----------|:------|-----------:|:-------|:----------------------------------------------|:------------------|:--------------------------|:-------------|:-----------------------------|:--------------------|:------|:------------------|:------------------------------|:---|
| 2025-06-30 00:00:00 | PRITCHARD ENGINEERING     | 134.4     |          |  134.4  | Y                  | MB         | Y         | N     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 134.4               | 100.0 |                   | Vendor Bill: 156051-0         |    |
| 2025-06-29 00:00:00 | BEAUTIFUL.AI              | 63.48     |          |   63.48 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-27 00:00:00 | COMFORT INN WINNIPEG A    | 886.06    |          |  886.06 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-26 00:00:00 | RIVER OAKS GOLF           | 96.73     |          |   96.73 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-26 00:00:00 | RIVER OAKS GOLF           | 287.84    |          |  287.84 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-25 00:00:00 | MANITOULIN TRANSPORT INC  | 960.78    |          |  960.78 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 960.78              | 100.0 |                   | Vendor Bill: 3303933603       |    |
| 2025-06-25 00:00:00 | GREAT CANADIAN OIL CHA    | 180.88    |          |  180.88 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-24 00:00:00 | AMZN Mktp CA              |           | 18.47    |  -18.47 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-23 00:00:00 | APPRUV.COM                | 810.39    |          |  810.39 | Y                  | SK         | N         | N     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 810.39              | 200.0 |                   | Vendor Bill: 980239110334-1   |    |
| 2025-06-21 00:00:00 | UBER *TRIP HELP.UBER.COM  | 93.74     |          |   93.74 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 93.74               | 100.0 |                   |                               |    |
| 2025-06-21 00:00:00 | SB BCN EVENTS CASTELLDEFE | 14.94     |          |   14.94 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 14.94               | 100.0 |                   |                               |    |
| 2025-06-21 00:00:00 | LB TAPHOUSE -CONC CYYC    | 74.61     |          |   74.61 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 3.01                         | 71.6                | 100.0 |                   |                               |    |
| 2025-06-21 00:00:00 | 0023 - SL BARCELONA AIRPO | 120.58    |          |  120.58 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 120.58              | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | LS STEELMET SUPPLY        | 122.04    |          |  122.04 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 122.04              | 200.0 |                   | Vendor Bill: I-111569         |    |
| 2025-06-20 00:00:00 | SB BCN EVENTS             | 23.55     |          |   23.55 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 23.55               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | CLUB NAUTIC CASTELLDEFELS | 72.18     |          |   72.18 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 72.18               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | UBER *TRIP                | 16.15     |          |   16.15 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 16.15               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | UBER *TRIP                | 28.96     |          |   28.96 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 28.96               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | AVENTURAS GASTRON MICA    | 383.58    |          |  383.58 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 383.58              | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | TALLER DE TAPAS           | 107.95    |          |  107.95 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 107.95              | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | CLUB 61                   | 66.26     |          |   66.26 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 66.26               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | SB BCN EVENTS             | 16.59     |          |   16.59 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 16.59               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | ECONOLODGE                | 110.91    |          |  110.91 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 110.91              | 200.0 |                   | Vendor Bill: *********-RM#320 |    |
| 2025-06-20 00:00:00 | UBER *TRIP                | 53.23     |          |   53.23 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 53.23               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | UBER *TRIP                | 70.13     |          |   70.13 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 70.13               | 100.0 |                   |                               |    |
| 2025-06-20 00:00:00 | COMFORT SUTIES SASKATOON  | 404.14    |          |  404.14 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 404.14              | 200.0 |                   | Vendor Bill: *********-RM#411 |    |
| 2025-06-19 00:00:00 | ECONOLODGE                | 123.22    |          |  123.22 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-19 00:00:00 | SB BCN EVENTS             | 32.47     |          |   32.47 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 32.47               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | L AGRICOLA REGIONAL SA    | 8.03      |          |    8.03 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-19 00:00:00 | UBER *TRIP                | 37.43     |          |   37.43 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 37.43               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | UBER *TRIP                | 33.31     |          |   33.31 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 33.31               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | UBER *TRIP                | 1.62      |          |    1.62 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 1.62                | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | WILLIAM F. WHITE INTER    | 428.06    |          |  428.06 | Y                  | MB         | Y         | Y     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 428.06              | 100.0 |                   | Vendor Bill: 45519727         |    |
| 2025-06-19 00:00:00 | UBER *TRIP                | 142.14    |          |  142.14 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 142.14              | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | BK21519 CASTELLDEFELS     | 11.84     |          |   11.84 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 11.84               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | AGRICOLA REGIONAL SA      | 16.64     |          |   16.64 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 16.64               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | AERISNACK SL              | 26.55     |          |   26.55 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 26.55               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | FGC MAE                   | 65.73     |          |   65.73 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 65.73               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | FFGC CREMALLERA MONTSERR  | 85.11     |          |   85.11 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 85.11               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | L AGRICOLA REGIONAL SA    | 45.08     |          |   45.08 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 45.08               | 100.0 |                   |                               |    |
| 2025-06-19 00:00:00 | PENDULO CASTELLDEFELS     | 228.51    |          |  228.51 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 228.51              | 100.0 |                   |                               |    |
| 2025-06-18 00:00:00 | MERX /INTERNET            | 73.5      |          |   73.5  | Y                  | SK         | Y         | N     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 73.5                | 200.0 |                   | Vendor Bill: 1016519          |    |
| 2025-06-18 00:00:00 | COMFORT SUTIES SASKATOON  | 542.64    |          |  542.64 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 542.64              | 200.0 |                   | Vendor Bill: *********-RM#411 |    |
| 2025-06-18 00:00:00 | UPS CANADA BILLING CTR    | 48.4      |          |   48.4  | Y                  | Shared     | N         | N     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 48.4                | 300.0 | Billed out to BPL | Vendor Bill: 5685447882       |    |
| 2025-06-18 00:00:00 | UPS CANADA BILLING CTR    | 70.27     |          |   70.27 | Y                  | Shared     | N         | N     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 70.27               | 300.0 | Billed out to TBA | Vendor Bill: 5682892834       |    |
| 2025-06-18 00:00:00 | SB BCN EVENTS             | 8.46      |          |    8.46 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 8.46                | 100.0 |                   |                               |    |
| 2025-06-18 00:00:00 | SB BCN EVENTS             | 8.46      |          |    8.46 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 8.46                | 100.0 |                   |                               |    |
| 2025-06-18 00:00:00 | UBER *TRIP                | 28.04     |          |   28.04 | N                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 28.04               | 100.0 |                   |                               |    |
| 2025-06-18 00:00:00 | PRITCHARD ENGINEERING     | 233.73    |          |  233.73 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 233.73              | 100.0 |                   | Vendor Bill: 166563           |    |
| 2025-06-18 00:00:00 | PRITCHARD ENGINEERING COM | 16.05     |          |   16.05 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 16.05               | 100.0 |                   | Vendor Bill: 166584           |    |
| 2025-06-18 00:00:00 | SB BCN EVENTS             | 40.79     |          |   40.79 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 40.79               | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | SB BCN EVENTS             | 3.5       |          |    3.5  | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 3.5                 | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | SB BCN EVENTS             | 8.49      |          |    8.49 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-17 00:00:00 | STAPLES.CA/43300562125    | 155.39    |          |  155.39 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 155.39              | 200.0 |                   | Vendor Bill: 43300562         |    |
| 2025-06-17 00:00:00 | UBER *TRIP                | 51.5      |          |   51.5  | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 51.5                | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | UBER *TRIP                | 50.01     |          |   50.01 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 50.01               | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | UBER *TRIP                | 24.15     |          |   24.15 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 24.15               | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | UBER *TRIP                | 24.05     |          |   24.05 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 24.05               | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | CAFETERIA KHENYAN         | 4.01      |          |    4.01 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 4.01                | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | CHALO DE TAPAS            | 64.37     |          |   64.37 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 64.37               | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | SANTA GAROTA              | 16.92     |          |   16.92 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 16.92               | 100.0 |                   |                               |    |
| 2025-06-17 00:00:00 | SB BCN EVENTS             | 27.38     |          |   27.38 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 27.38               | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | SB BCN EVENTS             | 8.49      |          |    8.49 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 8.49                | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | SB BCN EVENTS             | 3.5       |          |    3.5  |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-16 00:00:00 | UBER *TRIP                | 61.5      |          |   61.5  | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 61.5                | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | BURNETTS KEY SHOP         | 197.03    |          |  197.03 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 197.03              | 200.0 |                   | Vendor Bill: D20554 SK        |    |
| 2025-06-16 00:00:00 | UBER *TRIP                | 24.38     |          |   24.38 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 24.38               | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | UBER *TRIP                | 64.37     |          |   64.37 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 64.37               | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | UBER *TRIP                | 18.22     |          |   18.22 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 18.22               | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | STARBUCKS ARGENTERIA      | 13.11     |          |   13.11 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 13.11               | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | SOLRAIG BY TIBU-RON       | 150.54    |          |  150.54 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 150.54              | 100.0 |                   |                               |    |
| 2025-06-16 00:00:00 | CHIRINGUITO PARIPE        | 67.97     |          |   67.97 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.0                          | 67.97               | 100.0 |                   |                               |    |
| 2025-06-15 00:00:00 | Chilis Int Calgary        | 65.61     |          |   65.61 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 2.65                         | 62.96               | 100.0 |                   |                               |    |
| 2025-06-15 00:00:00 | UBER *TRIP                | 64.08     |          |   64.08 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 64.08               | 100.0 |                   |                               |    |
| 2025-06-15 00:00:00 | UBER *TRIP                | 60.93     |          |   60.93 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 60.93               | 100.0 |                   |                               |    |
| 2025-06-15 00:00:00 | SB BCN EVENTS CASTELLDEFE | 1776.3    |          | 1776.3  | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                    | 8000/        | 0.0                          | 1776.3              | 100.0 |                   |                               |    |
| 2025-06-15 00:00:00 | SB BCN EVENTS             | 14.98     |          |   14.98 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-15 00:00:00 | RESTAURANTE CORLEONE      | 58.04     |          |   58.04 | Y                  | MB         | Y         | Y     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 2.59                         | 55.45               | 100.0 |                   |                               |    |
| 2025-06-14 00:00:00 | AIRALO                    | 49.0      |          |   49    | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Telephone - Cellular      | 7940/        | 0.0                          | 49.0                | 100.0 |                   |                               |    |
| 2025-06-14 00:00:00 | 2054_YWG_Exchange_News    | 4.53      |          |    4.53 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 0.21                         | 4.32                | 100.0 |                   |                               |    |
| 2025-06-14 00:00:00 | YWG PRAIRIE BISTRO        | 86.96     |          |   86.96 | Y                  | MB         | Y         | Y     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment   | 7500/        | 3.42                         | 83.53999999999999   | 100.0 |                   |                               |    |
| 2025-06-13 00:00:00 | STAPLES STORE #58         |           | 123.19   | -123.19 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-13 00:00:00 | SAMSONITE 466             | 125.43    |          |  125.43 | Y                  | MB         | Y         | Y     |       0.05 | 0.07   | N                                             | GA                | IT Expense                | 7910/        | 5.6                          | 119.**************  | 100.0 |                   |                               |    |
| 2025-06-13 00:00:00 | COUNTRY INN & SUITES W 73 | 669.77    |          |  669.77 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 669.77              | 200.0 |                   | Vendor Bill: 990600359-RM#302 |    |
| 2025-06-13 00:00:00 | VICTORIA INN WINNIPEG 180 | 492.33    |          |  492.33 | Y                  | Shared     | Y         | Y     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 492.33              | 300.0 | How bill out?     | Vendor Bill: 1782170          |    |
| 2025-06-13 00:00:00 | WESTJET 8382197962568     | 720.78    |          |  720.78 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 720.78              | 200.0 |                   | Vendor Bill: MGHOWU           |    |
| 2025-06-12 00:00:00 | Amazon.ca*NA6V655N2       | 16.91     |          |   16.91 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-12 00:00:00 | IN *FASTCHECK             | 70.0      |          |   70    |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-12 00:00:00 | STAPLES STORE #58         | 123.19    |          |  123.19 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-12 00:00:00 | GENERAC POWER SYSTEMS     | 1052.64   |          | 1052.64 | Y                  | SK         | N         | N     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 1052.64             | 200.0 |                   | Vendor Bill: BOET-47999       |    |
| 2025-06-12 00:00:00 | PAYMENT - THANK YOU       |           |          |    0    |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-11 00:00:00 | AMZN Mktp CA*NA89V4GU2    | 63.69     |          |   63.69 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-11 00:00:00 | AMZN Mktp CA*NH6HL9SH1    | 63.69     |          |   63.69 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-10 00:00:00 | WGC*CAPITAL GRILLE        | 351.32    |          |  351.32 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Advertising and Promotion | 7110/        | 0.0                          | 351.32              | 100.0 |                   |                               |    |
| 2025-06-09 00:00:00 | VICTORIA INN WINNIPEG 180 | 3712.38   |          | 3712.38 | Y                  | Shared     | Y         | Y     |       0.05 | 0.07   | Y                                             |                   |                           | POSTED       | 0.0                          | 3712.38             | 300.0 | How bill out?     | Vendor Bill: 1782170          |    |
| 2025-06-09 00:00:00 | MB JUSTICE FINES/AMENDES  | 221.0     |          |  221    | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Penalties                 | 8230/        | 0.0                          | 221.0               | 100.0 |                   |                               |    |
| 2025-06-09 00:00:00 | COMFORT INN & SUITES      | 175.56    |          |  175.56 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 175.56              | 200.0 |                   | Vendor Bill: 990634164-RM#209 |    |
| 2025-06-07 00:00:00 | AMZN Mktp CA*NH6R788B2    | 200.16    |          |  200.16 |                    |            |           |       |       0.05 |        |                                               |                   |                           |              |                              |                     |       |                   |                               |    |
| 2025-06-06 00:00:00 | MAINSTAY SUITES BY CHO    | 719.28    |          |  719.28 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 719.28              | 200.0 |                   | Vendor Bill: 987627832-RM#321 |    |
| 2025-06-06 00:00:00 | COMFORT SUTIES SASKATOON  | 683.71    |          |  683.71 | Y                  | SK         | Y         | Y     |       0.05 | 0.06   | Y                                             |                   |                           | POSTED       | 0.0                          | 683.71              | 200.0 |                   | Vendor Bill: 990602439-RM#312 |    |
### Formulas:
| Cell   | Formula                                                                                                                                                                                                                                                                                                                                                                                                   |
|:-------|:----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| E2     | =IF(C2>0,C2,-D2)                                                                                                                                                                                                                                                                                                                                                                                          |
| J2     | =IF(G2="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K2     | =IF(G2="ON",0,VLOOKUP(G2,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O2     | =IF(L2="Y","POSTED",VLOOKUP(N2,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P2     | =ROUND(IF(L2="Y",0,IF(OR(RECONCILIATION!O2='Data Parameters'!$D$184,RECONCILIATION!O2='Data Parameters'!$D$185),0,IF(RECONCILIATION!G2="ON",RECONCILIATION!E2/(1+RECONCILIATION!J2)*0.13,IF(H2="N",0,IF(RECONCILIATION!I2="Y",RECONCILIATION!E2/(1+RECONCILIATION!J2+RECONCILIATION!K2)*RECONCILIATION!J2,RECONCILIATION!E2/(1+RECONCILIATION!J2)*RECONCILIATION!J2))))),2)                               |
| Q2     | =E2-P2                                                                                                                                                                                                                                                                                                                                                                                                    |
| R2     | <openpyxl.worksheet.formula.ArrayFormula object at 0x1391d2100>                                                                                                                                                                                                                                                                                                                                           |
| E3     | =IF(C3>0,C3,-D3)                                                                                                                                                                                                                                                                                                                                                                                          |
| J3     | =IF(G3="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K3     | =IF(G3="ON",0,VLOOKUP(G3,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O3     | =IF(L3="Y","POSTED",VLOOKUP(N3,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P3     | =ROUND(IF(L3="Y",0,IF(OR(RECONCILIATION!O3='Data Parameters'!$D$184,RECONCILIATION!O3='Data Parameters'!$D$185),0,IF(RECONCILIATION!G3="ON",RECONCILIATION!E3/(1+RECONCILIATION!J3)*0.13,IF(H3="N",0,IF(RECONCILIATION!I3="Y",RECONCILIATION!E3/(1+RECONCILIATION!J3+RECONCILIATION!K3)*RECONCILIATION!J3,RECONCILIATION!E3/(1+RECONCILIATION!J3)*RECONCILIATION!J3))))),2)                               |
| Q3     | =E3-P3                                                                                                                                                                                                                                                                                                                                                                                                    |
| R3     | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613640>                                                                                                                                                                                                                                                                                                                                           |
| E4     | =IF(C4>0,C4,-D4)                                                                                                                                                                                                                                                                                                                                                                                          |
| J4     | =IF(G4="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K4     | =IF(G4="ON",0,VLOOKUP(G4,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O4     | =IF(L4="Y","POSTED",VLOOKUP(N4,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P4     | =ROUND(IF(L4="Y",0,IF(OR(RECONCILIATION!O4='Data Parameters'!$D$184,RECONCILIATION!O4='Data Parameters'!$D$185),0,IF(RECONCILIATION!G4="ON",RECONCILIATION!E4/(1+RECONCILIATION!J4)*0.13,IF(H4="N",0,IF(RECONCILIATION!I4="Y",RECONCILIATION!E4/(1+RECONCILIATION!J4+RECONCILIATION!K4)*RECONCILIATION!J4,RECONCILIATION!E4/(1+RECONCILIATION!J4)*RECONCILIATION!J4))))),2)                               |
| Q4     | =E4-P4                                                                                                                                                                                                                                                                                                                                                                                                    |
| R4     | <openpyxl.worksheet.formula.ArrayFormula object at 0x1393aadc0>                                                                                                                                                                                                                                                                                                                                           |
| E5     | =IF(C5>0,C5,-D5)                                                                                                                                                                                                                                                                                                                                                                                          |
| J5     | =IF(G5="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K5     | =IF(G5="ON",0,VLOOKUP(G5,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O5     | =IF(L5="Y","POSTED",VLOOKUP(N5,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P5     | =ROUND(IF(L5="Y",0,IF(OR(RECONCILIATION!O5='Data Parameters'!$D$184,RECONCILIATION!O5='Data Parameters'!$D$185),0,IF(RECONCILIATION!G5="ON",RECONCILIATION!E5/(1+RECONCILIATION!J5)*0.13,IF(H5="N",0,IF(RECONCILIATION!I5="Y",RECONCILIATION!E5/(1+RECONCILIATION!J5+RECONCILIATION!K5)*RECONCILIATION!J5,RECONCILIATION!E5/(1+RECONCILIATION!J5)*RECONCILIATION!J5))))),2)                               |
| Q5     | =E5-P5                                                                                                                                                                                                                                                                                                                                                                                                    |
| R5     | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613a60>                                                                                                                                                                                                                                                                                                                                           |
| E6     | =IF(C6>0,C6,-D6)                                                                                                                                                                                                                                                                                                                                                                                          |
| J6     | =IF(G6="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K6     | =IF(G6="ON",0,VLOOKUP(G6,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O6     | =IF(L6="Y","POSTED",VLOOKUP(N6,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P6     | =ROUND(IF(L6="Y",0,IF(OR(RECONCILIATION!O6='Data Parameters'!$D$184,RECONCILIATION!O6='Data Parameters'!$D$185),0,IF(RECONCILIATION!G6="ON",RECONCILIATION!E6/(1+RECONCILIATION!J6)*0.13,IF(H6="N",0,IF(RECONCILIATION!I6="Y",RECONCILIATION!E6/(1+RECONCILIATION!J6+RECONCILIATION!K6)*RECONCILIATION!J6,RECONCILIATION!E6/(1+RECONCILIATION!J6)*RECONCILIATION!J6))))),2)                               |
| Q6     | =E6-P6                                                                                                                                                                                                                                                                                                                                                                                                    |
| R6     | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613d00>                                                                                                                                                                                                                                                                                                                                           |
| E7     | =IF(C7>0,C7,-D7)                                                                                                                                                                                                                                                                                                                                                                                          |
| J7     | =IF(G7="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K7     | =IF(G7="ON",0,VLOOKUP(G7,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O7     | =IF(L7="Y","POSTED",VLOOKUP(N7,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P7     | =ROUND(IF(L7="Y",0,IF(OR(RECONCILIATION!O7='Data Parameters'!$D$184,RECONCILIATION!O7='Data Parameters'!$D$185),0,IF(RECONCILIATION!G7="ON",RECONCILIATION!E7/(1+RECONCILIATION!J7)*0.13,IF(H7="N",0,IF(RECONCILIATION!I7="Y",RECONCILIATION!E7/(1+RECONCILIATION!J7+RECONCILIATION!K7)*RECONCILIATION!J7,RECONCILIATION!E7/(1+RECONCILIATION!J7)*RECONCILIATION!J7))))),2)                               |
| Q7     | =E7-P7                                                                                                                                                                                                                                                                                                                                                                                                    |
| R7     | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613580>                                                                                                                                                                                                                                                                                                                                           |
| E8     | =IF(C8>0,C8,-D8)                                                                                                                                                                                                                                                                                                                                                                                          |
| J8     | =IF(G8="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K8     | =IF(G8="ON",0,VLOOKUP(G8,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O8     | =IF(L8="Y","POSTED",VLOOKUP(N8,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P8     | =ROUND(IF(L8="Y",0,IF(OR(RECONCILIATION!O8='Data Parameters'!$D$184,RECONCILIATION!O8='Data Parameters'!$D$185),0,IF(RECONCILIATION!G8="ON",RECONCILIATION!E8/(1+RECONCILIATION!J8)*0.13,IF(H8="N",0,IF(RECONCILIATION!I8="Y",RECONCILIATION!E8/(1+RECONCILIATION!J8+RECONCILIATION!K8)*RECONCILIATION!J8,RECONCILIATION!E8/(1+RECONCILIATION!J8)*RECONCILIATION!J8))))),2)                               |
| Q8     | =E8-P8                                                                                                                                                                                                                                                                                                                                                                                                    |
| R8     | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613670>                                                                                                                                                                                                                                                                                                                                           |
| E9     | =IF(C9>0,C9,-D9)                                                                                                                                                                                                                                                                                                                                                                                          |
| J9     | =IF(G9="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                              |
| K9     | =IF(G9="ON",0,VLOOKUP(G9,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| O9     | =IF(L9="Y","POSTED",VLOOKUP(N9,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                            |
| P9     | =ROUND(IF(L9="Y",0,IF(OR(RECONCILIATION!O9='Data Parameters'!$D$184,RECONCILIATION!O9='Data Parameters'!$D$185),0,IF(RECONCILIATION!G9="ON",RECONCILIATION!E9/(1+RECONCILIATION!J9)*0.13,IF(H9="N",0,IF(RECONCILIATION!I9="Y",RECONCILIATION!E9/(1+RECONCILIATION!J9+RECONCILIATION!K9)*RECONCILIATION!J9,RECONCILIATION!E9/(1+RECONCILIATION!J9)*RECONCILIATION!J9))))),2)                               |
| Q9     | =E9-P9                                                                                                                                                                                                                                                                                                                                                                                                    |
| R9     | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613f40>                                                                                                                                                                                                                                                                                                                                           |
| E10    | =IF(C10>0,C10,-D10)                                                                                                                                                                                                                                                                                                                                                                                       |
| J10    | =IF(G10="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K10    | =IF(G10="ON",0,VLOOKUP(G10,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O10    | =IF(L10="Y","POSTED",VLOOKUP(N10,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P10    | =ROUND(IF(L10="Y",0,IF(OR(RECONCILIATION!O10='Data Parameters'!$D$184,RECONCILIATION!O10='Data Parameters'!$D$185),0,IF(RECONCILIATION!G10="ON",RECONCILIATION!E10/(1+RECONCILIATION!J10)*0.13,IF(H10="N",0,IF(RECONCILIATION!I10="Y",RECONCILIATION!E10/(1+RECONCILIATION!J10+RECONCILIATION!K10)*RECONCILIATION!J10,RECONCILIATION!E10/(1+RECONCILIATION!J10)*RECONCILIATION!J10))))),2)                |
| Q10    | =E10-P10                                                                                                                                                                                                                                                                                                                                                                                                  |
| R10    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613ee0>                                                                                                                                                                                                                                                                                                                                           |
| E11    | =IF(C11>0,C11,-D11)                                                                                                                                                                                                                                                                                                                                                                                       |
| J11    | =IF(G11="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K11    | =IF(G11="ON",0,VLOOKUP(G11,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O11    | =IF(L11="Y","POSTED",VLOOKUP(N11,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P11    | =ROUND(IF(L11="Y",0,IF(OR(RECONCILIATION!O11='Data Parameters'!$D$184,RECONCILIATION!O11='Data Parameters'!$D$185),0,IF(RECONCILIATION!G11="ON",RECONCILIATION!E11/(1+RECONCILIATION!J11)*0.13,IF(H11="N",0,IF(RECONCILIATION!I11="Y",RECONCILIATION!E11/(1+RECONCILIATION!J11+RECONCILIATION!K11)*RECONCILIATION!J11,RECONCILIATION!E11/(1+RECONCILIATION!J11)*RECONCILIATION!J11))))),2)                |
| Q11    | =E11-P11                                                                                                                                                                                                                                                                                                                                                                                                  |
| R11    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f6135b0>                                                                                                                                                                                                                                                                                                                                           |
| E12    | =IF(C12>0,C12,-D12)                                                                                                                                                                                                                                                                                                                                                                                       |
| J12    | =IF(G12="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K12    | =IF(G12="ON",0,VLOOKUP(G12,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O12    | =IF(L12="Y","POSTED",VLOOKUP(N12,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P12    | =ROUND(IF(L12="Y",0,IF(OR(RECONCILIATION!O12='Data Parameters'!$D$184,RECONCILIATION!O12='Data Parameters'!$D$185),0,IF(RECONCILIATION!G12="ON",RECONCILIATION!E12/(1+RECONCILIATION!J12)*0.13,IF(H12="N",0,IF(RECONCILIATION!I12="Y",RECONCILIATION!E12/(1+RECONCILIATION!J12+RECONCILIATION!K12)*RECONCILIATION!J12,RECONCILIATION!E12/(1+RECONCILIATION!J12)*RECONCILIATION!J12))))),2)                |
| Q12    | =E12-P12                                                                                                                                                                                                                                                                                                                                                                                                  |
| R12    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1389bfc10>                                                                                                                                                                                                                                                                                                                                           |
| E13    | =IF(C13>0,C13,-D13)                                                                                                                                                                                                                                                                                                                                                                                       |
| J13    | =IF(G13="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K13    | =IF(G13="ON",0,VLOOKUP(G13,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O13    | =IF(L13="Y","POSTED",VLOOKUP(N13,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| Q13    | =E13-P13                                                                                                                                                                                                                                                                                                                                                                                                  |
| R13    | <openpyxl.worksheet.formula.ArrayFormula object at 0x138fe4520>                                                                                                                                                                                                                                                                                                                                           |
| E14    | =IF(C14>0,C14,-D14)                                                                                                                                                                                                                                                                                                                                                                                       |
| J14    | =IF(G14="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K14    | =IF(G14="ON",0,VLOOKUP(G14,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O14    | =IF(L14="Y","POSTED",VLOOKUP(N14,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P14    | =ROUND(IF(L14="Y",0,IF(OR(RECONCILIATION!O14='Data Parameters'!$D$184,RECONCILIATION!O14='Data Parameters'!$D$185),0,IF(RECONCILIATION!G14="ON",RECONCILIATION!E14/(1+RECONCILIATION!J14)*0.13,IF(H14="N",0,IF(RECONCILIATION!I14="Y",RECONCILIATION!E14/(1+RECONCILIATION!J14+RECONCILIATION!K14)*RECONCILIATION!J14,RECONCILIATION!E14/(1+RECONCILIATION!J14)*RECONCILIATION!J14))))),2)                |
| Q14    | =E14-P14                                                                                                                                                                                                                                                                                                                                                                                                  |
| R14    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdde50>                                                                                                                                                                                                                                                                                                                                           |
| E15    | =IF(C15>0,C15,-D15)                                                                                                                                                                                                                                                                                                                                                                                       |
| J15    | =IF(G15="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K15    | =IF(G15="ON",0,VLOOKUP(G15,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O15    | =IF(L15="Y","POSTED",VLOOKUP(N15,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P15    | =ROUND(IF(L15="Y",0,IF(OR(RECONCILIATION!O15='Data Parameters'!$D$184,RECONCILIATION!O15='Data Parameters'!$D$185),0,IF(RECONCILIATION!G15="ON",RECONCILIATION!E15/(1+RECONCILIATION!J15)*0.13,IF(H15="N",0,IF(RECONCILIATION!I15="Y",RECONCILIATION!E15/(1+RECONCILIATION!J15+RECONCILIATION!K15)*RECONCILIATION!J15,RECONCILIATION!E15/(1+RECONCILIATION!J15)*RECONCILIATION!J15))))),2)                |
| Q15    | =E15-P15                                                                                                                                                                                                                                                                                                                                                                                                  |
| R15    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdda90>                                                                                                                                                                                                                                                                                                                                           |
| E16    | =IF(C16>0,C16,-D16)                                                                                                                                                                                                                                                                                                                                                                                       |
| J16    | =IF(G16="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K16    | =IF(G16="ON",0,VLOOKUP(G16,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O16    | =IF(L16="Y","POSTED",VLOOKUP(N16,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P16    | =ROUND(IF(L16="Y",0,IF(OR(RECONCILIATION!O16='Data Parameters'!$D$184,RECONCILIATION!O16='Data Parameters'!$D$185),0,IF(RECONCILIATION!G16="ON",RECONCILIATION!E16/(1+RECONCILIATION!J16)*0.13,IF(H16="N",0,IF(RECONCILIATION!I16="Y",RECONCILIATION!E16/(1+RECONCILIATION!J16+RECONCILIATION!K16)*RECONCILIATION!J16,RECONCILIATION!E16/(1+RECONCILIATION!J16)*RECONCILIATION!J16))))),2)                |
| Q16    | =E16-P16                                                                                                                                                                                                                                                                                                                                                                                                  |
| R16    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdd520>                                                                                                                                                                                                                                                                                                                                           |
| E17    | =IF(C17>0,C17,-D17)                                                                                                                                                                                                                                                                                                                                                                                       |
| J17    | =IF(G17="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K17    | =IF(G17="ON",0,VLOOKUP(G17,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O17    | =IF(L17="Y","POSTED",VLOOKUP(N17,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P17    | =ROUND(IF(L17="Y",0,IF(OR(RECONCILIATION!O17='Data Parameters'!$D$184,RECONCILIATION!O17='Data Parameters'!$D$185),0,IF(RECONCILIATION!G17="ON",RECONCILIATION!E17/(1+RECONCILIATION!J17)*0.13,IF(H17="N",0,IF(RECONCILIATION!I17="Y",RECONCILIATION!E17/(1+RECONCILIATION!J17+RECONCILIATION!K17)*RECONCILIATION!J17,RECONCILIATION!E17/(1+RECONCILIATION!J17)*RECONCILIATION!J17))))),2)                |
| Q17    | =E17-P17                                                                                                                                                                                                                                                                                                                                                                                                  |
| R17    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdd310>                                                                                                                                                                                                                                                                                                                                           |
| E18    | =IF(C18>0,C18,-D18)                                                                                                                                                                                                                                                                                                                                                                                       |
| J18    | =IF(G18="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K18    | =IF(G18="ON",0,VLOOKUP(G18,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O18    | =IF(L18="Y","POSTED",VLOOKUP(N18,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P18    | =ROUND(IF(L18="Y",0,IF(OR(RECONCILIATION!O18='Data Parameters'!$D$184,RECONCILIATION!O18='Data Parameters'!$D$185),0,IF(RECONCILIATION!G18="ON",RECONCILIATION!E18/(1+RECONCILIATION!J18)*0.13,IF(H18="N",0,IF(RECONCILIATION!I18="Y",RECONCILIATION!E18/(1+RECONCILIATION!J18+RECONCILIATION!K18)*RECONCILIATION!J18,RECONCILIATION!E18/(1+RECONCILIATION!J18)*RECONCILIATION!J18))))),2)                |
| Q18    | =E18-P18                                                                                                                                                                                                                                                                                                                                                                                                  |
| R18    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdd940>                                                                                                                                                                                                                                                                                                                                           |
| E19    | =IF(C19>0,C19,-D19)                                                                                                                                                                                                                                                                                                                                                                                       |
| J19    | =IF(G19="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K19    | =IF(G19="ON",0,VLOOKUP(G19,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O19    | =IF(L19="Y","POSTED",VLOOKUP(N19,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P19    | =ROUND(IF(L19="Y",0,IF(OR(RECONCILIATION!O19='Data Parameters'!$D$184,RECONCILIATION!O19='Data Parameters'!$D$185),0,IF(RECONCILIATION!G19="ON",RECONCILIATION!E19/(1+RECONCILIATION!J19)*0.13,IF(H19="N",0,IF(RECONCILIATION!I19="Y",RECONCILIATION!E19/(1+RECONCILIATION!J19+RECONCILIATION!K19)*RECONCILIATION!J19,RECONCILIATION!E19/(1+RECONCILIATION!J19)*RECONCILIATION!J19))))),2)                |
| Q19    | =E19-P19                                                                                                                                                                                                                                                                                                                                                                                                  |
| R19    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1480b09a0>                                                                                                                                                                                                                                                                                                                                           |
| E20    | =IF(C20>0,C20,-D20)                                                                                                                                                                                                                                                                                                                                                                                       |
| J20    | =IF(G20="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K20    | =IF(G20="ON",0,VLOOKUP(G20,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O20    | =IF(L20="Y","POSTED",VLOOKUP(N20,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P20    | =ROUND(IF(L20="Y",0,IF(OR(RECONCILIATION!O20='Data Parameters'!$D$184,RECONCILIATION!O20='Data Parameters'!$D$185),0,IF(RECONCILIATION!G20="ON",RECONCILIATION!E20/(1+RECONCILIATION!J20)*0.13,IF(H20="N",0,IF(RECONCILIATION!I20="Y",RECONCILIATION!E20/(1+RECONCILIATION!J20+RECONCILIATION!K20)*RECONCILIATION!J20,RECONCILIATION!E20/(1+RECONCILIATION!J20)*RECONCILIATION!J20))))),2)                |
| Q20    | =E20-P20                                                                                                                                                                                                                                                                                                                                                                                                  |
| R20    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1393dba30>                                                                                                                                                                                                                                                                                                                                           |
| E21    | =IF(C21>0,C21,-D21)                                                                                                                                                                                                                                                                                                                                                                                       |
| J21    | =IF(G21="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K21    | =IF(G21="ON",0,VLOOKUP(G21,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O21    | =IF(L21="Y","POSTED",VLOOKUP(N21,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P21    | =ROUND(IF(L21="Y",0,IF(OR(RECONCILIATION!O21='Data Parameters'!$D$184,RECONCILIATION!O21='Data Parameters'!$D$185),0,IF(RECONCILIATION!G21="ON",RECONCILIATION!E21/(1+RECONCILIATION!J21)*0.13,IF(H21="N",0,IF(RECONCILIATION!I21="Y",RECONCILIATION!E21/(1+RECONCILIATION!J21+RECONCILIATION!K21)*RECONCILIATION!J21,RECONCILIATION!E21/(1+RECONCILIATION!J21)*RECONCILIATION!J21))))),2)                |
| Q21    | =E21-P21                                                                                                                                                                                                                                                                                                                                                                                                  |
| R21    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ea0fa30>                                                                                                                                                                                                                                                                                                                                           |
| E22    | =IF(C22>0,C22,-D22)                                                                                                                                                                                                                                                                                                                                                                                       |
| J22    | =IF(G22="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K22    | =IF(G22="ON",0,VLOOKUP(G22,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O22    | =IF(L22="Y","POSTED",VLOOKUP(N22,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P22    | =ROUND(IF(L22="Y",0,IF(OR(RECONCILIATION!O22='Data Parameters'!$D$184,RECONCILIATION!O22='Data Parameters'!$D$185),0,IF(RECONCILIATION!G22="ON",RECONCILIATION!E22/(1+RECONCILIATION!J22)*0.13,IF(H22="N",0,IF(RECONCILIATION!I22="Y",RECONCILIATION!E22/(1+RECONCILIATION!J22+RECONCILIATION!K22)*RECONCILIATION!J22,RECONCILIATION!E22/(1+RECONCILIATION!J22)*RECONCILIATION!J22))))),2)                |
| Q22    | =E22-P22                                                                                                                                                                                                                                                                                                                                                                                                  |
| R22    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1480b02b0>                                                                                                                                                                                                                                                                                                                                           |
| E23    | =IF(C23>0,C23,-D23)                                                                                                                                                                                                                                                                                                                                                                                       |
| J23    | =IF(G23="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K23    | =IF(G23="ON",0,VLOOKUP(G23,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O23    | =IF(L23="Y","POSTED",VLOOKUP(N23,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P23    | =ROUND(IF(L23="Y",0,IF(OR(RECONCILIATION!O23='Data Parameters'!$D$184,RECONCILIATION!O23='Data Parameters'!$D$185),0,IF(RECONCILIATION!G23="ON",RECONCILIATION!E23/(1+RECONCILIATION!J23)*0.13,IF(H23="N",0,IF(RECONCILIATION!I23="Y",RECONCILIATION!E23/(1+RECONCILIATION!J23+RECONCILIATION!K23)*RECONCILIATION!J23,RECONCILIATION!E23/(1+RECONCILIATION!J23)*RECONCILIATION!J23))))),2)                |
| Q23    | =E23-P23                                                                                                                                                                                                                                                                                                                                                                                                  |
| R23    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1389b1100>                                                                                                                                                                                                                                                                                                                                           |
| E24    | =IF(C24>0,C24,-D24)                                                                                                                                                                                                                                                                                                                                                                                       |
| J24    | =IF(G24="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K24    | =IF(G24="ON",0,VLOOKUP(G24,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O24    | =IF(L24="Y","POSTED",VLOOKUP(N24,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P24    | =ROUND(IF(L24="Y",0,IF(OR(RECONCILIATION!O24='Data Parameters'!$D$184,RECONCILIATION!O24='Data Parameters'!$D$185),0,IF(RECONCILIATION!G24="ON",RECONCILIATION!E24/(1+RECONCILIATION!J24)*0.13,IF(H24="N",0,IF(RECONCILIATION!I24="Y",RECONCILIATION!E24/(1+RECONCILIATION!J24+RECONCILIATION!K24)*RECONCILIATION!J24,RECONCILIATION!E24/(1+RECONCILIATION!J24)*RECONCILIATION!J24))))),2)                |
| Q24    | =E24-P24                                                                                                                                                                                                                                                                                                                                                                                                  |
| R24    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ecec190>                                                                                                                                                                                                                                                                                                                                           |
| E25    | =IF(C25>0,C25,-D25)                                                                                                                                                                                                                                                                                                                                                                                       |
| J25    | =IF(G25="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K25    | =IF(G25="ON",0,VLOOKUP(G25,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O25    | =IF(L25="Y","POSTED",VLOOKUP(N25,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P25    | =ROUND(IF(L25="Y",0,IF(OR(RECONCILIATION!O25='Data Parameters'!$D$184,RECONCILIATION!O25='Data Parameters'!$D$185),0,IF(RECONCILIATION!G25="ON",RECONCILIATION!E25/(1+RECONCILIATION!J25)*0.13,IF(H25="N",0,IF(RECONCILIATION!I25="Y",RECONCILIATION!E25/(1+RECONCILIATION!J25+RECONCILIATION!K25)*RECONCILIATION!J25,RECONCILIATION!E25/(1+RECONCILIATION!J25)*RECONCILIATION!J25))))),2)                |
| Q25    | =E25-P25                                                                                                                                                                                                                                                                                                                                                                                                  |
| R25    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fcc3280>                                                                                                                                                                                                                                                                                                                                           |
| E26    | =IF(C26>0,C26,-D26)                                                                                                                                                                                                                                                                                                                                                                                       |
| J26    | =IF(G26="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K26    | =IF(G26="ON",0,VLOOKUP(G26,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O26    | =IF(L26="Y","POSTED",VLOOKUP(N26,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P26    | =ROUND(IF(L26="Y",0,IF(OR(RECONCILIATION!O26='Data Parameters'!$D$184,RECONCILIATION!O26='Data Parameters'!$D$185),0,IF(RECONCILIATION!G26="ON",RECONCILIATION!E26/(1+RECONCILIATION!J26)*0.13,IF(H26="N",0,IF(RECONCILIATION!I26="Y",RECONCILIATION!E26/(1+RECONCILIATION!J26+RECONCILIATION!K26)*RECONCILIATION!J26,RECONCILIATION!E26/(1+RECONCILIATION!J26)*RECONCILIATION!J26))))),2)                |
| Q26    | =E26-P26                                                                                                                                                                                                                                                                                                                                                                                                  |
| R26    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbcf700>                                                                                                                                                                                                                                                                                                                                           |
| E27    | =IF(C27>0,C27,-D27)                                                                                                                                                                                                                                                                                                                                                                                       |
| J27    | =IF(G27="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K27    | =IF(G27="ON",0,VLOOKUP(G27,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O27    | =IF(L27="Y","POSTED",VLOOKUP(N27,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P27    | =ROUND(IF(L27="Y",0,IF(OR(RECONCILIATION!O27='Data Parameters'!$D$184,RECONCILIATION!O27='Data Parameters'!$D$185),0,IF(RECONCILIATION!G27="ON",RECONCILIATION!E27/(1+RECONCILIATION!J27)*0.13,IF(H27="N",0,IF(RECONCILIATION!I27="Y",RECONCILIATION!E27/(1+RECONCILIATION!J27+RECONCILIATION!K27)*RECONCILIATION!J27,RECONCILIATION!E27/(1+RECONCILIATION!J27)*RECONCILIATION!J27))))),2)                |
| Q27    | =E27-P27                                                                                                                                                                                                                                                                                                                                                                                                  |
| R27    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdde20>                                                                                                                                                                                                                                                                                                                                           |
| E28    | =IF(C28>0,C28,-D28)                                                                                                                                                                                                                                                                                                                                                                                       |
| J28    | =IF(G28="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K28    | =IF(G28="ON",0,VLOOKUP(G28,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O28    | =IF(L28="Y","POSTED",VLOOKUP(N28,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P28    | =ROUND(IF(L28="Y",0,IF(OR(RECONCILIATION!O28='Data Parameters'!$D$184,RECONCILIATION!O28='Data Parameters'!$D$185),0,IF(RECONCILIATION!G28="ON",RECONCILIATION!E28/(1+RECONCILIATION!J28)*0.13,IF(H28="N",0,IF(RECONCILIATION!I28="Y",RECONCILIATION!E28/(1+RECONCILIATION!J28+RECONCILIATION!K28)*RECONCILIATION!J28,RECONCILIATION!E28/(1+RECONCILIATION!J28)*RECONCILIATION!J28))))),2)                |
| Q28    | =E28-P28                                                                                                                                                                                                                                                                                                                                                                                                  |
| R28    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbcf910>                                                                                                                                                                                                                                                                                                                                           |
| E29    | =IF(C29>0,C29,-D29)                                                                                                                                                                                                                                                                                                                                                                                       |
| J29    | =IF(G29="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K29    | =IF(G29="ON",0,VLOOKUP(G29,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O29    | =IF(L29="Y","POSTED",VLOOKUP(N29,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P29    | =ROUND(IF(L29="Y",0,IF(OR(RECONCILIATION!O29='Data Parameters'!$D$184,RECONCILIATION!O29='Data Parameters'!$D$185),0,IF(RECONCILIATION!G29="ON",RECONCILIATION!E29/(1+RECONCILIATION!J29)*0.13,IF(H29="N",0,IF(RECONCILIATION!I29="Y",RECONCILIATION!E29/(1+RECONCILIATION!J29+RECONCILIATION!K29)*RECONCILIATION!J29,RECONCILIATION!E29/(1+RECONCILIATION!J29)*RECONCILIATION!J29))))),2)                |
| Q29    | =E29-P29                                                                                                                                                                                                                                                                                                                                                                                                  |
| R29    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdd910>                                                                                                                                                                                                                                                                                                                                           |
| E30    | =IF(C30>0,C30,-D30)                                                                                                                                                                                                                                                                                                                                                                                       |
| J30    | =IF(G30="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K30    | =IF(G30="ON",0,VLOOKUP(G30,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O30    | =IF(L30="Y","POSTED",VLOOKUP(N30,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P30    | =ROUND(IF(L30="Y",0,IF(OR(RECONCILIATION!O30='Data Parameters'!$D$184,RECONCILIATION!O30='Data Parameters'!$D$185),0,IF(RECONCILIATION!G30="ON",RECONCILIATION!E30/(1+RECONCILIATION!J30)*0.13,IF(H30="N",0,IF(RECONCILIATION!I30="Y",RECONCILIATION!E30/(1+RECONCILIATION!J30+RECONCILIATION!K30)*RECONCILIATION!J30,RECONCILIATION!E30/(1+RECONCILIATION!J30)*RECONCILIATION!J30))))),2)                |
| Q30    | =E30-P30                                                                                                                                                                                                                                                                                                                                                                                                  |
| R30    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbcf0a0>                                                                                                                                                                                                                                                                                                                                           |
| E31    | =IF(C31>0,C31,-D31)                                                                                                                                                                                                                                                                                                                                                                                       |
| J31    | =IF(G31="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K31    | =IF(G31="ON",0,VLOOKUP(G31,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O31    | =IF(L31="Y","POSTED",VLOOKUP(N31,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P31    | =ROUND(IF(L31="Y",0,IF(OR(RECONCILIATION!O31='Data Parameters'!$D$184,RECONCILIATION!O31='Data Parameters'!$D$185),0,IF(RECONCILIATION!G31="ON",RECONCILIATION!E31/(1+RECONCILIATION!J31)*0.13,IF(H31="N",0,IF(RECONCILIATION!I31="Y",RECONCILIATION!E31/(1+RECONCILIATION!J31+RECONCILIATION!K31)*RECONCILIATION!J31,RECONCILIATION!E31/(1+RECONCILIATION!J31)*RECONCILIATION!J31))))),2)                |
| Q31    | =E31-P31                                                                                                                                                                                                                                                                                                                                                                                                  |
| R31    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc95130>                                                                                                                                                                                                                                                                                                                                           |
| E32    | =IF(C32>0,C32,-D32)                                                                                                                                                                                                                                                                                                                                                                                       |
| J32    | =IF(G32="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K32    | =IF(G32="ON",0,VLOOKUP(G32,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O32    | =IF(L32="Y","POSTED",VLOOKUP(N32,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P32    | =ROUND(IF(L32="Y",0,IF(OR(RECONCILIATION!O32='Data Parameters'!$D$184,RECONCILIATION!O32='Data Parameters'!$D$185),0,IF(RECONCILIATION!G32="ON",RECONCILIATION!E32/(1+RECONCILIATION!J32)*0.13,IF(H32="N",0,IF(RECONCILIATION!I32="Y",RECONCILIATION!E32/(1+RECONCILIATION!J32+RECONCILIATION!K32)*RECONCILIATION!J32,RECONCILIATION!E32/(1+RECONCILIATION!J32)*RECONCILIATION!J32))))),2)                |
| Q32    | =E32-P32                                                                                                                                                                                                                                                                                                                                                                                                  |
| R32    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ec16a90>                                                                                                                                                                                                                                                                                                                                           |
| E33    | =IF(C33>0,C33,-D33)                                                                                                                                                                                                                                                                                                                                                                                       |
| J33    | =IF(G33="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K33    | =IF(G33="ON",0,VLOOKUP(G33,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O33    | =IF(L33="Y","POSTED",VLOOKUP(N33,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P33    | =ROUND(IF(L33="Y",0,IF(OR(RECONCILIATION!O33='Data Parameters'!$D$184,RECONCILIATION!O33='Data Parameters'!$D$185),0,IF(RECONCILIATION!G33="ON",RECONCILIATION!E33/(1+RECONCILIATION!J33)*0.13,IF(H33="N",0,IF(RECONCILIATION!I33="Y",RECONCILIATION!E33/(1+RECONCILIATION!J33+RECONCILIATION!K33)*RECONCILIATION!J33,RECONCILIATION!E33/(1+RECONCILIATION!J33)*RECONCILIATION!J33))))),2)                |
| Q33    | =E33-P33                                                                                                                                                                                                                                                                                                                                                                                                  |
| R33    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc950d0>                                                                                                                                                                                                                                                                                                                                           |
| E34    | =IF(C34>0,C34,-D34)                                                                                                                                                                                                                                                                                                                                                                                       |
| J34    | =IF(G34="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K34    | =IF(G34="ON",0,VLOOKUP(G34,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O34    | =IF(L34="Y","POSTED",VLOOKUP(N34,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P34    | =ROUND(IF(L34="Y",0,IF(OR(RECONCILIATION!O34='Data Parameters'!$D$184,RECONCILIATION!O34='Data Parameters'!$D$185),0,IF(RECONCILIATION!G34="ON",RECONCILIATION!E34/(1+RECONCILIATION!J34)*0.13,IF(H34="N",0,IF(RECONCILIATION!I34="Y",RECONCILIATION!E34/(1+RECONCILIATION!J34+RECONCILIATION!K34)*RECONCILIATION!J34,RECONCILIATION!E34/(1+RECONCILIATION!J34)*RECONCILIATION!J34))))),2)                |
| Q34    | =E34-P34                                                                                                                                                                                                                                                                                                                                                                                                  |
| R34    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1391d2d60>                                                                                                                                                                                                                                                                                                                                           |
| E35    | =IF(C35>0,C35,-D35)                                                                                                                                                                                                                                                                                                                                                                                       |
| J35    | =IF(G35="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K35    | =IF(G35="ON",0,VLOOKUP(G35,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O35    | =IF(L35="Y","POSTED",VLOOKUP(N35,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P35    | =ROUND(IF(L35="Y",0,IF(OR(RECONCILIATION!O35='Data Parameters'!$D$184,RECONCILIATION!O35='Data Parameters'!$D$185),0,IF(RECONCILIATION!G35="ON",RECONCILIATION!E35/(1+RECONCILIATION!J35)*0.13,IF(H35="N",0,IF(RECONCILIATION!I35="Y",RECONCILIATION!E35/(1+RECONCILIATION!J35+RECONCILIATION!K35)*RECONCILIATION!J35,RECONCILIATION!E35/(1+RECONCILIATION!J35)*RECONCILIATION!J35))))),2)                |
| Q35    | =E35-P35                                                                                                                                                                                                                                                                                                                                                                                                  |
| R35    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbcf970>                                                                                                                                                                                                                                                                                                                                           |
| E36    | =IF(C36>0,C36,-D36)                                                                                                                                                                                                                                                                                                                                                                                       |
| J36    | =IF(G36="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K36    | =IF(G36="ON",0,VLOOKUP(G36,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O36    | =IF(L36="Y","POSTED",VLOOKUP(N36,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P36    | =ROUND(IF(L36="Y",0,IF(OR(RECONCILIATION!O36='Data Parameters'!$D$184,RECONCILIATION!O36='Data Parameters'!$D$185),0,IF(RECONCILIATION!G36="ON",RECONCILIATION!E36/(1+RECONCILIATION!J36)*0.13,IF(H36="N",0,IF(RECONCILIATION!I36="Y",RECONCILIATION!E36/(1+RECONCILIATION!J36+RECONCILIATION!K36)*RECONCILIATION!J36,RECONCILIATION!E36/(1+RECONCILIATION!J36)*RECONCILIATION!J36))))),2)                |
| Q36    | =E36-P36                                                                                                                                                                                                                                                                                                                                                                                                  |
| R36    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbd7760>                                                                                                                                                                                                                                                                                                                                           |
| E37    | =IF(C37>0,C37,-D37)                                                                                                                                                                                                                                                                                                                                                                                       |
| J37    | =IF(G37="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K37    | =IF(G37="ON",0,VLOOKUP(G37,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O37    | =IF(L37="Y","POSTED",VLOOKUP(N37,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P37    | =ROUND(IF(L37="Y",0,IF(OR(RECONCILIATION!O37='Data Parameters'!$D$184,RECONCILIATION!O37='Data Parameters'!$D$185),0,IF(RECONCILIATION!G37="ON",RECONCILIATION!E37/(1+RECONCILIATION!J37)*0.13,IF(H37="N",0,IF(RECONCILIATION!I37="Y",RECONCILIATION!E37/(1+RECONCILIATION!J37+RECONCILIATION!K37)*RECONCILIATION!J37,RECONCILIATION!E37/(1+RECONCILIATION!J37)*RECONCILIATION!J37))))),2)                |
| Q37    | =E37-P37                                                                                                                                                                                                                                                                                                                                                                                                  |
| R37    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbd7bb0>                                                                                                                                                                                                                                                                                                                                           |
| E38    | =IF(C38>0,C38,-D38)                                                                                                                                                                                                                                                                                                                                                                                       |
| J38    | =IF(G38="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K38    | =IF(G38="ON",0,VLOOKUP(G38,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O38    | =IF(L38="Y","POSTED",VLOOKUP(N38,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P38    | =ROUND(IF(L38="Y",0,IF(OR(RECONCILIATION!O38='Data Parameters'!$D$184,RECONCILIATION!O38='Data Parameters'!$D$185),0,IF(RECONCILIATION!G38="ON",RECONCILIATION!E38/(1+RECONCILIATION!J38)*0.13,IF(H38="N",0,IF(RECONCILIATION!I38="Y",RECONCILIATION!E38/(1+RECONCILIATION!J38+RECONCILIATION!K38)*RECONCILIATION!J38,RECONCILIATION!E38/(1+RECONCILIATION!J38)*RECONCILIATION!J38))))),2)                |
| Q38    | =E38-P38                                                                                                                                                                                                                                                                                                                                                                                                  |
| R38    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ec163a0>                                                                                                                                                                                                                                                                                                                                           |
| E39    | =IF(C39>0,C39,-D39)                                                                                                                                                                                                                                                                                                                                                                                       |
| J39    | =IF(G39="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K39    | =IF(G39="ON",0,VLOOKUP(G39,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O39    | =IF(L39="Y","POSTED",VLOOKUP(N39,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P39    | =ROUND(IF(L39="Y",0,IF(OR(RECONCILIATION!O39='Data Parameters'!$D$184,RECONCILIATION!O39='Data Parameters'!$D$185),0,IF(RECONCILIATION!G39="ON",RECONCILIATION!E39/(1+RECONCILIATION!J39)*0.13,IF(H39="N",0,IF(RECONCILIATION!I39="Y",RECONCILIATION!E39/(1+RECONCILIATION!J39+RECONCILIATION!K39)*RECONCILIATION!J39,RECONCILIATION!E39/(1+RECONCILIATION!J39)*RECONCILIATION!J39))))),2)                |
| Q39    | =E39-P39                                                                                                                                                                                                                                                                                                                                                                                                  |
| R39    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc95700>                                                                                                                                                                                                                                                                                                                                           |
| E40    | =IF(C40>0,C40,-D40)                                                                                                                                                                                                                                                                                                                                                                                       |
| J40    | =IF(G40="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K40    | =IF(G40="ON",0,VLOOKUP(G40,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O40    | =IF(L40="Y","POSTED",VLOOKUP(N40,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P40    | =ROUND(IF(L40="Y",0,IF(OR(RECONCILIATION!O40='Data Parameters'!$D$184,RECONCILIATION!O40='Data Parameters'!$D$185),0,IF(RECONCILIATION!G40="ON",RECONCILIATION!E40/(1+RECONCILIATION!J40)*0.13,IF(H40="N",0,IF(RECONCILIATION!I40="Y",RECONCILIATION!E40/(1+RECONCILIATION!J40+RECONCILIATION!K40)*RECONCILIATION!J40,RECONCILIATION!E40/(1+RECONCILIATION!J40)*RECONCILIATION!J40))))),2)                |
| Q40    | =E40-P40                                                                                                                                                                                                                                                                                                                                                                                                  |
| R40    | <openpyxl.worksheet.formula.ArrayFormula object at 0x138f486a0>                                                                                                                                                                                                                                                                                                                                           |
| E41    | =IF(C41>0,C41,-D41)                                                                                                                                                                                                                                                                                                                                                                                       |
| J41    | =IF(G41="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K41    | =IF(G41="ON",0,VLOOKUP(G41,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O41    | =IF(L41="Y","POSTED",VLOOKUP(N41,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P41    | =ROUND(IF(L41="Y",0,IF(OR(RECONCILIATION!O41='Data Parameters'!$D$184,RECONCILIATION!O41='Data Parameters'!$D$185),0,IF(RECONCILIATION!G41="ON",RECONCILIATION!E41/(1+RECONCILIATION!J41)*0.13,IF(H41="N",0,IF(RECONCILIATION!I41="Y",RECONCILIATION!E41/(1+RECONCILIATION!J41+RECONCILIATION!K41)*RECONCILIATION!J41,RECONCILIATION!E41/(1+RECONCILIATION!J41)*RECONCILIATION!J41))))),2)                |
| Q41    | =E41-P41                                                                                                                                                                                                                                                                                                                                                                                                  |
| R41    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ec168e0>                                                                                                                                                                                                                                                                                                                                           |
| E42    | =IF(C42>0,C42,-D42)                                                                                                                                                                                                                                                                                                                                                                                       |
| J42    | =IF(G42="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K42    | =IF(G42="ON",0,VLOOKUP(G42,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O42    | =IF(L42="Y","POSTED",VLOOKUP(N42,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P42    | =ROUND(IF(L42="Y",0,IF(OR(RECONCILIATION!O42='Data Parameters'!$D$184,RECONCILIATION!O42='Data Parameters'!$D$185),0,IF(RECONCILIATION!G42="ON",RECONCILIATION!E42/(1+RECONCILIATION!J42)*0.13,IF(H42="N",0,IF(RECONCILIATION!I42="Y",RECONCILIATION!E42/(1+RECONCILIATION!J42+RECONCILIATION!K42)*RECONCILIATION!J42,RECONCILIATION!E42/(1+RECONCILIATION!J42)*RECONCILIATION!J42))))),2)                |
| Q42    | =E42-P42                                                                                                                                                                                                                                                                                                                                                                                                  |
| R42    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc95070>                                                                                                                                                                                                                                                                                                                                           |
| E43    | =IF(C43>0,C43,-D43)                                                                                                                                                                                                                                                                                                                                                                                       |
| J43    | =IF(G43="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K43    | =IF(G43="ON",0,VLOOKUP(G43,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O43    | =IF(L43="Y","POSTED",VLOOKUP(N43,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P43    | =ROUND(IF(L43="Y",0,IF(OR(RECONCILIATION!O43='Data Parameters'!$D$184,RECONCILIATION!O43='Data Parameters'!$D$185),0,IF(RECONCILIATION!G43="ON",RECONCILIATION!E43/(1+RECONCILIATION!J43)*0.13,IF(H43="N",0,IF(RECONCILIATION!I43="Y",RECONCILIATION!E43/(1+RECONCILIATION!J43+RECONCILIATION!K43)*RECONCILIATION!J43,RECONCILIATION!E43/(1+RECONCILIATION!J43)*RECONCILIATION!J43))))),2)                |
| Q43    | =E43-P43                                                                                                                                                                                                                                                                                                                                                                                                  |
| R43    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbd7c10>                                                                                                                                                                                                                                                                                                                                           |
| E44    | =IF(C44>0,C44,-D44)                                                                                                                                                                                                                                                                                                                                                                                       |
| J44    | =IF(G44="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K44    | =IF(G44="ON",0,VLOOKUP(G44,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O44    | =IF(L44="Y","POSTED",VLOOKUP(N44,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P44    | =ROUND(IF(L44="Y",0,IF(OR(RECONCILIATION!O44='Data Parameters'!$D$184,RECONCILIATION!O44='Data Parameters'!$D$185),0,IF(RECONCILIATION!G44="ON",RECONCILIATION!E44/(1+RECONCILIATION!J44)*0.13,IF(H44="N",0,IF(RECONCILIATION!I44="Y",RECONCILIATION!E44/(1+RECONCILIATION!J44+RECONCILIATION!K44)*RECONCILIATION!J44,RECONCILIATION!E44/(1+RECONCILIATION!J44)*RECONCILIATION!J44))))),2)                |
| Q44    | =E44-P44                                                                                                                                                                                                                                                                                                                                                                                                  |
| R44    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13eaeca60>                                                                                                                                                                                                                                                                                                                                           |
| E45    | =IF(C45>0,C45,-D45)                                                                                                                                                                                                                                                                                                                                                                                       |
| J45    | =IF(G45="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K45    | =IF(G45="ON",0,VLOOKUP(G45,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O45    | =IF(L45="Y","POSTED",VLOOKUP(N45,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P45    | =ROUND(IF(L45="Y",0,IF(OR(RECONCILIATION!O45='Data Parameters'!$D$184,RECONCILIATION!O45='Data Parameters'!$D$185),0,IF(RECONCILIATION!G45="ON",RECONCILIATION!E45/(1+RECONCILIATION!J45)*0.13,IF(H45="N",0,IF(RECONCILIATION!I45="Y",RECONCILIATION!E45/(1+RECONCILIATION!J45+RECONCILIATION!K45)*RECONCILIATION!J45,RECONCILIATION!E45/(1+RECONCILIATION!J45)*RECONCILIATION!J45))))),2)                |
| Q45    | =E45-P45                                                                                                                                                                                                                                                                                                                                                                                                  |
| R45    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f9522e0>                                                                                                                                                                                                                                                                                                                                           |
| E46    | =IF(C46>0,C46,-D46)                                                                                                                                                                                                                                                                                                                                                                                       |
| J46    | =IF(G46="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K46    | =IF(G46="ON",0,VLOOKUP(G46,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O46    | =IF(L46="Y","POSTED",VLOOKUP(N46,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P46    | =ROUND(IF(L46="Y",0,IF(OR(RECONCILIATION!O46='Data Parameters'!$D$184,RECONCILIATION!O46='Data Parameters'!$D$185),0,IF(RECONCILIATION!G46="ON",RECONCILIATION!E46/(1+RECONCILIATION!J46)*0.13,IF(H46="N",0,IF(RECONCILIATION!I46="Y",RECONCILIATION!E46/(1+RECONCILIATION!J46+RECONCILIATION!K46)*RECONCILIATION!J46,RECONCILIATION!E46/(1+RECONCILIATION!J46)*RECONCILIATION!J46))))),2)                |
| Q46    | =E46-P46                                                                                                                                                                                                                                                                                                                                                                                                  |
| R46    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ecec1c0>                                                                                                                                                                                                                                                                                                                                           |
| E47    | =IF(C47>0,C47,-D47)                                                                                                                                                                                                                                                                                                                                                                                       |
| J47    | =IF(G47="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K47    | =IF(G47="ON",0,VLOOKUP(G47,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O47    | =IF(L47="Y","POSTED",VLOOKUP(N47,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P47    | =ROUND(IF(L47="Y",0,IF(OR(RECONCILIATION!O47='Data Parameters'!$D$184,RECONCILIATION!O47='Data Parameters'!$D$185),0,IF(RECONCILIATION!G47="ON",RECONCILIATION!E47/(1+RECONCILIATION!J47)*0.13,IF(H47="N",0,IF(RECONCILIATION!I47="Y",RECONCILIATION!E47/(1+RECONCILIATION!J47+RECONCILIATION!K47)*RECONCILIATION!J47,RECONCILIATION!E47/(1+RECONCILIATION!J47)*RECONCILIATION!J47))))),2)                |
| Q47    | =E47-P47                                                                                                                                                                                                                                                                                                                                                                                                  |
| R47    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13eaecac0>                                                                                                                                                                                                                                                                                                                                           |
| E48    | =IF(C48>0,C48,-D48)                                                                                                                                                                                                                                                                                                                                                                                       |
| J48    | =IF(G48="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K48    | =IF(G48="ON",0,VLOOKUP(G48,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O48    | =IF(L48="Y","POSTED",VLOOKUP(N48,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P48    | =ROUND(IF(L48="Y",0,IF(OR(RECONCILIATION!O48='Data Parameters'!$D$184,RECONCILIATION!O48='Data Parameters'!$D$185),0,IF(RECONCILIATION!G48="ON",RECONCILIATION!E48/(1+RECONCILIATION!J48)*0.13,IF(H48="N",0,IF(RECONCILIATION!I48="Y",RECONCILIATION!E48/(1+RECONCILIATION!J48+RECONCILIATION!K48)*RECONCILIATION!J48,RECONCILIATION!E48/(1+RECONCILIATION!J48)*RECONCILIATION!J48))))),2)                |
| Q48    | =E48-P48                                                                                                                                                                                                                                                                                                                                                                                                  |
| R48    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbcf8e0>                                                                                                                                                                                                                                                                                                                                           |
| E49    | =IF(C49>0,C49,-D49)                                                                                                                                                                                                                                                                                                                                                                                       |
| J49    | =IF(G49="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K49    | =IF(G49="ON",0,VLOOKUP(G49,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O49    | =IF(L49="Y","POSTED",VLOOKUP(N49,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P49    | =ROUND(IF(L49="Y",0,IF(OR(RECONCILIATION!O49='Data Parameters'!$D$184,RECONCILIATION!O49='Data Parameters'!$D$185),0,IF(RECONCILIATION!G49="ON",RECONCILIATION!E49/(1+RECONCILIATION!J49)*0.13,IF(H49="N",0,IF(RECONCILIATION!I49="Y",RECONCILIATION!E49/(1+RECONCILIATION!J49+RECONCILIATION!K49)*RECONCILIATION!J49,RECONCILIATION!E49/(1+RECONCILIATION!J49)*RECONCILIATION!J49))))),2)                |
| Q49    | =E49-P49                                                                                                                                                                                                                                                                                                                                                                                                  |
| R49    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1391d2d30>                                                                                                                                                                                                                                                                                                                                           |
| E50    | =IF(C50>0,C50,-D50)                                                                                                                                                                                                                                                                                                                                                                                       |
| J50    | =IF(G50="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K50    | =IF(G50="ON",0,VLOOKUP(G50,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O50    | =IF(L50="Y","POSTED",VLOOKUP(N50,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P50    | =ROUND(IF(L50="Y",0,IF(OR(RECONCILIATION!O50='Data Parameters'!$D$184,RECONCILIATION!O50='Data Parameters'!$D$185),0,IF(RECONCILIATION!G50="ON",RECONCILIATION!E50/(1+RECONCILIATION!J50)*0.13,IF(H50="N",0,IF(RECONCILIATION!I50="Y",RECONCILIATION!E50/(1+RECONCILIATION!J50+RECONCILIATION!K50)*RECONCILIATION!J50,RECONCILIATION!E50/(1+RECONCILIATION!J50)*RECONCILIATION!J50))))),2)                |
| Q50    | =E50-P50                                                                                                                                                                                                                                                                                                                                                                                                  |
| R50    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13eaec2b0>                                                                                                                                                                                                                                                                                                                                           |
| E51    | =IF(C51>0,C51,-D51)                                                                                                                                                                                                                                                                                                                                                                                       |
| J51    | =IF(G51="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K51    | =IF(G51="ON",0,VLOOKUP(G51,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O51    | =IF(L51="Y","POSTED",VLOOKUP(N51,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P51    | =ROUND(IF(L51="Y",0,IF(OR(RECONCILIATION!O51='Data Parameters'!$D$184,RECONCILIATION!O51='Data Parameters'!$D$185),0,IF(RECONCILIATION!G51="ON",RECONCILIATION!E51/(1+RECONCILIATION!J51)*0.13,IF(H51="N",0,IF(RECONCILIATION!I51="Y",RECONCILIATION!E51/(1+RECONCILIATION!J51+RECONCILIATION!K51)*RECONCILIATION!J51,RECONCILIATION!E51/(1+RECONCILIATION!J51)*RECONCILIATION!J51))))),2)                |
| Q51    | =E51-P51                                                                                                                                                                                                                                                                                                                                                                                                  |
| R51    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613b50>                                                                                                                                                                                                                                                                                                                                           |
| E52    | =IF(C52>0,C52,-D52)                                                                                                                                                                                                                                                                                                                                                                                       |
| J52    | =IF(G52="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K52    | =IF(G52="ON",0,VLOOKUP(G52,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O52    | =IF(L52="Y","POSTED",VLOOKUP(N52,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P52    | =ROUND(IF(L52="Y",0,IF(OR(RECONCILIATION!O52='Data Parameters'!$D$184,RECONCILIATION!O52='Data Parameters'!$D$185),0,IF(RECONCILIATION!G52="ON",RECONCILIATION!E52/(1+RECONCILIATION!J52)*0.13,IF(H52="N",0,IF(RECONCILIATION!I52="Y",RECONCILIATION!E52/(1+RECONCILIATION!J52+RECONCILIATION!K52)*RECONCILIATION!J52,RECONCILIATION!E52/(1+RECONCILIATION!J52)*RECONCILIATION!J52))))),2)                |
| Q52    | =E52-P52                                                                                                                                                                                                                                                                                                                                                                                                  |
| R52    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc14be0>                                                                                                                                                                                                                                                                                                                                           |
| E53    | =IF(C53>0,C53,-D53)                                                                                                                                                                                                                                                                                                                                                                                       |
| J53    | =IF(G53="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K53    | =IF(G53="ON",0,VLOOKUP(G53,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O53    | =IF(L53="Y","POSTED",VLOOKUP(N53,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P53    | =ROUND(IF(L53="Y",0,IF(OR(RECONCILIATION!O53='Data Parameters'!$D$184,RECONCILIATION!O53='Data Parameters'!$D$185),0,IF(RECONCILIATION!G53="ON",RECONCILIATION!E53/(1+RECONCILIATION!J53)*0.13,IF(H53="N",0,IF(RECONCILIATION!I53="Y",RECONCILIATION!E53/(1+RECONCILIATION!J53+RECONCILIATION!K53)*RECONCILIATION!J53,RECONCILIATION!E53/(1+RECONCILIATION!J53)*RECONCILIATION!J53))))),2)                |
| Q53    | =E53-P53                                                                                                                                                                                                                                                                                                                                                                                                  |
| R53    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbd76a0>                                                                                                                                                                                                                                                                                                                                           |
| E54    | =IF(C54>0,C54,-D54)                                                                                                                                                                                                                                                                                                                                                                                       |
| J54    | =IF(G54="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K54    | =IF(G54="ON",0,VLOOKUP(G54,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O54    | =IF(L54="Y","POSTED",VLOOKUP(N54,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P54    | =ROUND(IF(L54="Y",0,IF(OR(RECONCILIATION!O54='Data Parameters'!$D$184,RECONCILIATION!O54='Data Parameters'!$D$185),0,IF(RECONCILIATION!G54="ON",RECONCILIATION!E54/(1+RECONCILIATION!J54)*0.13,IF(H54="N",0,IF(RECONCILIATION!I54="Y",RECONCILIATION!E54/(1+RECONCILIATION!J54+RECONCILIATION!K54)*RECONCILIATION!J54,RECONCILIATION!E54/(1+RECONCILIATION!J54)*RECONCILIATION!J54))))),2)                |
| Q54    | =E54-P54                                                                                                                                                                                                                                                                                                                                                                                                  |
| R54    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbd7670>                                                                                                                                                                                                                                                                                                                                           |
| E55    | =IF(C55>0,C55,-D55)                                                                                                                                                                                                                                                                                                                                                                                       |
| J55    | =IF(G55="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K55    | =IF(G55="ON",0,VLOOKUP(G55,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O55    | =IF(L55="Y","POSTED",VLOOKUP(N55,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P55    | =ROUND(IF(L55="Y",0,IF(OR(RECONCILIATION!O55='Data Parameters'!$D$184,RECONCILIATION!O55='Data Parameters'!$D$185),0,IF(RECONCILIATION!G55="ON",RECONCILIATION!E55/(1+RECONCILIATION!J55)*0.13,IF(H55="N",0,IF(RECONCILIATION!I55="Y",RECONCILIATION!E55/(1+RECONCILIATION!J55+RECONCILIATION!K55)*RECONCILIATION!J55,RECONCILIATION!E55/(1+RECONCILIATION!J55)*RECONCILIATION!J55))))),2)                |
| Q55    | =E55-P55                                                                                                                                                                                                                                                                                                                                                                                                  |
| R55    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fd1f220>                                                                                                                                                                                                                                                                                                                                           |
| E56    | =IF(C56>0,C56,-D56)                                                                                                                                                                                                                                                                                                                                                                                       |
| J56    | =IF(G56="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K56    | =IF(G56="ON",0,VLOOKUP(G56,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O56    | =IF(L56="Y","POSTED",VLOOKUP(N56,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P56    | =ROUND(IF(L56="Y",0,IF(OR(RECONCILIATION!O56='Data Parameters'!$D$184,RECONCILIATION!O56='Data Parameters'!$D$185),0,IF(RECONCILIATION!G56="ON",RECONCILIATION!E56/(1+RECONCILIATION!J56)*0.13,IF(H56="N",0,IF(RECONCILIATION!I56="Y",RECONCILIATION!E56/(1+RECONCILIATION!J56+RECONCILIATION!K56)*RECONCILIATION!J56,RECONCILIATION!E56/(1+RECONCILIATION!J56)*RECONCILIATION!J56))))),2)                |
| Q56    | =E56-P56                                                                                                                                                                                                                                                                                                                                                                                                  |
| R56    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc145b0>                                                                                                                                                                                                                                                                                                                                           |
| E57    | =IF(C57>0,C57,-D57)                                                                                                                                                                                                                                                                                                                                                                                       |
| J57    | =IF(G57="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K57    | =IF(G57="ON",0,VLOOKUP(G57,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O57    | =IF(L57="Y","POSTED",VLOOKUP(N57,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P57    | =ROUND(IF(L57="Y",0,IF(OR(RECONCILIATION!O57='Data Parameters'!$D$184,RECONCILIATION!O57='Data Parameters'!$D$185),0,IF(RECONCILIATION!G57="ON",RECONCILIATION!E57/(1+RECONCILIATION!J57)*0.13,IF(H57="N",0,IF(RECONCILIATION!I57="Y",RECONCILIATION!E57/(1+RECONCILIATION!J57+RECONCILIATION!K57)*RECONCILIATION!J57,RECONCILIATION!E57/(1+RECONCILIATION!J57)*RECONCILIATION!J57))))),2)                |
| Q57    | =E57-P57                                                                                                                                                                                                                                                                                                                                                                                                  |
| R57    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc14670>                                                                                                                                                                                                                                                                                                                                           |
| E58    | =IF(C58>0,C58,-D58)                                                                                                                                                                                                                                                                                                                                                                                       |
| J58    | =IF(G58="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K58    | =IF(G58="ON",0,VLOOKUP(G58,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O58    | =IF(L58="Y","POSTED",VLOOKUP(N58,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P58    | =ROUND(IF(L58="Y",0,IF(OR(RECONCILIATION!O58='Data Parameters'!$D$184,RECONCILIATION!O58='Data Parameters'!$D$185),0,IF(RECONCILIATION!G58="ON",RECONCILIATION!E58/(1+RECONCILIATION!J58)*0.13,IF(H58="N",0,IF(RECONCILIATION!I58="Y",RECONCILIATION!E58/(1+RECONCILIATION!J58+RECONCILIATION!K58)*RECONCILIATION!J58,RECONCILIATION!E58/(1+RECONCILIATION!J58)*RECONCILIATION!J58))))),2)                |
| Q58    | =E58-P58                                                                                                                                                                                                                                                                                                                                                                                                  |
| R58    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbd7c70>                                                                                                                                                                                                                                                                                                                                           |
| E59    | =IF(C59>0,C59,-D59)                                                                                                                                                                                                                                                                                                                                                                                       |
| J59    | =IF(G59="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K59    | =IF(G59="ON",0,VLOOKUP(G59,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O59    | =IF(L59="Y","POSTED",VLOOKUP(N59,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P59    | =ROUND(IF(L59="Y",0,IF(OR(RECONCILIATION!O59='Data Parameters'!$D$184,RECONCILIATION!O59='Data Parameters'!$D$185),0,IF(RECONCILIATION!G59="ON",RECONCILIATION!E59/(1+RECONCILIATION!J59)*0.13,IF(H59="N",0,IF(RECONCILIATION!I59="Y",RECONCILIATION!E59/(1+RECONCILIATION!J59+RECONCILIATION!K59)*RECONCILIATION!J59,RECONCILIATION!E59/(1+RECONCILIATION!J59)*RECONCILIATION!J59))))),2)                |
| Q59    | =E59-P59                                                                                                                                                                                                                                                                                                                                                                                                  |
| R59    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613550>                                                                                                                                                                                                                                                                                                                                           |
| E60    | =IF(C60>0,C60,-D60)                                                                                                                                                                                                                                                                                                                                                                                       |
| J60    | =IF(G60="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K60    | =IF(G60="ON",0,VLOOKUP(G60,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O60    | =IF(L60="Y","POSTED",VLOOKUP(N60,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P60    | =ROUND(IF(L60="Y",0,IF(OR(RECONCILIATION!O60='Data Parameters'!$D$184,RECONCILIATION!O60='Data Parameters'!$D$185),0,IF(RECONCILIATION!G60="ON",RECONCILIATION!E60/(1+RECONCILIATION!J60)*0.13,IF(H60="N",0,IF(RECONCILIATION!I60="Y",RECONCILIATION!E60/(1+RECONCILIATION!J60+RECONCILIATION!K60)*RECONCILIATION!J60,RECONCILIATION!E60/(1+RECONCILIATION!J60)*RECONCILIATION!J60))))),2)                |
| Q60    | =E60-P60                                                                                                                                                                                                                                                                                                                                                                                                  |
| R60    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ea8dfd0>                                                                                                                                                                                                                                                                                                                                           |
| E61    | =IF(C61>0,C61,-D61)                                                                                                                                                                                                                                                                                                                                                                                       |
| J61    | =IF(G61="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K61    | =IF(G61="ON",0,VLOOKUP(G61,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O61    | =IF(L61="Y","POSTED",VLOOKUP(N61,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P61    | =ROUND(IF(L61="Y",0,IF(OR(RECONCILIATION!O61='Data Parameters'!$D$184,RECONCILIATION!O61='Data Parameters'!$D$185),0,IF(RECONCILIATION!G61="ON",RECONCILIATION!E61/(1+RECONCILIATION!J61)*0.13,IF(H61="N",0,IF(RECONCILIATION!I61="Y",RECONCILIATION!E61/(1+RECONCILIATION!J61+RECONCILIATION!K61)*RECONCILIATION!J61,RECONCILIATION!E61/(1+RECONCILIATION!J61)*RECONCILIATION!J61))))),2)                |
| Q61    | =E61-P61                                                                                                                                                                                                                                                                                                                                                                                                  |
| R61    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fdb2100>                                                                                                                                                                                                                                                                                                                                           |
| E62    | =IF(C62>0,C62,-D62)                                                                                                                                                                                                                                                                                                                                                                                       |
| J62    | =IF(G62="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K62    | =IF(G62="ON",0,VLOOKUP(G62,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O62    | =IF(L62="Y","POSTED",VLOOKUP(N62,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P62    | =ROUND(IF(L62="Y",0,IF(OR(RECONCILIATION!O62='Data Parameters'!$D$184,RECONCILIATION!O62='Data Parameters'!$D$185),0,IF(RECONCILIATION!G62="ON",RECONCILIATION!E62/(1+RECONCILIATION!J62)*0.13,IF(H62="N",0,IF(RECONCILIATION!I62="Y",RECONCILIATION!E62/(1+RECONCILIATION!J62+RECONCILIATION!K62)*RECONCILIATION!J62,RECONCILIATION!E62/(1+RECONCILIATION!J62)*RECONCILIATION!J62))))),2)                |
| Q62    | =E62-P62                                                                                                                                                                                                                                                                                                                                                                                                  |
| R62    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc14af0>                                                                                                                                                                                                                                                                                                                                           |
| E63    | =IF(C63>0,C63,-D63)                                                                                                                                                                                                                                                                                                                                                                                       |
| J63    | =IF(G63="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K63    | =IF(G63="ON",0,VLOOKUP(G63,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O63    | =IF(L63="Y","POSTED",VLOOKUP(N63,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P63    | =ROUND(IF(L63="Y",0,IF(OR(RECONCILIATION!O63='Data Parameters'!$D$184,RECONCILIATION!O63='Data Parameters'!$D$185),0,IF(RECONCILIATION!G63="ON",RECONCILIATION!E63/(1+RECONCILIATION!J63)*0.13,IF(H63="N",0,IF(RECONCILIATION!I63="Y",RECONCILIATION!E63/(1+RECONCILIATION!J63+RECONCILIATION!K63)*RECONCILIATION!J63,RECONCILIATION!E63/(1+RECONCILIATION!J63)*RECONCILIATION!J63))))),2)                |
| Q63    | =E63-P63                                                                                                                                                                                                                                                                                                                                                                                                  |
| R63    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc14190>                                                                                                                                                                                                                                                                                                                                           |
| E64    | =IF(C64>0,C64,-D64)                                                                                                                                                                                                                                                                                                                                                                                       |
| J64    | =IF(G64="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K64    | =IF(G64="ON",0,VLOOKUP(G64,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O64    | =IF(L64="Y","POSTED",VLOOKUP(N64,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P64    | =ROUND(IF(L64="Y",0,IF(OR(RECONCILIATION!O64='Data Parameters'!$D$184,RECONCILIATION!O64='Data Parameters'!$D$185),0,IF(RECONCILIATION!G64="ON",RECONCILIATION!E64/(1+RECONCILIATION!J64)*0.13,IF(H64="N",0,IF(RECONCILIATION!I64="Y",RECONCILIATION!E64/(1+RECONCILIATION!J64+RECONCILIATION!K64)*RECONCILIATION!J64,RECONCILIATION!E64/(1+RECONCILIATION!J64)*RECONCILIATION!J64))))),2)                |
| Q64    | =E64-P64                                                                                                                                                                                                                                                                                                                                                                                                  |
| R64    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1395c13d0>                                                                                                                                                                                                                                                                                                                                           |
| E65    | =IF(C65>0,C65,-D65)                                                                                                                                                                                                                                                                                                                                                                                       |
| J65    | =IF(G65="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K65    | =IF(G65="ON",0,VLOOKUP(G65,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O65    | =IF(L65="Y","POSTED",VLOOKUP(N65,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P65    | =ROUND(IF(L65="Y",0,IF(OR(RECONCILIATION!O65='Data Parameters'!$D$184,RECONCILIATION!O65='Data Parameters'!$D$185),0,IF(RECONCILIATION!G65="ON",RECONCILIATION!E65/(1+RECONCILIATION!J65)*0.13,IF(H65="N",0,IF(RECONCILIATION!I65="Y",RECONCILIATION!E65/(1+RECONCILIATION!J65+RECONCILIATION!K65)*RECONCILIATION!J65,RECONCILIATION!E65/(1+RECONCILIATION!J65)*RECONCILIATION!J65))))),2)                |
| Q65    | =E65-P65                                                                                                                                                                                                                                                                                                                                                                                                  |
| R65    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc14fd0>                                                                                                                                                                                                                                                                                                                                           |
| E66    | =IF(C66>0,C66,-D66)                                                                                                                                                                                                                                                                                                                                                                                       |
| J66    | =IF(G66="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K66    | =IF(G66="ON",0,VLOOKUP(G66,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O66    | =IF(L66="Y","POSTED",VLOOKUP(N66,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P66    | =ROUND(IF(L66="Y",0,IF(OR(RECONCILIATION!O66='Data Parameters'!$D$184,RECONCILIATION!O66='Data Parameters'!$D$185),0,IF(RECONCILIATION!G66="ON",RECONCILIATION!E66/(1+RECONCILIATION!J66)*0.13,IF(H66="N",0,IF(RECONCILIATION!I66="Y",RECONCILIATION!E66/(1+RECONCILIATION!J66+RECONCILIATION!K66)*RECONCILIATION!J66,RECONCILIATION!E66/(1+RECONCILIATION!J66)*RECONCILIATION!J66))))),2)                |
| Q66    | =E66-P66                                                                                                                                                                                                                                                                                                                                                                                                  |
| R66    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fca3dc0>                                                                                                                                                                                                                                                                                                                                           |
| E67    | =IF(C67>0,C67,-D67)                                                                                                                                                                                                                                                                                                                                                                                       |
| J67    | =IF(G67="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K67    | =IF(G67="ON",0,VLOOKUP(G67,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O67    | =IF(L67="Y","POSTED",VLOOKUP(N67,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P67    | =ROUND(IF(L67="Y",0,IF(OR(RECONCILIATION!O67='Data Parameters'!$D$184,RECONCILIATION!O67='Data Parameters'!$D$185),0,IF(RECONCILIATION!G67="ON",RECONCILIATION!E67/(1+RECONCILIATION!J67)*0.13,IF(H67="N",0,IF(RECONCILIATION!I67="Y",RECONCILIATION!E67/(1+RECONCILIATION!J67+RECONCILIATION!K67)*RECONCILIATION!J67,RECONCILIATION!E67/(1+RECONCILIATION!J67)*RECONCILIATION!J67))))),2)                |
| Q67    | =E67-P67                                                                                                                                                                                                                                                                                                                                                                                                  |
| R67    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fca30d0>                                                                                                                                                                                                                                                                                                                                           |
| E68    | =IF(C68>0,C68,-D68)                                                                                                                                                                                                                                                                                                                                                                                       |
| J68    | =IF(G68="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K68    | =IF(G68="ON",0,VLOOKUP(G68,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O68    | =IF(L68="Y","POSTED",VLOOKUP(N68,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P68    | =ROUND(IF(L68="Y",0,IF(OR(RECONCILIATION!O68='Data Parameters'!$D$184,RECONCILIATION!O68='Data Parameters'!$D$185),0,IF(RECONCILIATION!G68="ON",RECONCILIATION!E68/(1+RECONCILIATION!J68)*0.13,IF(H68="N",0,IF(RECONCILIATION!I68="Y",RECONCILIATION!E68/(1+RECONCILIATION!J68+RECONCILIATION!K68)*RECONCILIATION!J68,RECONCILIATION!E68/(1+RECONCILIATION!J68)*RECONCILIATION!J68))))),2)                |
| Q68    | =E68-P68                                                                                                                                                                                                                                                                                                                                                                                                  |
| R68    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fc148b0>                                                                                                                                                                                                                                                                                                                                           |
| E69    | =IF(C69>0,C69,-D69)                                                                                                                                                                                                                                                                                                                                                                                       |
| J69    | =IF(G69="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K69    | =IF(G69="ON",0,VLOOKUP(G69,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O69    | =IF(L69="Y","POSTED",VLOOKUP(N69,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P69    | =ROUND(IF(L69="Y",0,IF(OR(RECONCILIATION!O69='Data Parameters'!$D$184,RECONCILIATION!O69='Data Parameters'!$D$185),0,IF(RECONCILIATION!G69="ON",RECONCILIATION!E69/(1+RECONCILIATION!J69)*0.13,IF(H69="N",0,IF(RECONCILIATION!I69="Y",RECONCILIATION!E69/(1+RECONCILIATION!J69+RECONCILIATION!K69)*RECONCILIATION!J69,RECONCILIATION!E69/(1+RECONCILIATION!J69)*RECONCILIATION!J69))))),2)                |
| Q69    | =E69-P69                                                                                                                                                                                                                                                                                                                                                                                                  |
| R69    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fca3610>                                                                                                                                                                                                                                                                                                                                           |
| E70    | =IF(C70>0,C70,-D70)                                                                                                                                                                                                                                                                                                                                                                                       |
| J70    | =IF(G70="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K70    | =IF(G70="ON",0,VLOOKUP(G70,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O70    | =IF(L70="Y","POSTED",VLOOKUP(N70,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P70    | =ROUND(IF(L70="Y",0,IF(OR(RECONCILIATION!O70='Data Parameters'!$D$184,RECONCILIATION!O70='Data Parameters'!$D$185),0,IF(RECONCILIATION!G70="ON",RECONCILIATION!E70/(1+RECONCILIATION!J70)*0.13,IF(H70="N",0,IF(RECONCILIATION!I70="Y",RECONCILIATION!E70/(1+RECONCILIATION!J70+RECONCILIATION!K70)*RECONCILIATION!J70,RECONCILIATION!E70/(1+RECONCILIATION!J70)*RECONCILIATION!J70))))),2)                |
| Q70    | =E70-P70                                                                                                                                                                                                                                                                                                                                                                                                  |
| R70    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ea96760>                                                                                                                                                                                                                                                                                                                                           |
| E71    | =IF(C71>0,C71,-D71)                                                                                                                                                                                                                                                                                                                                                                                       |
| J71    | =IF(G71="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K71    | =IF(G71="ON",0,VLOOKUP(G71,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O71    | =IF(L71="Y","POSTED",VLOOKUP(N71,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P71    | =ROUND(IF(L71="Y",0,IF(OR(RECONCILIATION!O71='Data Parameters'!$D$184,RECONCILIATION!O71='Data Parameters'!$D$185),0,IF(RECONCILIATION!G71="ON",RECONCILIATION!E71/(1+RECONCILIATION!J71)*0.13,IF(H71="N",0,IF(RECONCILIATION!I71="Y",RECONCILIATION!E71/(1+RECONCILIATION!J71+RECONCILIATION!K71)*RECONCILIATION!J71,RECONCILIATION!E71/(1+RECONCILIATION!J71)*RECONCILIATION!J71))))),2)                |
| Q71    | =E71-P71                                                                                                                                                                                                                                                                                                                                                                                                  |
| R71    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d98b0>                                                                                                                                                                                                                                                                                                                                           |
| E72    | =IF(C72>0,C72,-D72)                                                                                                                                                                                                                                                                                                                                                                                       |
| J72    | =IF(G72="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K72    | =IF(G72="ON",0,VLOOKUP(G72,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O72    | =IF(L72="Y","POSTED",VLOOKUP(N72,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P72    | =ROUND(IF(L72="Y",0,IF(OR(RECONCILIATION!O72='Data Parameters'!$D$184,RECONCILIATION!O72='Data Parameters'!$D$185),0,IF(RECONCILIATION!G72="ON",RECONCILIATION!E72/(1+RECONCILIATION!J72)*0.13,IF(H72="N",0,IF(RECONCILIATION!I72="Y",RECONCILIATION!E72/(1+RECONCILIATION!J72+RECONCILIATION!K72)*RECONCILIATION!J72,RECONCILIATION!E72/(1+RECONCILIATION!J72)*RECONCILIATION!J72))))),2)                |
| Q72    | =E72-P72                                                                                                                                                                                                                                                                                                                                                                                                  |
| R72    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fbdd5b0>                                                                                                                                                                                                                                                                                                                                           |
| E73    | =IF(C73>0,C73,-D73)                                                                                                                                                                                                                                                                                                                                                                                       |
| J73    | =IF(G73="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K73    | =IF(G73="ON",0,VLOOKUP(G73,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O73    | =IF(L73="Y","POSTED",VLOOKUP(N73,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P73    | =ROUND(IF(L73="Y",0,IF(OR(RECONCILIATION!O73='Data Parameters'!$D$184,RECONCILIATION!O73='Data Parameters'!$D$185),0,IF(RECONCILIATION!G73="ON",RECONCILIATION!E73/(1+RECONCILIATION!J73)*0.13,IF(H73="N",0,IF(RECONCILIATION!I73="Y",RECONCILIATION!E73/(1+RECONCILIATION!J73+RECONCILIATION!K73)*RECONCILIATION!J73,RECONCILIATION!E73/(1+RECONCILIATION!J73)*RECONCILIATION!J73))))),2)                |
| Q73    | =E73-P73                                                                                                                                                                                                                                                                                                                                                                                                  |
| R73    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13fd1f370>                                                                                                                                                                                                                                                                                                                                           |
| E74    | =IF(C74>0,C74,-D74)                                                                                                                                                                                                                                                                                                                                                                                       |
| J74    | =IF(G74="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K74    | =IF(G74="ON",0,VLOOKUP(G74,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O74    | =IF(L74="Y","POSTED",VLOOKUP(N74,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| Q74    | =E74-P74                                                                                                                                                                                                                                                                                                                                                                                                  |
| R74    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613ac0>                                                                                                                                                                                                                                                                                                                                           |
| E75    | =IF(C75>0,C75,-D75)                                                                                                                                                                                                                                                                                                                                                                                       |
| J75    | =IF(G75="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K75    | =IF(G75="ON",0,VLOOKUP(G75,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O75    | =IF(L75="Y","POSTED",VLOOKUP(N75,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P75    | =ROUND(IF(L75="Y",0,IF(OR(RECONCILIATION!O75='Data Parameters'!$D$184,RECONCILIATION!O75='Data Parameters'!$D$185),0,IF(RECONCILIATION!G75="ON",RECONCILIATION!E75/(1+RECONCILIATION!J75)*0.13,IF(H75="N",0,IF(RECONCILIATION!I75="Y",RECONCILIATION!E75/(1+RECONCILIATION!J75+RECONCILIATION!K75)*RECONCILIATION!J75,RECONCILIATION!E75/(1+RECONCILIATION!J75)*RECONCILIATION!J75))))),2)                |
| Q75    | =E75-P75                                                                                                                                                                                                                                                                                                                                                                                                  |
| R75    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f6153a0>                                                                                                                                                                                                                                                                                                                                           |
| E76    | =IF(C76>0,C76,-D76)                                                                                                                                                                                                                                                                                                                                                                                       |
| J76    | =IF(G76="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K76    | =IF(G76="ON",0,VLOOKUP(G76,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O76    | =IF(L76="Y","POSTED",VLOOKUP(N76,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P76    | =ROUND(IF(L76="Y",0,IF(OR(RECONCILIATION!O76='Data Parameters'!$D$184,RECONCILIATION!O76='Data Parameters'!$D$185),0,IF(RECONCILIATION!G76="ON",RECONCILIATION!E76/(1+RECONCILIATION!J76)*0.13,IF(H76="N",0,IF(RECONCILIATION!I76="Y",RECONCILIATION!E76/(1+RECONCILIATION!J76+RECONCILIATION!K76)*RECONCILIATION!J76,RECONCILIATION!E76/(1+RECONCILIATION!J76)*RECONCILIATION!J76))))),2)                |
| Q76    | =E76-P76                                                                                                                                                                                                                                                                                                                                                                                                  |
| R76    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f6f1a60>                                                                                                                                                                                                                                                                                                                                           |
| E77    | =IF(C77>0,C77,-D77)                                                                                                                                                                                                                                                                                                                                                                                       |
| J77    | =IF(G77="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K77    | =IF(G77="ON",0,VLOOKUP(G77,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O77    | =IF(L77="Y","POSTED",VLOOKUP(N77,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P77    | =ROUND(IF(L77="Y",0,IF(OR(RECONCILIATION!O77='Data Parameters'!$D$184,RECONCILIATION!O77='Data Parameters'!$D$185),0,IF(RECONCILIATION!G77="ON",RECONCILIATION!E77/(1+RECONCILIATION!J77)*0.13,IF(H77="N",0,IF(RECONCILIATION!I77="Y",RECONCILIATION!E77/(1+RECONCILIATION!J77+RECONCILIATION!K77)*RECONCILIATION!J77,RECONCILIATION!E77/(1+RECONCILIATION!J77)*RECONCILIATION!J77))))),2)                |
| Q77    | =E77-P77                                                                                                                                                                                                                                                                                                                                                                                                  |
| R77    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d95e0>                                                                                                                                                                                                                                                                                                                                           |
| E78    | =IF(C78>0,C78,-D78)                                                                                                                                                                                                                                                                                                                                                                                       |
| J78    | =IF(G78="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K78    | =IF(G78="ON",0,VLOOKUP(G78,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O78    | =IF(L78="Y","POSTED",VLOOKUP(N78,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P78    | =ROUND(IF(L78="Y",0,IF(OR(RECONCILIATION!O78='Data Parameters'!$D$184,RECONCILIATION!O78='Data Parameters'!$D$185),0,IF(RECONCILIATION!G78="ON",RECONCILIATION!E78/(1+RECONCILIATION!J78)*0.13,IF(H78="N",0,IF(RECONCILIATION!I78="Y",RECONCILIATION!E78/(1+RECONCILIATION!J78+RECONCILIATION!K78)*RECONCILIATION!J78,RECONCILIATION!E78/(1+RECONCILIATION!J78)*RECONCILIATION!J78))))),2)                |
| Q78    | =E78-P78                                                                                                                                                                                                                                                                                                                                                                                                  |
| R78    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f613af0>                                                                                                                                                                                                                                                                                                                                           |
| E79    | =IF(C79>0,C79,-D79)                                                                                                                                                                                                                                                                                                                                                                                       |
| J79    | =IF(G79="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K79    | =IF(G79="ON",0,VLOOKUP(G79,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O79    | =IF(L79="Y","POSTED",VLOOKUP(N79,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P79    | =ROUND(IF(L79="Y",0,IF(OR(RECONCILIATION!O79='Data Parameters'!$D$184,RECONCILIATION!O79='Data Parameters'!$D$185),0,IF(RECONCILIATION!G79="ON",RECONCILIATION!E79/(1+RECONCILIATION!J79)*0.13,IF(H79="N",0,IF(RECONCILIATION!I79="Y",RECONCILIATION!E79/(1+RECONCILIATION!J79+RECONCILIATION!K79)*RECONCILIATION!J79,RECONCILIATION!E79/(1+RECONCILIATION!J79)*RECONCILIATION!J79))))),2)                |
| Q79    | =E79-P79                                                                                                                                                                                                                                                                                                                                                                                                  |
| R79    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13ea8d2e0>                                                                                                                                                                                                                                                                                                                                           |
| E80    | =IF(C80>0,C80,-D80)                                                                                                                                                                                                                                                                                                                                                                                       |
| J80    | =IF(G80="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K80    | =IF(G80="ON",0,VLOOKUP(G80,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O80    | =IF(L80="Y","POSTED",VLOOKUP(N80,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P80    | =ROUND(IF(L80="Y",0,IF(OR(RECONCILIATION!O80='Data Parameters'!$D$184,RECONCILIATION!O80='Data Parameters'!$D$185),0,IF(RECONCILIATION!G80="ON",RECONCILIATION!E80/(1+RECONCILIATION!J80)*0.13,IF(H80="N",0,IF(RECONCILIATION!I80="Y",RECONCILIATION!E80/(1+RECONCILIATION!J80+RECONCILIATION!K80)*RECONCILIATION!J80,RECONCILIATION!E80/(1+RECONCILIATION!J80)*RECONCILIATION!J80))))),2)                |
| Q80    | =E80-P80                                                                                                                                                                                                                                                                                                                                                                                                  |
| R80    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9970>                                                                                                                                                                                                                                                                                                                                           |
| E81    | =IF(C81>0,C81,-D81)                                                                                                                                                                                                                                                                                                                                                                                       |
| J81    | =IF(G81="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K81    | =IF(G81="ON",0,VLOOKUP(G81,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O81    | =IF(L81="Y","POSTED",VLOOKUP(N81,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| Q81    | =E81-P81                                                                                                                                                                                                                                                                                                                                                                                                  |
| R81    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9550>                                                                                                                                                                                                                                                                                                                                           |
| E82    | =IF(C82>0,C82,-D82)                                                                                                                                                                                                                                                                                                                                                                                       |
| J82    | =IF(G82="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K82    | =IF(G82="ON",0,VLOOKUP(G82,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O82    | =IF(L82="Y","POSTED",VLOOKUP(N82,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| Q82    | =E82-P82                                                                                                                                                                                                                                                                                                                                                                                                  |
| R82    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13971fe80>                                                                                                                                                                                                                                                                                                                                           |
| E83    | =IF(C83>0,C83,-D83)                                                                                                                                                                                                                                                                                                                                                                                       |
| J83    | =IF(G83="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K83    | =IF(G83="ON",0,VLOOKUP(G83,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O83    | =IF(L83="Y","POSTED",VLOOKUP(N83,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P83    | =ROUND(IF(L83="Y",0,IF(OR(RECONCILIATION!O83='Data Parameters'!$D$184,RECONCILIATION!O83='Data Parameters'!$D$185),0,IF(RECONCILIATION!G83="ON",RECONCILIATION!E83/(1+RECONCILIATION!J83)*0.13,IF(H83="N",0,IF(RECONCILIATION!I83="Y",RECONCILIATION!E83/(1+RECONCILIATION!J83+RECONCILIATION!K83)*RECONCILIATION!J83,RECONCILIATION!E83/(1+RECONCILIATION!J83)*RECONCILIATION!J83))))),2)                |
| Q83    | =E83-P83                                                                                                                                                                                                                                                                                                                                                                                                  |
| R83    | <openpyxl.worksheet.formula.ArrayFormula object at 0x139b007c0>                                                                                                                                                                                                                                                                                                                                           |
| E84    | =IF(C84>0,C84,-D84)                                                                                                                                                                                                                                                                                                                                                                                       |
| J84    | =IF(G84="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K84    | =IF(G84="ON",0,VLOOKUP(G84,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O84    | =IF(L84="Y","POSTED",VLOOKUP(N84,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P84    | =ROUND(IF(L84="Y",0,IF(OR(RECONCILIATION!O84='Data Parameters'!$D$184,RECONCILIATION!O84='Data Parameters'!$D$185),0,IF(RECONCILIATION!G84="ON",RECONCILIATION!E84/(1+RECONCILIATION!J84)*0.13,IF(H84="N",0,IF(RECONCILIATION!I84="Y",RECONCILIATION!E84/(1+RECONCILIATION!J84+RECONCILIATION!K84)*RECONCILIATION!J84,RECONCILIATION!E84/(1+RECONCILIATION!J84)*RECONCILIATION!J84))))),2)                |
| Q84    | =E84-P84                                                                                                                                                                                                                                                                                                                                                                                                  |
| R84    | <openpyxl.worksheet.formula.ArrayFormula object at 0x138baa8e0>                                                                                                                                                                                                                                                                                                                                           |
| E85    | =IF(C85>0,C85,-D85)                                                                                                                                                                                                                                                                                                                                                                                       |
| J85    | =IF(G85="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K85    | =IF(G85="ON",0,VLOOKUP(G85,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O85    | =IF(L85="Y","POSTED",VLOOKUP(N85,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P85    | =ROUND(IF(L85="Y",0,IF(OR(RECONCILIATION!O85='Data Parameters'!$D$184,RECONCILIATION!O85='Data Parameters'!$D$185),0,IF(RECONCILIATION!G85="ON",RECONCILIATION!E85/(1+RECONCILIATION!J85)*0.13,IF(H85="N",0,IF(RECONCILIATION!I85="Y",RECONCILIATION!E85/(1+RECONCILIATION!J85+RECONCILIATION!K85)*RECONCILIATION!J85,RECONCILIATION!E85/(1+RECONCILIATION!J85)*RECONCILIATION!J85))))),2)                |
| Q85    | =E85-P85                                                                                                                                                                                                                                                                                                                                                                                                  |
| R85    | <openpyxl.worksheet.formula.ArrayFormula object at 0x139bd6a30>                                                                                                                                                                                                                                                                                                                                           |
| E86    | =IF(C86>0,C86,-D86)                                                                                                                                                                                                                                                                                                                                                                                       |
| J86    | =IF(G86="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K86    | =IF(G86="ON",0,VLOOKUP(G86,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O86    | =IF(L86="Y","POSTED",VLOOKUP(N86,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P86    | =ROUND(IF(L86="Y",0,IF(OR(RECONCILIATION!O86='Data Parameters'!$D$184,RECONCILIATION!O86='Data Parameters'!$D$185),0,IF(RECONCILIATION!G86="ON",RECONCILIATION!E86/(1+RECONCILIATION!J86)*0.13,IF(H86="N",0,IF(RECONCILIATION!I86="Y",RECONCILIATION!E86/(1+RECONCILIATION!J86+RECONCILIATION!K86)*RECONCILIATION!J86,RECONCILIATION!E86/(1+RECONCILIATION!J86)*RECONCILIATION!J86))))),2)                |
| Q86    | =E86-P86                                                                                                                                                                                                                                                                                                                                                                                                  |
| R86    | <openpyxl.worksheet.formula.ArrayFormula object at 0x1393bec70>                                                                                                                                                                                                                                                                                                                                           |
| E87    | =IF(C87>0,C87,-D87)                                                                                                                                                                                                                                                                                                                                                                                       |
| J87    | =IF(G87="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K87    | =IF(G87="ON",0,VLOOKUP(G87,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O87    | =IF(L87="Y","POSTED",VLOOKUP(N87,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P87    | =ROUND(IF(L87="Y",0,IF(OR(RECONCILIATION!O87='Data Parameters'!$D$184,RECONCILIATION!O87='Data Parameters'!$D$185),0,IF(RECONCILIATION!G87="ON",RECONCILIATION!E87/(1+RECONCILIATION!J87)*0.13,IF(H87="N",0,IF(RECONCILIATION!I87="Y",RECONCILIATION!E87/(1+RECONCILIATION!J87+RECONCILIATION!K87)*RECONCILIATION!J87,RECONCILIATION!E87/(1+RECONCILIATION!J87)*RECONCILIATION!J87))))),2)                |
| Q87    | =E87-P87                                                                                                                                                                                                                                                                                                                                                                                                  |
| R87    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13955ce20>                                                                                                                                                                                                                                                                                                                                           |
| E88    | =IF(C88>0,C88,-D88)                                                                                                                                                                                                                                                                                                                                                                                       |
| J88    | =IF(G88="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K88    | =IF(G88="ON",0,VLOOKUP(G88,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O88    | =IF(L88="Y","POSTED",VLOOKUP(N88,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P88    | =ROUND(IF(L88="Y",0,IF(OR(RECONCILIATION!O88='Data Parameters'!$D$184,RECONCILIATION!O88='Data Parameters'!$D$185),0,IF(RECONCILIATION!G88="ON",RECONCILIATION!E88/(1+RECONCILIATION!J88)*0.13,IF(H88="N",0,IF(RECONCILIATION!I88="Y",RECONCILIATION!E88/(1+RECONCILIATION!J88+RECONCILIATION!K88)*RECONCILIATION!J88,RECONCILIATION!E88/(1+RECONCILIATION!J88)*RECONCILIATION!J88))))),2)                |
| Q88    | =E88-P88                                                                                                                                                                                                                                                                                                                                                                                                  |
| R88    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13eaec2e0>                                                                                                                                                                                                                                                                                                                                           |
| E89    | =IF(C89>0,C89,-D89)                                                                                                                                                                                                                                                                                                                                                                                       |
| J89    | =IF(G89="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K89    | =IF(G89="ON",0,VLOOKUP(G89,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O89    | =IF(L89="Y","POSTED",VLOOKUP(N89,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P89    | =ROUND(IF(L89="Y",0,IF(OR(RECONCILIATION!O89='Data Parameters'!$D$184,RECONCILIATION!O89='Data Parameters'!$D$185),0,IF(RECONCILIATION!G89="ON",RECONCILIATION!E89/(1+RECONCILIATION!J89)*0.13,IF(H89="N",0,IF(RECONCILIATION!I89="Y",RECONCILIATION!E89/(1+RECONCILIATION!J89+RECONCILIATION!K89)*RECONCILIATION!J89,RECONCILIATION!E89/(1+RECONCILIATION!J89)*RECONCILIATION!J89))))),2)                |
| Q89    | =E89-P89                                                                                                                                                                                                                                                                                                                                                                                                  |
| R89    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9a30>                                                                                                                                                                                                                                                                                                                                           |
| E90    | =IF(C90>0,C90,-D90)                                                                                                                                                                                                                                                                                                                                                                                       |
| J90    | =IF(G90="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K90    | =IF(G90="ON",0,VLOOKUP(G90,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O90    | =IF(L90="Y","POSTED",VLOOKUP(N90,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P90    | =ROUND(IF(L90="Y",0,IF(OR(RECONCILIATION!O90='Data Parameters'!$D$184,RECONCILIATION!O90='Data Parameters'!$D$185),0,IF(RECONCILIATION!G90="ON",RECONCILIATION!E90/(1+RECONCILIATION!J90)*0.13,IF(H90="N",0,IF(RECONCILIATION!I90="Y",RECONCILIATION!E90/(1+RECONCILIATION!J90+RECONCILIATION!K90)*RECONCILIATION!J90,RECONCILIATION!E90/(1+RECONCILIATION!J90)*RECONCILIATION!J90))))),2)                |
| Q90    | =E90-P90                                                                                                                                                                                                                                                                                                                                                                                                  |
| R90    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9370>                                                                                                                                                                                                                                                                                                                                           |
| E91    | =IF(C91>0,C91,-D91)                                                                                                                                                                                                                                                                                                                                                                                       |
| J91    | =IF(G91="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K91    | =IF(G91="ON",0,VLOOKUP(G91,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O91    | =IF(L91="Y","POSTED",VLOOKUP(N91,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P91    | =ROUND(IF(L91="Y",0,IF(OR(RECONCILIATION!O91='Data Parameters'!$D$184,RECONCILIATION!O91='Data Parameters'!$D$185),0,IF(RECONCILIATION!G91="ON",RECONCILIATION!E91/(1+RECONCILIATION!J91)*0.13,IF(H91="N",0,IF(RECONCILIATION!I91="Y",RECONCILIATION!E91/(1+RECONCILIATION!J91+RECONCILIATION!K91)*RECONCILIATION!J91,RECONCILIATION!E91/(1+RECONCILIATION!J91)*RECONCILIATION!J91))))),2)                |
| Q91    | =E91-P91                                                                                                                                                                                                                                                                                                                                                                                                  |
| R91    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d90a0>                                                                                                                                                                                                                                                                                                                                           |
| E92    | =IF(C92>0,C92,-D92)                                                                                                                                                                                                                                                                                                                                                                                       |
| J92    | =IF(G92="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K92    | =IF(G92="ON",0,VLOOKUP(G92,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O92    | =IF(L92="Y","POSTED",VLOOKUP(N92,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P92    | =ROUND(IF(L92="Y",0,IF(OR(RECONCILIATION!O92='Data Parameters'!$D$184,RECONCILIATION!O92='Data Parameters'!$D$185),0,IF(RECONCILIATION!G92="ON",RECONCILIATION!E92/(1+RECONCILIATION!J92)*0.13,IF(H92="N",0,IF(RECONCILIATION!I92="Y",RECONCILIATION!E92/(1+RECONCILIATION!J92+RECONCILIATION!K92)*RECONCILIATION!J92,RECONCILIATION!E92/(1+RECONCILIATION!J92)*RECONCILIATION!J92))))),2)                |
| Q92    | =E92-P92                                                                                                                                                                                                                                                                                                                                                                                                  |
| R92    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9100>                                                                                                                                                                                                                                                                                                                                           |
| E93    | =IF(C93>0,C93,-D93)                                                                                                                                                                                                                                                                                                                                                                                       |
| J93    | =IF(G93="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K93    | =IF(G93="ON",0,VLOOKUP(G93,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O93    | =IF(L93="Y","POSTED",VLOOKUP(N93,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P93    | =ROUND(IF(L93="Y",0,IF(OR(RECONCILIATION!O93='Data Parameters'!$D$184,RECONCILIATION!O93='Data Parameters'!$D$185),0,IF(RECONCILIATION!G93="ON",RECONCILIATION!E93/(1+RECONCILIATION!J93)*0.13,IF(H93="N",0,IF(RECONCILIATION!I93="Y",RECONCILIATION!E93/(1+RECONCILIATION!J93+RECONCILIATION!K93)*RECONCILIATION!J93,RECONCILIATION!E93/(1+RECONCILIATION!J93)*RECONCILIATION!J93))))),2)                |
| Q93    | =E93-P93                                                                                                                                                                                                                                                                                                                                                                                                  |
| R93    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9160>                                                                                                                                                                                                                                                                                                                                           |
| E94    | =IF(C94>0,C94,-D94)                                                                                                                                                                                                                                                                                                                                                                                       |
| J94    | =IF(G94="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K94    | =IF(G94="ON",0,VLOOKUP(G94,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O94    | =IF(L94="Y","POSTED",VLOOKUP(N94,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P94    | =ROUND(IF(L94="Y",0,IF(OR(RECONCILIATION!O94='Data Parameters'!$D$184,RECONCILIATION!O94='Data Parameters'!$D$185),0,IF(RECONCILIATION!G94="ON",RECONCILIATION!E94/(1+RECONCILIATION!J94)*0.13,IF(H94="N",0,IF(RECONCILIATION!I94="Y",RECONCILIATION!E94/(1+RECONCILIATION!J94+RECONCILIATION!K94)*RECONCILIATION!J94,RECONCILIATION!E94/(1+RECONCILIATION!J94)*RECONCILIATION!J94))))),2)                |
| Q94    | =E94-P94                                                                                                                                                                                                                                                                                                                                                                                                  |
| R94    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d93a0>                                                                                                                                                                                                                                                                                                                                           |
| E95    | =IF(C95>0,C95,-D95)                                                                                                                                                                                                                                                                                                                                                                                       |
| J95    | =IF(G95="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K95    | =IF(G95="ON",0,VLOOKUP(G95,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O95    | =IF(L95="Y","POSTED",VLOOKUP(N95,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P95    | =ROUND(IF(L95="Y",0,IF(OR(RECONCILIATION!O95='Data Parameters'!$D$184,RECONCILIATION!O95='Data Parameters'!$D$185),0,IF(RECONCILIATION!G95="ON",RECONCILIATION!E95/(1+RECONCILIATION!J95)*0.13,IF(H95="N",0,IF(RECONCILIATION!I95="Y",RECONCILIATION!E95/(1+RECONCILIATION!J95+RECONCILIATION!K95)*RECONCILIATION!J95,RECONCILIATION!E95/(1+RECONCILIATION!J95)*RECONCILIATION!J95))))),2)                |
| Q95    | =E95-P95                                                                                                                                                                                                                                                                                                                                                                                                  |
| R95    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9220>                                                                                                                                                                                                                                                                                                                                           |
| E96    | =IF(C96>0,C96,-D96)                                                                                                                                                                                                                                                                                                                                                                                       |
| J96    | =IF(G96="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K96    | =IF(G96="ON",0,VLOOKUP(G96,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O96    | =IF(L96="Y","POSTED",VLOOKUP(N96,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P96    | =ROUND(IF(L96="Y",0,IF(OR(RECONCILIATION!O96='Data Parameters'!$D$184,RECONCILIATION!O96='Data Parameters'!$D$185),0,IF(RECONCILIATION!G96="ON",RECONCILIATION!E96/(1+RECONCILIATION!J96)*0.13,IF(H96="N",0,IF(RECONCILIATION!I96="Y",RECONCILIATION!E96/(1+RECONCILIATION!J96+RECONCILIATION!K96)*RECONCILIATION!J96,RECONCILIATION!E96/(1+RECONCILIATION!J96)*RECONCILIATION!J96))))),2)                |
| Q96    | =E96-P96                                                                                                                                                                                                                                                                                                                                                                                                  |
| R96    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d94f0>                                                                                                                                                                                                                                                                                                                                           |
| E97    | =IF(C97>0,C97,-D97)                                                                                                                                                                                                                                                                                                                                                                                       |
| J97    | =IF(G97="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K97    | =IF(G97="ON",0,VLOOKUP(G97,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O97    | =IF(L97="Y","POSTED",VLOOKUP(N97,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P97    | =ROUND(IF(L97="Y",0,IF(OR(RECONCILIATION!O97='Data Parameters'!$D$184,RECONCILIATION!O97='Data Parameters'!$D$185),0,IF(RECONCILIATION!G97="ON",RECONCILIATION!E97/(1+RECONCILIATION!J97)*0.13,IF(H97="N",0,IF(RECONCILIATION!I97="Y",RECONCILIATION!E97/(1+RECONCILIATION!J97+RECONCILIATION!K97)*RECONCILIATION!J97,RECONCILIATION!E97/(1+RECONCILIATION!J97)*RECONCILIATION!J97))))),2)                |
| Q97    | =E97-P97                                                                                                                                                                                                                                                                                                                                                                                                  |
| R97    | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9340>                                                                                                                                                                                                                                                                                                                                           |
| E98    | =IF(C98>0,C98,-D98)                                                                                                                                                                                                                                                                                                                                                                                       |
| J98    | =IF(G98="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K98    | =IF(G98="ON",0,VLOOKUP(G98,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O98    | =IF(L98="Y","POSTED",VLOOKUP(N98,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P98    | =ROUND(IF(L98="Y",0,IF(OR(RECONCILIATION!O98='Data Parameters'!$D$184,RECONCILIATION!O98='Data Parameters'!$D$185),0,IF(RECONCILIATION!G98="ON",RECONCILIATION!E98/(1+RECONCILIATION!J98)*0.13,IF(H98="N",0,IF(RECONCILIATION!I98="Y",RECONCILIATION!E98/(1+RECONCILIATION!J98+RECONCILIATION!K98)*RECONCILIATION!J98,RECONCILIATION!E98/(1+RECONCILIATION!J98)*RECONCILIATION!J98))))),2)                |
| Q98    | =E98-P98                                                                                                                                                                                                                                                                                                                                                                                                  |
| R98    | <openpyxl.worksheet.formula.ArrayFormula object at 0x139623910>                                                                                                                                                                                                                                                                                                                                           |
| E99    | =IF(C99>0,C99,-D99)                                                                                                                                                                                                                                                                                                                                                                                       |
| J99    | =IF(G99="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                             |
| K99    | =IF(G99="ON",0,VLOOKUP(G99,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| O99    | =IF(L99="Y","POSTED",VLOOKUP(N99,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                          |
| P99    | =ROUND(IF(L99="Y",0,IF(OR(RECONCILIATION!O99='Data Parameters'!$D$184,RECONCILIATION!O99='Data Parameters'!$D$185),0,IF(RECONCILIATION!G99="ON",RECONCILIATION!E99/(1+RECONCILIATION!J99)*0.13,IF(H99="N",0,IF(RECONCILIATION!I99="Y",RECONCILIATION!E99/(1+RECONCILIATION!J99+RECONCILIATION!K99)*RECONCILIATION!J99,RECONCILIATION!E99/(1+RECONCILIATION!J99)*RECONCILIATION!J99))))),2)                |
| Q99    | =E99-P99                                                                                                                                                                                                                                                                                                                                                                                                  |
| R99    | <openpyxl.worksheet.formula.ArrayFormula object at 0x139bcd400>                                                                                                                                                                                                                                                                                                                                           |
| E100   | =IF(C100>0,C100,-D100)                                                                                                                                                                                                                                                                                                                                                                                    |
| J100   | =IF(G100="on",'Data Parameters'!$B$14,'Data Parameters'!$B$15)                                                                                                                                                                                                                                                                                                                                            |
| K100   | =IF(G100="ON",0,VLOOKUP(G100,'Data Parameters'!$A$12:$B$16,2,FALSE))                                                                                                                                                                                                                                                                                                                                      |
| O100   | =IF(L100="Y","POSTED",VLOOKUP(N100,'Data Parameters'!B:D,3,FALSE))                                                                                                                                                                                                                                                                                                                                        |
| P100   | =ROUND(IF(L100="Y",0,IF(OR(RECONCILIATION!O100='Data Parameters'!$D$184,RECONCILIATION!O100='Data Parameters'!$D$185),0,IF(RECONCILIATION!G100="ON",RECONCILIATION!E100/(1+RECONCILIATION!J100)*0.13,IF(H100="N",0,IF(RECONCILIATION!I100="Y",RECONCILIATION!E100/(1+RECONCILIATION!J100+RECONCILIATION!K100)*RECONCILIATION!J100,RECONCILIATION!E100/(1+RECONCILIATION!J100)*RECONCILIATION!J100))))),2) |
| Q100   | =E100-P100                                                                                                                                                                                                                                                                                                                                                                                                |
| R100   | <openpyxl.worksheet.formula.ArrayFormula object at 0x13f7d9be0>                                                                                                                                                                                                                                                                                                                                           |
Data for sheet saved as: 'RECONCILIATION.dat'

---


## Sheet: ALLOCATION
### Data:
| nan             |            |                                   |                    |         |       |         |                   |
|:----------------|:-----------|:----------------------------------|:-------------------|:--------|:------|:--------|:------------------|
|                 |            |                                   |                    |         |       |         |                   |
|                 |            |                                   |                    |         |       |         |                   |
|                 | 3774       |                                   |                    |         |       |         |                   |
| Journal Entry # |            |                                   |                    |         |       |         |                   |
|                 |            |                                   | formula            | formula |       | formula |                   |
| DIV             | GL Account | GL Account Description            | DR                 | CR      | Check |         | Needs to be $0.00 |
| 100             | 2020/      | TD Visa 4249                      |                    | 0       |       |         |                   |
| 200             | 2020/      | TD Visa 4249                      |                    | 0       |       |         |                   |
| 300             | 2020/      | TD Visa 4249                      |                    | 0       |       |         |                   |
| 100             | 2021/      | TD Visa 3774                      |                    |         |       |         |                   |
| 200             | 2021/      | TD Visa 3774                      |                    | 0       |       |         |                   |
| 300             | 2021/      | TD Visa 3774                      |                    | 0       |       |         |                   |
| 100             |            |                                   |                    | 0       |       |         |                   |
| 200             |            |                                   |                    |         |       |         |                   |
| 300             |            |                                   |                    | 0       |       |         |                   |
| 100             | 2180/      | GST/HST Paid - ITC                | 23.08              |         |       |         |                   |
| 200             | 2180/      | GST/HST Paid - ITC                | 0                  |         |       |         |                   |
| 300             | 2180/      | GST/HST Paid - ITC                | 0                  |         |       |         |                   |
| 100             | 1200/      | Prepaids                          | 0                  |         |       |         |                   |
| 100             | 1210/      | Supplier Prepayments              | 0                  |         |       |         |                   |
| 100             | 1540/      | CHB Solutions - Intercompany      | 0                  |         |       |         |                   |
| 100             | 1700/      | Equipment Inventory               | 0                  |         |       |         |                   |
| 100             | 1710/      | Parts Inventory                   | 0                  |         |       |         |                   |
| 100             | 1720/      | Inventory - Work-in-Progress      | 0                  |         |       |         |                   |
| 100             | 1730/      | Inventory - Clearing              | 0                  |         |       |         |                   |
| 100             | 1740/      | Inventory - Return Clearing       | 0                  |         |       |         |                   |
| 100             | 1750/      | Inventory - FX Adjustment         | 0                  |         |       |         |                   |
| 100             | 2300/      | Due to CHB Solutions              | 0                  |         |       |         |                   |
| 100             | 2305/      | Due to Jason & Heidi Chuback      | 0                  |         |       |         |                   |
| 100             | 2310/      | Accrued Social Committee          | 0                  |         |       |         |                   |
| 100             | 5000/      | Cost of sales - Units             | 0                  |         |       |         |                   |
| 100             | 5010/      | Cost of sales - Parts             | 0                  |         |       |         |                   |
| 100             | 5020/      | Cost of sales - Rentals           | 0                  |         |       |         |                   |
| 100             | 5030/      | Purchase discounts                | 0                  |         |       |         |                   |
| 100             | 5040/      | Freight Expense                   | 0                  |         |       |         |                   |
| 100             | 5045/      | Duty and brokerage                | 0                  |         |       |         |                   |
| 100             | 5050/      | Warranty recovery from supplier   | 0                  |         |       |         |                   |
| 100             | 5060/      | Misc. cost of sales               | 0                  |         |       |         |                   |
| 100             | 5070/      | Inventory variance                | 0                  |         |       |         |                   |
| 100             | 5200/      | Standard labour expense           | 0                  |         |       |         |                   |
| 100             | 5210/      | Standard labour variance          | 0                  |         |       |         |                   |
| 100             | 5390/      | Training - Technicians            | 0                  |         |       |         |                   |
| 100             | 5400/      | Subcontractor                     | 0                  |         |       |         |                   |
| 100             | 5410/      | Shop supplies                     | 0                  |         |       |         |                   |
| 100             | 5420/      | Mileage/travel/vehicle expense    | 0                  |         |       |         |                   |
| 100             | 5500/      | Foreign exchange (gain)/loss      | 0                  |         |       |         |                   |
| 100             | 6000/      | (Gain)/loss on disposal of assets | 0                  |         |       |         |                   |
| 100             | 6010/      | Interest revenue                  | 0                  |         |       |         |                   |
| 100             | 6020/      | Government grants                 | 0                  |         |       |         |                   |
| 100             | 7110/      | Advertising and Promotion         | 351.32             |         |       |         |                   |
| 100             | 7200/      | Donations                         | 0                  |         |       |         |                   |
| 100             | 7210/      | Dues and subscriptions            | 0                  |         |       |         |                   |
| 100             | 7250/      | Depreciation & Amortization       | 0                  |         |       |         |                   |
| 100             | 7300/      | Insurance                         | 0                  |         |       |         |                   |
| 100             | 7400/      | Licenses                          | 0                  |         |       |         |                   |
| 100             | 7500/      | Meals and Entertainment           | 2000.************* |         |       |         |                   |
| 100             | 7510/      | Golf Dues & Green Fees            | 0                  |         |       |         |                   |
| 100             | 7600/      | Office Supplies                   | 0                  |         |       |         |                   |
| 100             | 7700/      | Professional Fees - Accounting    | 0                  |         |       |         |                   |
| 100             | 7800/      | Professional Fees - Legal         | 0                  |         |       |         |                   |
| 100             | 7900/      | Safety Expense                    | 0                  |         |       |         |                   |
| 100             | 7910/      | IT Expense                        | 119.************** |         |       |         |                   |
| 100             | 7920/      | Software Expense                  | 0                  |         |       |         |                   |
| 100             | 7930/      | Telephone - Office                | 0                  |         |       |         |                   |
| 100             | 7940/      | Telephone - Cellular              | 49                 |         |       |         |                   |
| 100             | 8000/      | Travel                            | 2875.08            |         |       |         |                   |
| 100             | 8010/      | Parking                           | 0                  |         |       |         |                   |
| 100             | 8100/      | TV Cable                          | 0                  |         |       |         |                   |
| 100             | 8110/      | Internet                          | 0                  |         |       |         |                   |
| 100             | 8120/      | Electricity                       | 0                  |         |       |         |                   |
| 100             | 8130/      | Water                             | 0                  |         |       |         |                   |
| 100             | 8140/      | Natural gas                       | 0                  |         |       |         |                   |
| 100             | 8150/      | Building Repair and Maintenance   | 0                  |         |       |         |                   |
| 100             | 8160/      | Garbage                           | 0                  |         |       |         |                   |
| 100             | 8170/      | Shared Equipment                  | 0                  |         |       |         |                   |
| 100             | 8200/      | Property Tax                      | 0                  |         |       |         |                   |
| 100             | 8210/      | Interest expense                  | 0                  |         |       |         |                   |
| 100             | 8220/      | Bank charges                      | 0                  |         |       |         |                   |
| 100             | 8230/      | Penalties                         | 221                |         |       |         |                   |
| 100             | 8295/      | Vehicle - Lease                   | 0                  |         |       |         |                   |
| 100             | 8300/      | Vehicle - Insurance               | 0                  |         |       |         |                   |
| 100             | 8305/      | Vehicle - Allowance               | 0                  |         |       |         |                   |
| 100             | 8310/      | Vehicle - Repairs & Maintenance   | 0                  |         |       |         |                   |
| 100             | 8320/      | Vehicle - Gas                     | 0                  |         |       |         |                   |
| 100             | 8330/      | Cash over and short               | 0                  |         |       |         |                   |
| 100             | 8400/      | Bad Debts                         | 0                  |         |       |         |                   |
| 100             | 8500/      | Uniforms                          | 0                  |         |       |         |                   |
| 100             | 8998/      | Income tax expense                | 0                  |         |       |         |                   |
| 100             | 8999/      | Deferred income tax expense       | 0                  |         |       |         |                   |
| 100             | 1040/      | Payroll clearing account          | 0                  |         |       |         |                   |
| 100             | 2100/      | Accrued wages                     | 0                  |         |       |         |                   |
| 100             | 2110/      | Bonus payable                     | 0                  |         |       |         |                   |
| 100             | 2120/      | Accrued vacation                  | 0                  |         |       |         |                   |
| 100             | 2125/      | Accrued bank time                 | 0                  |         |       |         |                   |
| 100             | 2190/      | RRSP Payable                      | 0                  |         |       |         |                   |
| 100             | 5300/      | Technician wages                  | 0                  |         |       |         |                   |
| 100             | 5310/      | Technician wages - Overtime 1.5   | 0                  |         |       |         |                   |
| 100             | 5320/      | Technician wages - Doubletime 2.0 | 0                  |         |       |         |                   |
| 100             | 5330/      | Technician EI expense             | 0                  |         |       |         |                   |
| 100             | 5340/      | Technician CPP expense            | 0                  |         |       |         |                   |
### Formulas:
| Cell   | Formula                                                                                                                                                                                  |
|:-------|:-----------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------|
| A1     | =RECONCILIATION!#REF!                                                                                                                                                                    |
| A2     | =RECONCILIATION!#REF!                                                                                                                                                                    |
| A3     | =RECONCILIATION!#REF!                                                                                                                                                                    |
| B3     | =RECONCILIATION!#REF!                                                                                                                                                                    |
| A4     | =RECONCILIATION!#REF!                                                                                                                                                                    |
| G7     | =SUM(D:D)-SUM(E:E)                                                                                                                                                                       |
| C8     | =VLOOKUP(B8,'Data Parameters'!C2:D4,2,FALSE)                                                                                                                                             |
| E8     | =IF(B4="4249",SUMIFS(D:D,A:A,A8,B4,"4249"),0)                                                                                                                                            |
| C9     | =VLOOKUP(B9,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                         |
| E9     | =IF(B5="4249",SUMIFS(D:D,A:A,100,B5,"4249"),0)                                                                                                                                           |
| C10    | =VLOOKUP(B10,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E10    | =IF(B4="4249",SUMIF(A:A,"300",D:D),0)                                                                                                                                                    |
| C11    | =VLOOKUP(B11,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E11    | =IF(B3="3774",SUMIFS(D:D,A:A,100,B3,"3774"),0)                                                                                                                                           |
| C12    | =VLOOKUP(B12,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E12    | =IF(B4="3774",SUMIFS(D:D,A:A,100,B4,"3774"),0)                                                                                                                                           |
| C13    | =VLOOKUP(B13,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E13    | =IF(B5="3774",SUMIFS(D:D,A:A,100,B5,"3774"),0)                                                                                                                                           |
| B14    | =+IF(RIGHT(RECONCILIATION!#REF!,4)='Data Parameters'!$A$2,'Data Parameters'!$C$2,IF(RIGHT(RECONCILIATION!#REF!,4)='Data Parameters'!$A$3,'Data Parameters'!$C$3,'Data Parameters'!$C$4)) |
| C14    | =VLOOKUP(B14,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E14    | =IF(B4="8415",SUMIFS(D:D,A:A,100,B4,"8415"),0)                                                                                                                                           |
| B15    | =+IF(RIGHT(RECONCILIATION!#REF!,4)='Data Parameters'!$A$2,'Data Parameters'!$C$2,IF(RIGHT(RECONCILIATION!#REF!,4)='Data Parameters'!$A$3,'Data Parameters'!$C$3,'Data Parameters'!$C$4)) |
| C15    | =VLOOKUP(B15,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E15    | =IF(B3="8415",SUMIFS(D:D,A:A,100,B3,"8415"),0)                                                                                                                                           |
| B16    | =+IF(RIGHT(RECONCILIATION!#REF!,4)='Data Parameters'!$A$2,'Data Parameters'!$C$2,IF(RIGHT(RECONCILIATION!#REF!,4)='Data Parameters'!$A$3,'Data Parameters'!$C$3,'Data Parameters'!$C$4)) |
| C16    | =VLOOKUP(B16,'Data Parameters'!$C$2:$D$4,2,FALSE)                                                                                                                                        |
| E16    | =IF(B4="8415",SUMIFS(D:D,A:A,100,B4,"8415"),0)                                                                                                                                           |
| D17    | =SUMIF(RECONCILIATION!R:R,ALLOCATION!A17,RECONCILIATION!P:P)                                                                                                                             |
| D18    | =SUMIF(RECONCILIATION!R:R,ALLOCATION!A18,RECONCILIATION!P:P)                                                                                                                             |
| D19    | =SUMIF(RECONCILIATION!R:R,ALLOCATION!A19,RECONCILIATION!P:P)                                                                                                                             |
| D20    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A20,RECONCILIATION!O:O,ALLOCATION!B20)                                                                                          |
| D21    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A21,RECONCILIATION!O:O,ALLOCATION!B21)                                                                                          |
| D22    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A22,RECONCILIATION!O:O,ALLOCATION!B22)                                                                                          |
| D23    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A23,RECONCILIATION!O:O,ALLOCATION!B23)                                                                                          |
| D24    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A24,RECONCILIATION!O:O,ALLOCATION!B24)                                                                                          |
| D25    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A25,RECONCILIATION!O:O,ALLOCATION!B25)                                                                                          |
| D26    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A26,RECONCILIATION!O:O,ALLOCATION!B26)                                                                                          |
| D27    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A27,RECONCILIATION!O:O,ALLOCATION!B27)                                                                                          |
| D28    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A28,RECONCILIATION!O:O,ALLOCATION!B28)                                                                                          |
| D29    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A29,RECONCILIATION!O:O,ALLOCATION!B29)                                                                                          |
| D30    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A30,RECONCILIATION!O:O,ALLOCATION!B30)                                                                                          |
| D31    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A31,RECONCILIATION!O:O,ALLOCATION!B31)                                                                                          |
| D32    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A32,RECONCILIATION!O:O,ALLOCATION!B32)                                                                                          |
| D33    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A33,RECONCILIATION!O:O,ALLOCATION!B33)                                                                                          |
| D34    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A34,RECONCILIATION!O:O,ALLOCATION!B34)                                                                                          |
| D35    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A35,RECONCILIATION!O:O,ALLOCATION!B35)                                                                                          |
| D36    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A36,RECONCILIATION!O:O,ALLOCATION!B36)                                                                                          |
| D37    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A37,RECONCILIATION!O:O,ALLOCATION!B37)                                                                                          |
| D38    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A38,RECONCILIATION!O:O,ALLOCATION!B38)                                                                                          |
| D39    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A39,RECONCILIATION!O:O,ALLOCATION!B39)                                                                                          |
| D40    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A40,RECONCILIATION!O:O,ALLOCATION!B40)                                                                                          |
| D41    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A41,RECONCILIATION!O:O,ALLOCATION!B41)                                                                                          |
| D42    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A42,RECONCILIATION!O:O,ALLOCATION!B42)                                                                                          |
| D43    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A43,RECONCILIATION!O:O,ALLOCATION!B43)                                                                                          |
| D44    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A44,RECONCILIATION!O:O,ALLOCATION!B44)                                                                                          |
| D45    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A45,RECONCILIATION!O:O,ALLOCATION!B45)                                                                                          |
| D46    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A46,RECONCILIATION!O:O,ALLOCATION!B46)                                                                                          |
| D47    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A47,RECONCILIATION!O:O,ALLOCATION!B47)                                                                                          |
| D48    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A48,RECONCILIATION!O:O,ALLOCATION!B48)                                                                                          |
| D49    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A49,RECONCILIATION!O:O,ALLOCATION!B49)                                                                                          |
| D50    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A50,RECONCILIATION!O:O,ALLOCATION!B50)                                                                                          |
| D51    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A51,RECONCILIATION!O:O,ALLOCATION!B51)                                                                                          |
| D52    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A52,RECONCILIATION!O:O,ALLOCATION!B52)                                                                                          |
| D53    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A53,RECONCILIATION!O:O,ALLOCATION!B53)                                                                                          |
| D54    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A54,RECONCILIATION!O:O,ALLOCATION!B54)                                                                                          |
| D55    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A55,RECONCILIATION!O:O,ALLOCATION!B55)                                                                                          |
| D56    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A56,RECONCILIATION!O:O,ALLOCATION!B56)                                                                                          |
| D57    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A57,RECONCILIATION!O:O,ALLOCATION!B57)                                                                                          |
| D58    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A58,RECONCILIATION!O:O,ALLOCATION!B58)                                                                                          |
| D59    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A59,RECONCILIATION!O:O,ALLOCATION!B59)                                                                                          |
| D60    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A60,RECONCILIATION!O:O,ALLOCATION!B60)                                                                                          |
| D61    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A61,RECONCILIATION!O:O,ALLOCATION!B61)                                                                                          |
| D62    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A62,RECONCILIATION!O:O,ALLOCATION!B62)                                                                                          |
| D63    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A63,RECONCILIATION!O:O,ALLOCATION!B63)                                                                                          |
| D64    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A64,RECONCILIATION!O:O,ALLOCATION!B64)                                                                                          |
| D65    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A65,RECONCILIATION!O:O,ALLOCATION!B65)                                                                                          |
| D66    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A66,RECONCILIATION!O:O,ALLOCATION!B66)                                                                                          |
| D67    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A67,RECONCILIATION!O:O,ALLOCATION!B67)                                                                                          |
| D68    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A68,RECONCILIATION!O:O,ALLOCATION!B68)                                                                                          |
| D69    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A69,RECONCILIATION!O:O,ALLOCATION!B69)                                                                                          |
| D70    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A70,RECONCILIATION!O:O,ALLOCATION!B70)                                                                                          |
| D71    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A71,RECONCILIATION!O:O,ALLOCATION!B71)                                                                                          |
| D72    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A72,RECONCILIATION!O:O,ALLOCATION!B72)                                                                                          |
| D73    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A73,RECONCILIATION!O:O,ALLOCATION!B73)                                                                                          |
| D74    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A74,RECONCILIATION!O:O,ALLOCATION!B74)                                                                                          |
| D75    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A75,RECONCILIATION!O:O,ALLOCATION!B75)                                                                                          |
| D76    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A76,RECONCILIATION!O:O,ALLOCATION!B76)                                                                                          |
| D77    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A77,RECONCILIATION!O:O,ALLOCATION!B77)                                                                                          |
| D78    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A78,RECONCILIATION!O:O,ALLOCATION!B78)                                                                                          |
| D79    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A79,RECONCILIATION!O:O,ALLOCATION!B79)                                                                                          |
| D80    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A80,RECONCILIATION!O:O,ALLOCATION!B80)                                                                                          |
| D81    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A81,RECONCILIATION!O:O,ALLOCATION!B81)                                                                                          |
| D82    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A82,RECONCILIATION!O:O,ALLOCATION!B82)                                                                                          |
| D83    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A83,RECONCILIATION!O:O,ALLOCATION!B83)                                                                                          |
| D84    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A84,RECONCILIATION!O:O,ALLOCATION!B84)                                                                                          |
| D85    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A85,RECONCILIATION!O:O,ALLOCATION!B85)                                                                                          |
| D86    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A86,RECONCILIATION!O:O,ALLOCATION!B86)                                                                                          |
| D87    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A87,RECONCILIATION!O:O,ALLOCATION!B87)                                                                                          |
| D88    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A88,RECONCILIATION!O:O,ALLOCATION!B88)                                                                                          |
| D89    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A89,RECONCILIATION!O:O,ALLOCATION!B89)                                                                                          |
| D90    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A90,RECONCILIATION!O:O,ALLOCATION!B90)                                                                                          |
| D91    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A91,RECONCILIATION!O:O,ALLOCATION!B91)                                                                                          |
| D92    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A92,RECONCILIATION!O:O,ALLOCATION!B92)                                                                                          |
| D93    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A93,RECONCILIATION!O:O,ALLOCATION!B93)                                                                                          |
| D94    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A94,RECONCILIATION!O:O,ALLOCATION!B94)                                                                                          |
| D95    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A95,RECONCILIATION!O:O,ALLOCATION!B95)                                                                                          |
| D96    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A96,RECONCILIATION!O:O,ALLOCATION!B96)                                                                                          |
| D97    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A97,RECONCILIATION!O:O,ALLOCATION!B97)                                                                                          |
| D98    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A98,RECONCILIATION!O:O,ALLOCATION!B98)                                                                                          |
| D99    | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A99,RECONCILIATION!O:O,ALLOCATION!B99)                                                                                          |
| D100   | =SUMIFS(RECONCILIATION!Q:Q,RECONCILIATION!R:R,ALLOCATION!A100,RECONCILIATION!O:O,ALLOCATION!B100)                                                                                        |
Data for sheet saved as: 'ALLOCATION.dat'

---


## Sheet: Data Parameters
### Data:
|        |                                   |                                          |              |
|:-------|:----------------------------------|:-----------------------------------------|:-------------|
| 4249   | Jason Chuback                     | 2020/                                    | TD Visa 4249 |
| 3774   | Cody Tanner                       | 2021/                                    | TD Visa 3774 |
| 8514   | Don Baxter                        | 2022/                                    | TD Visa 8415 |
|        |                                   |                                          |              |
|        |                                   |                                          |              |
| Y      |                                   |                                          |              |
| N      |                                   |                                          |              |
| ON-HST |                                   |                                          |              |
|        |                                   |                                          |              |
|        |                                   |                                          |              |
| MB     | 0.07                              |                                          |              |
| SK     | 0.06                              |                                          |              |
| ON     | 0.13                              |                                          |              |
| GST    | 0.05                              |                                          |              |
| Shared | 0.07                              |                                          |              |
| COGS   |                                   |                                          |              |
| GA     |                                   |                                          |              |
|        |                                   |                                          |              |
|        |                                   |                                          |              |
|        |                                   |                                          |              |
| Code   | Description                       | Group                                    |              |
| 1200/  | Prepaids                          | Prepaid expenses and other receivables   | 1200/        |
| 1210/  | Supplier Prepayments              | Prepaid expenses and other receivables   | 1210/        |
| 1540/  | CHB Solutions - Intercompany      | Due to / (from) related companies        | 1540/        |
| 1700/  | Equipment Inventory               | Inventory                                | 1700/        |
| 1710/  | Parts Inventory                   | Inventory                                | 1710/        |
| 1720/  | Inventory - Work-in-Progress      | Inventory                                | 1720/        |
| 1730/  | Inventory - Clearing              | Inventory                                | 1730/        |
| 1740/  | Inventory - Return Clearing       | Inventory                                | 1740/        |
| 1750/  | Inventory - FX Adjustment         | Inventory                                | 1750/        |
| 2020/  | TD Visa 4249                      | Accounts payable and accrued liabilities | 2020/        |
| 2021/  | TD Visa 3774                      | Accounts payable and accrued liabilities | 2021/        |
| 2022/  | TD Visa 8415                      | Accounts payable and accrued liabilities | 2022/        |
| 2180/  | GST/HST Paid - ITC                | Accounts payable and accrued liabilities | 2180/        |
| 2300/  | Due to CHB Solutions              | Due to / (from) related companies        | 2300/        |
| 2305/  | Due to Jason & Heidi Chuback      | Due to / (from) related companies        | 2305/        |
| 2310/  | Accrued Social Committee          | Due to / (from) related companies        | 2310/        |
| 5000/  | Cost of sales - Units             | Cost of sales                            | 5000/        |
| 5010/  | Cost of sales - Parts             | Cost of sales                            | 5010/        |
| 5020/  | Cost of sales - Rentals           | Cost of sales                            | 5020/        |
| 5030/  | Purchase discounts                | Cost of sales                            | 5030/        |
| 5040/  | Freight Expense                   | Cost of sales                            | 5040/        |
| 5045/  | Duty and brokerage                | Cost of sales                            | 5045/        |
| 5050/  | Warranty recovery from supplier   | Cost of sales                            | 5050/        |
| 5060/  | Misc. cost of sales               | Cost of sales                            | 5060/        |
| 5070/  | Inventory variance                | Cost of sales                            | 5070/        |
| 5200/  | Standard labour expense           | Standard labour                          | 5200/        |
| 5210/  | Standard labour variance          | Standard labour                          | 5210/        |
| 5390/  | Training - Technicians            | Direct labour                            | 5390/        |
| 5400/  | Subcontractor                     | Direct labour                            | 5400/        |
| 5410/  | Shop supplies                     | Cost of sales                            | 5410/        |
| 5420/  | Mileage/travel/vehicle expense    | Cost of sales                            | 5420/        |
| 5500/  | Foreign exchange (gain)/loss      | Foreign exchange gain/loss               | 5500/        |
| 6000/  | (Gain)/loss on disposal of assets | Gain/loss on disposal of assets          | 6000/        |
| 6010/  | Interest revenue                  | Interest revenue                         | 6010/        |
| 6020/  | Government grants                 | Government grants                        | 6020/        |
| 7110/  | Advertising and Promotion         | Advertising and Promotion                | 7110/        |
| 7200/  | Donations                         | Advertising and Promotion                | 7200/        |
| 7210/  | Dues and subscriptions            | Dues and subscriptions                   | 7210/        |
| 7250/  | Depreciation & Amortization       | Amortization                             | 7250/        |
| 7300/  | Insurance                         | Insurance                                | 7300/        |
| 7400/  | Licenses                          | Licenses and taxes                       | 7400/        |
| 7500/  | Meals and Entertainment           | Meals and entertainment                  | 7500/        |
| 7510/  | Golf Dues & Green Fees            | Meals and entertainment                  | 7510/        |
| 7600/  | Office Supplies                   | Office supplies                          | 7600/        |
| 7700/  | Professional Fees - Accounting    | Professional fees                        | 7700/        |
| 7800/  | Professional Fees - Legal         | Professional fees                        | 7800/        |
| 7900/  | Safety Expense                    | Safety                                   | 7900/        |
| 7910/  | IT Expense                        | IT and Software                          | 7910/        |
| 7920/  | Software Expense                  | IT and Software                          | 7920/        |
| 7930/  | Telephone - Office                | Telephone                                | 7930/        |
| 7940/  | Telephone - Cellular              | Telephone                                | 7940/        |
| 8000/  | Travel                            | Travel                                   | 8000/        |
| 8010/  | Parking                           | Travel                                   | 8010/        |
| 8100/  | TV Cable                          | Rent and common area                     | 8100/        |
| 8110/  | Internet                          | Rent and common area                     | 8110/        |
| 8120/  | Electricity                       | Rent and common area                     | 8120/        |
| 8130/  | Water                             | Rent and common area                     | 8130/        |
| 8140/  | Natural gas                       | Rent and common area                     | 8140/        |
| 8150/  | Building Repair and Maintenance   | Rent and common area                     | 8150/        |
| 8160/  | Garbage                           | Rent and common area                     | 8160/        |
| 8170/  | Shared Equipment                  | Rent and common area                     | 8170/        |
| 8200/  | Property Tax                      | Rent and common area                     | 8200/        |
| 8210/  | Interest expense                  | Interest and bank charges                | 8210/        |
| 8220/  | Bank charges                      | Interest and bank charges                | 8220/        |
| 8230/  | Penalties                         | Interest and bank charges                | 8230/        |
| 8295/  | Vehicle - Lease                   | Automotive                               | 8295/        |
| 8300/  | Vehicle - Insurance               | Automotive                               | 8300/        |
| 8305/  | Vehicle - Allowance               | Automotive                               | 8305/        |
| 8310/  | Vehicle - Repairs & Maintenance   | Automotive                               | 8310/        |
| 8320/  | Vehicle - Gas                     | Automotive                               | 8320/        |
| 8330/  | Cash over and short               | Automotive                               | 8330/        |
| 8400/  | Bad Debts                         | Bad debt expense                         | 8400/        |
| 8500/  | Uniforms                          | Uniforms                                 | 8500/        |
| 8998/  | Income tax expense                | Current income tax expense               | 8998/        |
| 8999/  | Deferred income tax expense       | Deferred income tax expense              | 8999/        |
| 1040/  | Payroll clearing account          | Cash                                     | 1040/        |
| 2100/  | Accrued wages                     | Accounts payable and accrued liabilities | 2100/        |
| 2110/  | Bonus payable                     | Accounts payable and accrued liabilities | 2110/        |
| 2120/  | Accrued vacation                  | Accounts payable and accrued liabilities | 2120/        |
### Formulas:
| Cell   | Formula   |
|:-------|:----------|
| D23    | =A23      |
| D24    | =A24      |
| D25    | =A25      |
| D26    | =A26      |
| D27    | =A27      |
| D28    | =A28      |
| D29    | =A29      |
| D30    | =A30      |
| D31    | =A31      |
| D32    | =A32      |
| D33    | =A33      |
| D34    | =A34      |
| D35    | =A35      |
| D36    | =A36      |
| D37    | =A37      |
| D38    | =A38      |
| D39    | =A39      |
| D40    | =A40      |
| D41    | =A41      |
| D42    | =A42      |
| D43    | =A43      |
| D44    | =A44      |
| D45    | =A45      |
| D46    | =A46      |
| D47    | =A47      |
| D48    | =A48      |
| D49    | =A49      |
| D50    | =A50      |
| D51    | =A51      |
| D52    | =A52      |
| D53    | =A53      |
| D54    | =A54      |
| D55    | =A55      |
| D56    | =A56      |
| D57    | =A57      |
| D58    | =A58      |
| D59    | =A59      |
| D60    | =A60      |
| D61    | =A61      |
| D62    | =A62      |
| D63    | =A63      |
| D64    | =A64      |
| D65    | =A65      |
| D66    | =A66      |
| D67    | =A67      |
| D68    | =A68      |
| D69    | =A69      |
| D70    | =A70      |
| D71    | =A71      |
| D72    | =A72      |
| D73    | =A73      |
| D74    | =A74      |
| D75    | =A75      |
| D76    | =A76      |
| D77    | =A77      |
| D78    | =A78      |
| D79    | =A79      |
| D80    | =A80      |
| D81    | =A81      |
| D82    | =A82      |
| D83    | =A83      |
| D84    | =A84      |
| D85    | =A85      |
| D86    | =A86      |
| D87    | =A87      |
| D88    | =A88      |
| D89    | =A89      |
| D90    | =A90      |
| D91    | =A91      |
| D92    | =A92      |
| D93    | =A93      |
| D94    | =A94      |
| D95    | =A95      |
| D96    | =A96      |
| D97    | =A97      |
| D98    | =A98      |
| D99    | =A99      |
| D100   | =A100     |
Data for sheet saved as: 'Data Parameters.dat'

---


## Sheet: Sheet1
### Data:
| FieldServio Chart of Accounts   |                                       |                                          |         |        |           |        |            |                                                                                                                     |    |             |      |    |                 |      |
|:--------------------------------|:--------------------------------------|:-----------------------------------------|:--------|:-------|:----------|:-------|:-----------|:--------------------------------------------------------------------------------------------------------------------|:---|:------------|:-----|:---|:----------------|:-----|
| Company: Air Unlimited Inc.     |                                       |                                          |         |        |           |        |            |                                                                                                                     |    | Departments |      |    | Offices         |      |
|                                 |                                       |                                          |         |        |           |        |            |                                                                                                                     |    |             |      |    |                 |      |
| Code                            | Description                           | Group                                    | Source  | Contra | Inventory | Status | Bill Type  | Expense Type/Examples                                                                                               |    | Name        | Code |    | Name            | Code |
| 1200/                           | Prepaids                              | Prepaid expenses and other receivables   |         | No     | No        | Active |            | All expenses that are not incurred in within 30 days of invoice date. (Ex: Insurance, prepaid events, Jets tickets) |    | Generator   | 10   |    | Manitoba        | 100  |
| 1210/                           | Supplier Prepayments                  | Prepaid expenses and other receivables   |         | No     | No        | Active |            | Prepayments to suppliers for open PO's                                                                              |    | Compressor  | 20   |    | Saskatchewan    | 200  |
| 1540/                           | CHB Solutions - Intercompany          | Due to / (from) related companies        | J/E     | No     | No        | Active |            | Loans and FX transfers                                                                                              |    | Rental      | 30   |    | Shared Services | 300  |
| 1700/                           | Equipment Inventory                   | Inventory                                |         | No     | Yes       | Active |            | Inventory Asset                                                                                                     |    |             |      |    |                 |      |
| 1710/                           | Parts Inventory                       | Inventory                                |         | No     | Yes       | Active |            | Inventory Asset                                                                                                     |    |             |      |    |                 |      |
| 1720/                           | Inventory - Work-in-Progress          | Inventory                                |         | No     | Yes       | Active |            | Inventory assigned to Jobs                                                                                          |    |             |      |    |                 |      |
| 1730/                           | Inventory - Clearing                  | Inventory                                |         | No     | Yes       | Active |            | Accrued Liabilities - no direct posting                                                                             |    |             |      |    |                 |      |
| 1740/                           | Inventory - Return Clearing           | Inventory                                |         | No     | Yes       | Active |            | Parts not used on Jobs that need to be returned to the shelf                                                        |    |             |      |    |                 |      |
| 1750/                           | Inventory - FX Adjustment             | Inventory                                |         | No     | Yes       | Active |            | FX adjustments at month end                                                                                         |    |             |      |    |                 |      |
| 2020/                           | TD Visa 4249                          | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Visa Payments (bank account) - values through payment postings.                                                     |    |             |      |    |                 |      |
| 2021/                           | TD Visa 3774                          | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Visa Payments (bank account) - values through payment postings.                                                     |    |             |      |    |                 |      |
| 2022/                           | TD Visa 8415                          | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Visa Payments (bank account) - values through payment postings.                                                     |    |             |      |    |                 |      |
| 2180/                           | GST/HST Paid - ITC                    | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Sales tax paid                                                                                                      |    |             |      |    |                 |      |
| 2300/                           | Due to CHB Solutions                  | Due to / (from) related companies        |         | No     | No        | Active |            | Rent, etc                                                                                                           |    |             |      |    |                 |      |
| 2305/                           | Due to Jason & Heidi Chuback          | Due to / (from) related companies        |         | No     | No        | Active |            | Jason's personal expenses                                                                                           |    |             |      |    |                 |      |
| 2310/                           | Accrued Social Committee              | Due to / (from) related companies        |         | No     | No        | Active |            | Social committee expenses and donations                                                                             |    |             |      |    |                 |      |
| 5000/                           | Cost of sales - Units                 | Cost of sales                            | Job     | Yes    | No        | Active |            | Cost of sales from Jobs/Contracts                                                                                   |    |             |      |    |                 |      |
| 5010/                           | Cost of sales - Parts                 | Cost of sales                            | Job     | Yes    | No        | Active |            | Cost of sales from Jobs/Contracts                                                                                   |    |             |      |    |                 |      |
| 5020/                           | Cost of sales - Rentals               | Cost of sales                            | Job     | Yes    | No        | Active |            | Cost of sales from Jobs/Contracts                                                                                   |    |             |      |    |                 |      |
| 5030/                           | Purchase discounts                    | Cost of sales                            | Job     | Yes    | No        | Active |            | Discounts received from vendors                                                                                     |    |             |      |    |                 |      |
| 5040/                           | Freight Expense                       | Cost of sales                            |         | Yes    | No        | Active |            | Cost of freight in and out                                                                                          |    |             |      |    |                 |      |
| 5045/                           | Duty and brokerage                    | Cost of sales                            |         | Yes    | No        | Active |            | Cost of Duty and Brokerage                                                                                          |    |             |      |    |                 |      |
| 5050/                           | Warranty recovery from supplier       | Cost of sales                            |         | Yes    | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 5060/                           | Misc. cost of sales                   | Cost of sales                            |         | Yes    | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 5070/                           | Inventory variance                    | Cost of sales                            |         | Yes    | No        | Active |            | Inventory discrepancies and adjustments                                                                             |    |             |      |    |                 |      |
| 5200/                           | Standard labour expense               | Standard labour                          | Job     | Yes    | No        | Active |            | Cost of sales from jobs                                                                                             |    |             |      |    |                 |      |
| 5210/                           | Standard labour variance              | Standard labour                          | Job     | Yes    | No        | Active |            | Cost of sales from jobs                                                                                             |    |             |      |    |                 |      |
| 5390/                           | Training - Technicians                | Direct labour                            |         | Yes    | No        | Active |            | Technician training                                                                                                 |    |             |      |    |                 |      |
| 5400/                           | Subcontractor                         | Direct labour                            | Job     | Yes    | No        | Active |            | Cost of sales from PO's                                                                                             |    |             |      |    |                 |      |
| 5410/                           | Shop supplies                         | Cost of sales                            | PO-M    | Yes    | No        | Active |            | Cost of sales from PO's                                                                                             |    |             |      |    |                 |      |
| 5420/                           | Mileage/travel/vehicle expense        | Cost of sales                            | Job     | Yes    | No        | Active |            | Cost of sales from PO's; technician travel to perform work                                                          |    |             |      |    |                 |      |
| 5500/                           | Foreign exchange (gain)/loss          | Foreign exchange gain/loss               |         | Yes    | No        | Active |            | FX adjustments                                                                                                      |    |             |      |    |                 |      |
| 6000/                           | (Gain)/loss on disposal of assets     | Gain/loss on disposal of assets          |         | No     | No        | Active |            | Asset disposal                                                                                                      |    | 707.04      |      |    |                 |      |
| 6010/                           | Interest revenue                      | Interest revenue                         |         | No     | No        | Active |            | Interest received from the bank and late customer payments                                                          |    |             |      |    |                 |      |
| 6020/                           | Government grants                     | Government grants                        |         | No     | No        | Active |            | Funds received from government grants                                                                               |    |             |      |    |                 |      |
| 7110/                           | Advertising and Promotion             | Advertising and Promotion                |         | No     | No        | Active |            | Website, cards, sponsorships, customer appreciation                                                                 |    |             |      |    |                 |      |
| 7200/                           | Donations                             | Advertising and Promotion                |         | No     | No        | Active |            | Donations to charity/non-profits that provide a receipt                                                             |    |             |      |    |                 |      |
| 7210/                           | Dues and subscriptions                | Dues and subscriptions                   |         | No     | No        | Active |            | Professional dues and memberships                                                                                   |    |             |      |    |                 |      |
| 7250/                           | Depreciation & Amortization           | Amortization                             |         | No     | No        | Active |            | Asset depreciation                                                                                                  |    |             |      |    |                 |      |
| 7300/                           | Insurance                             | Insurance                                |         | No     | No        | Active |            | Commercial and liability insurance                                                                                  |    |             |      |    |                 |      |
| 7400/                           | Licenses                              | Licenses and taxes                       |         | No     | No        | Active |            | Business licenses and permits                                                                                       |    |             |      |    |                 |      |
| 7500/                           | Meals and Entertainment               | Meals and entertainment                  |         | No     | No        | Active |            | Meals and Entertainment                                                                                             |    |             |      |    |                 |      |
| 7510/                           | Golf Dues & Green Fees                | Meals and entertainment                  |         | No     | No        | Active |            | Golf membership dues and green fees                                                                                 |    |             |      |    |                 |      |
| 7600/                           | Office Supplies                       | Office supplies                          |         | No     | No        | Active |            | Office supplies purchased                                                                                           |    |             |      |    |                 |      |
| 7700/                           | Professional Fees - Accounting        | Professional fees                        |         | No     | No        | Active |            | Professional Fees - Accounting                                                                                      |    |             |      |    |                 |      |
| 7800/                           | Professional Fees - Legal             | Professional fees                        |         | No     | No        | Active |            | Professional Fees - Legal                                                                                           |    |             |      |    |                 |      |
| 7900/                           | Safety Expense                        | Safety                                   |         | No     | No        | Active |            | Safety supplies, software, training                                                                                 |    |             |      |    |                 |      |
| 7910/                           | IT Expense                            | IT and Software                          |         | No     | No        | Active |            | Hardware (Monitors, Cables, Switches, etc)                                                                          |    |             |      |    |                 |      |
| 7920/                           | Software Expense                      | IT and Software                          |         | No     | No        | Active |            | Software and subscriptions; Divide between offices by # of licenses used                                            |    |             |      |    |                 |      |
| 7930/                           | Telephone - Office                    | Telephone                                |         | No     | No        | Active | Predefined | Telephone - Office landlines & Wildix                                                                               |    |             |      |    |                 |      |
| 7940/                           | Telephone - Cellular                  | Telephone                                |         | No     | No        | Active | Predefined | Telephone - Cellular                                                                                                |    |             |      |    |                 |      |
| 8000/                           | Travel                                | Travel                                   | PO-M    | No     | No        | Active |            | Travel for sales and training                                                                                       |    |             |      |    |                 |      |
| 8010/                           | Parking                               | Travel                                   |         | No     | No        | Active |            | Parking expense                                                                                                     |    |             |      |    |                 |      |
| 8100/                           | TV Cable                              | Rent and common area                     |         | No     | No        | Active |            | TV Cable                                                                                                            |    |             |      |    |                 |      |
| 8110/                           | Internet                              | Rent and common area                     |         | No     | No        | Active | Predefined | Internet                                                                                                            |    |             |      |    |                 |      |
| 8120/                           | Electricity                           | Rent and common area                     |         | No     | No        | Active | Predefined | Electricity - Note to indicate "Office" or "Blue Shop", SK charges to SK Office                                     |    |             |      |    |                 |      |
| 8130/                           | Water                                 | Rent and common area                     |         | No     | No        | Active | Predefined | Water - Note to indicate "Office" or "Blue Shop", SK charges to SK Office                                           |    |             |      |    |                 |      |
| 8140/                           | Natural gas                           | Rent and common area                     |         | No     | No        | Active | Predefined | Natural gas - Note to indicate "Office" or "Blue Shop", SK charges to SK Office                                     |    |             |      |    |                 |      |
| 8150/                           | Building Repair and Maintenance       | Rent and common area                     |         | No     | No        | Active | Misc PO    | Repairs - Note to indicate "Office" or "Blue Shop", SK charges to SK Office                                         |    |             |      |    |                 |      |
| 8160/                           | Garbage                               | Rent and common area                     |         | No     | No        | Active | Predefined | Garbage disposal, SK charges to SK Office                                                                           |    |             |      |    |                 |      |
| 8170/                           | Shared Equipment                      | Rent and common area                     |         | No     | No        | Active | Misc PO    | Forklift, warehouse fixtures, etc.                                                                                  |    |             |      |    |                 |      |
| 8200/                           | Property Tax                          | Rent and common area                     |         | No     | No        | Active | Predefined | City property tax for 2116 Logan, SK charges to SK Office                                                           |    |             |      |    |                 |      |
| 8210/                           | Interest expense                      | Interest and bank charges                |         | No     | No        | Active |            | Interest charged on past due accounts                                                                               |    |             |      |    |                 |      |
| 8220/                           | Bank charges                          | Interest and bank charges                |         | No     | No        | Active |            | Monthly bank and direct withdrawal fees                                                                             |    |             |      |    |                 |      |
| 8230/                           | Penalties                             | Interest and bank charges                |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8295/                           | Vehicle - Lease                       | Automotive                               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8300/                           | Vehicle - Insurance                   | Automotive                               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8305/                           | Vehicle - Allowance                   | Automotive                               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8310/                           | Vehicle - Repairs & Maintenance       | Automotive                               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8320/                           | Vehicle - Gas                         | Automotive                               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8330/                           | Cash over and short                   | Automotive                               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8400/                           | Bad Debts                             | Bad debt expense                         |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8500/                           | Uniforms                              | Uniforms                                 |         | No     | No        | Active |            | Uniform - company portion                                                                                           |    |             |      |    |                 |      |
| 8998/                           | Income tax expense                    | Current income tax expense               |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 8999/                           | Deferred income tax expense           | Deferred income tax expense              |         | No     | No        | Active |            |                                                                                                                     |    |             |      |    |                 |      |
| 1040/                           | Payroll clearing account              | Cash                                     |         | No     | No        | Active |            | Employee Expenses                                                                                                   |    |             |      |    |                 |      |
| 2100/                           | Accrued wages                         | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 2110/                           | Bonus payable                         | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 2120/                           | Accrued vacation                      | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 2125/                           | Accrued bank time                     | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 2190/                           | RRSP Payable                          | Accounts payable and accrued liabilities |         | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 5300/                           | Technician wages                      | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 5310/                           | Technician wages - Overtime 1.5       | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 5320/                           | Technician wages - Doubletime 2.0     | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 5330/                           | Technician EI expense                 | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 5340/                           | Technician CPP expense                | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 5350/                           | Workers Compensation - Technicians    | Direct labour                            | J/E     | Yes    | No        | Active |            | Exp related to techs (50%)                                                                                          |    |             |      |    |                 |      |
| 5360/                           | Technician employee benefits expense  | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll - Split charges based on head count                                                                         |    |             |      |    |                 |      |
| 5370/                           | Technician RRSP                       | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll - Split charges based on head count                                                                         |    |             |      |    |                 |      |
| 5380/                           | Technician - Payroll tax (MHET / HET) | Direct labour                            | J/E     | Yes    | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 7000/                           | Wages - Management                    | Wages and subcontractors                 | J/E     | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 7010/                           | Wages - Office                        | Wages and subcontractors                 | J/E     | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 7020/                           | Wages - Sales                         | Wages and subcontractors                 | J/E     | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 7030/                           | EI Expense                            | Wages and subcontractors                 | J/E     | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 7040/                           | CPP Expense                           | Wages and subcontractors                 | J/E     | No     | No        | Active |            | Payroll                                                                                                             |    |             |      |    |                 |      |
| 7050/                           | Workers Compensation - Office/Sales   | Wages and subcontractors                 | AP Bill | No     | No        | Active |            | Exp related to office (50%)                                                                                         |    |             |      |    |                 |      |
| 7060/                           | Employee Benefits Expense             | Wages and subcontractors                 | AP Bill | No     | No        | Active |            | Payroll - Split charges based on head count                                                                         |    |             |      |    |                 |      |
### Formulas:
| Cell   | Formula     |
|:-------|:------------|
| K37    | =23568*0.03 |
Data for sheet saved as: 'Sheet1.dat'

---


