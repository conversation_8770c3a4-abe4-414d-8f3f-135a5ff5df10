Python code:

```python
import pandas as pd
import numpy as np
from metalake import load_data
import os

# Ensure output directory exists
os.makedirs("files", exist_ok=True)

chat_id = "687ff47bb3150354fe142b4d"

# 1. Load data into DataFrames
df_db = load_data(chat_id, "gl_detail_2025_06_visa_3774.dat")
df_ws = load_data(chat_id, "RECONCILIATION.dat")

# Handle empty dataframes
def empty_report(name):
    print(f"No data found in {name}. No further processing possible.")

if df_db.empty:
    empty_report("gl_detail_2025_06_visa_3774.dat")
elif df_ws.empty:
    empty_report("RECONCILIATION.dat")
else:
    # 2. Standardise date columns: convert to yyyy-mm-dd, rename to 'Date'
    def standardise_date(df):
        date_cols = [col for col in df.columns if 'date' in col.lower()]
        if date_cols:
            col = date_cols[0]
            df = df.rename(columns={col: 'Date'})
            df['Date'] = pd.to_datetime(df['Date']).dt.date.astype(str)
        return df

    df_db = standardise_date(df_db)
    df_ws = standardise_date(df_ws)

    # 3. Standardise amounts: convert to float, round to 2 decimals, rename column to 'Amount'
    def find_amount_column(df):
        for col in df.columns:
            if col.lower() in ['amount', 'value', 'charges']:
                return col
        # Fallback: try to find any that startswith
        for col in df.columns:
            if col.lower().startswith('amount') or col.lower().startswith('value') or col.lower().startswith('charge'):
                return col
        return None

    # For df_db
    amt_col_db = find_amount_column(df_db)
    if amt_col_db is not None:
        df_db = df_db.rename(columns={amt_col_db: 'Amount'})
        df_db['Amount'] = pd.to_numeric(df_db['Amount'], errors='coerce').round(2)
    else:
        raise Exception('No recognizable amount column in gl_detail_2025_06_visa_3774.dat')

    # For df_ws: prefer 'Charges' or 'Value' (as per preview)
    amt_col_ws = None
    if 'Charges' in df_ws.columns:
        amt_col_ws = 'Charges'
    elif 'Value' in df_ws.columns:
        amt_col_ws = 'Value'
    else:
        amt_col_ws = find_amount_column(df_ws)
    if amt_col_ws is not None:
        df_ws = df_ws.rename(columns={amt_col_ws: 'Amount'})
        df_ws['Amount'] = pd.to_numeric(df_ws['Amount'], errors='coerce').round(2)
    else:
        raise Exception('No recognizable amount column in RECONCILIATION.dat')

    # 4. Create composite key of Date + Amount in both
    df_db['Key'] = df_db['Date'].astype(str) + '_' + df_db['Amount'].map(lambda x: f"{x:.2f}")
    df_ws['Key'] = df_ws['Date'].astype(str) + '_' + df_ws['Amount'].map(lambda x: f"{x:.2f}")

    # 5. Perform left-anti join: find rows in df_ws whose Key not in df_db
    missing = df_ws[~df_ws['Key'].isin(df_db['Key'])].copy()
    missing = missing.drop(columns=['Key'])

    # 6. Output CSV and .dat
    missing.to_csv('files/missing_transactions.csv', index=False)
    missing.to_csv('files/missing_transactions.dat', index=False, sep='|')

    # 7. Markdown preview (first 20 rows)
    n_preview = min(20, len(missing))
    if n_preview == 0:
        print("No missing transactions found. All worksheet items exist in the database extract.")
    else:
        preview = missing.head(n_preview)
        print(preview.to_markdown(index=False))
        if len(missing) > 20:
            print(f"\nNote: Showing first 20 of {len(missing)} rows.")

````

Output:

| Date       | Description               |   Amount | Credit   |   Value | Receipt provided   | Province   | GST/HST   | PST   |   GST/HST% | PST%   | Is this related to an AP vendor account/ PO   | COGS or General   | Expense Description     | GL Account   |  GST/HST-Input Tax Credit    |  Pre-Tax Expense    | Div   | Status   | Links to Field Servio   |    |
|:-----------|:--------------------------|---------:|:---------|--------:|:-------------------|:-----------|:----------|:------|-----------:|:-------|:----------------------------------------------|:------------------|:------------------------|:-------------|:-----------------------------|:--------------------|:------|:---------|:------------------------|:---|
| 2025-06-29 | BEAUTIFUL.AI              |    63.48 |          |   63.48 |                    |            |           |       |       0.05 |        |                                               |                   |                         |              |                              |                     |       |          |                         |    |
| 2025-06-27 | COMFORT INN WINNIPEG A    |   886.06 |          |  886.06 |                    |            |           |       |       0.05 |        |                                               |                   |                         |              |                              |                     |       |          |                         |    |
| 2025-06-26 | RIVER OAKS GOLF           |    96.73 |          |   96.73 |                    |            |           |       |       0.05 |        |                                               |                   |                         |              |                              |                     |       |          |                         |    |
| 2025-06-26 | RIVER OAKS GOLF           |   287.84 |          |  287.84 |                    |            |           |       |       0.05 |        |                                               |                   |                         |              |                              |                     |       |          |                         |    |
| 2025-06-25 | GREAT CANADIAN OIL CHA    |   180.88 |          |  180.88 |                    |            |           |       |       0.05 |        |                                               |                   |                         |              |                              |                     |       |          |                         |    |
| 2025-06-24 | AMZN Mktp CA              |   nan    | 18.47    |  -18.47 |                    |            |           |       |       0.05 |        |                                               |                   |                         |              |                              |                     |       |          |                         |    |
| 2025-06-21 | UBER *TRIP HELP.UBER.COM  |    93.74 |          |   93.74 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                  | 8000/        | 0.0                          | 93.74               | 100.0 |          |                         |    |
| 2025-06-21 | SB BCN EVENTS CASTELLDEFE |    14.94 |          |   14.94 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 14.94               | 100.0 |          |                         |    |
| 2025-06-21 | LB TAPHOUSE -CONC CYYC    |    74.61 |          |   74.61 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 3.01                         | 71.6                | 100.0 |          |                         |    |
| 2025-06-21 | 0023 - SL BARCELONA AIRPO |   120.58 |          |  120.58 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 120.58              | 100.0 |          |                         |    |
| 2025-06-20 | SB BCN EVENTS             |    23.55 |          |   23.55 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 23.55               | 100.0 |          |                         |    |
| 2025-06-20 | CLUB NAUTIC CASTELLDEFELS |    72.18 |          |   72.18 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 72.18               | 100.0 |          |                         |    |
| 2025-06-20 | UBER *TRIP                |    16.15 |          |   16.15 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                  | 8000/        | 0.0                          | 16.15               | 100.0 |          |                         |    |
| 2025-06-20 | UBER *TRIP                |    28.96 |          |   28.96 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                  | 8000/        | 0.0                          | 28.96               | 100.0 |          |                         |    |
| 2025-06-20 | AVENTURAS GASTRON MICA    |   383.58 |          |  383.58 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 383.58              | 100.0 |          |                         |    |
| 2025-06-20 | TALLER DE TAPAS           |   107.95 |          |  107.95 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 107.95              | 100.0 |          |                         |    |
| 2025-06-20 | CLUB 61                   |    66.26 |          |   66.26 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 66.26               | 100.0 |          |                         |    |
| 2025-06-20 | SB BCN EVENTS             |    16.59 |          |   16.59 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Meals and Entertainment | 7500/        | 0.0                          | 16.59               | 100.0 |          |                         |    |
| 2025-06-20 | UBER *TRIP                |    53.23 |          |   53.23 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                  | 8000/        | 0.0                          | 53.23               | 100.0 |          |                         |    |
| 2025-06-20 | UBER *TRIP                |    70.13 |          |   70.13 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                  | 8000/        | 0.0                          | 70.13               | 100.0 |          |                         |    |

Note: Showing first 20 of 82 rows.

