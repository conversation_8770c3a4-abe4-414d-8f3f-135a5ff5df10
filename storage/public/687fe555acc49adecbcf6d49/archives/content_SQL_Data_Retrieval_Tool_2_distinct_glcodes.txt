SQL Query:

````
SELECT DISTINCT GeneralLedger_Code AS GL_Code
FROM GeneralLedgerDetail;
````

Data retrieved successfully:

First 100 rows of data:

|   GL_Code |
|----------:|
|      1000 |
|      1005 |
|      1020 |
|      1025 |
|      1030 |
|      1040 |
|      1100 |
|      1110 |
|      1120 |
|      1200 |
|      1300 |
|      1410 |
|      1420 |
|      1430 |
|      1440 |
|      1450 |
|      1480 |
|      1490 |
|      1530 |
|      1540 |
|      1610 |
|      1620 |
|      1630 |
|      1640 |
|      1650 |
|      1660 |
|      1670 |
|      1690 |
|      1691 |
|      1699 |
|      1700 |
|      1710 |
|      1720 |
|      1730 |
|      1740 |
|      1750 |
|      1800 |
|      1805 |
|      1810 |
|      1815 |
|      1820 |
|      1825 |
|      1830 |
|      1840 |
|      1845 |
|      1850 |
|      1855 |
|      1860 |
|      1865 |
|      1910 |
|      1960 |
|      2000 |
|      2010 |
|      2020 |
|      2021 |
|      2022 |
|      2030 |
|      2050 |
|      2060 |
|      2110 |
|      2120 |
|      2130 |
|      2160 |
|      2170 |
|      2180 |
|      2200 |
|      2210 |
|      2300 |
|      2305 |
|      2310 |
|      2420 |
|      2500 |
|      2510 |
|      2520 |
|      2600 |
|      3000 |
|      3100 |
|      3120 |
|      4000 |
|      4010 |
|      4020 |
|      4030 |
|      4040 |
|      4050 |
|      4070 |
|      4080 |
|      4400 |
|      4410 |
|      5000 |
|      5010 |
|      5020 |
|      5030 |
|      5040 |
|      5045 |
|      5060 |
|      5070 |
|      5200 |
|      5300 |
|      5330 |
|      5340 |