Python code:

```python
import pandas as pd
import os
from metalake import load_data

# Ensure output directory exists
os.makedirs('files', exist_ok=True)

chat_id = "687ff863b3150354fe142c87"

# 1. Load generalledger_june2025.dat and filter for GL_Code == 2021
ledger = load_data(chat_id, 'generalledger_june2025.dat')
if ledger.empty:
    print("No data found in generalledger_june2025.dat. No further processing possible.")
else:
    df_ledger = ledger[ledger['GL_Code'] == 2021].copy()
    if df_ledger.empty:
        print("No rows found in generalledger_june2025.dat for GL_Code 2021. No further processing possible.")
    else:
        # Keep TransactionDate and Amount
        df_ledger = df_ledger[['TransactionDate', 'Amount']].copy()
        df_ledger['Date'] = pd.to_datetime(df_ledger['TransactionDate']).dt.date.astype(str)
        df_ledger['SignedAmount'] = pd.to_numeric(df_ledger['Amount'], errors='coerce').round(2)
        df_ledger = df_ledger[['Date', 'SignedAmount']].dropna()

        # 3. Load RECONCILIATION.dat and build Date, SignedAmount
        df_ws = load_data(chat_id, 'RECONCILIATION.dat')
        if df_ws.empty:
            print("No data found in RECONCILIATION.dat. No further processing possible.")
        else:
            # Find the first column containing 'Date' in the name
            date_col = next((col for col in df_ws.columns if 'date' in col.lower()), None)
            if not date_col:
                print("No date column found in RECONCILIATION.dat. Cannot continue.")
            else:
                df_ws['Date'] = pd.to_datetime(df_ws[date_col], errors='coerce').dt.date.astype(str)

                # Determine SignedAmount
                if 'Value' in df_ws.columns and df_ws['Value'].notna().any():
                    df_ws['SignedAmount'] = pd.to_numeric(df_ws['Value'], errors='coerce').round(2)
                elif 'Amount' in df_ws.columns:
                    df_ws['SignedAmount'] = pd.to_numeric(df_ws['Amount'], errors='coerce').round(2)
                elif set(['Charges', 'Credit']).issubset(df_ws.columns):
                    charges = pd.to_numeric(df_ws['Charges'], errors='coerce').fillna(0)
                    credit = pd.to_numeric(df_ws['Credit'], errors='coerce').fillna(0)
                    df_ws['SignedAmount'] = (charges - credit).round(2)
                else:
                    print("No usable amount/value columns found in RECONCILIATION.dat. Cannot continue.")
                    df_ws['SignedAmount'] = pd.NA
                # Only keep Date and SignedAmount, drop NA
                df_ws = df_ws[['Date', 'SignedAmount']].dropna()

                # 5. Left-anti join: df_ws rows whose (Date, SignedAmount) not in df_ledger
                df_ledger['Key'] = df_ledger['Date'] + '_' + df_ledger['SignedAmount'].map(lambda x: f"{x:.2f}")
                df_ws['Key'] = df_ws['Date'] + '_' + df_ws['SignedAmount'].map(lambda x: f"{x:.2f}")
                missing = df_ws[~df_ws['Key'].isin(df_ledger['Key'])].copy().drop(columns=['Key'])

                # 6. Save to files and preview
                missing.to_csv('files/missing_transactions_v2.csv', index=False)
                missing.to_csv('files/missing_transactions_v2.dat', index=False, sep='|')
                n_preview = min(20, len(missing))
                if n_preview == 0:
                    print("No missing worksheet transactions found for GL_Code 2021. All are present in the ledger.")
                else:
                    print(missing.head(n_preview).to_markdown(index=False))
                    if len(missing) > 20:
                        print(f"\nNote: Showing first 20 of {len(missing)} rows.")
````

Output:

No rows found in generalledger_june2025.dat for GL_Code 2021. No further processing possible.

