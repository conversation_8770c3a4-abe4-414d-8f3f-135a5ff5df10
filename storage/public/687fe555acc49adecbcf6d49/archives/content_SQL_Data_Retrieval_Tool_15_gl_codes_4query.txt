SQL Query:

````
SELECT DISTINCT
  GeneralLedger_Code AS GL_Code,
  GeneralLedger_ChartName AS GL_ChartName
FROM GeneralLedgerDetail
WHERE GeneralLedger_Code IN ('1025', '2000', '2021', '2030')
ORDER BY GL_Code;
````

Data retrieved successfully:

|   GL_Code | GL_ChartName                             |
|----------:|:-----------------------------------------|
|      1025 | Cash                                     |
|      2000 | Accounts payable and accrued liabilities |
|      2021 | Accounts payable and accrued liabilities |
|      2030 | Accounts payable and accrued liabilities |