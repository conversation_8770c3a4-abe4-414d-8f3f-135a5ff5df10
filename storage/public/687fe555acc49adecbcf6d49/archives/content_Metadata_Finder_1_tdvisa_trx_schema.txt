Metadata of all data sources:

````
[
  {
    "data_source_name": "FieldServio",
    "selected_table_column_metadata": {
      "Payments": {
        "table_name": "Payments",
        "fields": {
          "Payment_ID": {
            "name": "Payment_ID",
            "description": "A unique identifier for each payment entry, ensuring distinct reference for every transaction within the Payments table.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "3073833",
              "3123445",
              "3149130",
              "3167630",
              "3228503",
              "and 14086 more..."
            ]
          },
          "Payment_EntityType": {
            "name": "Payment_EntityType",
            "description": "Categorizes the payment type, with possible values including 'Bill', 'Invoice', 'Customer Credit', 'Other', and 'A/R Deposit', clarifying the nature of the payment.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Bill",
              "Invoice",
              "Customer Credit",
              "Other",
              "A/R Deposit"
            ]
          },
          "Payment_BankingTransactionID": {
            "name": "Payment_BankingTransactionID",
            "description": "Links to a specific banking transaction; this field is absent in about 26% of records and may be shared among multiple payments.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1479375",
              "1503653",
              "1526219",
              "1566962",
              "1574349",
              "and 5261 more..."
            ]
          },
          "Payment_InvoiceId": {
            "name": "Payment_InvoiceId",
            "description": "Connects the payment to a specific invoice; present in only 52% of records, suggesting that multiple payments can be associated with one invoice.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "2579638",
              "2579639",
              "2579640",
              "2579642",
              "2579945",
              "and 6325 more..."
            ]
          },
          "Payment_InvoiceName": {
            "name": "Payment_InvoiceName",
            "description": "Holds the name associated with the invoice linked to the payment, missing in about 48% of records and often shared among related payments.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "CHQ 2422",
              "0000148391",
              "EFT",
              "8380-1",
              "10364-1",
              "and 6316 more..."
            ]
          },
          "Payment_InvoiceCustomerID": {
            "name": "Payment_InvoiceCustomerID",
            "description": "Identifies the customer related to the invoice for the payment; this field is absent in 48% of records, indicating multiple payments may link to the same customer.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "380124",
              "380204",
              "380230",
              "380356",
              "380428",
              "and 1079 more..."
            ]
          },
          "Payment_BillID": {
            "name": "Payment_BillID",
            "description": "Indicates the ID of the corresponding bill for the payment; populated for less than half of the records, typically unique for pairs of records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1529163",
              "1540163",
              "1541672",
              "1556201",
              "1557223",
              "and 5517 more..."
            ]
          },
          "Payment_BillReference": {
            "name": "Payment_BillReference",
            "description": "Contains a reference name for the bill associated with the payment, available for fewer than 50% of records and usually shared among related payments.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "6916424",
              "6917614",
              "6922873",
              "6943680",
              "6955498",
              "and 4847 more..."
            ]
          },
          "Payment_BillVendorID": {
            "name": "Payment_BillVendorID",
            "description": "Identifies the vendor linked to the bill; this field is present in less than 50% of records and typically unique across groups of records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "81061",
              "81111",
              "81233",
              "81247",
              "81297",
              "and 358 more..."
            ]
          },
          "Payment_PaymentAmount": {
            "name": "Payment_PaymentAmount",
            "description": "Records the total amount of the payment as a monetary value, which may include both positive and negative figures reflecting adjustments.",
            "dataType": "money",
            "is_unstructured": false
          },
          "Payment_CurrencyName": {
            "name": "Payment_CurrencyName",
            "description": "Specifies the currency in which the payment is made, with the majority in Canadian Dollars and a smaller portion in US Dollars.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "Canadian Dollars",
              "US Dollars"
            ]
          },
          "Payment_CurrencyRate": {
            "name": "Payment_CurrencyRate",
            "description": "Represents the applicable exchange rate for the payment, always a positive decimal value typically below 100.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "Payment_CurrencyAmount": {
            "name": "Payment_CurrencyAmount",
            "description": "Shows the payment amount in an alternative currency, which may be positive or negative, indicating conversion or adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "Payment_CreatedDate": {
            "name": "Payment_CreatedDate",
            "description": "Records the date and time when the payment entry was created, aiding the tracking of payment history.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_PaymentDate": {
            "name": "Payment_PaymentDate",
            "description": "Denotes the actual date on which the payment was processed, essential for financial tracking and reporting.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_ProcessedDate": {
            "name": "Payment_ProcessedDate",
            "description": "Captures the date when the payment was fully processed, critical for auditing purposes and payment completion tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_UpdatedDate": {
            "name": "Payment_UpdatedDate",
            "description": "Indicates the last date when the payment record was modified, facilitating version control and change tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_PaymentRecordSource": {
            "name": "Payment_PaymentRecordSource",
            "description": "Provides context on the source or method through which the payment record was generated.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Paid a bill.",
              "Paid an invoice.",
              "Migrated from Invoices_Credits",
              "Exchange Rate Adjustment",
              "Other Deposit Record.",
              "Payment from Account.",
              "Credit Offset Applied",
              "Credit for Cash Deposit.",
              "Customer Credit - Refund Credit",
              "Auto Payment to RMA"
            ]
          },
          "Payment_PaymentNotes": {
            "name": "Payment_PaymentNotes",
            "description": "Allows for additional notes or comments related to the payment; typically contains unique information relevant to grouped records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "",
              "Auto Payment for Process Internal Job Type",
              "EFT",
              "Spot Rate Change",
              "AP Batch: 12329",
              "and 4365 more..."
            ]
          },
          "Payment_CreatedBy": {
            "name": "Payment_CreatedBy",
            "description": "Indicates the user who created the payment record, bolstering accountability and tracking of user actions.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Tatiana Sanchez",
              "Sheena Rafferty",
              "Hetal  Adalja",
              "Evelyn Guerra",
              "Purita Janolo",
              "Limuel Fidelino",
              "Kelly Clarke",
              "Lylgenia Binua",
              "Jonathan  Miranda",
              "Lana Struc",
              "Support User",
              "Carlos Padilla",
              "Virgil Bougie"
            ]
          },
          "Payment_TypeName": {
            "name": "Payment_TypeName",
            "description": "Classifies the payment method, with options including 'Check', 'Manual', 'EFT', 'Visa', 'MasterCard', and more, reflecting the various payment processing methods.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Manual",
              "EFT",
              "Credit Offset",
              "Check",
              "Exchange Rate Adjustment",
              "Visa",
              "MasterCard",
              "Cash"
            ]
          },
          "Payment_Status": {
            "name": "Payment_Status",
            "description": "Indicates the current state of the payment, with most records marked as 'Processed' and a small percentage as 'Voided', offering insight into payment completion.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Processed",
              "Voided"
            ]
          }
        }
      },
      "VendorBills": {
        "table_name": "VendorBills",
        "fields": {
          "VendorBills_BillId": {
            "name": "VendorBills_BillId",
            "description": "A unique integer identifier for each bill record, ensuring distinct referencing of entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1486017",
              "1500576",
              "1502251",
              "1511492",
              "1530883",
              "and 6063 more..."
            ]
          },
          "VendorBills_ReferenceNumber": {
            "name": "VendorBills_ReferenceNumber",
            "description": "A varchar field that holds a reference number associated with the bill, typically unique among related bills.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "799284",
              "814001",
              "6917614",
              "6924753",
              "6932270",
              "and 5284 more..."
            ]
          },
          "VendorBills_Total": {
            "name": "VendorBills_Total",
            "description": "A decimal field representing the overall amount of the bill, accommodating both positive and negative values for credits or adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_SubTotal": {
            "name": "VendorBills_SubTotal",
            "description": "A decimal value showing the subtotal amount before tax and adjustments, which can also be positive or negative.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_TaxAmount": {
            "name": "VendorBills_TaxAmount",
            "description": "A decimal field indicating the tax amount applied to the bill, flexible to accommodate positive and negative values for tax reversals.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_AmountPaid": {
            "name": "VendorBills_AmountPaid",
            "description": "A decimal field recording the amount paid towards the bill, which can be positive for payments or negative for refunds.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_Balance": {
            "name": "VendorBills_Balance",
            "description": "The outstanding balance on the bill after payments, which can be either positive or negative, indicating amounts still owed.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyTotal": {
            "name": "VendorBills_CurrencyTotal",
            "description": "This decimal field captures the total amount of the bill in the specified currency, allowing for multi-currency transactions.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencySubTotal": {
            "name": "VendorBills_CurrencySubTotal",
            "description": "Reflects the subtotal amount in the specified currency, accommodating both positive and negative values.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyTax": {
            "name": "VendorBills_CurrencyTax",
            "description": "Indicates the tax amount in the specified currency, similar to the TaxAmount field, and can also be positive or negative.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyPaid": {
            "name": "VendorBills_CurrencyPaid",
            "description": "Captures the amount paid in the specified currency, allowing for both positive payments and negative adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyBalance": {
            "name": "VendorBills_CurrencyBalance",
            "description": "Represents the remaining balance in the specified currency after accounting for payments, which can be positive or negative.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_BillDate": {
            "name": "VendorBills_BillDate",
            "description": "A datetime field recording the date when the bill was issued, crucial for financial tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_DiscountDate": {
            "name": "VendorBills_DiscountDate",
            "description": "A datetime field indicating when discounts were applied to the bill, with potential missing values for some records.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_DateCreated": {
            "name": "VendorBills_DateCreated",
            "description": "A datetime field that captures when the bill record was created, essential for auditing purposes.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_DueDate": {
            "name": "VendorBills_DueDate",
            "description": "A datetime field showing the payment due date for the bill, important for managing accounts payable.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_SupplierID": {
            "name": "VendorBills_SupplierID",
            "description": "An integer foreign key linking to the SupplierInformation table, indicating the supplier associated with the bill.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "81061",
              "81073",
              "81079",
              "81111",
              "81113",
              "and 372 more..."
            ]
          },
          "VendorBills_SupplierName": {
            "name": "VendorBills_SupplierName",
            "description": "A varchar field holding the name of the supplier related to the bill, typically unique within groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Ingersoll-Rand (USD)",
              "Generac Power Systems, Inc.",
              "Geo. H. Young & Co. Ltd.",
              "Tom Beggs Agencies",
              "Oil Mart Ltd",
              "and 369 more..."
            ]
          },
          "VendorBills_Status": {
            "name": "VendorBills_Status",
            "description": "A varchar field representing the current processing state of the bill, with values such as 'Paid in Full', 'Approved', or 'Pending Approval'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Paid in Full",
              "Approved",
              "Pending Approval",
              "Voided"
            ]
          },
          "VendorBills_Memo": {
            "name": "VendorBills_Memo",
            "description": "A varchar field for additional comments or notes related to the bill, generally unique within groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Import",
              "Items Returned to supplier",
              "",
              "Bill Created for PO: SO2116",
              "Bill Created for PO: SO1117",
              "and 4197 more..."
            ]
          },
          "VendorBills_Items_ID": {
            "name": "VendorBills_Items_ID",
            "description": "An integer foreign key linking to the Items table, with potential missing values in a subset of records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "2825095",
              "2825758",
              "2825785",
              "2826223",
              "2826327",
              "and 2770 more..."
            ]
          },
          "VendorBills_Items_Name": {
            "name": "VendorBills_Items_Name",
            "description": "A nvarchar field holding the name of the item associated with the bill, with possible missing values in some records.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "Subset of values": [
              "23424922",
              "38035531",
              "38459582",
              "39911631",
              "92692284",
              "and 2768 more..."
            ]
          },
          "VendorBills_Item_Description": {
            "name": "VendorBills_Item_Description",
            "description": "A nvarchar field providing item descriptions, which may be missing in a significant number of records.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "Subset of values": [
              "Freight Charges",
              "Oil Filter",
              "Air Filter",
              "Separator",
              "Fuel Filter",
              "and 2103 more..."
            ]
          },
          "VendorBills_Item_Manufacturer": {
            "name": "VendorBills_Item_Manufacturer",
            "description": "A varchar field recording the item's manufacturer, with limited data availability in the records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Generac",
              "Ingersoll Rand",
              "N/A",
              "CUMMINS",
              "Toromont Cat",
              "CAG",
              "Kaeser",
              "Atlas Copco",
              "KOHLER",
              "SULLAIR",
              "DeVillbiss/DeVair",
              "Gardner Denver",
              "Detroit Diesel",
              "Chicago Pneumatic",
              "Omega",
              "Eaton",
              "MTU",
              "Nano",
              "Hankison",
              "Asco"
            ]
          },
          "VendorBills_Items_Quantity": {
            "name": "VendorBills_Items_Quantity",
            "description": "A decimal field indicating the quantity of items on the bill, essential for inventory management.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_Items_Cost": {
            "name": "VendorBills_Items_Cost",
            "description": "A decimal field storing the costs of items associated with vendor bills, capable of holding both positive and negative values.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_JobID": {
            "name": "VendorBills_JobID",
            "description": "An integer representing the unique identifier for jobs linked to vendor bills, with values available in less than half of the records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1827955",
              "1834090",
              "1841923",
              "1842402",
              "1845449",
              "and 1937 more..."
            ]
          },
          "VendorBills_JobName": {
            "name": "VendorBills_JobName",
            "description": "A varchar field indicating the name of the job related to the vendor bill, with limited data availability.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "PS-4058635",
              "PS-4016174",
              "PS-4178671",
              "PS-4113259",
              "PS-4204386",
              "and 1936 more..."
            ]
          },
          "VendorBills_POID": {
            "name": "VendorBills_POID",
            "description": "An integer serving as a unique identifier for purchase orders linked to the vendor bills, with some records missing values.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "5406644",
              "5431196",
              "5443464",
              "5467506",
              "5596817",
              "and 6886 more..."
            ]
          },
          "VendorBills_POName": {
            "name": "VendorBills_POName",
            "description": "A varchar field holding the name of the purchase order related to the vendor bill, with potential missing values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "SO2116",
              "SO1117",
              "SO1591",
              "SO2260",
              "SO1239",
              "and 2338 more..."
            ]
          },
          "VendorBills_VendorBillID": {
            "name": "VendorBills_VendorBillID",
            "description": "An integer uniquely identifying each vendor bill, with some records having missing values.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1486017",
              "1500576",
              "1502251",
              "1511492",
              "1530883",
              "and 2898 more..."
            ]
          },
          "VendorBills_OfficeID": {
            "name": "VendorBills_OfficeID",
            "description": "An integer categorizing the office associated with the vendor bill, with specific office values.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "419",
              "420",
              "421"
            ]
          },
          "VendorBills_Office": {
            "name": "VendorBills_Office",
            "description": "A varchar field describing the office related to the vendor bill, potentially categorical.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "MB Office",
              "SK Office",
              "Shared Services"
            ]
          },
          "VendorBills_CreatedBy": {
            "name": "VendorBills_CreatedBy",
            "description": "A varchar field indicating the user or entity responsible for creating the vendor bill.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Tatiana Sanchez",
              "Bryce McDonald",
              "Virgil Bougie",
              "Josh Dahlgrin",
              "Katie Munro",
              "and 25 more..."
            ]
          },
          "VendorBills_TaxAuthorityId": {
            "name": "VendorBills_TaxAuthorityId",
            "description": "An integer identifier for the tax authority associated with the vendor bill, with some records missing this value.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "28037",
              "28039",
              "28041",
              "28042",
              "28043",
              "and 1 more..."
            ]
          },
          "VendorBills_TaxAuthorityName": {
            "name": "VendorBills_TaxAuthorityName",
            "description": "A varchar field specifying the name of the tax authority related to the vendor bill, with potential missing values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "GST 5%",
              "ON HST 13%",
              "MB PST 7%",
              "Tax Exempt",
              "SK PST 6%",
              "USD GST 5%"
            ]
          },
          "VendorBills_TaxScheduleName": {
            "name": "VendorBills_TaxScheduleName",
            "description": "A varchar field denoting the tax schedule related to the vendor bill, with some records having missing values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "GST Only",
              "Both taxes exempt",
              "MB - Sales Tax - GST 5% + PST 7%",
              "ON - Sales Tax - HST 13%",
              "MB - Sales Tax - GST 5%",
              "SK - Sales Tax - GST 5% + PST 6%",
              "SK - Sales Tax - GST 5%",
              "USD GST Only",
              "SK - Sales Tax - PST 6%",
              "MB - Sales Tax - PST 7%"
            ]
          },
          "VendorBills_State": {
            "name": "VendorBills_State",
            "description": "A varchar field indicating the state related to the vendor bill, with dominant values for specific states.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Ma",
              "SK"
            ]
          },
          "VendorBills_TaxPaid": {
            "name": "VendorBills_TaxPaid",
            "description": "A decimal field reflecting the tax amount paid associated with the vendor bill, with many records missing this value.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_BillTypeID": {
            "name": "VendorBills_BillTypeID",
            "description": "An integer identifying the type of bill, with a majority of records falling under a few specific values.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2"
            ]
          },
          "VendorBills_InvType": {
            "name": "VendorBills_InvType",
            "description": "A varchar field describing the type of invoice associated with the vendor bill, with many records missing values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "W",
              "I",
              "R"
            ]
          },
          "VendorBills_InvStatus": {
            "name": "VendorBills_InvStatus",
            "description": "A varchar field indicating the status of the invoice related to the vendor bill, with many records having missing values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "X",
              "A"
            ]
          }
        }
      },
      "GeneralLedgerDetail": {
        "table_name": "GeneralLedgerDetail",
        "fields": {
          "GeneralLedger_ChartType": {
            "name": "GeneralLedger_ChartType",
            "description": "Categorizes the account type in the general ledger, with possible values including Revenues, Assets, Liabilities, Expenses, and Shareholder Equity.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Assets",
              "Liabilities",
              "Expenses",
              "Revenues",
              "Shareholder Equity"
            ]
          },
          "GeneralLedger_ChartName": {
            "name": "GeneralLedger_ChartName",
            "description": "Descriptive label for the account as it appears in the general ledger, providing clarity on the account's purpose.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Accounts payable and accrued liabilities",
              "Inventory",
              "Cash",
              "Accounts receivable",
              "Revenue",
              "and 37 more..."
            ]
          },
          "GeneralLedger_SubTypeName": {
            "name": "GeneralLedger_SubTypeName",
            "description": "Further classifies the account to give specific insights into the nature of transactions recorded within it.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Accounts Payable",
              "Accounts receivable",
              "Inventory - Work-in-Progress",
              "Inventory Clearing",
              "A/R - Clearing",
              "and 160 more..."
            ]
          },
          "GeneralLedger_Code": {
            "name": "GeneralLedger_Code",
            "description": "A unique identifier for each account in the general ledger, essential for transaction categorization.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "1020",
              "1025",
              "1100",
              "1300",
              "1710",
              "and 160 more..."
            ]
          },
          "GeneralLedger_Amount": {
            "name": "GeneralLedger_Amount",
            "description": "Represents the total monetary value of the transaction, accommodating both positive and negative values for credits and debits.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "GeneralLedger_Debit": {
            "name": "GeneralLedger_Debit",
            "description": "The amount debited in the transaction, indicating increases in assets or expenses, or decreases in liabilities or equity.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "GeneralLedger_Credit": {
            "name": "GeneralLedger_Credit",
            "description": "The amount credited in the transaction, reflecting decreases in assets or expenses and increases in liabilities or equity.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "GeneralLedger_TransactionID": {
            "name": "GeneralLedger_TransactionID",
            "description": "A unique identifier for the transaction, typically shared among three related records, aiding in tracking entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "33280705",
              "33343220",
              "33762442",
              "34110228",
              "34261956",
              "and 34631 more..."
            ]
          },
          "GeneralLedger_TransactionDate": {
            "name": "GeneralLedger_TransactionDate",
            "description": "Records the date and time of the transaction, critical for tracking financial activity timelines.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "GeneralLedger_TransactionType": {
            "name": "GeneralLedger_TransactionType",
            "description": "Indicates the nature of the transaction, including various processes like invoices, payments, and adjustments.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Process Invoice - A/R",
              "Bill Approved",
              "Bill - Add Payment",
              "Invoice - Add Payment",
              "Bill Added - Bill Date Change",
              "and 61 more..."
            ]
          },
          "GeneralLedger_CreatedByName": {
            "name": "GeneralLedger_CreatedByName",
            "description": "The name of the individual who created the transaction entry, ensuring accountability and traceability.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Tatiana Sanchez",
              "Sheena Rafferty",
              "Evelyn Guerra",
              "Hetal  Adalja",
              "Limuel Fidelino",
              "and 39 more..."
            ]
          },
          "GeneralLedger_OfficeID": {
            "name": "GeneralLedger_OfficeID",
            "description": "Identifier for the office linked to the transaction, aiding in the categorization of transactions by location.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "419",
              "420",
              "421"
            ]
          },
          "GeneralLedger_OfficeAcctCode": {
            "name": "GeneralLedger_OfficeAcctCode",
            "description": "Code representing the financial account associated with the office, utilized for reporting and analysis.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "100",
              "200",
              "300"
            ]
          },
          "GeneralLedger_OfficeName": {
            "name": "GeneralLedger_OfficeName",
            "description": "The name of the office related to the transaction, providing context for the transaction's origin.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "MB Office",
              "SK Office",
              "Shared Services"
            ]
          },
          "GeneralLedger_DepartmentID": {
            "name": "GeneralLedger_DepartmentID",
            "description": "Identifier for the department associated with the transaction, available for less than half of the records.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "496",
              "497",
              "498"
            ]
          },
          "GeneralLedger_DepartmentName": {
            "name": "GeneralLedger_DepartmentName",
            "description": "Name of the department linked to the transaction, available for less than half of the records.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "Compressor",
              "Generator",
              "Rental "
            ]
          },
          "GeneralLedger_DepartmentAcctCode": {
            "name": "GeneralLedger_DepartmentAcctCode",
            "description": "Code representing the financial account of the department, available for less than half of the records.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "10",
              "20",
              "30"
            ]
          },
          "GeneralLedger_CustomerId": {
            "name": "GeneralLedger_CustomerId",
            "description": "Identifier for the customer associated with the transaction, connecting specific clients to their transactions.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "380079",
              "380204",
              "380230",
              "380504",
              "and 1115 more..."
            ]
          },
          "GeneralLedger_CustomerName": {
            "name": "GeneralLedger_CustomerName",
            "description": "Name of the customer involved in the transaction, providing clarity on the financial interaction.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "None",
              "Genrep Ltd.",
              "City of Winnipeg",
              "BPL Sales Ltd.",
              "PCS Inc. (Nutrien)",
              "and 1111 more..."
            ]
          },
          "GeneralLedger_CustomerAccountNum": {
            "name": "GeneralLedger_CustomerAccountNum",
            "description": "Account number for the customer related to the transaction, essential for financial tracking.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "None",
              "GEN10792",
              "CIT10642",
              "BPL10579",
              "PCS10285",
              "and 1115 more..."
            ]
          },
          "GeneralLedger_LocationID": {
            "name": "GeneralLedger_LocationID",
            "description": "Identifier for the location associated with the transaction, available for less than half of the records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "658879",
              "658884",
              "658898",
              "658900",
              "658913",
              "and 1366 more..."
            ]
          },
          "GeneralLedger_LocationName": {
            "name": "GeneralLedger_LocationName",
            "description": "Name of the location linked to the transaction, available for less than half of the records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "City of Winnipeg - Fleet Management Agency",
              "TOM BEGGS (USD)",
              "MacDon Industries Ltd.",
              "GFL Environmental",
              "Winnipeg Transit",
              "and 1290 more..."
            ]
          },
          "GeneralLedger_InvoiceID": {
            "name": "GeneralLedger_InvoiceID",
            "description": "Identifier for the invoice associated with the transaction, typically unique for a group of 15 related records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "2611890",
              "2616687",
              "2618075",
              "2619167",
              "2635537",
              "and 7269 more..."
            ]
          },
          "GeneralLedger_InvoiceVoidedDate": {
            "name": "GeneralLedger_InvoiceVoidedDate",
            "description": "Date when the invoice was voided, available for a small percentage of records.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "GeneralLedger_InvoiceDate": {
            "name": "GeneralLedger_InvoiceDate",
            "description": "Date when the invoice was issued, providing context for billing timelines.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "GeneralLedger_InvoiceSentDate": {
            "name": "GeneralLedger_InvoiceSentDate",
            "description": "Records the date and time when the invoice was sent, available for less than half of the records.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "GeneralLedger_InvoiceSentTransactionDate": {
            "name": "GeneralLedger_InvoiceSentTransactionDate",
            "description": "Transaction date associated with the invoice being sent, recorded as datetime.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "GeneralLedger_InvoiceDueDate": {
            "name": "GeneralLedger_InvoiceDueDate",
            "description": "Due date for the invoice, indicating when payment is expected, recorded as datetime.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "GeneralLedger_InvoiceName": {
            "name": "GeneralLedger_InvoiceName",
            "description": "Name or identifier of the invoice, typically unique for a group of 15 records, available for less than half of the records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "8488-1",
              "8637-1",
              "4474-1",
              "9819-1",
              "5666-1",
              "and 7268 more..."
            ]
          },
          "GeneralLedger_InvoiceNotes": {
            "name": "GeneralLedger_InvoiceNotes",
            "description": "Additional notes related to the invoice, available for less than half of the records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "\n\rProposal Terms and Conditions\r\n\r\nGST#100095942RT0001\r\n\r\nThe presented proposal is preliminary; additional components may be necessary and are extra.\r\n\r\nApplicable shipping fees may be incurred, with charges payable based on the actual freight cost.\r\n\r\nAir Unlimited does not assume liability for delays in freight and transit\r\n\r\nThis proposal remains valid for 15 days from proposal date and it subject to potential alterations.\r\n\r\nFor clients with an established account, our standard payment terms apply. If you do not have an established account, prepayment will be required prior to parts ordered and/or the commencement of services.\r\n\r\nNOTES: All credit card payments are subject to a 3% processing fee\r\n\r\nUnits Return Policy - No returns on units allowed.\r\n\r\nParts Return Policy - Please refer to below webpage.\r\n\r\nFor all other details please refer to Terms & Conditions | Air Unlimited Inc. ",
              "\n\rTerms and Conditions\r\n \r\nTERMS: \r\nPayment terms net 30 days unless otherwise noted above. Overdue accounts charged interest of 2% per month (24% per annum) on unpaid balances. \r\n\r\nNOTES:  \r\nAll credit card payments are subject to a 3% processing fee. \r\n\r\nUnits Return Policy - No returns on units allowed. \r\n\r\nParts Return Policy - Please refer to below webpage.  \r\n\r\nFor all other details please refer to www.airunlimited.ca/termsandconditions  ",
              "",
              "\n\rGST#100095942RT0001\r\n\r\nThe presented estimation is preliminary; additional components might be necessary.\r\n\r\nApplicable shipping fees may be incurred, with charges payable based on actual freight cost.\r\n\r\nAir Unlimited does not assume liability for delays in freight and transit.\r\n\r\nThis estimate remains valid for 15 days and is subject to potential alterations. \r\n\r\nTERMS: For clients with an established account, our standard payment terms apply. If you do not have an established account, prepayment will be required prior to the commencement of services.\r\n\r\nAll credit card payments are subject to a 3% processing fee.\r\n\r\nUnits Return Policy - No returns on units allowed.\r\n\r\nParts Return Policy - Please refer to below webpage.\r\n\r\nFor all other details please refer to https://www.airunlimited.ca/terms-and-conditions",
              "\n\rGST# 100095942RT0001\r\n\r\nTerms and Conditions\r\n \r\nTERMS: \r\nPayment terms net 30 days unless otherwise noted above. Overdue accounts charged interest of 2% per month (24% per annum) on unpaid balances. \r\n\r\nNOTES:  \r\nAll credit card payments are subject to a 3% processing fee. \r\n\r\nUnits Return Policy - No returns on units allowed. \r\n\r\nParts Return Policy - Please refer to below webpage.  \r\n\r\nFor all other details please refer to www.airunlimited.ca/termsandconditions  ",
              "and 1182 more..."
            ]
          },
          "GeneralLedger_JobID": {
            "name": "GeneralLedger_JobID",
            "description": "Unique identifier for a job linked to the invoice, available for less than half of the records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1826573",
              "1827300",
              "1827353",
              "1829579",
              "1830152",
              "and 3881 more..."
            ]
          },
          "GeneralLedger_JobName": {
            "name": "GeneralLedger_JobName",
            "description": "Name of the job connected to the invoice, with limited availability of data.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "ES-3883061",
              "ESP-3704176",
              "SR-3759380",
              "ESP-3699163",
              "ESP-3717535",
              "and 3881 more..."
            ]
          },
          "GeneralLedger_JobType": {
            "name": "GeneralLedger_JobType",
            "description": "Specifies the type of job related to the invoice, with limited data availability.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Part Sale-COM",
              "Service Request-COM",
              "PMA-COM",
              "PM Fixed Bid - GEN",
              "Service Request - GEN",
              "and 23 more..."
            ]
          },
          "GeneralLedger_JobDescription": {
            "name": "GeneralLedger_JobDescription",
            "description": "Description of the job associated with the invoice, typically unique for a group of 40 records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "",
              "Pick up",
              "Start up and commissioning - 8:30am",
              "Northern Lights Personal Care Home - Flin Flon, MB\r\nSD0200GG178.7D18HPNN3 / 3014205600\r\n\u00c3\u201aH07ATBA30800C5XM / 2483808-001WE\r\n",
              "Pickup",
              "and 2829 more..."
            ]
          },
          "GeneralLedger_InvoiceSalesPerson": {
            "name": "GeneralLedger_InvoiceSalesPerson",
            "description": "Indicates the salesperson responsible for the invoice, with limited data availability.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Niko Matias",
              "Virgil Bougie",
              "Katie Munro",
              "Kory Murphy",
              "Josh Dahlgrin",
              "Koralee Thompson",
              "Kyle Mclean",
              "Daelyn Boettcher",
              "Dallas Skiftun",
              "Benjamin Lawrence",
              "Brian Park",
              "Aleisha Mazier",
              "Juan Londono",
              "Jeff Gostick",
              "Shaylin Knot",
              "Conner Steer",
              "Cody Tanner",
              "Bea Jaime",
              "Robyne Lemieux",
              "Carlos Padilla",
              "Iryna Melncychuk"
            ]
          },
          "GeneralLedger_CustomerAssignedSalesPerson": {
            "name": "GeneralLedger_CustomerAssignedSalesPerson",
            "description": "Identifies the salesperson assigned to the customer, with very limited data availability.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Virgil Bougie",
              "Josh Dahlgrin",
              "Niko Matias",
              "Aleisha Mazier",
              "Koralee Thompson",
              "Daelyn Boettcher",
              "Katie Munro",
              "Dallas Skiftun",
              "Kory Murphy",
              "Brian Park",
              "Carlos Padilla",
              "Jeff Gostick",
              "Shaylin Knot",
              "Juan Londono",
              "Kyle Mclean"
            ]
          },
          "GeneralLedger_BillID": {
            "name": "GeneralLedger_BillID",
            "description": "Unique identifier for the bill related to the invoice, average uniqueness for a group of 18 records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1488079",
              "1488970",
              "1492960",
              "1520617",
              "1523810",
              "and 6045 more..."
            ]
          },
          "GeneralLedger_BillRefNumber": {
            "name": "GeneralLedger_BillRefNumber",
            "description": "Reference number for the bill, typically unique for a group of 21 records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "106324",
              "107200",
              "802673",
              "1631941",
              "1632800",
              "and 5249 more..."
            ]
          },
          "GeneralLedger_BillMemo": {
            "name": "GeneralLedger_BillMemo",
            "description": "Allows for additional memos relating to the bill, with limited data availability.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Items Returned to supplier",
              "",
              "Import",
              "Bill Created for PO: SO1059",
              "Bill Created for PO: SO2190",
              "and 4165 more..."
            ]
          },
          "GeneralLedger_BillStatus": {
            "name": "GeneralLedger_BillStatus",
            "description": "Indicates the current status of the bill, with possible values such as 'Paid in Full', 'Voided', or 'Pending Approval'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Paid in Full",
              "",
              "Voided",
              "Pending Approval"
            ]
          },
          "GeneralLedger_PaymentNotes": {
            "name": "GeneralLedger_PaymentNotes",
            "description": "Notes related to payments associated with the bill, with data available for less than half of the records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "",
              "Auto Payment for Process Internal Job Type",
              "EFT",
              "Spot Rate Change",
              "AP Batch: 12329",
              "and 2619 more..."
            ]
          },
          "GeneralLedger_Description": {
            "name": "GeneralLedger_Description",
            "description": "Description associated with the general ledger entry, typically unique for a group of 3 records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Average Costs",
              "Inventory Adjustment",
              "Trial Balance Import - AirUnlimited",
              "Bill 313088 & 316439 for Ingersoll-Rand Canada Sales & Service ULC",
              "Bill 796849&797042 for Oil Mart Ltd",
              "and 31307 more..."
            ]
          }
        }
      },
      "IncomeStatement": {
        "table_name": "IncomeStatement",
        "fields": {
          "IncomeStatement_GLID": {
            "name": "IncomeStatement_GLID",
            "description": "A unique integer identifier for each record in the Income Statement table, serving as the primary key to distinctly reference each entry.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "34281301",
              "34430329",
              "34838632",
              "35092503",
              "35249852",
              "and 28685 more..."
            ]
          },
          "IncomeStatement_CompanyName": {
            "name": "IncomeStatement_CompanyName",
            "description": "The name of the company linked to the income statement entry, primarily 'Winnipeg-Head Office' with a smaller portion for 'CHB Group', stored as a varchar.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Winnipeg-Head Office",
              "CHB Group"
            ]
          },
          "IncomeStatement_AccountType": {
            "name": "IncomeStatement_AccountType",
            "description": "A varchar field indicating whether the entry is classified as 'Expenses' or 'Revenues', with a majority of records categorized as Expenses.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Expenses",
              "Revenues"
            ]
          },
          "IncomeStatement_SubTypeID": {
            "name": "IncomeStatement_SubTypeID",
            "description": "An integer field that may provide further categorization of account types, though specific subtype details are not defined.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "30259",
              "30260",
              "30261",
              "30262",
              "30264",
              "and 83 more..."
            ]
          },
          "IncomeStatement_BaseAccountName": {
            "name": "IncomeStatement_BaseAccountName",
            "description": "A varchar field that describes the financial category at a high level, associated with the entry.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Standard labour expense",
              "Cost of sales - Parts",
              "Revenue - Parts",
              "Revenue - Service",
              "Shop supplies recovery",
              "and 83 more..."
            ]
          },
          "IncomeStatement_AccountCode": {
            "name": "IncomeStatement_AccountCode",
            "description": "A varchar field that assigns a specific code to the account for accounting purposes, facilitating transaction identification and categorization.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "4000",
              "4010",
              "4020",
              "4030",
              "4050",
              "and 83 more..."
            ]
          },
          "IncomeStatement_OfficeID": {
            "name": "IncomeStatement_OfficeID",
            "description": "An integer field representing the ID of the office linked to the record, with known values corresponding to different operational locations.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "419",
              "420",
              "421"
            ]
          },
          "IncomeStatement_OfficeName": {
            "name": "IncomeStatement_OfficeName",
            "description": "A varchar field naming the office related to the income statement entry, with values such as 'MB Office', 'Shared Services', and 'SK Office'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "MB Office",
              "SK Office",
              "Shared Services"
            ]
          },
          "IncomeStatement_DepartmentName": {
            "name": "IncomeStatement_DepartmentName",
            "description": "An nvarchar field indicating the department relevant to the entry, noting that around 34% of records are missing this value.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "Compressor",
              "Generator",
              "Rental "
            ]
          },
          "IncomeStatement_OfficeAccountingCode": {
            "name": "IncomeStatement_OfficeAccountingCode",
            "description": "An nvarchar field providing accounting codes specific to the office, with possible values like 100, 300, and 200.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "100",
              "200",
              "300"
            ]
          },
          "IncomeStatement_DepartmentAccountingCode": {
            "name": "IncomeStatement_DepartmentAccountingCode",
            "description": "An nvarchar field providing accounting codes specific to the department, with about 34% of records missing this information.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "10",
              "20",
              "30"
            ]
          },
          "IncomeStatement_AccountName": {
            "name": "IncomeStatement_AccountName",
            "description": "A varchar field specifying the name of the account associated with the entry, offering additional context for the financial transaction.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "5200-10-100 Standard labour expense",
              "5200-20-100 Standard labour expense",
              "5010-20-100 Cost of sales - Parts",
              "4010-20-100 Revenue - Parts",
              "5070-000-100 Inventory variance",
              "and 314 more..."
            ]
          },
          "IncomeStatement_GroupName": {
            "name": "IncomeStatement_GroupName",
            "description": "A varchar field that categorizes the entry into broader financial groups, enhancing financial analysis and reporting.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Revenue",
              "Cost of sales",
              "Standard labour",
              "Foreign exchange gain/loss",
              "Wages and subcontractors",
              "Management fee",
              "Rent and common area",
              "Travel",
              "IT and Software",
              "Automotive",
              "Office supplies",
              "Direct labour",
              "Interest and bank charges",
              "Meals and entertainment",
              "Telephone",
              "Bad debt expense",
              "Advertising and Promotion",
              "Safety",
              "Dues and subscriptions",
              "Professional fees",
              "Insurance",
              "Interest revenue",
              "Licenses and taxes",
              "Gain/loss on disposal of assets",
              "Amortization"
            ]
          },
          "IncomeStatement_Amount": {
            "name": "IncomeStatement_Amount",
            "description": "A decimal field representing the financial amount of the entry, which can be positive or negative, indicating revenues and expenses respectively.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "IncomeStatement_TransactionDate": {
            "name": "IncomeStatement_TransactionDate",
            "description": "A datetime field documenting the date and time of the financial transaction, providing temporal context for the entry.",
            "dataType": "datetime",
            "is_unstructured": false
          }
        }
      },
      "SupplierInformation": {
        "table_name": "SupplierInformation",
        "fields": {
          "Supplier_ID": {
            "name": "Supplier_ID",
            "description": "A unique integer identifier for each supplier, ensuring distinct reference for every supplier record.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "81017",
              "81019",
              "81021",
              "81023",
              "81025",
              "and 561 more..."
            ]
          },
          "Supplier_Name": {
            "name": "Supplier_Name",
            "description": "A unique variable character string representing the supplier's name, facilitating easy identification and retrieval.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Atlas Copco Compressors Canada",
              "Brandt Tractor Ltd.",
              "Toromont Cat",
              "Airtek Limited",
              "Air Unlimited Inc",
              "and 558 more..."
            ]
          },
          "Supplier_AccountNumber": {
            "name": "Supplier_AccountNumber",
            "description": "A unique variable character string that denotes the specific account number assigned to each supplier for account management.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "5001",
              "5004",
              "5008",
              "5011",
              "5012",
              "and 561 more..."
            ]
          },
          "Supplier_TaxID": {
            "name": "Supplier_TaxID",
            "description": "A variable character string for the supplier's tax identification number, generally unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "*********",
              "*********",
              "*********",
              "*********",
              "*********",
              "and 49 more..."
            ]
          },
          "Supplier_Terms": {
            "name": "Supplier_Terms",
            "description": "A variable character string that details the agreed payment terms with the supplier, using standardized categorical values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "COD",
              "N30",
              "Credit Card",
              "N07",
              "1% net 10",
              "2% net 15"
            ]
          },
          "Supplier_Category": {
            "name": "Supplier_Category",
            "description": "A variable character string that classifies the supplier, currently not holding specific values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              " "
            ]
          },
          "Supplier_QualityRating": {
            "name": "Supplier_QualityRating",
            "description": "A variable character string representing the supplier's quality rating, currently not holding specific values.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              " "
            ]
          },
          "Supplier_1099Type": {
            "name": "Supplier_1099Type",
            "description": "A variable character string indicating the type of 1099 reporting applicable to the supplier, currently set to 'null'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "None"
            ]
          },
          "Supplier_Notes": {
            "name": "Supplier_Notes",
            "description": "A variable character string for additional notes or details regarding the supplier, generally unique within small groups.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "Payment: EFT",
              "Payment: Wire",
              "Only for Banking Purposes - ***DO NOT USE THIS SUPPLIER*** ",
              "Payment: Cheque",
              "and 49 more..."
            ]
          },
          "Supplier_Email": {
            "name": "Supplier_Email",
            "description": "A variable character string containing the supplier's email address, typically unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "and 257 more..."
            ]
          },
          "Supplier_Website": {
            "name": "Supplier_Website",
            "description": "A variable character string for the supplier's website URL, usually unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "<EMAIL>",
              "and 89 more..."
            ]
          },
          "Supplier_Phone": {
            "name": "Supplier_Phone",
            "description": "A unique variable character string representing the primary contact number for the supplier, ensuring direct communication.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "************",
              "************",
              "************",
              "************",
              "and 480 more..."
            ]
          },
          "Supplier_Fax": {
            "name": "Supplier_Fax",
            "description": "A variable character string for the supplier's fax number, generally unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "************",
              "************",
              "************",
              "************",
              "and 30 more..."
            ]
          },
          "Supplier_Phone2": {
            "name": "Supplier_Phone2",
            "description": "A variable character string for an optional secondary phone number of the supplier, typically unique within small groups.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "************",
              "************",
              "************",
              "************",
              "and 31 more..."
            ]
          },
          "Supplier_Address": {
            "name": "Supplier_Address",
            "description": "A unique variable character string for the supplier's primary address, ensuring accurate location identification.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "2116 Logan Ave",
              "100-1025 South Belt Line Rd",
              "1407 Dugald Rd",
              "320-830 King Edward St",
              "and 519 more..."
            ]
          },
          "Supplier_Address2": {
            "name": "Supplier_Address2",
            "description": "A variable character string for a secondary address for the supplier, often unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              " ",
              "Station A",
              "Station M",
              "PO Box 4918 STN A",
              "Unit 1-4",
              "and 56 more..."
            ]
          },
          "Supplier_City": {
            "name": "Supplier_City",
            "description": "A variable character string representing the city of the supplier's location, generally unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Winnipeg",
              "Saskatoon",
              "Regina",
              " ",
              "Toronto",
              "and 135 more..."
            ]
          },
          "Supplier_State": {
            "name": "Supplier_State",
            "description": "A variable character string indicating the state where the supplier operates, usually unique within small groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "MB",
              "SK",
              "ON",
              "AB",
              " ",
              "and 32 more..."
            ]
          },
          "Supplier_Zip": {
            "name": "Supplier_Zip",
            "description": "A unique variable character string holding the postal code of the supplier's address for precise location identification.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "46360",
              "56701",
              "75019",
              " ",
              "R2R 0J2",
              "and 480 more..."
            ]
          },
          "Supplier_Internal": {
            "name": "Supplier_Internal",
            "description": "A variable character string indicating if the supplier is classified as internal, with predominant values of 'No' and '0'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "No"
            ]
          },
          "Supplier_Freight": {
            "name": "Supplier_Freight",
            "description": "A variable character string showing the freight status of the supplier, with most records indicating 'No'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "No",
              "Yes"
            ]
          },
          "Supplier_PaidACH": {
            "name": "Supplier_PaidACH",
            "description": "A variable character string indicating if the supplier is paid through ACH, which is consistently marked as 'No'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "No"
            ]
          },
          "Supplier_Currency": {
            "name": "Supplier_Currency",
            "description": "A variable character string specifying the currency for supplier transactions, predominantly Canadian Dollars.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "Canadian Dollars",
              "US Dollars"
            ]
          },
          "Supplier_Active": {
            "name": "Supplier_Active",
            "description": "A variable character string indicating the supplier's active status, with most records marked as 'Yes'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Yes",
              "No"
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n  \nTo identify credit-card or bank-account transaction data (specifically for TD Visa account 3774), I focused on tables tracking financial transactions (Payments, VendorBills, GeneralLedgerDetail, IncomeStatement). I prioritized columns for transaction date, description, amount (charge/credit), and any account identifier. Since there\u2019s no explicit \"ACCOUNT_MASTER\" or masking column, I included any columns that could store account numbers, references, payment types, or descriptions that may contain \"3774\" or \"Visa\". I also provide all columns from those tables for context, as requested. Business rules are noted where present. ER diagram and related tables are included per instructions.  \n  \n  \n**Table Columns:**   \n  \n- table: Payments  \n  columns: Payment_ID, Payment_EntityType, Payment_BankingTransactionID, Payment_InvoiceId, Payment_InvoiceName, Payment_InvoiceCustomerID, Payment_BillID, Payment_BillReference, Payment_BillVendorID, Payment_PaymentAmount, Payment_CurrencyName, Payment_CurrencyRate, Payment_CurrencyAmount, Payment_CreatedDate, Payment_PaymentDate, Payment_ProcessedDate, Payment_UpdatedDate, Payment_PaymentRecordSource, Payment_PaymentNotes, Payment_CreatedBy, Payment_TypeName, Payment_Status  \n  \n- table: VendorBills  \n  columns: VendorBills_BillId, VendorBills_ReferenceNumber, VendorBills_Total, VendorBills_SubTotal, VendorBills_TaxAmount, VendorBills_AmountPaid, VendorBills_Balance, VendorBills_CurrencyTotal, VendorBills_CurrencySubTotal, VendorBills_CurrencyTax, VendorBills_CurrencyPaid, VendorBills_CurrencyBalance, VendorBills_BillDate, VendorBills_DiscountDate, VendorBills_DateCreated, VendorBills_DueDate, VendorBills_SupplierID, VendorBills_SupplierName, VendorBills_Status, VendorBills_Memo, VendorBills_Items_ID, VendorBills_Items_Name, VendorBills_Item_Description, VendorBills_Item_Manufacturer, VendorBills_Items_Quantity, VendorBills_Items_Cost, VendorBills_JobID, VendorBills_JobName, VendorBills_POID, VendorBills_POName, VendorBills_VendorBillID, VendorBills_OfficeID, VendorBills_Office, VendorBills_CreatedBy, VendorBills_TaxAuthorityId, VendorBills_TaxAuthorityName, VendorBills_TaxScheduleName, VendorBills_State, VendorBills_TaxPaid, VendorBills_BillTypeID, VendorBills_InvType, VendorBills_InvStatus  \n  \n- table: GeneralLedgerDetail  \n  columns: GeneralLedger_ChartType, GeneralLedger_ChartName, GeneralLedger_SubTypeName, GeneralLedger_Code, GeneralLedger_Amount, GeneralLedger_Debit, GeneralLedger_Credit, GeneralLedger_TransactionID, GeneralLedger_TransactionDate, GeneralLedger_TransactionType, GeneralLedger_CreatedByName, GeneralLedger_OfficeID, GeneralLedger_OfficeAcctCode, GeneralLedger_OfficeName, GeneralLedger_DepartmentID, GeneralLedger_DepartmentName, GeneralLedger_DepartmentAcctCode, GeneralLedger_CustomerId, GeneralLedger_CustomerName, GeneralLedger_CustomerAccountNum, GeneralLedger_LocationID, GeneralLedger_LocationName, GeneralLedger_InvoiceID, GeneralLedger_InvoiceVoidedDate, GeneralLedger_InvoiceDate, GeneralLedger_InvoiceSentDate, GeneralLedger_InvoiceSentTransactionDate, GeneralLedger_InvoiceDueDate, GeneralLedger_InvoiceName, GeneralLedger_InvoiceNotes, GeneralLedger_JobID, GeneralLedger_JobName, GeneralLedger_JobType, GeneralLedger_JobDescription, GeneralLedger_InvoiceSalesPerson, GeneralLedger_CustomerAssignedSalesPerson, GeneralLedger_BillID, GeneralLedger_BillRefNumber, GeneralLedger_BillMemo, GeneralLedger_BillStatus, GeneralLedger_PaymentNotes, GeneralLedger_Description  \n  \n- table: IncomeStatement  \n  columns: IncomeStatement_GLID, IncomeStatement_CompanyName, IncomeStatement_AccountType, IncomeStatement_SubTypeID, IncomeStatement_BaseAccountName, IncomeStatement_AccountCode, IncomeStatement_OfficeID, IncomeStatement_OfficeName, IncomeStatement_DepartmentName, IncomeStatement_OfficeAccountingCode, IncomeStatement_DepartmentAccountingCode, IncomeStatement_AccountName, IncomeStatement_GroupName, IncomeStatement_Amount, IncomeStatement_TransactionDate  \n  \n- table: SupplierInformation (for cross-reference to vendor accounts if needed)  \n  columns: Supplier_ID, Supplier_Name, Supplier_AccountNumber, Supplier_TaxID, Supplier_Terms, Supplier_Category, Supplier_QualityRating, Supplier_1099Type, Supplier_Notes, Supplier_Email, Supplier_Website, Supplier_Phone, Supplier_Fax, Supplier_Phone2, Supplier_Address, Supplier_Address2, Supplier_City, Supplier_State, Supplier_Zip, Supplier_Internal, Supplier_Freight, Supplier_PaidACH, Supplier_Currency, Supplier_Active  \n  \n- ER diagram: See ER diagram section above for full relationships, including connections between Payments, VendorBills, SupplierInformation, and GeneralLedgerDetail.  \n  \n- Related reference tables:  \n  - SupplierInformation (as above; for payee/account cross-reference)  \n  - No explicit ACCOUNT_MASTER or TRANSACTION_STATUS tables are present in this schema. Statuses are encoded as columns such as Payment_Status, VendorBills_Status, GeneralLedger_BillStatus, etc.  \n  \n- Business Rules/Interpretation:  \n  - Positive/Negative Amounts:  \n    - Payments: Payment_PaymentAmount, Payment_CurrencyAmount can be positive (credits/receipts) or negative (debits/charges/adjustments).  \n    - VendorBills: VendorBills_Total and related fields can be positive (bills) or negative (credits/adjustments).  \n    - GeneralLedgerDetail: GeneralLedger_Amount, GeneralLedger_Debit, GeneralLedger_Credit: positive/negative per accounting standards.  \n    - IncomeStatement_Amount: positive for revenues, negative for expenses.  \n  - Posted vs Pending:  \n    - There is no explicit pending/posted field, but Payment_Status or VendorBills_Status (e.g., \"Processed\", \"Voided\", \"Pending Approval\") can be used to infer.  \n  - Account Numbers:  \n    - Payment_TypeName can indicate \"Visa\", \"MasterCard\", etc., but there is no dedicated masked card number field. In SupplierInformation, Supplier_AccountNumber could be a reference to vendor accounts. Transaction descriptions (GeneralLedger_Description, VendorBills_Memo) may include card/account identifiers (such as \"3774\").  \n  \n- Contextual columns:  \n  - All columns in the above tables included for context as requested.  \n  \n- No explicit masked or full card/account id field; search for \"3774\" or TD Visa in description, memo, reference, or account number columns.  \n  \n",
    "other_table_columns": {
      "AssetRevenue": [
        "AssetRevenue_AssetID",
        "AssetRevenue_AssetName",
        "AssetRevenue_ContractID",
        "AssetRevenue_ContractNumber",
        "AssetRevenue_ContractYear",
        "AssetRevenue_ContractMonth",
        "AssetRevenue_Revenue"
      ],
      "InventoryDetail": [
        "InventoryDetail_ID",
        "InventoryDetail_Type",
        "InventoryDetail_CreatedDate",
        "InventoryDetail_WarehouseID",
        "InventoryDetail_Warehouse",
        "InventoryDetail_BinID",
        "InventoryDetail_BinName",
        "InventoryDetail_ItemID",
        "InventoryDetail_Item_ItemLine",
        "InventoryDetail_Item",
        "InventoryDetail_ItemCode",
        "InventoryDetail_ItemInventoryCosting",
        "InventoryDetail_Item_ItemType",
        "InventoryDetail_Item_Category",
        "InventoryDetail_Item_Description",
        "InventoryDetail_Item_SupplierID",
        "InventoryDetail_Item_SupplierName",
        "InventoryDetail_Item_Price",
        "InventoryDetail_Item_PriceExtended",
        "InventoryDetail_Quantity",
        "InventoryDetail_Cost",
        "InventoryDetail_Item_CostExtended",
        "InventoryDetail_WarehouseMaxQty",
        "InventoryDetail_WarehouseMinQty",
        "InventoryDetail_WarehouseOffice",
        "InventoryDetail_Warehouse_ItemQty",
        "InventoryDetail_EquipmentID",
        "InventoryDetail_LocationID",
        "InventoryDetail_Equipment_SerialNumber",
        "InventoryDetail_Equipment_Make",
        "InventoryDetail_Equipment_Model",
        "InventoryDetail_Equipment_SoldCondition",
        "InventoryDetail_JobID",
        "InventoryDetail_JobItemId",
        "InventoryDetail_JobDateEntered",
        "InventoryDetail_JobCustomerID",
        "InventoryDetail_JobCustomer",
        "InventoryDetail_JobName",
        "InventoryDetail_JobStatus",
        "InventoryDetail_LastInvoiceDate",
        "InventoryDetail_InvoiceCount",
        "InventoryDetail_ReportRunDate"
      ],
      "EquipmentHours": [
        "EquipmentHours_EquipmentID",
        "EquipmentHours_Name",
        "EquipmentHours_Description",
        "EquipmentHours_Status",
        "EquipmentHours_SerialNumber",
        "EquipmentHours_StartDate",
        "EquipmentHours_WarrantyExpiration",
        "EquipmentHours_EndCustomerID",
        "EquipmentHours_EndCustomerName",
        "EquipmentHours_SoldCustomerID",
        "EquipmentHours_SoldCustomerName",
        "EquipmentHours_LocationID",
        "EquipmentHours_LocationName",
        "EquipmentHours_LocationAddress",
        "EquipmentHours_LocationCity",
        "EquipmentHours_LocationState",
        "EquipmentHours_LocationZip",
        "EquipmentHours_LocationDefaultJobOffice",
        "EquipmentHours_Hours",
        "EquipmentHours_HoursUpdatedDate",
        "EquipmentHours_JobCompletedDate"
      ],
      "AR_Pacing": [
        "ARPacing_OfficeID",
        "ARPacing_OfficeName",
        "ARPacing_AgingDate",
        "ARPacing_CurrentDue",
        "ARPacing_CreditTotal",
        "ARPacing_ThirtyDue",
        "ARPacing_SixtyDue",
        "ARPacing_NinetyDue",
        "ARPacing_BeyondNinetyDue",
        "ARPacing_PastDue",
        "ARPacing_TotalDue",
        "ARPacing_AverageInvoiceAge"
      ],
      "InventoryReturns": [
        "InventoryReturns_ReturnID",
        "InventoryReturns_SourceID",
        "InventoryReturns_DestinationID",
        "InventoryReturns_ItemID",
        "InventoryReturns_ActionTaken",
        "InventoryReturns_JobID",
        "InventoryReturns_JobName",
        "InventoryReturns_POName",
        "InventoryReturns_ReturnQueueType",
        "InventoryReturns_ReturnDestinationType",
        "InventoryReturns_ItemName",
        "InventoryReturns_ItemCode",
        "InventoryReturns_ItemDescription",
        "InventoryReturns_ReturnQuantity",
        "InventoryReturns_UnitCost",
        "InventoryReturns_ExtendedCost",
        "InventoryReturns_UnitPrice",
        "InventoryReturns_ExtendedPrice",
        "InventoryReturns_ReturnReason",
        "InventoryReturns_ReturnDate",
        "InventoryReturns_OfficeName",
        "InventoryReturns_WarehouseName",
        "InventoryReturns_ReturnedBy"
      ],
      "ContactInformation": [
        "Contact_ID",
        "Contact_CustomerID",
        "Contact_CompanyName",
        "Contact_CompanyType",
        "Contact_FirstName",
        "Contact_LastName",
        "Contact_Title",
        "Contact_Category",
        "Contact_Email",
        "Contact_Key",
        "Contact_isActive",
        "Contact_WorkPhone",
        "Contact_CellPhone",
        "Contact_Fax",
        "Contact_HomePhone",
        "Contact_Address1",
        "Contact_Address2",
        "Contact_City",
        "Contact_State",
        "Contact_Zip",
        "Contact_DateEntered",
        "Contact_EnteredBy",
        "Contact_SalesPerson",
        "Contact_SalesPerson_ID",
        "Contact_Other",
        "Contact_NextTel_ID",
        "Contact_Supplier_ID",
        "Contact_Supplier",
        "CON_ModifiedBy"
      ],
      "LocationInformation": [
        "Location_ID",
        "Location_Name",
        "Location_CustomerID",
        "Location_Customer_AccountNumber",
        "Location_CustomerName",
        "Location_Customer_Currency",
        "Location_Customer_Active",
        "Location_Customer_NextServiceDate",
        "Location_CustomerOffice",
        "Location_PreferredTechnician",
        "Location_Address1",
        "Location_Address2",
        "Location_City",
        "Location_State",
        "Location_Zip",
        "Location_Latitude",
        "Location_Longitude",
        "Location_DateEntered",
        "Location_EnteredBy",
        "Location_SalespersonPrimary",
        "Location_TaxSchedule",
        "Location_DefaultJobOffice"
      ],
      "PaylocityProductivity": [],
      "ContractInvoices": [
        "ContractInvoices_ContractID",
        "ContractInvoices_ContractNumber",
        "ContractInvoices_StartDate",
        "ContractInvoices_CustomerID",
        "ContractInvoices_CustomerName",
        "ContractInvoices_CustomerAccountNumber",
        "ContractInvoices_Office",
        "ContractInvoices_ContractAmount",
        "ContractInvoices_EstimatedServiceAmount",
        "ContractInvoices_RenewalDate",
        "ContractInvoices_SentTransactionDate",
        "ContractInvoices_Status",
        "ContractInvoices_Type",
        "ContractInvoices_InvoiceID",
        "ContractInvoices_InvoiceName",
        "ContractInvoices_InvoiceSource",
        "ContractInvoices_JobID",
        "ContractInvoices_JobName",
        "ContractInvoices_PO",
        "ContractInvoices_InvoiceTotalAmount",
        "ContractInvoices_PaidAmount",
        "ContractInvoices_InvoiceStatus",
        "ContractInvoices_InvoiceDate",
        "ContractInvoices_LastInvoiceDate",
        "ContractInvoices_InvoiceDueDate",
        "ContractInvoices_InvoiceSentDate",
        "ContractInvoices_InvoiceGLDate",
        "ContractInvoices_InvoiceItemID",
        "ContractInvoices_InvoiceBasicItemID",
        "ContractInvoices_InvoiceItemDescription",
        "ContractInvoices_InvoiceItemQuantity",
        "ContractInvoices_InvoiceItemUnitPrice",
        "ContractInvoices_InvoiceItemTaxAmount",
        "ContractInvoices_InvoiceItemTotalAmount",
        "ContractInvoices_TaxSchedule",
        "ContractInvoices_PaymentTerms"
      ],
      "InvoicesNotSent": [
        "InvoicesNotSent_InvoiceID",
        "InvoicesNotSent_InvoiceName",
        "InvoicesNotSent_InvoiceType",
        "InvoicesNotSent_InvoiceSource",
        "InvoicesNotSent_InvoiceDate",
        "InvoicesNotSent_CustomerID",
        "InvoicesNotSent_CustomerName",
        "InvoicesNotSent_InvoiceTotal",
        "InvoicesNotSent_CreatedByID",
        "InvoicesNotSent_CreatedByName",
        "InvoicesNotSent_JobID",
        "InvoicesNotSent_JobName",
        "InvoicesNotSent_ContractID",
        "InvoicesNotSent_ContractNumber",
        "InvoicesNotSent_PONumber",
        "InvoicesNotSent_ShipToName"
      ],
      "InventoryDetailLastSevenDays": [
        "InventoryDetailLastSevenDays_ID",
        "InventoryDetailLastSevenDays_Type",
        "InventoryDetailLastSevenDays_Status",
        "InventoryDetailLastSevenDays_CreatedDate",
        "InventoryDetailLastSevenDays_POItemID",
        "InventoryDetailLastSevenDays_WarehouseID",
        "InventoryDetailLastSevenDays_Warehouse",
        "InventoryDetailLastSevenDays_BinID",
        "InventoryDetailLastSevenDays_BinName",
        "InventoryDetailLastSevenDays_ItemID",
        "InventoryDetailLastSevenDays_Item_ItemLine",
        "InventoryDetailLastSevenDays_Item",
        "InventoryDetailLastSevenDays_ItemCode",
        "InventoryDetailLastSevenDays_ItemInventoryCosting",
        "InventoryDetailLastSevenDays_Item_ItemType",
        "InventoryDetailLastSevenDays_Item_Category",
        "InventoryDetailLastSevenDays_Item_Description",
        "InventoryDetailLastSevenDays_Item_SupplierID",
        "InventoryDetailLastSevenDays_Item_SupplierName",
        "InventoryDetailLastSevenDays_Item_Price",
        "InventoryDetailLastSevenDays_Item_PriceExtended",
        "InventoryDetailLastSevenDays_Quantity",
        "InventoryDetailLastSevenDays_OrigQuantity",
        "InventoryDetailLastSevenDays_Cost",
        "InventoryDetailLastSevenDays_Item_CostExtended",
        "InventoryDetailLastSevenDays_WarehouseMaxQty",
        "InventoryDetailLastSevenDays_WarehouseMinQty",
        "InventoryDetailLastSevenDays_WarehouseOffice",
        "InventoryDetailLastSevenDays_Warehouse_ItemQty",
        "InventoryDetailLastSevenDays_EquipmentID",
        "InventoryDetailLastSevenDays_LocationID",
        "InventoryDetailLastSevenDays_Equipment_SerialNumber",
        "InventoryDetailLastSevenDays_Equipment_Model",
        "InventoryDetailLastSevenDays_JobID",
        "InventoryDetailLastSevenDays_JobItemId",
        "InventoryDetailLastSevenDays_JobDateEntered",
        "InventoryDetailLastSevenDays_JobCustomerID",
        "InventoryDetailLastSevenDays_JobCustomer",
        "InventoryDetailLastSevenDays_JobName",
        "InventoryDetailLastSevenDays_JobStatus",
        "InventoryDetailLastSevenDays_LastInvoiceDate",
        "InventoryDetailLastSevenDays_InvoiceCount"
      ],
      "InventoryDetailLastCloseout": [],
      "JobRecommendationTasks": [
        "RecTasks_Jobid",
        "RecTasks_JobName",
        "RecTasks_ ProjectName",
        "RecTasks_JobStatus",
        "RecTasks_JobUrgency",
        "RecTasks_JobType",
        "RecTasks_Office",
        "RecTasks_Type",
        "RecTasks_EquipmentID",
        "RecTasks_Equipment",
        "RecTasks_Serial",
        "RecTasks_Task",
        "RecTasks_TaskDescription",
        "RecTasks_RecommendationUrgency",
        "RecTasks_RecommendationType",
        "RecTasks_Recommendation",
        "RecTasks_CreatedBy",
        "RecTasks_RecStatus",
        "RecTasks_CreatedDate",
        "RecTasks_CustomerID",
        "RecTasks_CustomerName",
        "RecTasks_AccountNumber",
        "RecTasks_LocationID",
        "RecTasks_LocationName",
        "RecTasks_ContactID",
        "RecTasks_ContactName",
        "RecTasks_InvoiceStatus"
      ],
      "BidsSummary": [
        "BidsSummary_ID",
        "BidsSummary_Name",
        "BidsSummary_Description",
        "BidsSummary_ProjectName",
        "BidsSummary_Category",
        "BidsSummary_Status",
        "BidsSummary_Type",
        "BidsSummary_SalesPersonID",
        "BidsSummary_SalesPersonName",
        "BidsSummary_DateEntered",
        "BidsSummary_DateSubmitted",
        "BidsSummary_StartDate",
        "BidsSummary_BidDate",
        "BidsSummary_EstimatedDuration",
        "BidsSummary_EstimatedDays",
        "BidsSummary_NumberOfTechs",
        "BidsSummary_CreatedByID",
        "BidsSummary_CreatedByName",
        "BidsSummary_ModifiedByID",
        "BidsSummary_ModifiedByName",
        "BidsSummary_ModifiedDate",
        "BidsSummary_Priority",
        "BidsSummary_AdditionalSpecifications",
        "BidsSummary_Office",
        "BidsSummary_Customer_ID",
        "BidsSummary_Customer",
        "BidsSummary_JobLocation",
        "BidsSummary_Location_ID",
        "BidsSummary_Location_Name",
        "BidsSummary_Contact_ID",
        "BidsSummary_Contact_Name",
        "BidsSummary_AssignedResourceID",
        "BidsSummary_AssignedResourceName",
        "BidsSummary_Contract_ID",
        "BidsSummary_Contract_Number",
        "BidsSummary_QuoteAmount",
        "BidsSummary_CustomerPO",
        "BidsSummary_StatusHistory",
        "BidSummary_FirstProposalSentDate",
        "BidSummary_CurrentProposalSentDate",
        "BidSummary_ProposalSentQty"
      ],
      "InvoiceSummary": [
        "InvoiceSummary_InvoiceID",
        "InvoiceSummary_InvoiceName",
        "InvoiceSummary_InvoiceType",
        "InvoiceSummary_JobID",
        "InvoiceSummary_JobName",
        "InvoiceSummary_ContractID",
        "InvoiceSummary_ContractNumber",
        "InvoiceSummary_ContractName",
        "InvoiceSummary_CustomerID",
        "InvoiceSummary_CustomerName",
        "InvoiceSummary_BillToID",
        "InvoiceSummary_BillToName",
        "InvoiceSummary_ShipToName",
        "InvoiceSummary_ShipToCity",
        "InvoiceSummary_ShipToState",
        "InvoiceSummary_ShipToZip",
        "InvoiceSummary_JobType",
        "InvoiceSummary_InvoiceStatus",
        "InvoiceSummary_SalesResource",
        "InvoiceSummary_VoidedBy",
        "InvoiceSummary_CreatedBy",
        "InvoiceSummary_SentBy",
        "InvoiceSummary_Office",
        "InvoiceSummary_Currency",
        "InvoiceSummary_CurrencySubTotal",
        "InvoiceSummary_CurrencyTax",
        "InvoiceSummary_CurrencyTotalAmt",
        "InvoiceSummary_InvoiceSource",
        "InvoiceSummary_InvoiceAmount",
        "InvoiceSummary_InvoiceAmountDue",
        "InvoiceSummary_PONumber",
        "InvoiceSummary_PaymentTerms",
        "InvoiceSummary_InvoiceDate",
        "InvoiceSummary_SentDate",
        "InvoiceSummary_SentTransactionDate",
        "InvoiceSummary_VoidedDate",
        "InvoiceSummary_VoidedTransactionDate",
        "InvoiceSummary_DueDate",
        "InvoiceSummary_PaidDate",
        "InvoiceSummary_PaidStatus",
        "InvoiceSummary_DeferredRevenue",
        "InvoiceSummary_Retainage",
        "InvoiceSummary_TaxSchedule",
        "InvoiceSummary_TaxExemptionCode",
        "InvoiceSummary_LastNote",
        "InvoiceSummary_InvoiceEmployeeID",
        "InvoiceSummary_InvoiceEmployeeName"
      ],
      "PurchaseOrderSummary": [
        "PurchaseOrderSummary_ID",
        "PurchaseOrderSummary_Name",
        "PurchaseOrderSummary_Job_ID",
        "PurchaseOrderSummary_Job_Name",
        "PurchaseOrderSummary_Bid_Name",
        "PurchaseOrderSummary_Status",
        "PurchaseOrderSummary_Type",
        "PurchaseOrderSummary_PaymentTerms",
        "PurchaseOrderSummary_DueDays",
        "PurchaseOrderSummary_Terms",
        "PurchaseOrderSummary_Office",
        "PurchaseOrderSummary_Supplier_ID",
        "PurchaseOrderSummary_Supplier_Name",
        "PurchaseOrderSummary_Warehouse_Name",
        "PurchaseOrderSummary_Date",
        "PurchaseOrderSummary_OrderedBy",
        "PurchaseOrderSummary_Job_Location",
        "PurchaseOrderSummary_Via",
        "PurchaseOrderSummary_DeliveryDate",
        "PurchaseOrderSummary_DateRequired",
        "PurchaseOrderSummary_Total"
      ],
      "SalesAndPurchases": [
        "SalesAndPurchases_ID",
        "SalesAndPurchases_ItemName",
        "SalesAndPurchases_Description",
        "SalesAndPurchases_Category",
        "SalesAndPurchases_Type",
        "SalesAndPurchases_InventoryType",
        "SalesAndPurchases_Manufacturer_Code",
        "SalesAndPurchases_SupplierID",
        "SalesAndPurchases_SupplierAccount",
        "SalesAndPurchases_SupplierName",
        "SalesAndPurchases_ProductLineName",
        "SalesAndPurchases_Price",
        "SalesAndPurchases_InvoiceSalesQty",
        "SalesAndPurchases_InvoiceSalesTotal",
        "SalesAndPurchases_PurchaseOrderQty",
        "SalesAndPurchases_PurchaseOrderTotal"
      ],
      "Rentals": [
        "Rental_Asset_ID",
        "Rental_Equipment_ID",
        "Rental_Asset_Name",
        "Rental_SerialNumber",
        "Rental_Group_RequiresSerialForCheckout",
        "Rental_Status",
        "Rental_Group",
        "Rental_Type",
        "Rental_Office",
        "Rental_InitialValue",
        "Rental_CurrentValue",
        "Rental_Contract_ID",
        "Rental_ContractStatus",
        "Rental_ContractNumber",
        "Rental_ContractStartDate",
        "Rental_EndDate",
        "Rental_Contract_Unit",
        "Rental_Contract_Cost",
        "Rental_Contract_Tax",
        "Rental_Contract_Total"
      ],
      "Events": [
        "Event_ID",
        "Event_Date",
        "Event_Subject",
        "Event_Text",
        "Event_Status",
        "Event_Priority",
        "Event_EventType",
        "Event_EventStartTime",
        "Event_EventEndTime",
        "Event_NextCallDate",
        "Event_AssignedEmployee",
        "Event_CompanyID",
        "Event_CompanyName",
        "Event_CompanyOffice",
        "Event_Attendees",
        "Event_OpportunityID",
        "Event_OpportunityName",
        "Event_OpportunityNextSteps"
      ],
      "ContractEquipment": [
        "ContractEquipment_ContractID",
        "ContractEquipment_ContractName",
        "ContractEquipment_ContractStatus",
        "ContractEquipment_ContractType",
        "ContractEquipment_ContractStartDate",
        "ContractEquipment_ContractRenewalDate",
        "ContractEquipment_ProjectName",
        "ContractEquipment_ServicesScheduledThrough",
        "ContractEquipment_CustomerID",
        "ContractEquipment_Customer",
        "ContractEquipment_CustomerAccountNumber",
        "ContractEquipment_LocationID",
        "ContractEquipment_LocationName",
        "ContractEquipment_EquipmentID",
        "ContractEquipment_EquipmentName",
        "ContractEquipment_EquipmentDescription",
        "ContractEquipment_ItemNumber",
        "ContractEquipment_EquipmentSerial",
        "ContractEquipment_EquipmentStatus",
        "ContractEquipment_EquipmentType",
        "ContractEquipment_EquipmentManufacturer",
        "ContractEquipment_BaseItemID",
        "ContractEquipment_BaseItemName"
      ],
      "InventoryArchive": [
        "InventoryArchive_ArchiveDate",
        "InventoryArchive_OfficeName",
        "InventoryArchive_ItemID",
        "InventoryArchive_ItemCode",
        "InventoryArchive_ItemName",
        "InventoryArchive_InventoryType",
        "InventoryArchive_Quantity",
        "InventoryArchive_Cost"
      ],
      "EnhancedCommissionPlans": [],
      "Items": [
        "Items_ID",
        "Items_Name",
        "Items_Description",
        "Items_Category",
        "Items_ItemCode",
        "Items_ProductLine",
        "Items_Type",
        "Items_InventoryType",
        "Items_SupplierID",
        "Items_SupplierName",
        "Items_ManufacturerID",
        "Items_ManufacturerName",
        "Items_RetailPrice",
        "Items_CostDate",
        "Items_MaterialCost",
        "Items_MaterialCostDate",
        "Items_MFGPrice",
        "Items_MFGPriceDate",
        "Items_MinAmount",
        "Items_MaxAmount",
        "Items_DateCreated",
        "Items_Createdby",
        "Items_LastModified",
        "Items_ModifiedBy",
        "Items_Status",
        "Items_InventoryCosting",
        "Items_AllowTechAdd",
        "Items_RequiresPO",
        "Items_AcceptsPayment",
        "Items_LinkedItemsMethod",
        "Items_HasDynamicPricing",
        "Items_AllowFractionalInvoicing",
        "Items_AvalaraInSyc"
      ],
      "AssetDepreciation": [
        "AssetDepreciation_AssetID",
        "AssetDepreciation_EquipmentID",
        "AssetDepreciation_StartDate",
        "AssetDepreciation_EndDate",
        "AssetDepreciation_Amount",
        "AssetDepreciation_FinancialYear",
        "AssetDepreciation_Quarter",
        "AssetDepreciation_Period",
        "AssetDepreciation_DepreciationType",
        "AssetDepreciation_ProcessedFlag"
      ],
      "JobsBacklog": [
        "JobsBacklog_ID",
        "JobsBacklog_Name",
        "JobsBacklog_Status",
        "JobsBacklog_Type",
        "JobsBacklog_Office",
        "JobsBacklog_SalesPerson",
        "JobsBacklog_PromisedDate",
        "JobsBacklog_Revenue",
        "JobsBacklog_Cost"
      ],
      "JobsDetail": [
        "JobsDetail_ID",
        "JobsDetail_Name",
        "JobsDetail_ProjectName",
        "JobsDetail_Description",
        "JobsDetail_Status",
        "JobsDetail_CustomerPO",
        "JobsDetail_Type",
        "JobsDetail_Office",
        "JobsDetail_DepartmentID",
        "JobsDetail_DepartmentName",
        "JobsDetail_EnteredBy",
        "JobsDetail_ModifiedBy",
        "JobsDetail_SalesPerson",
        "JobsDetail_Technician",
        "JobsDetail_AssignedResource",
        "JobsDetail_EstimatedDuration",
        "JobsDetail_EstimatedDays",
        "JobsDetail_ScheduledDate",
        "JobsDetail_PromisedDate",
        "JobsDetail_CompletedDate",
        "JobsDetail_POFufilledDate",
        "JobsDetail_PODeliveryDate",
        "JobsDetail_POPromiseDate",
        "JobsDetail_ModifiedDate",
        "JobsDetail_InvoiceDate",
        "JobsDetail_DateEntered",
        "JobsDetail_ContractID",
        "JobsDetail_ContractNumber",
        "JobsDetail_Urgency",
        "JobsDetail_Priority",
        "JobsDetail_PriorityNotes",
        "JobsDetail_JobItemLineID",
        "JobsDetail_Item",
        "JobsDetail_ItemId",
        "JobsDetail_ItemDescription",
        "JobsDetail_ItemType",
        "JobsDetail_ItemCategory",
        "JobsDetail_ItemProductLine",
        "JobsDetail_ItemStatus",
        "JobsDetail_Item_Technician",
        "JobsDetail_Item_TechnicianID",
        "JobsDetail_Item_TechnicianStatus",
        "JobsDetail_ItemLaborResourceID",
        "JobsDetail_Item_EquipmentID",
        "JobsDetail_Item_Equipment",
        "JobsDetail_Item_EquipmentSerial",
        "JobsDetail_Item_ActualQuantity",
        "JobsDetail_Item_OrderedQuantity",
        "JobsDetail_Item_EstimatedQuantity",
        "JobsDetail_Item_InvoicedQuantity",
        "JobsDetail_Item_UsedQuantity",
        "JobsDetail_Item_PickedQuantity",
        "JobsDetail_Item_WIPQuantity",
        "JobsDetail_Item_WIPCost",
        "JobsDetail_Item_WIPExtendedCost",
        "JobsDetail_Item_MaterialCost",
        "JobsDetail_Item_POCost",
        "JobsDetail_Item_JobRevenue",
        "JobsDetail_Item_JobCost",
        "JobsDetail_Item_InvoiceName",
        "JobsDetail_Item_InvoicedRevenue",
        "JobsDetail_Item_InvoicedCost",
        "JobsDetail_Item_UnitPrice",
        "JobsDetail_Item_CurrencyPrice",
        "JobsDetail_Item_Total",
        "JobsDetail_Item_TotalDiscountAmt",
        "JobsDetail_Item_DiscountPercent",
        "JobsDetail_Item_ItemNotes",
        "JobsDetail_Customer_ID",
        "JobsDetail_Customer_Name",
        "JobsDetail_Customer_AccountNumber",
        "JobsDetail_Location_ID",
        "JobsDetail_Location_Name",
        "JobsDetail_Location_Address1",
        "JobsDetail_Location_Address2",
        "JobsDetail_Location_City",
        "JobsDetail_Location_State",
        "JobsDetail_Location_Zip",
        "JobsDetail_ShipToOverride",
        "JobsDetail_Contact_ID",
        "JobsDetail_Contact_Name",
        "JobsDetail_Contact_Title",
        "JobsDetail_Contact_Email",
        "JobsDetail_Contact_Cell",
        "JobsDetail_Contact_WorkPhone"
      ],
      "CustomerInformation": [
        "Customer_ID",
        "Customer_Name",
        "Customer_AccountNumber",
        "Customer_OfficeID",
        "Customer_OfficeName",
        "Customer_Status",
        "Customer_CreditStatus",
        "Customer_Type",
        "Customer_Currency",
        "Customer_Reseller",
        "Customer_WarrantyCustomer",
        "Customer_Active",
        "Customer_NextServiceDate",
        "Customer_Phone",
        "Customer_Email",
        "Customer_Website",
        "Customer_InvoicePreference",
        "Customer_Fax",
        "Customer_Address1",
        "Customer_Address2",
        "Customer_City",
        "Customer_State",
        "Customer_Zip",
        "Customer_Country",
        "Customer_Category",
        "Customer_DateCreated",
        "Customer_Balance",
        "Customer_Terms",
        "Customer_StatementCycle",
        "Customer_TaxID",
        "Customer_TaxExemptCertificateNum",
        "Customer_SalesPerson",
        "Customer_SalesPersonId",
        "Customer_Contacts",
        "Customer_AvgDaysToPay",
        "Customer_CreditLimit",
        "Customer_RequiresPO"
      ],
      "PayrollReport": [
        "PayrollReport_ID",
        "PayrollReport_EmployeeID",
        "PayrollReport_EmployeeNumber",
        "PayrollReport_EmployeeName",
        "PayrollReport_WeekDayName",
        "PayrollReport_WorkDate",
        "PayrollReport_StartTimeTime",
        "PayrollReport_EndTimeTime",
        "PayrollReport_TimeTypeName",
        "PayrollReport_EntryHours",
        "PayrollReport_RegularHours",
        "PayrollReport_TimeAndHalfHours",
        "PayrollReport_JobType",
        "PayrollReport_JobName",
        "PayrollReport_JobStatus",
        "PayrollReport_JobId",
        "PayrollReport_BilledHourSet",
        "PayrollReport_CompanyID",
        "PayrollReport_CompanyAccountNumber",
        "PayrollReport_CompanyName",
        "PayrollReport_LocationID",
        "PayrollReport_LocationName",
        "PayrollReport_LocationStreet",
        "PayrollReport_LocationAdditionalAddressInfo",
        "PayrollReport_LocationCity",
        "PayrollReport_LocationState",
        "PayrollReport_LocationZip",
        "PayrollReport_LastInvoiceDate",
        "PayrollReport_LastInvoiceName"
      ],
      "OpportunityAttributes": [],
      "EquipmentDetail": [
        "Equipment_ID",
        "Equipment_Name",
        "Equipment_Description",
        "Equipment_SerialNumber",
        "Equipment_EquipmentType",
        "Equipment_ManufacturerName",
        "Equipment_StartDate",
        "Equipment_WarrantyExpiration",
        "Equipment_EquipmentStatus",
        "Equipment_EndCustomerName",
        "Equipment_SoldCustomerName",
        "Equipment_SalesPersonName",
        "Equipment_LocationName",
        "Equipment_LocationAddr1",
        "Equipment_LocationAddr2",
        "Equipment_LocationCity",
        "Equipment_LocationState",
        "Equipment_LocationZip",
        "Equipment_Hours",
        "Equipment_HoursLastUpdated",
        "Equipment_ItemName",
        "Equipment_ItemLine",
        "Equipment_InvoiceID",
        "Equipment_InvoiceNumber",
        "Equipment_InvoiceDate",
        "Equipment_CreatedDate",
        "Equipment_CreatedBy",
        "Equipment_ModifiedBy",
        "Equipment_ModifiedDate",
        "Equipment_PurchaseOrderID",
        "Equipment_PurchaseOrderName",
        "Equipment_PurchaseOrderBillID",
        "Equipment_PurchaseOrderBillNumber",
        "Equipment_PurchaseOrderBillDate",
        "Equipment_ContractID",
        "Equipment_ContractNumber",
        "Equipment_ContractExpiration",
        "Equipment_ContractStatus",
        "Equipment_IsInventory"
      ],
      "InvoiceOpportunityEvents": [
        "InvoiceOpportunityEvents_BidID",
        "InvoiceOpportunityEvents_OpportunityID",
        "InvoiceOpportunityEvents_OpportunityName",
        "InvoiceOpportunityEvents_OpportunityAmount",
        "InvoiceOpportunityEvents_OpportunityExpRevenue",
        "InvoiceOpportunityEvents_OpportunityType",
        "InvoiceOpportunityEvents_LeadSource",
        "InvoiceOpportunityEvents_OpportunityCreationDate",
        "InvoiceOpportunityEvents_OpportunityCloseDate",
        "InvoiceOpportunityEvents_OpportunityStage",
        "InvoiceOpportunityEvents_JobID",
        "InvoiceOpportunityEvents_JobName",
        "InvoiceOpportunityEvents_ProjectName",
        "InvoiceOpportunityEvents_JobType",
        "InvoiceOpportunityEvents_JobStatus",
        "InvoiceOpportunityEvents_CustomerID",
        "InvoiceOpportunityEvents_CustomerName",
        "InvoiceOpportunityEvents_JobOffice",
        "InvoiceOpportunityEvents_InvoiceID",
        "InvoiceOpportunityEvents_InvoiceBillToID",
        "InvoiceOpportunityEvents_TaxSchedule",
        "InvoiceOpportunityEvents_InvoiceName",
        "InvoiceOpportunityEvents_InvoiceQuoteAmt",
        "InvoiceOpportunityEvents_InvoiceAmount",
        "InvoiceOpportunityEvents_InvoiceStatus",
        "InvoiceOpportunityEvents_InvoiceDate",
        "InvoiceOpportunityEvents_SentDate",
        "InvoiceOpportunityEvents_VoidedDate",
        "InvoiceOpportunityEvents_DueDate",
        "InvoiceOpportunityEvents_PaidDate",
        "InvoiceOpportunityEvents_EventCount",
        "InvoiceOpportunityEvents_EmailCount",
        "InvoiceOpportunityEvents_FaceToFaceCount",
        "InvoiceOpportunityEvents_FollowUpCount",
        "InvoiceOpportunityEvents_ColdCallCount",
        "InvoiceOpportunityEvents_PreventiveMaintenanceCount",
        "InvoiceOpportunityEvents_LastEventDate"
      ],
      "UserPermissions": [
        "UserPermissions_Role_Name",
        "UserPermissions_Permission_Name",
        "UserPermissions_Description",
        "UserPermissions_Employee_Name",
        "UserPermissions_EmployeeID"
      ],
      "PurchaseOrderDetail": [
        "PurchaseOrderDetail_ID",
        "PurchaseOrderDetail_Name",
        "PurchaseOrderDetail_Job_ID",
        "PurchaseOrderDetail_Job_Name",
        "PurchaseOrderDetail_Job_Type",
        "PurchaseOrderDetail_Job_Status",
        "PurchaseOrderDetail_Job_Office",
        "PurchaseOrderDetail_Job_AssignedResource",
        "PurchaseOrderDetail_Job_Urgency",
        "PurchaseOrderDetail_Job_PriorityNotes",
        "PurchaseOrderDetail_Job_CustomerPO",
        "PurchaseOrderDetail_Job_Description",
        "PurchaseOrderDetail_Job_EstimatedDuration",
        "PurchaseOrderDetail_Job_EstimatedDays",
        "PurchaseOrderDetail_Job_NumberTechs",
        "PurchaseOrderDetail_Job_ShipToOverride",
        "PurchaseOrderDetail_Job_CreatedBy",
        "PurchaseOrderDetail_Job_CreatedDate",
        "PurchaseOrderDetail_Job_ModifiedBy",
        "PurchaseOrderDetail_Job_ModifiedDate",
        "PurchaseOrderDetail_Bid_Name",
        "PurchaseOrderDetail_Status",
        "PurchaseOrderDetail_Type",
        "PurchaseOrderDetail_Terms",
        "PurchaseOrderDetail_Notes",
        "PurchaseOrderDetail_Office",
        "PurchaseOrderDetail_PO_ShipToOverride",
        "PurchaseOrderDetail_Customer_ID",
        "PurchaseOrderDetail_Customer_Name",
        "PurchaseOrderDetail_Supplier_ID",
        "PurchaseOrderDetail_Supplier_Name",
        "PurchaseOrderDetail_Supplier_AccountNo",
        "PurchaseOrderDetail_Supplier_Phone",
        "PurchaseOrderDetail_Warehouse_ID",
        "PurchaseOrderDetail_Warehouse_Name",
        "PurchaseOrderDetail_Warehouse_Address1",
        "PurchaseOrderDetail_Warehouse_City",
        "PurchaseOrderDetail_Warehouse_State",
        "PurchaseOrderDetail_Warehouse_Zip",
        "PurchaseOrderDetail_Date",
        "PurchaseOrderDetail_OrderedBy_ID",
        "PurchaseOrderDetail_OrderedBy",
        "PurchaseOrderDetail_CreatedBy",
        "PurchaseOrderDetail_Item_POFufilledDate",
        "PurchaseOrderDetail_PODeliveryDate",
        "PurchaseOrderDetail_Job_Location",
        "PurchaseOrderDetail_Via",
        "PurchaseOrderDetail_DateRequired",
        "PurchaseOrderDetail_PromiseDate",
        "PurchaseOrderDetail_Item_ID",
        "PurchaseOrderDetail_Item_Name",
        "PurchaseOrderDetail_Item_Code",
        "PurchaseOrderDetail_Item_Manufacturer",
        "PurchaseOrderDetail_Item_Description",
        "PurchaseOrderDetail_Item_Status",
        "PurchaseOrderDetail_Item_Quantity",
        "PurchaseOrderDetail_Item_OrderQty",
        "PurchaseOrderDetail_Item_Cost",
        "PurchaseOrderDetail_Item_ExtendedCost",
        "PurchaseOrderDetail_Item_Price",
        "PurchaseOrderDetail_Item_ExtendedPrice",
        "PurchaseOrderDetail_Item_ItemLine",
        "PurchaseOrderDetail_Item_JobRevenue",
        "PurchaseOrderDetail_Item_Category",
        "PurchaseOrderDetail_Item_Type",
        "PurchaseOrderDetail_Item_ReceivedByID",
        "PurchaseOrderDetail_Item_ReceivedBy",
        "PurchaseOrderDetail_JobItemID",
        "PurchaseOrderDetail_POLineID",
        "PurchaseOrderDetail_POModifiedDate"
      ],
      "Contracts": [
        "Contract_ContractID",
        "Contract_Number",
        "Contract_ProjectName",
        "Contract_Status",
        "Contract_PO",
        "Contract_ContractTypeID",
        "Contract_Type",
        "Contract_StartDate",
        "Contract_FirstVisitDate",
        "Contract_NumberOfVisits",
        "Contract_NumberOfYears",
        "Contract_FirstInvoiceDate",
        "Contract_ContractRenewalDate",
        "Contract_ServiceScheduledThrough",
        "Contract_OfficeID",
        "Contract_Office",
        "Contract_Amount",
        "Contract_Estimated_Cost",
        "Contract_TotalInvoicedAmount",
        "Contract_TotalInvoicedTaxAmount",
        "Contract_TotalAmountPaid",
        "Contract_TotalInvoiceAmountNotSent",
        "Contract_TotalJobCount",
        "Contract_IncurredJobCostsToDate",
        "Contract_PaidJobCostsToDate",
        "Contract_JobVisitsComplete",
        "Contract_ProjectedCostsNotIncurred",
        "Contract_CustomerID",
        "Contract_CustomerName",
        "Contract_CustomerAccountNumber",
        "Contract_CustomerOffice",
        "Contract_CustomerStatus",
        "Contract_CustomerIsActive",
        "Contract_CustomerAddress",
        "Contract_LocationID",
        "Contract_LocationAddress1",
        "Contract_LocationAddress2",
        "Contract_LocationCity",
        "Contract_LocationState",
        "Contract_LocationZip",
        "Contract_ContactPersonID",
        "Contract_ContactPerson",
        "Contract_ContractSalesPersonID",
        "Contract_ContractSalesPerson",
        "Contract_CustomerSalesPersonID",
        "Contract_CustomerSalesPerson",
        "Contract_OpportunityID"
      ],
      "InvoiceDetail": [
        "InvoiceDetail_InvoiceID",
        "InvoiceDetail_InvoiceName",
        "InvoiceDetail_JobID",
        "InvoiceDetail_JobName",
        "InvoiceDetail_ContractID",
        "InvoiceDetail_ContractNumber",
        "InvoiceDetail_ContractName",
        "InvoiceDetail_CustomerID",
        "InvoiceDetail_CustomerName",
        "InvoiceDetail_BillToID",
        "InvoiceDetail_BillToName",
        "InvoiceDetail_ShipToName",
        "InvoiceDetail_ShipToCity",
        "InvoiceDetail_ShipToState",
        "InvoiceDetail_ShipToZip",
        "InvoiceDetail_JobType",
        "InvoiceDetail_InvoiceStatus",
        "InvoiceDetail_SalesResource",
        "InvoiceDetail_VoidedBy",
        "InvoiceDetail_CreatedBy",
        "InvoiceDetail_SentBy",
        "InvoiceDetail_Office",
        "InvoiceDetail_Currency",
        "InvoiceDetail_CurrencySubTotal",
        "InvoiceDetail_CurrencyTax",
        "InvoiceDetail_CurrencyTotalAmt",
        "InvoiceDetail_InvoiceType",
        "InvoiceDetail_InvoiceSource",
        "InvoiceDetail_InvoiceAmount",
        "InvoiceDetail_InvoiceAmountDue",
        "InvoiceDetail_PONumber",
        "InvoiceDetail_PaymentTerms",
        "InvoiceDetail_InvoiceDate",
        "InvoiceDetail_SentDate",
        "InvoiceDetail_SentTransactionDate",
        "InvoiceDetail_VoidedDate",
        "InvoiceDetail_VoidedTransactionDate",
        "InvoiceDetail_DueDate",
        "InvoiceDetail_PaidDate",
        "InvoiceDetail_PaidStatus",
        "InvoiceDetail_DeferredRevenue",
        "InvoiceDetail_Retainage",
        "InvoiceDetail_TaxSchedule",
        "InvoiceDetail_ItemID",
        "InvoiceDetail_ItemCategory",
        "InvoiceDetail_ItemType",
        "InvoiceDetail_ItemName",
        "InvoiceDetail_ItemDescription",
        "InvoiceDetail_ItemQuantity",
        "InvoiceDetail_ItemUnitCost",
        "InvoiceDetail_ItemInvoicedCost",
        "InvoiceDetail_ItemUnitPrice",
        "InvoiceDetail_ItemCurrencyPrice",
        "InvoiceDetail_ItemInvoicedPrice",
        "InvoiceDetail_ItemPriceOverride",
        "InvoiceDetail_ItemPriceOverrideAmount",
        "InvoiceDetail_ItemTaxAmount",
        "InvoiceDetail_ItemGrandTotalPrice",
        "InvoiceDetail_ItemDiscountPercent",
        "InvoiceDetail_ItemDiscountAmount",
        "InvoiceDetail_ItemCurrencyAmount",
        "InvoiceDetail_ItemCurrencyTax",
        "InvoiceDetail_ItemCurrencyTotal",
        "InvoiceDetail_ItemLaborResource",
        "InvoiceDetail_JobTechnicians",
        "InvoiceDetail_InvoiceEmployeeID",
        "InvoiceDetail_InvoiceEmployeeName"
      ],
      "WarehouseInventory": [
        "Warehouse_Inventory_ID",
        "Warehouse_Id",
        "Warehouse_Name",
        "Warehouse_Office_ID",
        "Warehouse_Office",
        "Warehouse_ItemName",
        "Warehouse_ItemID",
        "Warehouse_MinQty",
        "Warehouse_MaxQty",
        "Warehouse_ItemQty",
        "Warehouse_Inventory_Status"
      ],
      "TimeSheetsDetail": [
        "TimeSheetsDetail_EmployeeId",
        "TimeSheetsDetail_EmployeeName",
        "TimeSheetsDetail_EmployeeNumber",
        "TimeSheetsDetail_ReportMonthStart",
        "TimeSheetsDetail_ReportMonthEnd",
        "TimeSheetsDetail_WorkDate",
        "TimeSheetsDetail_StartTime",
        "TimeSheetsDetail_EndTime",
        "TimeSheetsDetail_TimeType",
        "TimeSheetsDetail_Description",
        "TimeSheetsDetail_JobHours",
        "TimeSheetsDetail_NonJobHours",
        "TimeSheetsDetail_JobID",
        "TimeSheetsDetail_JobName",
        "TimeSheetsDetail_JobType",
        "TimeSheetsDetail_JobCity",
        "TimeSheetsDetail_JobState",
        "TimeSheetsDetail_JobPostalCode",
        "TimeSheetsDetail_OfficeName",
        "TimeSheetsDetail_ApprovedDate",
        "TimeSheetsDetail_ApprovedBy",
        "TimeSheetsDetail_BilledLaborType"
      ],
      "BidsDetail": [
        "BidsDetail_ID",
        "BidsDetail_Name",
        "BidsDetail_Status",
        "BidsDetail_Item_ID",
        "BidsDetail_Item_Name",
        "BidsDetail_Equipment_ID",
        "BidsDetail_Equipment_Name",
        "BidsDetail_Item_UnitCost",
        "BidsDetail_Item_CostOverride",
        "BidsDetail_Item_CurrencyPrice",
        "BidsDetail_Item_DiscountPct",
        "BidsDetail_Item_DiscountAmt",
        "BidsDetail_Item_QuantityToStockOrder",
        "BidsDetail_Item_QuantityToStd",
        "BidsDetail_Item_QuantityToOrderOverNight",
        "BidsDetail_Item_QuantityOverride",
        "BidsDetail_Item_SelectedSupplier_ID",
        "BidsDetail_Item_SupplierName",
        "BidsDetail_Item_GroupID",
        "BidsDetail_OfficeID",
        "BidsDetail_OfficeName",
        "BidsDetail_StartDate",
        "BidsDetail_ContractID",
        "BidsDetail_Contract_Number"
      ],
      "CustomerPricing": [
        "CustomerPricing_PricingType",
        "CustomerPricing_CustomerID",
        "CustomerPricing_CustomerName",
        "CustomerPricing_CustomerOffice",
        "CustomerPricing_Name",
        "CustomerPricing_Description",
        "CustomerPricing_Discount_Markup",
        "CustomerPricing_Amount"
      ],
      "Opportunities": [
        "Opportunity_ID",
        "Opportunity_Name",
        "Opportunity_Office",
        "Opportunity_Description",
        "Opportunity_NextSteps",
        "Opportunity_Salesperson",
        "Opportunity_Stage",
        "Opportunity_Type",
        "Opportunity_ForecastCategory",
        "Opportunity_LeadSource",
        "Opportunity_CreatedDate",
        "Opportunity_LastModifiedDate",
        "Opportunity_CloseDate",
        "Opportunity_CreatedBy",
        "Opportunity_Probability",
        "Opportunity_Amount",
        "Opportunity_ExpectedRevenue",
        "Opportunity_CustomerName"
      ],
      "Assets": [
        "Asset_ID",
        "Asset_Office",
        "Asset_Location",
        "Asset_Group",
        "Asset_Name",
        "Asset_IsRental",
        "Asset_Type",
        "Asset_InitialValue",
        "Asset_CurrentValue",
        "Asset_DepreciationGLAccount",
        "Asset_DateCreated",
        "Asset_AssetValueGLAccount",
        "Asset_RevenueGLAccount",
        "Asset_EquipmentID",
        "Asset_EquipmentName",
        "Asset_EquipmentDescription",
        "Asset_SerialNumber",
        "Asset_EquipmentMake",
        "Asset_EquipmentModel",
        "Asset_EquipmentReading",
        "Asset_EquipmentDateCreated",
        "Asset_Status",
        "Asset_EquipmentStartDate",
        "Asset_EquipmentStatus",
        "Asset_ProcessedDepreciation",
        "Asset_FutureDepreciation",
        "Asset_DepreciationStart",
        "Asset_DepreciationEnd"
      ],
      "Tasks": [
        "Tasks_TaskID",
        "Tasks_TaskType",
        "Tasks_TaskStatus",
        "Tasks_TaskTitle",
        "Tasks_TaskDescription",
        "Tasks_TaskDueDate",
        "Tasks_TaskCreatedDate",
        "Tasks_TaskCreatedByID",
        "Tasks_TaskCreatedBy",
        "Tasks_ModifiedDate",
        "Tasks_TaskModifiedByID",
        "Tasks_TaskModifiedBy",
        "Tasks_TaskMembers",
        "Tasks_TaskSubscribers",
        "Tasks_TaskPendingJobID",
        "Tasks_TaskPendingJobName",
        "Tasks_TaskJobID",
        "Tasks_TaskJobName",
        "Tasks_TaskOpportunityID",
        "Tasks_TaskOpportunityName",
        "Tasks_TaskContractID",
        "Tasks_TaskContractName"
      ],
      "JobsSummary": [
        "JobsSummary_ID",
        "JobsSummary_Name",
        "JobsSummary_ProjectName",
        "JobsSummary_Description",
        "JobsSummary_Status",
        "JobsSummary_InvoiceStatus",
        "JobsSummary_InvoicesAllSent",
        "JobsSummary_Type",
        "JobsSummary_DepartmentID",
        "JobsSummary_DepartmentName",
        "JobsSummary_Office",
        "JobsSummary_ContractID",
        "JobsSummary_ContractNumber",
        "JobsSummary_Customer_ID",
        "JobsSummary_Customer",
        "JobsSummary_CustomerAccountNumber",
        "JobsSummary_JobLocation",
        "JobsSummary_Location_ID",
        "JobsSummary_Location_Name",
        "JobsSummary_CreatedByID",
        "JobsSummary_CreatedByName",
        "JobsSummary_ModifiedByID",
        "JobsSummary_ModifiedByName",
        "JobsSummary_ModifiedDate",
        "JobsSummary_SalesPerson",
        "JobsSummary_AssignedResourceID",
        "JobsSummary_AssignedResourceName",
        "JobsSummary_Priority_Notes",
        "JobsSummary_ScheduledDate",
        "JobsSummary_PromisedDate",
        "JobsSummary_CompletedDate",
        "JobsSummary_DateEntered",
        "JobsSummary_QuoteAmount",
        "JobsSummary_Urgency",
        "JobsSummary_Priority",
        "JobsSumary_CreditOverrideNote",
        "JobsSummary_FirstProposalSentDate",
        "JobsSummary_CurrentProposalSentDate",
        "JobsSummary_ProposalSentQty",
        "JobsSummary_BidCreatedDate",
        "JobsSummary_BidCreatedByID",
        "JobsSummary_BidCreatedByName",
        "JobsSummary_BidModifiedDate",
        "JobsSummary_BidModifiedByID",
        "JobsSummary_BidModifiedByName",
        "JobsSummary_BidAssignedResourceID",
        "JobsSummary_BidAssignedResourceName",
        "JobsSummary_BidOrigDescription"
      ],
      "InventoryAdjustments": [
        "InventoryAdjustments_ItemID",
        "InventoryAdjustments_AdjustmentDate",
        "InventoryAdjustments_ItemCode",
        "InventoryAdjustments_ItemName",
        "InventoryAdjustments_ItemDescription",
        "InventoryAdjustments_OfficeName",
        "InventoryAdjustments_WarehouseName",
        "InventoryAdjustments_AdjustedQuantity",
        "InventoryAdjustments_AdjustmentReason",
        "InventoryAdjustments_Cost",
        "InventoryAdjustments_Currency",
        "InventoryAdjustments_ItemCategory",
        "InventoryAdjustments_ItemProductLine",
        "InventoryAdjustments_Supplier",
        "InventoryAdjustments_Manufacturer",
        "InventoryAdjustments_AdjustedBy",
        "InventoryAdjustments_AdjustmentTransactionID",
        "InventoryAdjustments_InventoryID"
      ],
      "EmployeeDetail": [
        "EmployeeDetail_EmployeeID",
        "EmployeeDetail_EmployeeNumber",
        "EmployeeDetail_EmployeeName",
        "EmployeeDetail_Status",
        "EmployeeDetail_PrimaryOfficeID",
        "EmployeeDetail_PrimaryOffice",
        "EmployeeDetail_Address1",
        "EmployeeDetail_Address2",
        "EmployeeDetail_City",
        "EmployeeDetail_State",
        "EmployeeDetail_Zip",
        "EmployeeDetail_WorkPhone",
        "EmployeeDetail_CellPhone",
        "EmployeeDetail_Title",
        "EmployeeDetail_Email",
        "EmployeeDetail_IsSalesPerson",
        "EmployeeDetail_IsJobAssignee",
        "EmployeeDetail_IsTechnician",
        "EmployeeDetail_ManagerID",
        "EmployeeDetail_ManagerName"
      ],
      "EquipmentAttributes": [
        "Equipment_ID",
        "Equipment_Name",
        "Equipment_SerialNumber",
        "Equipment_ItemNumber",
        "Compressor_CFM",
        "Compressor_HP",
        "Compressor_Voltage",
        "Generator_Fuel",
        "Generator_Kilowatt",
        "Generator_Voltage"
      ],
      "ServicePackageDetail": [
        "ServicePackage_ID",
        "ServicePackage_Name",
        "ServicePackage_Equipment_ID",
        "ServicePackage_Equipment_Name",
        "ServicePackage_Equipment_SerialNumber",
        "ServicePackage_Customer_ID",
        "ServicePackage_Customer_Name",
        "ServicePackage_Location_ID",
        "ServicePackage_Location_Name",
        "ServicePackage_ItemID",
        "ServicePackage_Item_ItemCode",
        "ServicePackage_Item_Name",
        "ServicePackage_Item_Quantity",
        "ServicePackage_Item_OverridePrice"
      ],
      "OpportunityProposalDetail": [
        "OpportunityProposalDetail_ID",
        "OpportunityProposalDetail_Name",
        "OpportunityProposalDetail_Office",
        "OpportunityProposalDetail_Description",
        "OpportunityProposalDetail_NextSteps",
        "OpportunityProposalDetail_Salesperson",
        "OpportunityProposalDetail_Stage",
        "OpportunityProposalDetail_Type",
        "OpportunityProposalDetail_ForecastCategory",
        "OpportunityProposalDetail_LeadSource",
        "OpportunityProposalDetail_CloseDate",
        "OpportunityProposalDetail_Probability",
        "OpportunityProposalDetail_Amount",
        "OpportunityProposalDetail_ExpectedRevenue",
        "OpportunityProposalDetail_CustomerName",
        "OpportunityProposalDetail_CustomerID",
        "OpportunityProposalDetail_LocationID",
        "OpportunityProposalDetail_LocationName",
        "OpportunityProposalDetail_JobName",
        "OpportunityProposalDetail_JobType",
        "OpportunityProposalDetail_JobStatus",
        "OpportunityProposalDetail_JobDatePromised",
        "OpportunityProposalDetail_ProposalDate",
        "OpportunityProposalDetail_ItemCode",
        "OpportunityProposalDetail_ItemName",
        "OpportunityProposalDetail_ItemCategory",
        "OpportunityProposalDetail_ItemManufacturer",
        "OpportunityProposalDetail_EquipItemNumber",
        "OpportunityProposalDetail_EquipName",
        "OpportunityProposalDetail_EquipSerialNum",
        "OpportunityProposalDetail_Quantity",
        "OpportunityProposalDetail_UnitPrice",
        "OpportunityProposalDetail_CurrencyPrice",
        "OpportunityProposalDetail_Subtotal",
        "OpportunityProposalDetail_Tax",
        "OpportunityProposalDetail_TotalPrice",
        "OpportunityProposalDetail_DiscountAmt"
      ]
    }
  }
]
````