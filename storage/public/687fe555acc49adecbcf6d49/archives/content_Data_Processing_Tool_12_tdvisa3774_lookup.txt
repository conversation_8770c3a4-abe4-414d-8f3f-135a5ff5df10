Python code:

```python
import pandas as pd
import os
from metalake import load_data

# Ensure output directory exists
os.makedirs('files', exist_ok=True)

chat_id = "687ff55ab3150354fe142b94"

# 1. Load both data files
df1 = load_data(chat_id, "Sheet1.dat")
df2 = load_data(chat_id, "Data Parameters.dat")

# Handle empty dataframes
if df1.empty and df2.empty:
    print("No data found in Sheet1.dat and Data Parameters.dat. No further processing possible.")
elif df1.empty:
    print("No data found in Sheet1.dat. No further processing possible.")
elif df2.empty:
    print("No data found in Data Parameters.dat. No further processing possible.")
else:
    # Standardize columns for vertical stacking
    df1 = df1.copy()
    df1.columns = [str(c) for c in range(df1.shape[1])]
    df1['Source'] = 'Sheet1.dat'
    df2 = df2.copy()
    df2.columns = [str(c) for c in range(df2.shape[1])]
    df2['Source'] = 'Data Parameters.dat'
    all_cols = sorted(set(df1.columns) | set(df2.columns))
    df1 = df1.reindex(columns=all_cols, fill_value='')
    df2 = df2.reindex(columns=all_cols, fill_value='')
    df_all = pd.concat([df1, df2], ignore_index=True, sort=False)
    # Lowercase for searching
    df_lower = df_all.applymap(lambda x: str(x).lower() if pd.notnull(x) else x)
    # Find rows with 'td visa 3774'
    visa_3774_rows = df_lower.apply(lambda row: any('td visa 3774' in str(cell) for cell in row), axis=1)
    result_rows = df_all[visa_3774_rows].copy()
    # Extract GL code(s) from Sheet1 if found
    gl_codes = []
    if not result_rows.empty:
        # Try to find GL codes by matching label in df2 then finding in df1
        # Assume df2 contains the label, df1 contains the code
        # Try to find '3774' in df1
        possible_code_cols = [col for col in df1.columns if col != 'Source']
        for col in possible_code_cols:
            codes = df1[col][df1[col].astype(str).str.contains('3774', case=False, na=False)]
            gl_codes.extend(list(codes))
        gl_codes = [str(code).strip() for code in gl_codes if str(code).strip()]
    # Output and save
    result_rows.to_csv('files/visa_3774_lookup.csv', index=False)
    result_rows.to_csv('files/visa_3774_lookup.dat', index=False, sep='|')
    # Markdown preview (first 50 rows)
    n_preview = min(50, len(result_rows))
    if n_preview == 0:
        print("No rows found containing 'TD Visa 3774'.")
    else:
        print(result_rows.head(n_preview).to_markdown(index=False))
        if len(result_rows) > 50:
            print(f"\nNote: Showing first 50 of {len(result_rows)} rows.")
    # Display extracted GL codes if any
    if gl_codes:
        print("\nIdentified GL account code(s) for TD Visa 3774:")
        for code in sorted(set(gl_codes)):
            print(f"  - {code}")
    else:
        print("\nNo GL account code(s) found for TD Visa 3774 in Sheet1.dat.")

````

Output:

| 0            | 1   | Source              |
|:-------------|:----|:--------------------|
| TD Visa 3774 |     | Data Parameters.dat |

No GL account code(s) found for TD Visa 3774 in Sheet1.dat.

