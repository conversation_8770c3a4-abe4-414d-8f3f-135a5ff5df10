SQL Query:

````
SELECT
  DATE_FORMAT(Invoice_Date, '%Y-%m') AS Month,
  SUM(Total_Charge) AS Total_Revenue
FROM Invoices
WHERE Invoice_Date >= '2025-01-01' AND Invoice_Date <= '2025-12-31'
GROUP BY DATE_FORMAT(Invoice_Date, '%Y-%m')
ORDER BY DATE_FORMAT(Invoice_Date, '%Y-%m') ASC;
````

Data retrieved successfully:

| Month   |   Total_Revenue |
|:--------|----------------:|
| 2025-01 |          289592 |
| 2025-02 |          262110 |
| 2025-03 |          287058 |
| 2025-04 |          283328 |
| 2025-05 |          293763 |
| 2025-06 |          271276 |
| 2025-07 |          223458 |