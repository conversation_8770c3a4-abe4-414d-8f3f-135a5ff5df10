Metadata of all data sources:

````
[
  {
    "data_source_name": "FieldServio",
    "selected_table_column_metadata": {
      "Payments": {
        "table_name": "Payments",
        "fields": {
          "Payment_BillID": {
            "name": "Payment_BillID",
            "description": "Indicates the ID of the corresponding bill for the payment; populated for less than half of the records, typically unique for pairs of records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1529163",
              "1540163",
              "1541672",
              "1556201",
              "1557223",
              "and 5517 more..."
            ]
          },
          "Payment_PaymentAmount": {
            "name": "Payment_PaymentAmount",
            "description": "Records the total amount of the payment as a monetary value, which may include both positive and negative figures reflecting adjustments.",
            "dataType": "money",
            "is_unstructured": false
          },
          "Payment_CurrencyName": {
            "name": "Payment_CurrencyName",
            "description": "Specifies the currency in which the payment is made, with the majority in Canadian Dollars and a smaller portion in US Dollars.",
            "dataType": "nvarchar",
            "is_unstructured": false,
            "All distinct values": [
              "Canadian Dollars",
              "US Dollars"
            ]
          },
          "Payment_CurrencyRate": {
            "name": "Payment_CurrencyRate",
            "description": "Represents the applicable exchange rate for the payment, always a positive decimal value typically below 100.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "Payment_CurrencyAmount": {
            "name": "Payment_CurrencyAmount",
            "description": "Shows the payment amount in an alternative currency, which may be positive or negative, indicating conversion or adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "Payment_PaymentDate": {
            "name": "Payment_PaymentDate",
            "description": "Denotes the actual date on which the payment was processed, essential for financial tracking and reporting.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_ProcessedDate": {
            "name": "Payment_ProcessedDate",
            "description": "Captures the date when the payment was fully processed, critical for auditing purposes and payment completion tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_UpdatedDate": {
            "name": "Payment_UpdatedDate",
            "description": "Indicates the last date when the payment record was modified, facilitating version control and change tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_TypeName": {
            "name": "Payment_TypeName",
            "description": "Classifies the payment method, with options including 'Check', 'Manual', 'EFT', 'Visa', 'MasterCard', and more, reflecting the various payment processing methods.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Manual",
              "EFT",
              "Credit Offset",
              "Check",
              "Exchange Rate Adjustment",
              "Visa",
              "MasterCard",
              "Cash"
            ]
          },
          "Payment_Status": {
            "name": "Payment_Status",
            "description": "Indicates the current state of the payment, with most records marked as 'Processed' and a small percentage as 'Voided', offering insight into payment completion.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Processed",
              "Voided"
            ]
          }
        }
      },
      "VendorBills": {
        "table_name": "VendorBills",
        "fields": {
          "VendorBills_BillId": {
            "name": "VendorBills_BillId",
            "description": "A unique integer identifier for each bill record, ensuring distinct referencing of entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1486017",
              "1500576",
              "1502251",
              "1511492",
              "1530883",
              "and 6063 more..."
            ]
          }
        }
      }
    },
    "reasoning": "**Reasoning:**\n\nTo answer the question, we need to identify tables/columns that track payments applied to vendor bills, including amounts, dates, links to bill IDs, and payment status/reversal. The primary table for payment transactions is **Payments**, and bill details reside in **VendorBills**. Relationships are established via the `Payment_BillID` linking to `VendorBills_BillId`. Status and reversal indicators are present in Payments. Relevant metadata, business rules, and status enums must be provided as per the data dictionary.\n\n  \n\n\n**Table Columns:** \n\n\n- table: Payments  \n  columns:  \n    - Payment_PaymentAmount (money)  \n    - Payment_PaymentDate (datetime)  \n    - Payment_BillID (int)  \n    - Payment_Status (varchar)  \n    - Payment_TypeName (varchar)  \n    - Payment_CurrencyName (nvarchar)  \n    - Payment_CurrencyRate (decimal)  \n    - Payment_CurrencyAmount (decimal)  \n    - Payment_ProcessedDate (datetime)  \n    - Payment_UpdatedDate (datetime)  \n\n- table: VendorBills  \n  columns:  \n    - VendorBills_BillId (int)  \n\n  \n\n\n**Business Rules and Metadata:**  \n\n\n- **Payment_PaymentAmount:**  \n  - Data type: money  \n  - Records the total amount of the payment as a monetary value, which may include both positive and negative figures reflecting adjustments (sign conventions: negative values may indicate refunds or reversals).  \n  - Multi-currency: use `Payment_CurrencyName` for currency, and `Payment_CurrencyRate` for exchange rate. `Payment_CurrencyAmount` shows the amount in the specified currency.\n\n- **Payment_PaymentDate:**  \n  - Data type: datetime  \n  - Denotes the actual date on which the payment was processed.\n\n- **Payment_BillID:**  \n  - Data type: int  \n  - Foreign key linking payment to the corresponding bill (`VendorBills_BillId`).\n\n- **Payment_Status:**  \n  - Data type: varchar  \n  - Indicates current state of the payment.  \n  - Enum/status values: \"Processed\", \"Voided\" (Voided indicates reversal).\n\n- **Payment_TypeName:**  \n  - Data type: varchar  \n  - Classifies the payment method (e.g., \"Manual\", \"EFT\", \"Check\", \"Credit Offset\", etc.).\n\n- **VendorBills_BillId:**  \n  - Data type: int  \n  - Unique ID for each vendor bill (used to join with Payments).\n\n- **Status Interpretation:**  \n  - \"Processed\" means a valid, completed payment.  \n  - \"Voided\" indicates reversal or cancellation of the payment.\n\n- **Sign Conventions:**  \n  - Positive amount: payment to vendor.  \n  - Negative amount: adjustment, reversal, or refund.\n\n- **Multi-currency Handling:**  \n  - Use `Payment_CurrencyName` and `Payment_CurrencyAmount` for the amount in the specific currency.  \n  - `Payment_CurrencyRate` applies for currency conversion.\n\n"
  }
]
````