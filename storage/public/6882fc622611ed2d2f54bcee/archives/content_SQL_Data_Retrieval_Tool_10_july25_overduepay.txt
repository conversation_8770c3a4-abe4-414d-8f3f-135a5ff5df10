SQL Query:

````
SELECT
  SUM(p.Payment_PaymentAmount) AS total_payments,
  COUNT(DISTINCT p.Payment_BillID) AS bills_paid
FROM Payments p
INNER JOIN VendorBills vb ON p.Payment_BillID = vb.VendorBills_BillId
WHERE
  p.Payment_PaymentDate >= '2025-07-01' AND
  p.Payment_PaymentDate < '2025-08-01' AND
  p.Payment_Status = 'Processed' AND
  p.Payment_PaymentAmount > 0 AND
  vb.VendorBills_DueDate < '2025-07-01';
````

Data retrieved successfully:

|   total_payments |   bills_paid |
|-----------------:|-------------:|
|           463299 |          103 |