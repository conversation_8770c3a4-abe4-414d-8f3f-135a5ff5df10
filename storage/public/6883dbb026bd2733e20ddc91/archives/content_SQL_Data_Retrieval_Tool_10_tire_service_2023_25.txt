SQL Query:

````
SELECT
  year,
  month_number,
  month_name,
  COUNT(*) AS tire_service_invoice_count
FROM (
  SELECT 
    EXTRACT(YEAR FROM Invoice_Date) AS year,
    EXTRACT(MONTH FROM Invoice_Date) AS month_number,
    MONTHNAME(Invoice_Date) AS month_name
  FROM Invoices
  WHERE Service_Type_ID = 1
    AND Invoice_Date >= '2023-01-01'
    AND Invoice_Date <= '2025-12-31'
) AS sub
GROUP BY year, month_number, month_name
ORDER BY year ASC, month_number ASC;
````

Data retrieved successfully:

|   year |   month_number | month_name   |   tire_service_invoice_count |
|-------:|---------------:|:-------------|-----------------------------:|
|   2023 |              1 | January      |                          633 |
|   2023 |              2 | February     |                          417 |
|   2023 |              3 | March        |                          451 |
|   2023 |              4 | April        |                          409 |
|   2023 |              5 | May          |                          457 |
|   2023 |              6 | June         |                          445 |
|   2023 |              7 | July         |                          462 |
|   2023 |              8 | August       |                          458 |
|   2023 |              9 | September    |                          442 |
|   2023 |             10 | October      |                          477 |
|   2023 |             11 | November     |                          447 |
|   2023 |             12 | December     |                          444 |
|   2024 |              1 | January      |                          435 |
|   2024 |              2 | February     |                          448 |
|   2024 |              3 | March        |                          487 |
|   2024 |              4 | April        |                          452 |
|   2024 |              5 | May          |                          494 |
|   2024 |              6 | June         |                          474 |
|   2024 |              7 | July         |                          468 |
|   2024 |              8 | August       |                          498 |
|   2024 |              9 | September    |                          470 |
|   2024 |             10 | October      |                          480 |
|   2024 |             11 | November     |                          445 |
|   2024 |             12 | December     |                          510 |
|   2025 |              1 | January      |                          492 |
|   2025 |              2 | February     |                          497 |
|   2025 |              3 | March        |                          526 |
|   2025 |              4 | April        |                          511 |
|   2025 |              5 | May          |                          539 |
|   2025 |              6 | June         |                          501 |
|   2025 |              7 | July         |                          396 |