SQL Query:

````
SELECT
  YEAR(Service_Date) AS year,
  MONTH(Service_Date) AS month_number,
  MONTHNAME(Service_Date) AS month_name,
  COUNT(*) AS tire_service_count
FROM ServiceRecords
WHERE Service_Type_ID = 1
  AND Service_Date >= '2024-01-01'
  AND Service_Date <= '2025-12-31'
GROUP BY
  YEAR(Service_Date),
  MONTH(Service_Date),
  MONTHNAME(Service_Date)
ORDER BY
  year ASC,
  month_number ASC;
````

Data retrieved successfully:

|   year |   month_number | month_name   |   tire_service_count |
|-------:|---------------:|:-------------|---------------------:|
|   2024 |              1 | January      |                  435 |
|   2024 |              2 | February     |                  448 |
|   2024 |              3 | March        |                  487 |
|   2024 |              4 | April        |                  452 |
|   2024 |              5 | May          |                  494 |
|   2024 |              6 | June         |                  474 |
|   2024 |              7 | July         |                  468 |
|   2024 |              8 | August       |                  498 |
|   2024 |              9 | September    |                  470 |
|   2024 |             10 | October      |                  480 |
|   2024 |             11 | November     |                  445 |
|   2024 |             12 | December     |                  510 |
|   2025 |              1 | January      |                  492 |
|   2025 |              2 | February     |                  497 |
|   2025 |              3 | March        |                  526 |
|   2025 |              4 | April        |                  511 |
|   2025 |              5 | May          |                  539 |
|   2025 |              6 | June         |                  501 |
|   2025 |              7 | July         |                  396 |