Python code:

```python
import pandas as pd
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
from metalake import load_data

# Inputs
data_file = "tire_service_monthly_2024_2025.dat"
chat_id = "6883d19154ce45d84d28c705"

# 1. Load full dataset
df = load_data(chat_id, data_file)

# Handle empty DataFrame gracefully
if df.empty:
    print("No data available for the requested tire service monthly metrics.")
else:
    # 2. Sort by year, month_number
    df = df.sort_values(['year', 'month_number']).reset_index(drop=True)

    # 3. Compute MoM change and MoM percent change
    df['mom_change'] = df['tire_service_count'].diff()
    df['mom_pct'] = df['tire_service_count'].pct_change() * 100

    # First row should have nulls for mom_change and mom_pct
    df.loc[0, ['mom_change', 'mom_pct']] = pd.NA

    # 4. Compute YoY percent change for months in 2025 Jan–Jul
    # Merge 2024 and 2025 rows by month_number
    yoy = df.pivot(index='month_number', columns='year', values='tire_service_count')
    # Only compute for months present in both years
    if 2025 in yoy.columns and 2024 in yoy.columns:
        yoy['yoy_pct'] = 100 * (yoy[2025] - yoy[2024]) / yoy[2024]
        # Map yoy_pct back to original DataFrame
        df['yoy_pct'] = df.apply(lambda row: yoy.loc[row['month_number'], 'yoy_pct']
                                 if row['year'] == 2025 and row['month_number'] <= 7 and
                                    row['month_number'] in yoy.index else pd.NA, axis=1)
    else:
        df['yoy_pct'] = pd.NA

    # 5. Select and order columns
    out_cols = ['year', 'month_number', 'month_name', 'tire_service_count', 'mom_change', 'mom_pct', 'yoy_pct']
    df_out = df[out_cols].copy()

    # Format currency columns to two decimals where appropriate
    for col in ['mom_pct', 'yoy_pct']:
        df_out[col] = df_out[col].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "")
    df_out['mom_change'] = df_out['mom_change'].apply(lambda x: f"{x:.2f}" if pd.notnull(x) else "")

    # 6. Save full result as CSV
    csv_path = "files/tire_service_monthly_metrics.csv"
    df_out.to_csv(csv_path, index=False)

    # 7. Display up to first 50 rows as markdown (note if truncated)
    display_rows = df_out.head(50)
    if len(df_out) > 50:
        print("First 50 rows (truncated):")
    else:
        print("Full results:")
    print(display_rows.to_markdown(index=False))

    # 8. Create a line chart: year-month on x, count on y
    # Build a datetime index for continuous x-axis
    import datetime
    df_chart = df.copy()
    df_chart['date'] = df_chart.apply(lambda row: datetime.date(row['year'], row['month_number'], 1), axis=1)
    plt.figure(figsize=(10,6))
    plt.plot(df_chart['date'], df_chart['tire_service_count'], marker='o', label='Tire Service Count')
    plt.title('Monthly Tire Service Count (2024-2025)')
    plt.xlabel('Month')
    plt.ylabel('Tire Service Count')
    plt.grid(True, linestyle='--', alpha=0.5)
    plt.legend()
    plt.tight_layout()
    fig_path = "files/tire_service_trend.png"
    plt.savefig(fig_path)

````

Output:

Full results:
|   year |   month_number | month_name   |   tire_service_count | mom_change   | mom_pct   | yoy_pct   |
|-------:|---------------:|:-------------|---------------------:|:-------------|:----------|:----------|
|   2024 |              1 | January      |                  435 |              |           |           |
|   2024 |              2 | February     |                  448 | 13.00        | 2.99      |           |
|   2024 |              3 | March        |                  487 | 39.00        | 8.71      |           |
|   2024 |              4 | April        |                  452 | -35.00       | -7.19     |           |
|   2024 |              5 | May          |                  494 | 42.00        | 9.29      |           |
|   2024 |              6 | June         |                  474 | -20.00       | -4.05     |           |
|   2024 |              7 | July         |                  468 | -6.00        | -1.27     |           |
|   2024 |              8 | August       |                  498 | 30.00        | 6.41      |           |
|   2024 |              9 | September    |                  470 | -28.00       | -5.62     |           |
|   2024 |             10 | October      |                  480 | 10.00        | 2.13      |           |
|   2024 |             11 | November     |                  445 | -35.00       | -7.29     |           |
|   2024 |             12 | December     |                  510 | 65.00        | 14.61     |           |
|   2025 |              1 | January      |                  492 | -18.00       | -3.53     | 13.10     |
|   2025 |              2 | February     |                  497 | 5.00         | 1.02      | 10.94     |
|   2025 |              3 | March        |                  526 | 29.00        | 5.84      | 8.01      |
|   2025 |              4 | April        |                  511 | -15.00       | -2.85     | 13.05     |
|   2025 |              5 | May          |                  539 | 28.00        | 5.48      | 9.11      |
|   2025 |              6 | June         |                  501 | -38.00       | -7.05     | 5.70      |
|   2025 |              7 | July         |                  396 | -105.00      | -20.96    | -15.38    |

