Python code:

```python
from metalake import load_data
import pandas as pd
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import os

# File paths
csv_path = "files/paid_po_by_month_2025_full.csv"
img_path = "files/paid_po_by_month_2025_full.png"

# Parameters
chat_id = "687fa6b2b6ce4a317eec205d"
data_file = "paid_po_by_month_2025.dat"

# 1. Load filtered data
df = load_data(chat_id, data_file)

# 2. Prepare a DataFrame containing all months of 2025
all_months = pd.date_range("2025-01-01", "2025-12-01", freq="MS")
full_df = pd.DataFrame({"PayMonth": all_months})

if df.empty:
    # If no data, just output zeros for all months
    full_df["Paid_PO_Count"] = 0
else:
    # Ensure PayMonth in df is datetime and remove timezone
    df["PayMonth"] = pd.to_datetime(df["PayMonth"]).dt.tz_localize(None)
    # Merge on PayMonth (left join to keep all months)
    merged = pd.merge(full_df, df, on="PayMonth", how="left")
    # If Paid_PO_Count exists only once, use it directly, else fillna
    if 'Paid_PO_Count_y' in merged.columns and 'Paid_PO_Count_x' in merged.columns:
        merged["Paid_PO_Count"] = merged["Paid_PO_Count_y"].fillna(merged["Paid_PO_Count_x"]).fillna(0).astype(int)
    elif 'Paid_PO_Count' in merged.columns:
        merged["Paid_PO_Count"] = merged["Paid_PO_Count"].fillna(0).astype(int)
    else:
        merged["Paid_PO_Count"] = 0
    full_df = merged[["PayMonth", "Paid_PO_Count"]]

# 3. Sort by PayMonth
full_df = full_df.sort_values("PayMonth").reset_index(drop=True)

# 4. Save as CSV
os.makedirs("files", exist_ok=True)
full_df.to_csv(csv_path, index=False)

# 5. Plot bar chart (all 12 months on X)
full_df['Month_Abbr'] = full_df['PayMonth'].dt.strftime('%b')
plt.figure(figsize=(10, 5))
plt.bar(full_df['Month_Abbr'], full_df['Paid_PO_Count'], color='royalblue')
plt.xlabel('PayMonth')
plt.ylabel('Paid_PO_Count')
plt.title('Purchase Orders Paid per Month – 2025 (Full Year)')
plt.tight_layout()
plt.savefig(img_path)

# Display up to 50 rows as markdown
display_rows = min(50, len(full_df))
print(full_df.head(display_rows).to_markdown(index=False))
if len(full_df) > 50:
    print(f"\nNote: Only the first 50 of {len(full_df)} rows are shown above.")

````

Output:

| PayMonth            |   Paid_PO_Count | Month_Abbr   |
|:--------------------|----------------:|:-------------|
| 2025-01-01 00:00:00 |             211 | Jan          |
| 2025-02-01 00:00:00 |             338 | Feb          |
| 2025-03-01 00:00:00 |             326 | Mar          |
| 2025-04-01 00:00:00 |             270 | Apr          |
| 2025-05-01 00:00:00 |             212 | May          |
| 2025-06-01 00:00:00 |             293 | Jun          |
| 2025-07-01 00:00:00 |             107 | Jul          |
| 2025-08-01 00:00:00 |               0 | Aug          |
| 2025-09-01 00:00:00 |               0 | Sep          |
| 2025-10-01 00:00:00 |               0 | Oct          |
| 2025-11-01 00:00:00 |               0 | Nov          |
| 2025-12-01 00:00:00 |               0 | Dec          |

