SQL Query:

````
WITH BillSummary AS (
    SELECT
        vb.VendorBills_BillId AS BillId,
        MIN(vb.VendorBills_DueDate) AS DueDate,
        MIN(vb.VendorBills_Balance) AS Balance,
        MIN(vb.VendorBills_SupplierName) AS SupplierName
    FROM VendorBills vb
    GROUP BY vb.VendorBills_BillId
)
SELECT
    FORMAT(DATEFROMPARTS(YEAR(sub.DueDate), MONTH(sub.DueDate), 1), 'yyyy-MM') AS DueMonth,
    CAST(sub.SupplierName AS NVARCHAR(MAX)) AS Supplier,
    SUM(sub.Balance) AS OverdueAmount
FROM BillSummary sub
WHERE sub.Balance > 0
  AND sub.DueDate < CAST(GETDATE() AS DATE)
  AND (
      (YEAR(sub.DueDate) = 2025 AND MONTH(sub.DueDate) = 6)
      OR (YEAR(sub.DueDate) = 2025 AND MONTH(sub.DueDate) = 7)
  )
GROUP BY 
    FORMAT(DATEFROMPARTS(YEAR(sub.DueDate), MONTH(sub.DueDate), 1), 'yyyy-MM'),
    CAST(sub.SupplierName AS NVARCHAR(MAX))
ORDER BY 
    Supplier,
    DueMonth;
````

Data retrieved successfully:

| DueMonth   | Supplier            |   OverdueAmount |
|:-----------|:--------------------|----------------:|
| 2025-07    | King's Services     |             775 |
| 2025-07    | Prime Warranty Inc. |            1677 |