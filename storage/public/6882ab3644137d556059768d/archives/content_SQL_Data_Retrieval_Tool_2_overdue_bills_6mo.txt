SQL Query:

````
SELECT 
  FORMAT(DATEFROMPARTS(YEAR(VendorBills_DueDate), MONTH(VendorBills_DueDate), 1), 'yyyy-MM') AS due_month,
  SUM(VendorBills_Balance) AS overdue_amount
FROM VendorBills
WHERE 
  VendorBills_DueDate >= DATEADD(month, -6, DATEFROMPARTS(YEAR(GETDATE()), MONTH(GETDATE()), 1))
  AND VendorBills_DueDate < GETDATE()
  AND VendorBills_Balance > 0
  AND VendorBills_Status NOT IN ('Paid in Full', 'Voided')
GROUP BY FORMAT(DATEFROMPARTS(YEAR(VendorBills_DueDate), MONTH(VendorBills_DueDate), 1), 'yyyy-MM')
ORDER BY due_month ASC;
````

Data retrieved successfully:

| due_month   |   overdue_amount |
|:------------|-----------------:|
| 2025-02     |  16279.6         |
| 2025-03     |  86664.5         |
| 2025-04     | 148607           |
| 2025-05     | 186600           |
| 2025-06     |      3.05992e+06 |
| 2025-07     | 659285           |