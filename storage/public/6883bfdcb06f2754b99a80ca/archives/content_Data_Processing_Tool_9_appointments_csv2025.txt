Python code:

```python
import pandas as pd
import os
from metalake import load_data

# Input parameters
chat_id = "6883c39eb06f2754b99a8274"
data_file_name = "appointments_by_month_2025.dat"
csv_output_path = "files/appointments_by_month_2025.csv"

# Ensure output directory exists
os.makedirs("files", exist_ok=True)

# Load the data
df = load_data(chat_id, data_file_name)

if df.empty:
    print("No data found in appointments_by_month_2025.dat. No file was generated.")
else:
    # Save the DataFrame unchanged to CSV
    df.to_csv(csv_output_path, index=False)
    # Display up to 50 rows
    print(df.head(50).to_markdown(index=False))
    if len(df) > 50:
        print(f"\nNote: Only the first 50 of {len(df)} rows are shown above.")

````

Output:

| month_start   |   appointment_count_2025 |
|:--------------|-------------------------:|
| 2025-01-01    |                     4495 |
| 2025-02-01    |                     4099 |
| 2025-03-01    |                     4635 |
| 2025-04-01    |                     4374 |
| 2025-05-01    |                     4597 |
| 2025-06-01    |                     4369 |
| 2025-07-01    |                     3470 |

