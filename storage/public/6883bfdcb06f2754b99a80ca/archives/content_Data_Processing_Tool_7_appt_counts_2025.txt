Python code:

```python
import pandas as pd
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
from metalake import load_data
from datetime import datetime
import os

# Set variables
data_file_name = "appointments_by_month_2025.dat"
chat_id = "6883c2c8b06f2754b99a823a"
csv_output_path = "files/appointments_by_month_2025.csv"
chart_output_path = "files/appointments_by_month_2025.png"

# Create output directory if it doesn't exist
os.makedirs("files", exist_ok=True)

# Load data
df = load_data(chat_id, data_file_name)

if df.empty:
    print("No data found for 2025 appointment counts. No files were generated.")
else:
    # 1. Save the data exactly as is to CSV
    df.to_csv(csv_output_path, index=False)

    # 2. Prepare data for plotting
    # Convert 'month_start' to datetime and get month abbreviations for x-axis labels
    df_sorted = df.copy()
    df_sorted['month_start'] = pd.to_datetime(df_sorted['month_start'])
    df_sorted = df_sorted.sort_values('month_start')
    df_sorted['month_abbr'] = df_sorted['month_start'].dt.strftime('%b')

    # Plot bar chart
    plt.figure(figsize=(10, 6))
    plt.bar(df_sorted['month_abbr'], df_sorted['appointment_count_2025'], color='skyblue')
    plt.xlabel('Month')
    plt.ylabel('Appointment Count')
    plt.title('Monthly Appointment Counts – 2025')
    plt.tight_layout()
    plt.savefig(chart_output_path)
    plt.close()

    # Display up to 50 rows in markdown table
    display_df = df.head(50)
    print(display_df.to_markdown(index=False))
    if len(df) > 50:
        print(f"\nNote: Only the first 50 of {len(df)} rows are shown above.")

````

Output:

| month_start   |   appointment_count_2025 |
|:--------------|-------------------------:|
| 2025-01-01    |                     4495 |
| 2025-02-01    |                     4099 |
| 2025-03-01    |                     4635 |
| 2025-04-01    |                     4374 |
| 2025-05-01    |                     4597 |
| 2025-06-01    |                     4369 |
| 2025-07-01    |                     3470 |

