SQL Query:

````
WITH BillSummary AS (
    SELECT
        vb.VendorBills_BillId AS BillId,
        MIN(vb.VendorBills_DueDate) AS DueDate,
        MIN(vb.VendorBills_Balance) AS Balance,
        MIN(CASE 
              WHEN COALESCE(vb.VendorBills_SupplierName, '') <> ''
                   THEN vb.VendorBills_SupplierName
              ELSE 'SupplierID_' + CAST(vb.VendorBills_SupplierID AS VARCHAR(50))
            END) AS Supplier
    FROM VendorBills vb
    GROUP BY vb.VendorBills_BillId
)
SELECT 
    FORMAT(DATEFROMPARTS(YEAR(bs.DueDate), MONTH(bs.DueDate), 1), 'yyyy-MM') AS DueMonth,
    bs.Supplier AS Supplier,
    SUM(bs.Balance) AS OverdueAmount
FROM BillSummary bs
WHERE 
    bs.Balance > 0
    AND bs.DueDate < CAST(GETDATE() AS DATE)
    AND (
        (YEAR(bs.DueDate) = 2025 AND MONTH(bs.DueDate) = 6)
        OR
        (YEAR(bs.DueDate) = 2025 AND MONTH(bs.DueDate) = 7)
    )
GROUP BY 
    FORMAT(DATEFROMPARTS(YEAR(bs.DueDate), MONTH(bs.DueDate), 1), 'yyyy-MM'),
    bs.Supplier
ORDER BY 
    DueMonth ASC, 
    OverdueAmount DESC;
````

Data retrieved successfully:

| DueMonth   | Supplier            |   OverdueAmount |
|:-----------|:--------------------|----------------:|
| 2025-07    | Prime Warranty Inc. |            1677 |
| 2025-07    | King's Services     |             775 |