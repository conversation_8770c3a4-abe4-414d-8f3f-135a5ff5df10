Python code:

```python
import pandas as pd
from metalake import load_data

# Load the full, pre-filtered dataset
chat_id = "68840eb379f96eb79e159ff4"
data_file = "weekly_tech_productivity_12w.dat"
df = load_data(chat_id, data_file)

# Check for empty DataFrame
if df.empty:
    print("No data available for the last 12 completed weeks of technician productivity.")
else:
    # Select required columns
    result = df[['week_start', 'avg_jobs_per_tech']].copy()
    # Ensure ascending order by week_start
    result = result.sort_values('week_start', ascending=True)
    # Format avg_jobs_per_tech to two decimals
    result['avg_jobs_per_tech'] = pd.to_numeric(result['avg_jobs_per_tech'], errors='coerce').round(2)
    # Save to CSV and DAT files
    result.to_csv("files/weekly_avg_jobs_last12w.csv", index=False)
    result.to_csv("files/weekly_avg_jobs_last12w.dat", index=False)
    # Display up to 50 rows as Markdown table
    display_rows = min(len(result), 50)
    print(result.head(display_rows).to_markdown(index=False))
    if len(result) > 50:
        print("Note: Table truncated to first 50 rows.")

````

Output:

| week_start               |   avg_jobs_per_tech |
|:-------------------------|--------------------:|
| 2025-04-28T00:00:00.000Z |               24.33 |
| 2025-05-05T00:00:00.000Z |               23.91 |
| 2025-05-12T00:00:00.000Z |               24.84 |
| 2025-05-19T00:00:00.000Z |               24.12 |
| 2025-05-26T00:00:00.000Z |               23.98 |
| 2025-06-02T00:00:00.000Z |               22.84 |
| 2025-06-09T00:00:00.000Z |               22.69 |
| 2025-06-16T00:00:00.000Z |               22.38 |
| 2025-06-23T00:00:00.000Z |               22.51 |
| 2025-06-30T00:00:00.000Z |               22.96 |
| 2025-07-07T00:00:00.000Z |               22.69 |
| 2025-07-14T00:00:00.000Z |               22.98 |

