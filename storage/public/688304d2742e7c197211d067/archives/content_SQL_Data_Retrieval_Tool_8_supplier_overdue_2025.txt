SQL Query:

````
WITH BillSummary AS (
    SELECT
        vb.VendorBills_BillId AS BillId,
        MIN(vb.VendorBills_DueDate) AS DueDate,
        MIN(vb.VendorBills_Balance) AS Balance,
        MIN(vb.VendorBills_SupplierName) AS Supplier
    FROM VendorBills vb
    GROUP BY vb.VendorBills_BillId
)
SELECT
    FORMAT(DATEFROMPARTS(YEAR(s.DueDate), MONTH(s.DueDate), 1), 'yyyy-MM') AS DueMonth,
    COALESCE(CAST(s.Supplier AS NVARCHAR(MAX)), '<Unknown>') AS Supplier,
    SUM(s.Balance) AS OverdueAmount
FROM BillSummary s
WHERE s.Balance > 0
  AND s.DueDate < CAST(GETDATE() AS DATE)
  AND (
        (YEAR(s.DueDate) = 2025 AND MONTH(s.DueDate) = 6)
        OR (YEAR(s.DueDate) = 2025 AND MONTH(s.DueDate) = 7)
      )
GROUP BY 
    FORMAT(DATEFROMPARTS(YEAR(s.DueDate), MONTH(s.DueDate), 1), 'yyyy-MM'),
    COALESCE(CAST(s.Supplier AS NVARCHAR(MAX)), '<Unknown>')
ORDER BY DueMonth, OverdueAmount DESC;
````

Data retrieved successfully:

| DueMonth   | Supplier            |   OverdueAmount |
|:-----------|:--------------------|----------------:|
| 2025-07    | Prime Warranty Inc. |            1677 |
| 2025-07    | King's Services     |             775 |