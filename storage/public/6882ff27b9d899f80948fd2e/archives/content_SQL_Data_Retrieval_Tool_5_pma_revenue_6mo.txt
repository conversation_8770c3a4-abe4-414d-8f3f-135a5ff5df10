SQL Query:

````
SELECT SUM(I.InvoiceSummary_InvoiceAmount) AS TotalRevenue
FROM Contracts AS C
INNER JOIN InvoiceSummary AS I ON C.Contract_Number = I.InvoiceSummary_ContractNumber
WHERE CAST(C.Contract_Type AS VARCHAR(MAX)) IN ('PMA - GEN', 'PMA-COM')
  AND I.InvoiceSummary_InvoiceType = 'Invoice'
  AND I.InvoiceSummary_InvoiceStatus IN ('Sent', 'Paid in Full', 'Partial Payment Received', 'Approved', 'Ready for Accounting Review')
  AND I.InvoiceSummary_InvoiceDate >= DATEADD(month, -6, GETDATE());
````

Data retrieved successfully:

|   TotalRevenue |
|---------------:|
|    1.41871e+06 |