SQL Query:

````
WITH BillSummary AS (
    SELECT
        vb.VendorBills_BillId AS BillId,
        MIN(vb.VendorBills_DueDate) AS DueDate,
        MIN(vb.VendorBills_Balance) AS Balance,
        MIN(vb.VendorBills_SupplierID) AS SupplierID,
        MIN(vb.VendorBills_SupplierName) AS SupplierName
    FROM VendorBills vb
    GROUP BY vb.VendorBills_BillId
)
SELECT
    CASE WHEN bs.SupplierID IS NULL OR bs.SupplierID = 0
         THEN 'Unassigned_Supplier'
         ELSE 'SupplierID_' + CAST(bs.SupplierID AS VARCHAR) END AS SupplierBucket,
    COALESCE(bs.SupplierName, '') AS SampleName,
    SUM(bs.Balance) AS OverdueAmount
FROM BillSummary bs
WHERE bs.Balance > 0
  AND bs.DueDate >= '2025-07-01'
  AND bs.DueDate < '2025-08-01'
  AND bs.DueDate < CAST(GETDATE() AS DATE)
GROUP BY 
    CASE WHEN bs.SupplierID IS NULL OR bs.SupplierID = 0
         THEN 'Unassigned_Supplier'
         ELSE 'SupplierID_' + CAST(bs.SupplierID AS VARCHAR) END,
    COALESCE(bs.SupplierName, '')
ORDER BY OverdueAmount DESC
OFFSET 0 ROWS FETCH NEXT 20 ROWS ONLY;
````

Data retrieved successfully:

| SupplierBucket   | SampleName                                               |   OverdueAmount |
|:-----------------|:---------------------------------------------------------|----------------:|
| SupplierID_81653 | Receiver General For Canada                              |       400000    |
| SupplierID_81817 | Standpoint Technologies LLC                              |        27156.4  |
| SupplierID_98891 | Don Anderson Haulage Ltd (Anderson Heavy Haul & Rigging) |        22312.5  |
| SupplierID_81279 | EDA Mechanical Services Ltd                              |        21711.1  |
| SupplierID_81223 | Schneider Electric Canada Inc                            |        20433.8  |
| SupplierID_81303 | Geo. H. Young & Co. Ltd.                                 |        18867.9  |
| SupplierID_97370 | Dave Copp Steel Inc.                                     |         6622.19 |
| SupplierID_99830 | Nomad Box Bar                                            |         4756.5  |
| SupplierID_99831 | Duke's Burger Company                                    |         4712.96 |
| SupplierID_81469 | West End Radiators                                       |         4698.72 |
| SupplierID_81447 | Tom Beggs Agencies                                       |         4300.07 |
| SupplierID_81739 | Wildwood Transport Inc.                                  |         2800    |
| SupplierID_81345 | John Brooks Company Limited                              |         2295.3  |
| SupplierID_99269 | Marmon Keystone Canada Inc                               |         2167.48 |
| SupplierID_81171 | Samuel Pressure Vessel Group Canada (SPVG Canada)        |         1872.7  |
| SupplierID_81647 | Prime Warranty Inc.                                      |         1677    |
| SupplierID_84588 | Gardewine North                                          |         1588.85 |
| SupplierID_81405 | Purolator Inc.                                           |         1434.05 |
| SupplierID_81123 | Jay's Transportation Group Ltd.                          |         1390.39 |
| SupplierID_85960 | Ainsworth Inc                                            |         1185.87 |