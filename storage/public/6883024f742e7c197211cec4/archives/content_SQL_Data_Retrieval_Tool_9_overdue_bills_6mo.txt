SQL Query:

````
WITH BillSummary AS (
    SELECT
        vb.VendorBills_BillId AS BillId,
        MIN(vb.VendorBills_DueDate) AS DueDate,
        MIN(vb.VendorBills_Balance) AS Balance
    FROM VendorBills vb
    GROUP BY vb.VendorBills_BillId
)
SELECT
    FORMAT(DATEFROMPARTS(YEAR(bs.DueDate), MON<PERSON>(bs.DueDate), 1), 'yyyy-MM') AS DueMonth,
    SUM(bs.Balance) AS OverdueAmount
FROM BillSummary bs
WHERE bs.Balance > 0
  AND bs.DueDate < CAST(GETDATE() AS DATE)
  AND bs.DueDate >= DATEFROMPARTS(YEAR(DATEADD(month, -5, GETDATE())), MONTH(DATEADD(month, -5, GETDATE())), 1)
GROUP BY FORMAT(DATEFROMPARTS(YEAR(bs.DueDate), <PERSON><PERSON><PERSON>(bs.DueDate), 1), 'yyyy-MM')
ORDER BY DueMonth;
````

Data retrieved successfully:

| DueMonth   |   OverdueAmount |
|:-----------|----------------:|
| 2025-02    |  34244.2        |
| 2025-03    | 102102          |
| 2025-04    | 136336          |
| 2025-05    | 164483          |
| 2025-06    |      1.5854e+06 |
| 2025-07    | 539256          |