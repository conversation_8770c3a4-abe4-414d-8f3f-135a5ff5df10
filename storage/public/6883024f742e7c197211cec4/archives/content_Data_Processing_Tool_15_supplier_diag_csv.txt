Python code:

```python
import pandas as pd
from metalake import load_data

# Set identifiers
chat_id = "68830645742e7c197211d10d"
data_file_name = "diagnostic_supplier_bucket_july2025.dat"

# Load the full, filtered dataset
df = load_data(chat_id, data_file_name)

# Handle empty DataFrame
if df.empty:
    print("No supplier diagnostic data available for July 2025.")
else:
    # Save the full result to CSV
    csv_path = "files/diagnostic_supplier_bucket_july2025.csv"
    df.to_csv(csv_path, index=False)
    # Display up to 50 rows as markdown
    display_rows = min(50, len(df))
    print(df.head(display_rows).to_markdown(index=False))
    if len(df) > 50:
        print(f"\nNote: Only first 50 of {len(df)} rows shown above.")

````

Output:

| SupplierBucket   | SampleName                                               |   OverdueAmount |
|:-----------------|:---------------------------------------------------------|----------------:|
| SupplierID_81653 | Receiver General For Canada                              |       400000    |
| SupplierID_81817 | Standpoint Technologies LLC                              |        27156.4  |
| SupplierID_98891 | Don Anderson Haulage Ltd (Anderson Heavy Haul & Rigging) |        22312.5  |
| SupplierID_81279 | EDA Mechanical Services Ltd                              |        21711.1  |
| SupplierID_81223 | Schneider Electric Canada Inc                            |        20433.8  |
| SupplierID_81303 | Geo. H. Young & Co. Ltd.                                 |        18867.9  |
| SupplierID_97370 | Dave Copp Steel Inc.                                     |         6622.19 |
| SupplierID_99830 | Nomad Box Bar                                            |         4756.5  |
| SupplierID_99831 | Duke's Burger Company                                    |         4712.96 |
| SupplierID_81469 | West End Radiators                                       |         4698.72 |
| SupplierID_81447 | Tom Beggs Agencies                                       |         4300.07 |
| SupplierID_81739 | Wildwood Transport Inc.                                  |         2800    |
| SupplierID_81345 | John Brooks Company Limited                              |         2295.3  |
| SupplierID_99269 | Marmon Keystone Canada Inc                               |         2167.48 |
| SupplierID_81171 | Samuel Pressure Vessel Group Canada (SPVG Canada)        |         1872.7  |
| SupplierID_81647 | Prime Warranty Inc.                                      |         1677    |
| SupplierID_84588 | Gardewine North                                          |         1588.85 |
| SupplierID_81405 | Purolator Inc.                                           |         1434.05 |
| SupplierID_81123 | Jay's Transportation Group Ltd.                          |         1390.39 |
| SupplierID_85960 | Ainsworth Inc                                            |         1185.87 |

