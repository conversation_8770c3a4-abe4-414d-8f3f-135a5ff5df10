SQL Query:

````
SELECT
  DATEFROMPARTS(YEAR(I.InvoiceSummary_InvoiceDate), MONTH(I.InvoiceSummary_InvoiceDate), 1) AS MonthStart,
  SUM(I.InvoiceSummary_InvoiceAmount) AS TotalRevenue
FROM Contracts AS C
INNER JOIN InvoiceSummary AS I ON C.Contract_Number = I.InvoiceSummary_ContractNumber
WHERE CAST(C.Contract_Type AS VARCHAR(MAX)) IN ('PMA - GEN', 'PMA-COM')
  AND I.InvoiceSummary_InvoiceType = 'Invoice'
  AND I.InvoiceSummary_InvoiceStatus IN ('Sent', 'Paid in Full', 'Partial Payment Received', 'Approved', 'Ready for Accounting Review')
  AND I.InvoiceSummary_InvoiceDate >= DATEADD(month, -6, GETDATE())
  AND I.InvoiceSummary_InvoiceDate < DATEADD(day, 1, CAST(GETDATE() AS date))
GROUP BY DATEFROMPARTS(YEAR(I.InvoiceSummary_InvoiceDate), MONTH(I.InvoiceSummary_InvoiceDate), 1)
ORDER BY MonthStart ASC;
````

Data retrieved successfully:

| MonthStart               |   TotalRevenue |
|:-------------------------|---------------:|
| 2025-01-01T00:00:00.000Z |        14299.2 |
| 2025-02-01T00:00:00.000Z |       205116   |
| 2025-03-01T00:00:00.000Z |       218458   |
| 2025-04-01T00:00:00.000Z |       267810   |
| 2025-05-01T00:00:00.000Z |       224928   |
| 2025-06-01T00:00:00.000Z |       249157   |
| 2025-07-01T00:00:00.000Z |       228460   |