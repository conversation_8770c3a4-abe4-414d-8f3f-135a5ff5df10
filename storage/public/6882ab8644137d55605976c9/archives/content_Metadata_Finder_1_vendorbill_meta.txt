Metadata of all data sources:

````
[
  {
    "data_source_name": "FieldServio",
    "selected_table_column_metadata": {
      "VendorBills": {
        "table_name": "VendorBills",
        "fields": {
          "VendorBills_BillId": {
            "name": "VendorBills_BillId",
            "description": "A unique integer identifier for each bill record, ensuring distinct referencing of entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1486017",
              "1500576",
              "1502251",
              "1511492",
              "1530883",
              "and 6063 more..."
            ]
          },
          "VendorBills_Total": {
            "name": "VendorBills_Total",
            "description": "A decimal field representing the overall amount of the bill, accommodating both positive and negative values for credits or adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_AmountPaid": {
            "name": "VendorBills_AmountPaid",
            "description": "A decimal field recording the amount paid towards the bill, which can be positive for payments or negative for refunds.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_Balance": {
            "name": "VendorBills_Balance",
            "description": "The outstanding balance on the bill after payments, which can be either positive or negative, indicating amounts still owed.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_BillDate": {
            "name": "VendorBills_BillDate",
            "description": "A datetime field recording the date when the bill was issued, crucial for financial tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_DueDate": {
            "name": "VendorBills_DueDate",
            "description": "A datetime field showing the payment due date for the bill, important for managing accounts payable.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_SupplierID": {
            "name": "VendorBills_SupplierID",
            "description": "An integer foreign key linking to the SupplierInformation table, indicating the supplier associated with the bill.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "81061",
              "81073",
              "81079",
              "81111",
              "81113",
              "and 372 more..."
            ]
          },
          "VendorBills_Status": {
            "name": "VendorBills_Status",
            "description": "A varchar field representing the current processing state of the bill, with values such as 'Paid in Full', 'Approved', or 'Pending Approval'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Paid in Full",
              "Approved",
              "Pending Approval",
              "Voided"
            ]
          }
        }
      },
      "SupplierInformation": {
        "table_name": "SupplierInformation",
        "fields": {
          "Supplier_ID": {
            "name": "Supplier_ID",
            "description": "A unique integer identifier for each supplier, ensuring distinct reference for every supplier record.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "81017",
              "81019",
              "81021",
              "81023",
              "81025",
              "and 561 more..."
            ]
          },
          "Supplier_Name": {
            "name": "Supplier_Name",
            "description": "A unique variable character string representing the supplier's name, facilitating easy identification and retrieval.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Atlas Copco Compressors Canada",
              "Brandt Tractor Ltd.",
              "Toromont Cat",
              "Airtek Limited",
              "Air Unlimited Inc",
              "and 558 more..."
            ]
          }
        }
      }
    },
    "reasoning": "**Reasoning:**  \n\n\nTo calculate overdue vendor bill amounts, we need to identify vendor (supplier) bill records, their financial amounts, billing and due dates, current payment status or outstanding balance, and vendor identifiers. The primary source is the `VendorBills` table, which tracks all supplier bills and related payment/status data. The `SupplierInformation` table is required for vendor (supplier) metadata. Status values and business rules are extracted from the relevant column definitions.\n\n  \n\n**Table Columns:**  \n\n\n- table: VendorBills  \n  columns:  \n    - VendorBills_BillId  \n    - VendorBills_Total  \n    - VendorBills_Balance  \n    - VendorBills_AmountPaid  \n    - VendorBills_BillDate  \n    - VendorBills_DueDate  \n    - VendorBills_Status  \n    - VendorBills_SupplierID  \n\n- table: SupplierInformation  \n  columns:  \n    - Supplier_ID  \n    - Supplier_Name  \n\n  \n\n**Business Rules & Status Code Definitions:**  \n\n- `VendorBills_Status` values: 'Paid in Full', 'Approved', 'Pending Approval', 'Voided'  \n- `VendorBills_Balance`: Outstanding balance (positive or negative); indicates unpaid amount  \n- `VendorBills_SupplierID` links to `SupplierInformation.Supplier_ID`  \n- Overdue is determined by comparing `VendorBills_DueDate` with current date and checking if `VendorBills_Balance` > 0 or status is not 'Paid in Full'",
    "other_table_columns": {
      "AssetRevenue": [
        "AssetRevenue_AssetID",
        "AssetRevenue_AssetName",
        "AssetRevenue_ContractID",
        "AssetRevenue_ContractNumber",
        "AssetRevenue_ContractYear",
        "AssetRevenue_ContractMonth",
        "AssetRevenue_Revenue"
      ],
      "InventoryDetail": [
        "InventoryDetail_ID",
        "InventoryDetail_Type",
        "InventoryDetail_CreatedDate",
        "InventoryDetail_WarehouseID",
        "InventoryDetail_Warehouse",
        "InventoryDetail_BinID",
        "InventoryDetail_BinName",
        "InventoryDetail_ItemID",
        "InventoryDetail_Item_ItemLine",
        "InventoryDetail_Item",
        "InventoryDetail_ItemCode",
        "InventoryDetail_ItemInventoryCosting",
        "InventoryDetail_Item_ItemType",
        "InventoryDetail_Item_Category",
        "InventoryDetail_Item_Description",
        "InventoryDetail_Item_SupplierID",
        "InventoryDetail_Item_SupplierName",
        "InventoryDetail_Item_Price",
        "InventoryDetail_Item_PriceExtended",
        "InventoryDetail_Quantity",
        "InventoryDetail_Cost",
        "InventoryDetail_Item_CostExtended",
        "InventoryDetail_WarehouseMaxQty",
        "InventoryDetail_WarehouseMinQty",
        "InventoryDetail_WarehouseOffice",
        "InventoryDetail_Warehouse_ItemQty",
        "InventoryDetail_EquipmentID",
        "InventoryDetail_LocationID",
        "InventoryDetail_Equipment_SerialNumber",
        "InventoryDetail_Equipment_Make",
        "InventoryDetail_Equipment_Model",
        "InventoryDetail_Equipment_SoldCondition",
        "InventoryDetail_JobID",
        "InventoryDetail_JobItemId",
        "InventoryDetail_JobDateEntered",
        "InventoryDetail_JobCustomerID",
        "InventoryDetail_JobCustomer",
        "InventoryDetail_JobName",
        "InventoryDetail_JobStatus",
        "InventoryDetail_LastInvoiceDate",
        "InventoryDetail_InvoiceCount",
        "InventoryDetail_ReportRunDate"
      ],
      "EquipmentHours": [
        "EquipmentHours_EquipmentID",
        "EquipmentHours_Name",
        "EquipmentHours_Description",
        "EquipmentHours_Status",
        "EquipmentHours_SerialNumber",
        "EquipmentHours_StartDate",
        "EquipmentHours_WarrantyExpiration",
        "EquipmentHours_EndCustomerID",
        "EquipmentHours_EndCustomerName",
        "EquipmentHours_SoldCustomerID",
        "EquipmentHours_SoldCustomerName",
        "EquipmentHours_LocationID",
        "EquipmentHours_LocationName",
        "EquipmentHours_LocationAddress",
        "EquipmentHours_LocationCity",
        "EquipmentHours_LocationState",
        "EquipmentHours_LocationZip",
        "EquipmentHours_LocationDefaultJobOffice",
        "EquipmentHours_Hours",
        "EquipmentHours_HoursUpdatedDate",
        "EquipmentHours_JobCompletedDate"
      ],
      "AR_Pacing": [
        "ARPacing_OfficeID",
        "ARPacing_OfficeName",
        "ARPacing_AgingDate",
        "ARPacing_CurrentDue",
        "ARPacing_CreditTotal",
        "ARPacing_ThirtyDue",
        "ARPacing_SixtyDue",
        "ARPacing_NinetyDue",
        "ARPacing_BeyondNinetyDue",
        "ARPacing_PastDue",
        "ARPacing_TotalDue",
        "ARPacing_AverageInvoiceAge"
      ],
      "InventoryReturns": [
        "InventoryReturns_ReturnID",
        "InventoryReturns_SourceID",
        "InventoryReturns_DestinationID",
        "InventoryReturns_ItemID",
        "InventoryReturns_ActionTaken",
        "InventoryReturns_JobID",
        "InventoryReturns_JobName",
        "InventoryReturns_POName",
        "InventoryReturns_ReturnQueueType",
        "InventoryReturns_ReturnDestinationType",
        "InventoryReturns_ItemName",
        "InventoryReturns_ItemCode",
        "InventoryReturns_ItemDescription",
        "InventoryReturns_ReturnQuantity",
        "InventoryReturns_UnitCost",
        "InventoryReturns_ExtendedCost",
        "InventoryReturns_UnitPrice",
        "InventoryReturns_ExtendedPrice",
        "InventoryReturns_ReturnReason",
        "InventoryReturns_ReturnDate",
        "InventoryReturns_OfficeName",
        "InventoryReturns_WarehouseName",
        "InventoryReturns_ReturnedBy"
      ],
      "ContactInformation": [
        "Contact_ID",
        "Contact_CustomerID",
        "Contact_CompanyName",
        "Contact_CompanyType",
        "Contact_FirstName",
        "Contact_LastName",
        "Contact_Title",
        "Contact_Category",
        "Contact_Email",
        "Contact_Key",
        "Contact_isActive",
        "Contact_WorkPhone",
        "Contact_CellPhone",
        "Contact_Fax",
        "Contact_HomePhone",
        "Contact_Address1",
        "Contact_Address2",
        "Contact_City",
        "Contact_State",
        "Contact_Zip",
        "Contact_DateEntered",
        "Contact_EnteredBy",
        "Contact_SalesPerson",
        "Contact_SalesPerson_ID",
        "Contact_Other",
        "Contact_NextTel_ID",
        "Contact_Supplier_ID",
        "Contact_Supplier",
        "CON_ModifiedBy"
      ],
      "LocationInformation": [
        "Location_ID",
        "Location_Name",
        "Location_CustomerID",
        "Location_Customer_AccountNumber",
        "Location_CustomerName",
        "Location_Customer_Currency",
        "Location_Customer_Active",
        "Location_Customer_NextServiceDate",
        "Location_CustomerOffice",
        "Location_PreferredTechnician",
        "Location_Address1",
        "Location_Address2",
        "Location_City",
        "Location_State",
        "Location_Zip",
        "Location_Latitude",
        "Location_Longitude",
        "Location_DateEntered",
        "Location_EnteredBy",
        "Location_SalespersonPrimary",
        "Location_TaxSchedule",
        "Location_DefaultJobOffice"
      ],
      "PaylocityProductivity": [],
      "ContractInvoices": [
        "ContractInvoices_ContractID",
        "ContractInvoices_ContractNumber",
        "ContractInvoices_StartDate",
        "ContractInvoices_CustomerID",
        "ContractInvoices_CustomerName",
        "ContractInvoices_CustomerAccountNumber",
        "ContractInvoices_Office",
        "ContractInvoices_ContractAmount",
        "ContractInvoices_EstimatedServiceAmount",
        "ContractInvoices_RenewalDate",
        "ContractInvoices_SentTransactionDate",
        "ContractInvoices_Status",
        "ContractInvoices_Type",
        "ContractInvoices_InvoiceID",
        "ContractInvoices_InvoiceName",
        "ContractInvoices_InvoiceSource",
        "ContractInvoices_JobID",
        "ContractInvoices_JobName",
        "ContractInvoices_PO",
        "ContractInvoices_InvoiceTotalAmount",
        "ContractInvoices_PaidAmount",
        "ContractInvoices_InvoiceStatus",
        "ContractInvoices_InvoiceDate",
        "ContractInvoices_LastInvoiceDate",
        "ContractInvoices_InvoiceDueDate",
        "ContractInvoices_InvoiceSentDate",
        "ContractInvoices_InvoiceGLDate",
        "ContractInvoices_InvoiceItemID",
        "ContractInvoices_InvoiceBasicItemID",
        "ContractInvoices_InvoiceItemDescription",
        "ContractInvoices_InvoiceItemQuantity",
        "ContractInvoices_InvoiceItemUnitPrice",
        "ContractInvoices_InvoiceItemTaxAmount",
        "ContractInvoices_InvoiceItemTotalAmount",
        "ContractInvoices_TaxSchedule",
        "ContractInvoices_PaymentTerms"
      ],
      "InvoicesNotSent": [
        "InvoicesNotSent_InvoiceID",
        "InvoicesNotSent_InvoiceName",
        "InvoicesNotSent_InvoiceType",
        "InvoicesNotSent_InvoiceSource",
        "InvoicesNotSent_InvoiceDate",
        "InvoicesNotSent_CustomerID",
        "InvoicesNotSent_CustomerName",
        "InvoicesNotSent_InvoiceTotal",
        "InvoicesNotSent_CreatedByID",
        "InvoicesNotSent_CreatedByName",
        "InvoicesNotSent_JobID",
        "InvoicesNotSent_JobName",
        "InvoicesNotSent_ContractID",
        "InvoicesNotSent_ContractNumber",
        "InvoicesNotSent_PONumber",
        "InvoicesNotSent_ShipToName"
      ],
      "InventoryDetailLastSevenDays": [
        "InventoryDetailLastSevenDays_ID",
        "InventoryDetailLastSevenDays_Type",
        "InventoryDetailLastSevenDays_Status",
        "InventoryDetailLastSevenDays_CreatedDate",
        "InventoryDetailLastSevenDays_POItemID",
        "InventoryDetailLastSevenDays_WarehouseID",
        "InventoryDetailLastSevenDays_Warehouse",
        "InventoryDetailLastSevenDays_BinID",
        "InventoryDetailLastSevenDays_BinName",
        "InventoryDetailLastSevenDays_ItemID",
        "InventoryDetailLastSevenDays_Item_ItemLine",
        "InventoryDetailLastSevenDays_Item",
        "InventoryDetailLastSevenDays_ItemCode",
        "InventoryDetailLastSevenDays_ItemInventoryCosting",
        "InventoryDetailLastSevenDays_Item_ItemType",
        "InventoryDetailLastSevenDays_Item_Category",
        "InventoryDetailLastSevenDays_Item_Description",
        "InventoryDetailLastSevenDays_Item_SupplierID",
        "InventoryDetailLastSevenDays_Item_SupplierName",
        "InventoryDetailLastSevenDays_Item_Price",
        "InventoryDetailLastSevenDays_Item_PriceExtended",
        "InventoryDetailLastSevenDays_Quantity",
        "InventoryDetailLastSevenDays_OrigQuantity",
        "InventoryDetailLastSevenDays_Cost",
        "InventoryDetailLastSevenDays_Item_CostExtended",
        "InventoryDetailLastSevenDays_WarehouseMaxQty",
        "InventoryDetailLastSevenDays_WarehouseMinQty",
        "InventoryDetailLastSevenDays_WarehouseOffice",
        "InventoryDetailLastSevenDays_Warehouse_ItemQty",
        "InventoryDetailLastSevenDays_EquipmentID",
        "InventoryDetailLastSevenDays_LocationID",
        "InventoryDetailLastSevenDays_Equipment_SerialNumber",
        "InventoryDetailLastSevenDays_Equipment_Model",
        "InventoryDetailLastSevenDays_JobID",
        "InventoryDetailLastSevenDays_JobItemId",
        "InventoryDetailLastSevenDays_JobDateEntered",
        "InventoryDetailLastSevenDays_JobCustomerID",
        "InventoryDetailLastSevenDays_JobCustomer",
        "InventoryDetailLastSevenDays_JobName",
        "InventoryDetailLastSevenDays_JobStatus",
        "InventoryDetailLastSevenDays_LastInvoiceDate",
        "InventoryDetailLastSevenDays_InvoiceCount"
      ],
      "InventoryDetailLastCloseout": [],
      "JobRecommendationTasks": [
        "RecTasks_Jobid",
        "RecTasks_JobName",
        "RecTasks_ ProjectName",
        "RecTasks_JobStatus",
        "RecTasks_JobUrgency",
        "RecTasks_JobType",
        "RecTasks_Office",
        "RecTasks_Type",
        "RecTasks_EquipmentID",
        "RecTasks_Equipment",
        "RecTasks_Serial",
        "RecTasks_Task",
        "RecTasks_TaskDescription",
        "RecTasks_RecommendationUrgency",
        "RecTasks_RecommendationType",
        "RecTasks_Recommendation",
        "RecTasks_CreatedBy",
        "RecTasks_RecStatus",
        "RecTasks_CreatedDate",
        "RecTasks_CustomerID",
        "RecTasks_CustomerName",
        "RecTasks_AccountNumber",
        "RecTasks_LocationID",
        "RecTasks_LocationName",
        "RecTasks_ContactID",
        "RecTasks_ContactName",
        "RecTasks_InvoiceStatus"
      ],
      "BidsSummary": [
        "BidsSummary_ID",
        "BidsSummary_Name",
        "BidsSummary_Description",
        "BidsSummary_ProjectName",
        "BidsSummary_Category",
        "BidsSummary_Status",
        "BidsSummary_Type",
        "BidsSummary_SalesPersonID",
        "BidsSummary_SalesPersonName",
        "BidsSummary_DateEntered",
        "BidsSummary_DateSubmitted",
        "BidsSummary_StartDate",
        "BidsSummary_BidDate",
        "BidsSummary_EstimatedDuration",
        "BidsSummary_EstimatedDays",
        "BidsSummary_NumberOfTechs",
        "BidsSummary_CreatedByID",
        "BidsSummary_CreatedByName",
        "BidsSummary_ModifiedByID",
        "BidsSummary_ModifiedByName",
        "BidsSummary_ModifiedDate",
        "BidsSummary_Priority",
        "BidsSummary_AdditionalSpecifications",
        "BidsSummary_Office",
        "BidsSummary_Customer_ID",
        "BidsSummary_Customer",
        "BidsSummary_JobLocation",
        "BidsSummary_Location_ID",
        "BidsSummary_Location_Name",
        "BidsSummary_Contact_ID",
        "BidsSummary_Contact_Name",
        "BidsSummary_AssignedResourceID",
        "BidsSummary_AssignedResourceName",
        "BidsSummary_Contract_ID",
        "BidsSummary_Contract_Number",
        "BidsSummary_QuoteAmount",
        "BidsSummary_CustomerPO",
        "BidsSummary_StatusHistory",
        "BidSummary_FirstProposalSentDate",
        "BidSummary_CurrentProposalSentDate",
        "BidSummary_ProposalSentQty"
      ],
      "InvoiceSummary": [
        "InvoiceSummary_InvoiceID",
        "InvoiceSummary_InvoiceName",
        "InvoiceSummary_InvoiceType",
        "InvoiceSummary_JobID",
        "InvoiceSummary_JobName",
        "InvoiceSummary_ContractID",
        "InvoiceSummary_ContractNumber",
        "InvoiceSummary_ContractName",
        "InvoiceSummary_CustomerID",
        "InvoiceSummary_CustomerName",
        "InvoiceSummary_BillToID",
        "InvoiceSummary_BillToName",
        "InvoiceSummary_ShipToName",
        "InvoiceSummary_ShipToCity",
        "InvoiceSummary_ShipToState",
        "InvoiceSummary_ShipToZip",
        "InvoiceSummary_JobType",
        "InvoiceSummary_InvoiceStatus",
        "InvoiceSummary_SalesResource",
        "InvoiceSummary_VoidedBy",
        "InvoiceSummary_CreatedBy",
        "InvoiceSummary_SentBy",
        "InvoiceSummary_Office",
        "InvoiceSummary_Currency",
        "InvoiceSummary_CurrencySubTotal",
        "InvoiceSummary_CurrencyTax",
        "InvoiceSummary_CurrencyTotalAmt",
        "InvoiceSummary_InvoiceSource",
        "InvoiceSummary_InvoiceAmount",
        "InvoiceSummary_InvoiceAmountDue",
        "InvoiceSummary_PONumber",
        "InvoiceSummary_PaymentTerms",
        "InvoiceSummary_InvoiceDate",
        "InvoiceSummary_SentDate",
        "InvoiceSummary_SentTransactionDate",
        "InvoiceSummary_VoidedDate",
        "InvoiceSummary_VoidedTransactionDate",
        "InvoiceSummary_DueDate",
        "InvoiceSummary_PaidDate",
        "InvoiceSummary_PaidStatus",
        "InvoiceSummary_DeferredRevenue",
        "InvoiceSummary_Retainage",
        "InvoiceSummary_TaxSchedule",
        "InvoiceSummary_TaxExemptionCode",
        "InvoiceSummary_LastNote",
        "InvoiceSummary_InvoiceEmployeeID",
        "InvoiceSummary_InvoiceEmployeeName"
      ],
      "PurchaseOrderSummary": [
        "PurchaseOrderSummary_ID",
        "PurchaseOrderSummary_Name",
        "PurchaseOrderSummary_Job_ID",
        "PurchaseOrderSummary_Job_Name",
        "PurchaseOrderSummary_Bid_Name",
        "PurchaseOrderSummary_Status",
        "PurchaseOrderSummary_Type",
        "PurchaseOrderSummary_PaymentTerms",
        "PurchaseOrderSummary_DueDays",
        "PurchaseOrderSummary_Terms",
        "PurchaseOrderSummary_Office",
        "PurchaseOrderSummary_Supplier_ID",
        "PurchaseOrderSummary_Supplier_Name",
        "PurchaseOrderSummary_Warehouse_Name",
        "PurchaseOrderSummary_Date",
        "PurchaseOrderSummary_OrderedBy",
        "PurchaseOrderSummary_Job_Location",
        "PurchaseOrderSummary_Via",
        "PurchaseOrderSummary_DeliveryDate",
        "PurchaseOrderSummary_DateRequired",
        "PurchaseOrderSummary_Total"
      ],
      "SalesAndPurchases": [
        "SalesAndPurchases_ID",
        "SalesAndPurchases_ItemName",
        "SalesAndPurchases_Description",
        "SalesAndPurchases_Category",
        "SalesAndPurchases_Type",
        "SalesAndPurchases_InventoryType",
        "SalesAndPurchases_Manufacturer_Code",
        "SalesAndPurchases_SupplierID",
        "SalesAndPurchases_SupplierAccount",
        "SalesAndPurchases_SupplierName",
        "SalesAndPurchases_ProductLineName",
        "SalesAndPurchases_Price",
        "SalesAndPurchases_InvoiceSalesQty",
        "SalesAndPurchases_InvoiceSalesTotal",
        "SalesAndPurchases_PurchaseOrderQty",
        "SalesAndPurchases_PurchaseOrderTotal"
      ],
      "SupplierInformation": [
        "Supplier_AccountNumber",
        "Supplier_TaxID",
        "Supplier_Terms",
        "Supplier_Category",
        "Supplier_QualityRating",
        "Supplier_1099Type",
        "Supplier_Notes",
        "Supplier_Email",
        "Supplier_Website",
        "Supplier_Phone",
        "Supplier_Fax",
        "Supplier_Phone2",
        "Supplier_Address",
        "Supplier_Address2",
        "Supplier_City",
        "Supplier_State",
        "Supplier_Zip",
        "Supplier_Internal",
        "Supplier_Freight",
        "Supplier_PaidACH",
        "Supplier_Currency",
        "Supplier_Active"
      ],
      "Rentals": [
        "Rental_Asset_ID",
        "Rental_Equipment_ID",
        "Rental_Asset_Name",
        "Rental_SerialNumber",
        "Rental_Group_RequiresSerialForCheckout",
        "Rental_Status",
        "Rental_Group",
        "Rental_Type",
        "Rental_Office",
        "Rental_InitialValue",
        "Rental_CurrentValue",
        "Rental_Contract_ID",
        "Rental_ContractStatus",
        "Rental_ContractNumber",
        "Rental_ContractStartDate",
        "Rental_EndDate",
        "Rental_Contract_Unit",
        "Rental_Contract_Cost",
        "Rental_Contract_Tax",
        "Rental_Contract_Total"
      ],
      "Events": [
        "Event_ID",
        "Event_Date",
        "Event_Subject",
        "Event_Text",
        "Event_Status",
        "Event_Priority",
        "Event_EventType",
        "Event_EventStartTime",
        "Event_EventEndTime",
        "Event_NextCallDate",
        "Event_AssignedEmployee",
        "Event_CompanyID",
        "Event_CompanyName",
        "Event_CompanyOffice",
        "Event_Attendees",
        "Event_OpportunityID",
        "Event_OpportunityName",
        "Event_OpportunityNextSteps"
      ],
      "ContractEquipment": [
        "ContractEquipment_ContractID",
        "ContractEquipment_ContractName",
        "ContractEquipment_ContractStatus",
        "ContractEquipment_ContractType",
        "ContractEquipment_ContractStartDate",
        "ContractEquipment_ContractRenewalDate",
        "ContractEquipment_ProjectName",
        "ContractEquipment_ServicesScheduledThrough",
        "ContractEquipment_CustomerID",
        "ContractEquipment_Customer",
        "ContractEquipment_CustomerAccountNumber",
        "ContractEquipment_LocationID",
        "ContractEquipment_LocationName",
        "ContractEquipment_EquipmentID",
        "ContractEquipment_EquipmentName",
        "ContractEquipment_EquipmentDescription",
        "ContractEquipment_ItemNumber",
        "ContractEquipment_EquipmentSerial",
        "ContractEquipment_EquipmentStatus",
        "ContractEquipment_EquipmentType",
        "ContractEquipment_EquipmentManufacturer",
        "ContractEquipment_BaseItemID",
        "ContractEquipment_BaseItemName"
      ],
      "InventoryArchive": [
        "InventoryArchive_ArchiveDate",
        "InventoryArchive_OfficeName",
        "InventoryArchive_ItemID",
        "InventoryArchive_ItemCode",
        "InventoryArchive_ItemName",
        "InventoryArchive_InventoryType",
        "InventoryArchive_Quantity",
        "InventoryArchive_Cost"
      ],
      "VendorBills": [
        "VendorBills_ReferenceNumber",
        "VendorBills_SubTotal",
        "VendorBills_TaxAmount",
        "VendorBills_CurrencyTotal",
        "VendorBills_CurrencySubTotal",
        "VendorBills_CurrencyTax",
        "VendorBills_CurrencyPaid",
        "VendorBills_CurrencyBalance",
        "VendorBills_DiscountDate",
        "VendorBills_DateCreated",
        "VendorBills_SupplierName",
        "VendorBills_Memo",
        "VendorBills_Items_ID",
        "VendorBills_Items_Name",
        "VendorBills_Item_Description",
        "VendorBills_Item_Manufacturer",
        "VendorBills_Items_Quantity",
        "VendorBills_Items_Cost",
        "VendorBills_JobID",
        "VendorBills_JobName",
        "VendorBills_POID",
        "VendorBills_POName",
        "VendorBills_VendorBillID",
        "VendorBills_OfficeID",
        "VendorBills_Office",
        "VendorBills_CreatedBy",
        "VendorBills_TaxAuthorityId",
        "VendorBills_TaxAuthorityName",
        "VendorBills_TaxScheduleName",
        "VendorBills_State",
        "VendorBills_TaxPaid",
        "VendorBills_BillTypeID",
        "VendorBills_InvType",
        "VendorBills_InvStatus"
      ],
      "EnhancedCommissionPlans": [],
      "Items": [
        "Items_ID",
        "Items_Name",
        "Items_Description",
        "Items_Category",
        "Items_ItemCode",
        "Items_ProductLine",
        "Items_Type",
        "Items_InventoryType",
        "Items_SupplierID",
        "Items_SupplierName",
        "Items_ManufacturerID",
        "Items_ManufacturerName",
        "Items_RetailPrice",
        "Items_CostDate",
        "Items_MaterialCost",
        "Items_MaterialCostDate",
        "Items_MFGPrice",
        "Items_MFGPriceDate",
        "Items_MinAmount",
        "Items_MaxAmount",
        "Items_DateCreated",
        "Items_Createdby",
        "Items_LastModified",
        "Items_ModifiedBy",
        "Items_Status",
        "Items_InventoryCosting",
        "Items_AllowTechAdd",
        "Items_RequiresPO",
        "Items_AcceptsPayment",
        "Items_LinkedItemsMethod",
        "Items_HasDynamicPricing",
        "Items_AllowFractionalInvoicing",
        "Items_AvalaraInSyc"
      ],
      "AssetDepreciation": [
        "AssetDepreciation_AssetID",
        "AssetDepreciation_EquipmentID",
        "AssetDepreciation_StartDate",
        "AssetDepreciation_EndDate",
        "AssetDepreciation_Amount",
        "AssetDepreciation_FinancialYear",
        "AssetDepreciation_Quarter",
        "AssetDepreciation_Period",
        "AssetDepreciation_DepreciationType",
        "AssetDepreciation_ProcessedFlag"
      ],
      "JobsBacklog": [
        "JobsBacklog_ID",
        "JobsBacklog_Name",
        "JobsBacklog_Status",
        "JobsBacklog_Type",
        "JobsBacklog_Office",
        "JobsBacklog_SalesPerson",
        "JobsBacklog_PromisedDate",
        "JobsBacklog_Revenue",
        "JobsBacklog_Cost"
      ],
      "JobsDetail": [
        "JobsDetail_ID",
        "JobsDetail_Name",
        "JobsDetail_ProjectName",
        "JobsDetail_Description",
        "JobsDetail_Status",
        "JobsDetail_CustomerPO",
        "JobsDetail_Type",
        "JobsDetail_Office",
        "JobsDetail_DepartmentID",
        "JobsDetail_DepartmentName",
        "JobsDetail_EnteredBy",
        "JobsDetail_ModifiedBy",
        "JobsDetail_SalesPerson",
        "JobsDetail_Technician",
        "JobsDetail_AssignedResource",
        "JobsDetail_EstimatedDuration",
        "JobsDetail_EstimatedDays",
        "JobsDetail_ScheduledDate",
        "JobsDetail_PromisedDate",
        "JobsDetail_CompletedDate",
        "JobsDetail_POFufilledDate",
        "JobsDetail_PODeliveryDate",
        "JobsDetail_POPromiseDate",
        "JobsDetail_ModifiedDate",
        "JobsDetail_InvoiceDate",
        "JobsDetail_DateEntered",
        "JobsDetail_ContractID",
        "JobsDetail_ContractNumber",
        "JobsDetail_Urgency",
        "JobsDetail_Priority",
        "JobsDetail_PriorityNotes",
        "JobsDetail_JobItemLineID",
        "JobsDetail_Item",
        "JobsDetail_ItemId",
        "JobsDetail_ItemDescription",
        "JobsDetail_ItemType",
        "JobsDetail_ItemCategory",
        "JobsDetail_ItemProductLine",
        "JobsDetail_ItemStatus",
        "JobsDetail_Item_Technician",
        "JobsDetail_Item_TechnicianID",
        "JobsDetail_Item_TechnicianStatus",
        "JobsDetail_ItemLaborResourceID",
        "JobsDetail_Item_EquipmentID",
        "JobsDetail_Item_Equipment",
        "JobsDetail_Item_EquipmentSerial",
        "JobsDetail_Item_ActualQuantity",
        "JobsDetail_Item_OrderedQuantity",
        "JobsDetail_Item_EstimatedQuantity",
        "JobsDetail_Item_InvoicedQuantity",
        "JobsDetail_Item_UsedQuantity",
        "JobsDetail_Item_PickedQuantity",
        "JobsDetail_Item_WIPQuantity",
        "JobsDetail_Item_WIPCost",
        "JobsDetail_Item_WIPExtendedCost",
        "JobsDetail_Item_MaterialCost",
        "JobsDetail_Item_POCost",
        "JobsDetail_Item_JobRevenue",
        "JobsDetail_Item_JobCost",
        "JobsDetail_Item_InvoiceName",
        "JobsDetail_Item_InvoicedRevenue",
        "JobsDetail_Item_InvoicedCost",
        "JobsDetail_Item_UnitPrice",
        "JobsDetail_Item_CurrencyPrice",
        "JobsDetail_Item_Total",
        "JobsDetail_Item_TotalDiscountAmt",
        "JobsDetail_Item_DiscountPercent",
        "JobsDetail_Item_ItemNotes",
        "JobsDetail_Customer_ID",
        "JobsDetail_Customer_Name",
        "JobsDetail_Customer_AccountNumber",
        "JobsDetail_Location_ID",
        "JobsDetail_Location_Name",
        "JobsDetail_Location_Address1",
        "JobsDetail_Location_Address2",
        "JobsDetail_Location_City",
        "JobsDetail_Location_State",
        "JobsDetail_Location_Zip",
        "JobsDetail_ShipToOverride",
        "JobsDetail_Contact_ID",
        "JobsDetail_Contact_Name",
        "JobsDetail_Contact_Title",
        "JobsDetail_Contact_Email",
        "JobsDetail_Contact_Cell",
        "JobsDetail_Contact_WorkPhone"
      ],
      "CustomerInformation": [
        "Customer_ID",
        "Customer_Name",
        "Customer_AccountNumber",
        "Customer_OfficeID",
        "Customer_OfficeName",
        "Customer_Status",
        "Customer_CreditStatus",
        "Customer_Type",
        "Customer_Currency",
        "Customer_Reseller",
        "Customer_WarrantyCustomer",
        "Customer_Active",
        "Customer_NextServiceDate",
        "Customer_Phone",
        "Customer_Email",
        "Customer_Website",
        "Customer_InvoicePreference",
        "Customer_Fax",
        "Customer_Address1",
        "Customer_Address2",
        "Customer_City",
        "Customer_State",
        "Customer_Zip",
        "Customer_Country",
        "Customer_Category",
        "Customer_DateCreated",
        "Customer_Balance",
        "Customer_Terms",
        "Customer_StatementCycle",
        "Customer_TaxID",
        "Customer_TaxExemptCertificateNum",
        "Customer_SalesPerson",
        "Customer_SalesPersonId",
        "Customer_Contacts",
        "Customer_AvgDaysToPay",
        "Customer_CreditLimit",
        "Customer_RequiresPO"
      ],
      "PayrollReport": [
        "PayrollReport_ID",
        "PayrollReport_EmployeeID",
        "PayrollReport_EmployeeNumber",
        "PayrollReport_EmployeeName",
        "PayrollReport_WeekDayName",
        "PayrollReport_WorkDate",
        "PayrollReport_StartTimeTime",
        "PayrollReport_EndTimeTime",
        "PayrollReport_TimeTypeName",
        "PayrollReport_EntryHours",
        "PayrollReport_RegularHours",
        "PayrollReport_TimeAndHalfHours",
        "PayrollReport_JobType",
        "PayrollReport_JobName",
        "PayrollReport_JobStatus",
        "PayrollReport_JobId",
        "PayrollReport_BilledHourSet",
        "PayrollReport_CompanyID",
        "PayrollReport_CompanyAccountNumber",
        "PayrollReport_CompanyName",
        "PayrollReport_LocationID",
        "PayrollReport_LocationName",
        "PayrollReport_LocationStreet",
        "PayrollReport_LocationAdditionalAddressInfo",
        "PayrollReport_LocationCity",
        "PayrollReport_LocationState",
        "PayrollReport_LocationZip",
        "PayrollReport_LastInvoiceDate",
        "PayrollReport_LastInvoiceName"
      ],
      "OpportunityAttributes": [],
      "EquipmentDetail": [
        "Equipment_ID",
        "Equipment_Name",
        "Equipment_Description",
        "Equipment_SerialNumber",
        "Equipment_EquipmentType",
        "Equipment_ManufacturerName",
        "Equipment_StartDate",
        "Equipment_WarrantyExpiration",
        "Equipment_EquipmentStatus",
        "Equipment_EndCustomerName",
        "Equipment_SoldCustomerName",
        "Equipment_SalesPersonName",
        "Equipment_LocationName",
        "Equipment_LocationAddr1",
        "Equipment_LocationAddr2",
        "Equipment_LocationCity",
        "Equipment_LocationState",
        "Equipment_LocationZip",
        "Equipment_Hours",
        "Equipment_HoursLastUpdated",
        "Equipment_ItemName",
        "Equipment_ItemLine",
        "Equipment_InvoiceID",
        "Equipment_InvoiceNumber",
        "Equipment_InvoiceDate",
        "Equipment_CreatedDate",
        "Equipment_CreatedBy",
        "Equipment_ModifiedBy",
        "Equipment_ModifiedDate",
        "Equipment_PurchaseOrderID",
        "Equipment_PurchaseOrderName",
        "Equipment_PurchaseOrderBillID",
        "Equipment_PurchaseOrderBillNumber",
        "Equipment_PurchaseOrderBillDate",
        "Equipment_ContractID",
        "Equipment_ContractNumber",
        "Equipment_ContractExpiration",
        "Equipment_ContractStatus",
        "Equipment_IsInventory"
      ],
      "IncomeStatement": [
        "IncomeStatement_GLID",
        "IncomeStatement_CompanyName",
        "IncomeStatement_AccountType",
        "IncomeStatement_SubTypeID",
        "IncomeStatement_BaseAccountName",
        "IncomeStatement_AccountCode",
        "IncomeStatement_OfficeID",
        "IncomeStatement_OfficeName",
        "IncomeStatement_DepartmentName",
        "IncomeStatement_OfficeAccountingCode",
        "IncomeStatement_DepartmentAccountingCode",
        "IncomeStatement_AccountName",
        "IncomeStatement_GroupName",
        "IncomeStatement_Amount",
        "IncomeStatement_TransactionDate"
      ],
      "InvoiceOpportunityEvents": [
        "InvoiceOpportunityEvents_BidID",
        "InvoiceOpportunityEvents_OpportunityID",
        "InvoiceOpportunityEvents_OpportunityName",
        "InvoiceOpportunityEvents_OpportunityAmount",
        "InvoiceOpportunityEvents_OpportunityExpRevenue",
        "InvoiceOpportunityEvents_OpportunityType",
        "InvoiceOpportunityEvents_LeadSource",
        "InvoiceOpportunityEvents_OpportunityCreationDate",
        "InvoiceOpportunityEvents_OpportunityCloseDate",
        "InvoiceOpportunityEvents_OpportunityStage",
        "InvoiceOpportunityEvents_JobID",
        "InvoiceOpportunityEvents_JobName",
        "InvoiceOpportunityEvents_ProjectName",
        "InvoiceOpportunityEvents_JobType",
        "InvoiceOpportunityEvents_JobStatus",
        "InvoiceOpportunityEvents_CustomerID",
        "InvoiceOpportunityEvents_CustomerName",
        "InvoiceOpportunityEvents_JobOffice",
        "InvoiceOpportunityEvents_InvoiceID",
        "InvoiceOpportunityEvents_InvoiceBillToID",
        "InvoiceOpportunityEvents_TaxSchedule",
        "InvoiceOpportunityEvents_InvoiceName",
        "InvoiceOpportunityEvents_InvoiceQuoteAmt",
        "InvoiceOpportunityEvents_InvoiceAmount",
        "InvoiceOpportunityEvents_InvoiceStatus",
        "InvoiceOpportunityEvents_InvoiceDate",
        "InvoiceOpportunityEvents_SentDate",
        "InvoiceOpportunityEvents_VoidedDate",
        "InvoiceOpportunityEvents_DueDate",
        "InvoiceOpportunityEvents_PaidDate",
        "InvoiceOpportunityEvents_EventCount",
        "InvoiceOpportunityEvents_EmailCount",
        "InvoiceOpportunityEvents_FaceToFaceCount",
        "InvoiceOpportunityEvents_FollowUpCount",
        "InvoiceOpportunityEvents_ColdCallCount",
        "InvoiceOpportunityEvents_PreventiveMaintenanceCount",
        "InvoiceOpportunityEvents_LastEventDate"
      ],
      "UserPermissions": [
        "UserPermissions_Role_Name",
        "UserPermissions_Permission_Name",
        "UserPermissions_Description",
        "UserPermissions_Employee_Name",
        "UserPermissions_EmployeeID"
      ],
      "PurchaseOrderDetail": [
        "PurchaseOrderDetail_ID",
        "PurchaseOrderDetail_Name",
        "PurchaseOrderDetail_Job_ID",
        "PurchaseOrderDetail_Job_Name",
        "PurchaseOrderDetail_Job_Type",
        "PurchaseOrderDetail_Job_Status",
        "PurchaseOrderDetail_Job_Office",
        "PurchaseOrderDetail_Job_AssignedResource",
        "PurchaseOrderDetail_Job_Urgency",
        "PurchaseOrderDetail_Job_PriorityNotes",
        "PurchaseOrderDetail_Job_CustomerPO",
        "PurchaseOrderDetail_Job_Description",
        "PurchaseOrderDetail_Job_EstimatedDuration",
        "PurchaseOrderDetail_Job_EstimatedDays",
        "PurchaseOrderDetail_Job_NumberTechs",
        "PurchaseOrderDetail_Job_ShipToOverride",
        "PurchaseOrderDetail_Job_CreatedBy",
        "PurchaseOrderDetail_Job_CreatedDate",
        "PurchaseOrderDetail_Job_ModifiedBy",
        "PurchaseOrderDetail_Job_ModifiedDate",
        "PurchaseOrderDetail_Bid_Name",
        "PurchaseOrderDetail_Status",
        "PurchaseOrderDetail_Type",
        "PurchaseOrderDetail_Terms",
        "PurchaseOrderDetail_Notes",
        "PurchaseOrderDetail_Office",
        "PurchaseOrderDetail_PO_ShipToOverride",
        "PurchaseOrderDetail_Customer_ID",
        "PurchaseOrderDetail_Customer_Name",
        "PurchaseOrderDetail_Supplier_ID",
        "PurchaseOrderDetail_Supplier_Name",
        "PurchaseOrderDetail_Supplier_AccountNo",
        "PurchaseOrderDetail_Supplier_Phone",
        "PurchaseOrderDetail_Warehouse_ID",
        "PurchaseOrderDetail_Warehouse_Name",
        "PurchaseOrderDetail_Warehouse_Address1",
        "PurchaseOrderDetail_Warehouse_City",
        "PurchaseOrderDetail_Warehouse_State",
        "PurchaseOrderDetail_Warehouse_Zip",
        "PurchaseOrderDetail_Date",
        "PurchaseOrderDetail_OrderedBy_ID",
        "PurchaseOrderDetail_OrderedBy",
        "PurchaseOrderDetail_CreatedBy",
        "PurchaseOrderDetail_Item_POFufilledDate",
        "PurchaseOrderDetail_PODeliveryDate",
        "PurchaseOrderDetail_Job_Location",
        "PurchaseOrderDetail_Via",
        "PurchaseOrderDetail_DateRequired",
        "PurchaseOrderDetail_PromiseDate",
        "PurchaseOrderDetail_Item_ID",
        "PurchaseOrderDetail_Item_Name",
        "PurchaseOrderDetail_Item_Code",
        "PurchaseOrderDetail_Item_Manufacturer",
        "PurchaseOrderDetail_Item_Description",
        "PurchaseOrderDetail_Item_Status",
        "PurchaseOrderDetail_Item_Quantity",
        "PurchaseOrderDetail_Item_OrderQty",
        "PurchaseOrderDetail_Item_Cost",
        "PurchaseOrderDetail_Item_ExtendedCost",
        "PurchaseOrderDetail_Item_Price",
        "PurchaseOrderDetail_Item_ExtendedPrice",
        "PurchaseOrderDetail_Item_ItemLine",
        "PurchaseOrderDetail_Item_JobRevenue",
        "PurchaseOrderDetail_Item_Category",
        "PurchaseOrderDetail_Item_Type",
        "PurchaseOrderDetail_Item_ReceivedByID",
        "PurchaseOrderDetail_Item_ReceivedBy",
        "PurchaseOrderDetail_JobItemID",
        "PurchaseOrderDetail_POLineID",
        "PurchaseOrderDetail_POModifiedDate"
      ],
      "Contracts": [
        "Contract_ContractID",
        "Contract_Number",
        "Contract_ProjectName",
        "Contract_Status",
        "Contract_PO",
        "Contract_ContractTypeID",
        "Contract_Type",
        "Contract_StartDate",
        "Contract_FirstVisitDate",
        "Contract_NumberOfVisits",
        "Contract_NumberOfYears",
        "Contract_FirstInvoiceDate",
        "Contract_ContractRenewalDate",
        "Contract_ServiceScheduledThrough",
        "Contract_OfficeID",
        "Contract_Office",
        "Contract_Amount",
        "Contract_Estimated_Cost",
        "Contract_TotalInvoicedAmount",
        "Contract_TotalInvoicedTaxAmount",
        "Contract_TotalAmountPaid",
        "Contract_TotalInvoiceAmountNotSent",
        "Contract_TotalJobCount",
        "Contract_IncurredJobCostsToDate",
        "Contract_PaidJobCostsToDate",
        "Contract_JobVisitsComplete",
        "Contract_ProjectedCostsNotIncurred",
        "Contract_CustomerID",
        "Contract_CustomerName",
        "Contract_CustomerAccountNumber",
        "Contract_CustomerOffice",
        "Contract_CustomerStatus",
        "Contract_CustomerIsActive",
        "Contract_CustomerAddress",
        "Contract_LocationID",
        "Contract_LocationAddress1",
        "Contract_LocationAddress2",
        "Contract_LocationCity",
        "Contract_LocationState",
        "Contract_LocationZip",
        "Contract_ContactPersonID",
        "Contract_ContactPerson",
        "Contract_ContractSalesPersonID",
        "Contract_ContractSalesPerson",
        "Contract_CustomerSalesPersonID",
        "Contract_CustomerSalesPerson",
        "Contract_OpportunityID"
      ],
      "InvoiceDetail": [
        "InvoiceDetail_InvoiceID",
        "InvoiceDetail_InvoiceName",
        "InvoiceDetail_JobID",
        "InvoiceDetail_JobName",
        "InvoiceDetail_ContractID",
        "InvoiceDetail_ContractNumber",
        "InvoiceDetail_ContractName",
        "InvoiceDetail_CustomerID",
        "InvoiceDetail_CustomerName",
        "InvoiceDetail_BillToID",
        "InvoiceDetail_BillToName",
        "InvoiceDetail_ShipToName",
        "InvoiceDetail_ShipToCity",
        "InvoiceDetail_ShipToState",
        "InvoiceDetail_ShipToZip",
        "InvoiceDetail_JobType",
        "InvoiceDetail_InvoiceStatus",
        "InvoiceDetail_SalesResource",
        "InvoiceDetail_VoidedBy",
        "InvoiceDetail_CreatedBy",
        "InvoiceDetail_SentBy",
        "InvoiceDetail_Office",
        "InvoiceDetail_Currency",
        "InvoiceDetail_CurrencySubTotal",
        "InvoiceDetail_CurrencyTax",
        "InvoiceDetail_CurrencyTotalAmt",
        "InvoiceDetail_InvoiceType",
        "InvoiceDetail_InvoiceSource",
        "InvoiceDetail_InvoiceAmount",
        "InvoiceDetail_InvoiceAmountDue",
        "InvoiceDetail_PONumber",
        "InvoiceDetail_PaymentTerms",
        "InvoiceDetail_InvoiceDate",
        "InvoiceDetail_SentDate",
        "InvoiceDetail_SentTransactionDate",
        "InvoiceDetail_VoidedDate",
        "InvoiceDetail_VoidedTransactionDate",
        "InvoiceDetail_DueDate",
        "InvoiceDetail_PaidDate",
        "InvoiceDetail_PaidStatus",
        "InvoiceDetail_DeferredRevenue",
        "InvoiceDetail_Retainage",
        "InvoiceDetail_TaxSchedule",
        "InvoiceDetail_ItemID",
        "InvoiceDetail_ItemCategory",
        "InvoiceDetail_ItemType",
        "InvoiceDetail_ItemName",
        "InvoiceDetail_ItemDescription",
        "InvoiceDetail_ItemQuantity",
        "InvoiceDetail_ItemUnitCost",
        "InvoiceDetail_ItemInvoicedCost",
        "InvoiceDetail_ItemUnitPrice",
        "InvoiceDetail_ItemCurrencyPrice",
        "InvoiceDetail_ItemInvoicedPrice",
        "InvoiceDetail_ItemPriceOverride",
        "InvoiceDetail_ItemPriceOverrideAmount",
        "InvoiceDetail_ItemTaxAmount",
        "InvoiceDetail_ItemGrandTotalPrice",
        "InvoiceDetail_ItemDiscountPercent",
        "InvoiceDetail_ItemDiscountAmount",
        "InvoiceDetail_ItemCurrencyAmount",
        "InvoiceDetail_ItemCurrencyTax",
        "InvoiceDetail_ItemCurrencyTotal",
        "InvoiceDetail_ItemLaborResource",
        "InvoiceDetail_JobTechnicians",
        "InvoiceDetail_InvoiceEmployeeID",
        "InvoiceDetail_InvoiceEmployeeName"
      ],
      "WarehouseInventory": [
        "Warehouse_Inventory_ID",
        "Warehouse_Id",
        "Warehouse_Name",
        "Warehouse_Office_ID",
        "Warehouse_Office",
        "Warehouse_ItemName",
        "Warehouse_ItemID",
        "Warehouse_MinQty",
        "Warehouse_MaxQty",
        "Warehouse_ItemQty",
        "Warehouse_Inventory_Status"
      ],
      "TimeSheetsDetail": [
        "TimeSheetsDetail_EmployeeId",
        "TimeSheetsDetail_EmployeeName",
        "TimeSheetsDetail_EmployeeNumber",
        "TimeSheetsDetail_ReportMonthStart",
        "TimeSheetsDetail_ReportMonthEnd",
        "TimeSheetsDetail_WorkDate",
        "TimeSheetsDetail_StartTime",
        "TimeSheetsDetail_EndTime",
        "TimeSheetsDetail_TimeType",
        "TimeSheetsDetail_Description",
        "TimeSheetsDetail_JobHours",
        "TimeSheetsDetail_NonJobHours",
        "TimeSheetsDetail_JobID",
        "TimeSheetsDetail_JobName",
        "TimeSheetsDetail_JobType",
        "TimeSheetsDetail_JobCity",
        "TimeSheetsDetail_JobState",
        "TimeSheetsDetail_JobPostalCode",
        "TimeSheetsDetail_OfficeName",
        "TimeSheetsDetail_ApprovedDate",
        "TimeSheetsDetail_ApprovedBy",
        "TimeSheetsDetail_BilledLaborType"
      ],
      "Payments": [
        "Payment_ID",
        "Payment_EntityType",
        "Payment_BankingTransactionID",
        "Payment_InvoiceId",
        "Payment_InvoiceName",
        "Payment_InvoiceCustomerID",
        "Payment_BillID",
        "Payment_BillReference",
        "Payment_BillVendorID",
        "Payment_PaymentAmount",
        "Payment_CurrencyName",
        "Payment_CurrencyRate",
        "Payment_CurrencyAmount",
        "Payment_CreatedDate",
        "Payment_PaymentDate",
        "Payment_ProcessedDate",
        "Payment_UpdatedDate",
        "Payment_PaymentRecordSource",
        "Payment_PaymentNotes",
        "Payment_CreatedBy",
        "Payment_TypeName",
        "Payment_Status"
      ],
      "BidsDetail": [
        "BidsDetail_ID",
        "BidsDetail_Name",
        "BidsDetail_Status",
        "BidsDetail_Item_ID",
        "BidsDetail_Item_Name",
        "BidsDetail_Equipment_ID",
        "BidsDetail_Equipment_Name",
        "BidsDetail_Item_UnitCost",
        "BidsDetail_Item_CostOverride",
        "BidsDetail_Item_CurrencyPrice",
        "BidsDetail_Item_DiscountPct",
        "BidsDetail_Item_DiscountAmt",
        "BidsDetail_Item_QuantityToStockOrder",
        "BidsDetail_Item_QuantityToStd",
        "BidsDetail_Item_QuantityToOrderOverNight",
        "BidsDetail_Item_QuantityOverride",
        "BidsDetail_Item_SelectedSupplier_ID",
        "BidsDetail_Item_SupplierName",
        "BidsDetail_Item_GroupID",
        "BidsDetail_OfficeID",
        "BidsDetail_OfficeName",
        "BidsDetail_StartDate",
        "BidsDetail_ContractID",
        "BidsDetail_Contract_Number"
      ],
      "CustomerPricing": [
        "CustomerPricing_PricingType",
        "CustomerPricing_CustomerID",
        "CustomerPricing_CustomerName",
        "CustomerPricing_CustomerOffice",
        "CustomerPricing_Name",
        "CustomerPricing_Description",
        "CustomerPricing_Discount_Markup",
        "CustomerPricing_Amount"
      ],
      "Opportunities": [
        "Opportunity_ID",
        "Opportunity_Name",
        "Opportunity_Office",
        "Opportunity_Description",
        "Opportunity_NextSteps",
        "Opportunity_Salesperson",
        "Opportunity_Stage",
        "Opportunity_Type",
        "Opportunity_ForecastCategory",
        "Opportunity_LeadSource",
        "Opportunity_CreatedDate",
        "Opportunity_LastModifiedDate",
        "Opportunity_CloseDate",
        "Opportunity_CreatedBy",
        "Opportunity_Probability",
        "Opportunity_Amount",
        "Opportunity_ExpectedRevenue",
        "Opportunity_CustomerName"
      ],
      "Assets": [
        "Asset_ID",
        "Asset_Office",
        "Asset_Location",
        "Asset_Group",
        "Asset_Name",
        "Asset_IsRental",
        "Asset_Type",
        "Asset_InitialValue",
        "Asset_CurrentValue",
        "Asset_DepreciationGLAccount",
        "Asset_DateCreated",
        "Asset_AssetValueGLAccount",
        "Asset_RevenueGLAccount",
        "Asset_EquipmentID",
        "Asset_EquipmentName",
        "Asset_EquipmentDescription",
        "Asset_SerialNumber",
        "Asset_EquipmentMake",
        "Asset_EquipmentModel",
        "Asset_EquipmentReading",
        "Asset_EquipmentDateCreated",
        "Asset_Status",
        "Asset_EquipmentStartDate",
        "Asset_EquipmentStatus",
        "Asset_ProcessedDepreciation",
        "Asset_FutureDepreciation",
        "Asset_DepreciationStart",
        "Asset_DepreciationEnd"
      ],
      "Tasks": [
        "Tasks_TaskID",
        "Tasks_TaskType",
        "Tasks_TaskStatus",
        "Tasks_TaskTitle",
        "Tasks_TaskDescription",
        "Tasks_TaskDueDate",
        "Tasks_TaskCreatedDate",
        "Tasks_TaskCreatedByID",
        "Tasks_TaskCreatedBy",
        "Tasks_ModifiedDate",
        "Tasks_TaskModifiedByID",
        "Tasks_TaskModifiedBy",
        "Tasks_TaskMembers",
        "Tasks_TaskSubscribers",
        "Tasks_TaskPendingJobID",
        "Tasks_TaskPendingJobName",
        "Tasks_TaskJobID",
        "Tasks_TaskJobName",
        "Tasks_TaskOpportunityID",
        "Tasks_TaskOpportunityName",
        "Tasks_TaskContractID",
        "Tasks_TaskContractName"
      ],
      "JobsSummary": [
        "JobsSummary_ID",
        "JobsSummary_Name",
        "JobsSummary_ProjectName",
        "JobsSummary_Description",
        "JobsSummary_Status",
        "JobsSummary_InvoiceStatus",
        "JobsSummary_InvoicesAllSent",
        "JobsSummary_Type",
        "JobsSummary_DepartmentID",
        "JobsSummary_DepartmentName",
        "JobsSummary_Office",
        "JobsSummary_ContractID",
        "JobsSummary_ContractNumber",
        "JobsSummary_Customer_ID",
        "JobsSummary_Customer",
        "JobsSummary_CustomerAccountNumber",
        "JobsSummary_JobLocation",
        "JobsSummary_Location_ID",
        "JobsSummary_Location_Name",
        "JobsSummary_CreatedByID",
        "JobsSummary_CreatedByName",
        "JobsSummary_ModifiedByID",
        "JobsSummary_ModifiedByName",
        "JobsSummary_ModifiedDate",
        "JobsSummary_SalesPerson",
        "JobsSummary_AssignedResourceID",
        "JobsSummary_AssignedResourceName",
        "JobsSummary_Priority_Notes",
        "JobsSummary_ScheduledDate",
        "JobsSummary_PromisedDate",
        "JobsSummary_CompletedDate",
        "JobsSummary_DateEntered",
        "JobsSummary_QuoteAmount",
        "JobsSummary_Urgency",
        "JobsSummary_Priority",
        "JobsSumary_CreditOverrideNote",
        "JobsSummary_FirstProposalSentDate",
        "JobsSummary_CurrentProposalSentDate",
        "JobsSummary_ProposalSentQty",
        "JobsSummary_BidCreatedDate",
        "JobsSummary_BidCreatedByID",
        "JobsSummary_BidCreatedByName",
        "JobsSummary_BidModifiedDate",
        "JobsSummary_BidModifiedByID",
        "JobsSummary_BidModifiedByName",
        "JobsSummary_BidAssignedResourceID",
        "JobsSummary_BidAssignedResourceName",
        "JobsSummary_BidOrigDescription"
      ],
      "InventoryAdjustments": [
        "InventoryAdjustments_ItemID",
        "InventoryAdjustments_AdjustmentDate",
        "InventoryAdjustments_ItemCode",
        "InventoryAdjustments_ItemName",
        "InventoryAdjustments_ItemDescription",
        "InventoryAdjustments_OfficeName",
        "InventoryAdjustments_WarehouseName",
        "InventoryAdjustments_AdjustedQuantity",
        "InventoryAdjustments_AdjustmentReason",
        "InventoryAdjustments_Cost",
        "InventoryAdjustments_Currency",
        "InventoryAdjustments_ItemCategory",
        "InventoryAdjustments_ItemProductLine",
        "InventoryAdjustments_Supplier",
        "InventoryAdjustments_Manufacturer",
        "InventoryAdjustments_AdjustedBy",
        "InventoryAdjustments_AdjustmentTransactionID",
        "InventoryAdjustments_InventoryID"
      ],
      "GeneralLedgerDetail": [
        "GeneralLedger_ChartType",
        "GeneralLedger_ChartName",
        "GeneralLedger_SubTypeName",
        "GeneralLedger_Code",
        "GeneralLedger_Amount",
        "GeneralLedger_Debit",
        "GeneralLedger_Credit",
        "GeneralLedger_TransactionID",
        "GeneralLedger_TransactionDate",
        "GeneralLedger_TransactionType",
        "GeneralLedger_CreatedByName",
        "GeneralLedger_OfficeID",
        "GeneralLedger_OfficeAcctCode",
        "GeneralLedger_OfficeName",
        "GeneralLedger_DepartmentID",
        "GeneralLedger_DepartmentName",
        "GeneralLedger_DepartmentAcctCode",
        "GeneralLedger_CustomerId",
        "GeneralLedger_CustomerName",
        "GeneralLedger_CustomerAccountNum",
        "GeneralLedger_LocationID",
        "GeneralLedger_LocationName",
        "GeneralLedger_InvoiceID",
        "GeneralLedger_InvoiceVoidedDate",
        "GeneralLedger_InvoiceDate",
        "GeneralLedger_InvoiceSentDate",
        "GeneralLedger_InvoiceSentTransactionDate",
        "GeneralLedger_InvoiceDueDate",
        "GeneralLedger_InvoiceName",
        "GeneralLedger_InvoiceNotes",
        "GeneralLedger_JobID",
        "GeneralLedger_JobName",
        "GeneralLedger_JobType",
        "GeneralLedger_JobDescription",
        "GeneralLedger_InvoiceSalesPerson",
        "GeneralLedger_CustomerAssignedSalesPerson",
        "GeneralLedger_BillID",
        "GeneralLedger_BillRefNumber",
        "GeneralLedger_BillMemo",
        "GeneralLedger_BillStatus",
        "GeneralLedger_PaymentNotes",
        "GeneralLedger_Description"
      ],
      "EmployeeDetail": [
        "EmployeeDetail_EmployeeID",
        "EmployeeDetail_EmployeeNumber",
        "EmployeeDetail_EmployeeName",
        "EmployeeDetail_Status",
        "EmployeeDetail_PrimaryOfficeID",
        "EmployeeDetail_PrimaryOffice",
        "EmployeeDetail_Address1",
        "EmployeeDetail_Address2",
        "EmployeeDetail_City",
        "EmployeeDetail_State",
        "EmployeeDetail_Zip",
        "EmployeeDetail_WorkPhone",
        "EmployeeDetail_CellPhone",
        "EmployeeDetail_Title",
        "EmployeeDetail_Email",
        "EmployeeDetail_IsSalesPerson",
        "EmployeeDetail_IsJobAssignee",
        "EmployeeDetail_IsTechnician",
        "EmployeeDetail_ManagerID",
        "EmployeeDetail_ManagerName"
      ],
      "EquipmentAttributes": [
        "Equipment_ID",
        "Equipment_Name",
        "Equipment_SerialNumber",
        "Equipment_ItemNumber",
        "Compressor_CFM",
        "Compressor_HP",
        "Compressor_Voltage",
        "Generator_Fuel",
        "Generator_Kilowatt",
        "Generator_Voltage"
      ],
      "ServicePackageDetail": [
        "ServicePackage_ID",
        "ServicePackage_Name",
        "ServicePackage_Equipment_ID",
        "ServicePackage_Equipment_Name",
        "ServicePackage_Equipment_SerialNumber",
        "ServicePackage_Customer_ID",
        "ServicePackage_Customer_Name",
        "ServicePackage_Location_ID",
        "ServicePackage_Location_Name",
        "ServicePackage_ItemID",
        "ServicePackage_Item_ItemCode",
        "ServicePackage_Item_Name",
        "ServicePackage_Item_Quantity",
        "ServicePackage_Item_OverridePrice"
      ],
      "OpportunityProposalDetail": [
        "OpportunityProposalDetail_ID",
        "OpportunityProposalDetail_Name",
        "OpportunityProposalDetail_Office",
        "OpportunityProposalDetail_Description",
        "OpportunityProposalDetail_NextSteps",
        "OpportunityProposalDetail_Salesperson",
        "OpportunityProposalDetail_Stage",
        "OpportunityProposalDetail_Type",
        "OpportunityProposalDetail_ForecastCategory",
        "OpportunityProposalDetail_LeadSource",
        "OpportunityProposalDetail_CloseDate",
        "OpportunityProposalDetail_Probability",
        "OpportunityProposalDetail_Amount",
        "OpportunityProposalDetail_ExpectedRevenue",
        "OpportunityProposalDetail_CustomerName",
        "OpportunityProposalDetail_CustomerID",
        "OpportunityProposalDetail_LocationID",
        "OpportunityProposalDetail_LocationName",
        "OpportunityProposalDetail_JobName",
        "OpportunityProposalDetail_JobType",
        "OpportunityProposalDetail_JobStatus",
        "OpportunityProposalDetail_JobDatePromised",
        "OpportunityProposalDetail_ProposalDate",
        "OpportunityProposalDetail_ItemCode",
        "OpportunityProposalDetail_ItemName",
        "OpportunityProposalDetail_ItemCategory",
        "OpportunityProposalDetail_ItemManufacturer",
        "OpportunityProposalDetail_EquipItemNumber",
        "OpportunityProposalDetail_EquipName",
        "OpportunityProposalDetail_EquipSerialNum",
        "OpportunityProposalDetail_Quantity",
        "OpportunityProposalDetail_UnitPrice",
        "OpportunityProposalDetail_CurrencyPrice",
        "OpportunityProposalDetail_Subtotal",
        "OpportunityProposalDetail_Tax",
        "OpportunityProposalDetail_TotalPrice",
        "OpportunityProposalDetail_DiscountAmt"
      ]
    }
  }
]
````