Metadata of all data sources:

````
[
  {
    "data_source_name": "superlube",
    "selected_table_column_metadata": {
      "Invoices": {
        "table_name": "Invoices",
        "fields": {
          "Invoice_ID": {
            "name": "Invoice_ID",
            "description": "A unique integer identifier for each invoice in the table, serving as the primary key to distinctly reference each record.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 271030 more..."
            ]
          },
          "Service_Type_ID": {
            "name": "Service_Type_ID",
            "description": "An integer that categorizes the type of service billed in the invoice, linking to the ServiceTypes table and limited to values under 100.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "Customer_ID": {
            "name": "Customer_ID",
            "description": "An integer that identifies the customer related to the invoice, establishing a relationship with the CustomerData table, indicating multiple invoices per customer.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "4",
              "6",
              "7",
              "and 17423 more..."
            ]
          },
          "Service_ID": {
            "name": "Service_ID",
            "description": "A unique integer identifier for the specific service rendered, serving as a foreign key to the ServiceRecords table, ensuring each service is distinctly referenced.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 271030 more..."
            ]
          },
          "Invoice_Date": {
            "name": "Invoice_Date",
            "description": "The date when the invoice was issued, crucial for tracking service delivery timing and for financial reporting.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2019-01-06",
              "2019-01-09",
              "2019-01-10",
              "2019-01-08",
              "2019-01-13",
              "and 2352 more..."
            ]
          },
          "Total_Charge": {
            "name": "Total_Charge",
            "description": "A floating-point number indicating the total billed amount for services on the invoice, essential for revenue tracking and financial analysis.",
            "dataType": "float",
            "is_unstructured": false,
            "Subset of values": [
              "22.5",
              "22.5001",
              "22.5002",
              "22.5003",
              "22.5004",
              "and 266698 more..."
            ]
          }
        }
      },
      "ServiceRecords": {
        "table_name": "ServiceRecords",
        "fields": {
          "Service_ID": {
            "name": "Service_ID",
            "description": "A unique integer that identifies each service record, ensuring distinct tracking and referencing of individual service entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 270722 more..."
            ]
          },
          "Customer_ID": {
            "name": "Customer_ID",
            "description": "An integer that links to the associated customer's profile, allowing for the grouping of multiple service records under the same customer.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "4",
              "6",
              "7",
              "and 17407 more..."
            ]
          },
          "Employee_ID": {
            "name": "Employee_ID",
            "description": "An integer identifier for the employee who performed the service, enabling tracking of employee performance and service responsibilities.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "6",
              "and 52 more..."
            ]
          },
          "Service_Type_ID": {
            "name": "Service_Type_ID",
            "description": "An integer that categorizes the service provided, linking to predefined service types in the ServiceTypes table.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "Time_Taken": {
            "name": "Time_Taken",
            "description": "A positive floating-point number representing the duration of the service in hours or minutes, allowing for analysis of service efficiency.",
            "dataType": "float",
            "is_unstructured": false,
            "Subset of values": [
              "14.25",
              "14.2501",
              "14.2502",
              "14.2503",
              "14.2504",
              "and 261607 more..."
            ]
          },
          "Service_Date": {
            "name": "Service_Date",
            "description": "The date when the service was performed, recorded in a date format to track service history and assist in scheduling.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2019-01-06",
              "2019-01-09",
              "2019-01-10",
              "2019-01-08",
              "2019-01-13",
              "and 2350 more..."
            ]
          }
        }
      },
      "ServiceTypes": {
        "table_name": "ServiceTypes",
        "fields": {
          "Service_Type_ID": {
            "name": "Service_Type_ID",
            "description": "A unique integer identifier for each service type, ensuring no two services share the same ID, with values ranging from 1 to 99.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "base_charge": {
            "name": "base_charge",
            "description": "The starting fee associated with the service type, represented as a floating-point number, which varies uniquely across services.",
            "dataType": "float",
            "is_unstructured": false,
            "All distinct values": [
              "25",
              "60",
              "100",
              "150"
            ]
          }
        }
      },
      "Salaries": {
        "table_name": "Salaries",
        "fields": {
          "Salary_ID": {
            "name": "Salary_ID",
            "description": "A unique integer identifier for each salary record, serving as the primary key for the Salaries table to ensure distinct tracking of salary entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 2708 more..."
            ]
          },
          "Employee_ID": {
            "name": "Employee_ID",
            "description": "An integer that uniquely identifies the employee associated with the salary record, allowing for multiple entries per employee over time to reflect salary history.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "6",
              "and 64 more..."
            ]
          },
          "Pay_Date": {
            "name": "Pay_Date",
            "description": "The date on which the salary payment was issued, essential for payroll management and tracking historical payment schedules.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2019-01-31",
              "2019-02-28",
              "2019-03-31",
              "2019-04-30",
              "2019-05-31",
              "and 72 more..."
            ]
          },
          "Amount": {
            "name": "Amount",
            "description": "A floating-point number representing the total salary amount paid to the employee on the corresponding Pay_Date, reflecting individual salary payments.",
            "dataType": "float",
            "is_unstructured": false,
            "Subset of values": [
              "2500",
              "2800",
              "3000",
              "3200",
              "3500",
              "and 1 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nTo calculate gross profit and related P&L line items (Gross Profit, Gross Income/Total Income, Cost of Goods Sold) for QuickBooks-like reporting, we need revenue (Total_Charge from Invoices), costs (if present\u2014none explicit, but ServiceRecords may inform COGS if labor/time included), and all relevant date fields for filtering (e.g., Invoice_Date). Business rules for profit calculation and period filtering are inferred from the metadata descriptions.\n\n  \n**Table Columns:**  \n- table: Invoices  \n   columns: Invoice_ID, Service_Type_ID, Customer_ID, Service_ID, Invoice_Date, Total_Charge  \n- table: ServiceRecords  \n   columns: Service_ID, Customer_ID, Employee_ID, Service_Type_ID, Time_Taken, Service_Date  \n- table: ServiceTypes  \n   columns: Service_Type_ID, base_charge  \n- table: Salaries  \n   columns: Salary_ID, Employee_ID, Pay_Date, Amount  \n  \n\n**Relevant business rules/considerations:**  \n- Gross Profit = Total Income (sum of Total_Charge) - Cost of Goods Sold (labor, parts, or base_charge if applicable).  \n- Use Invoice_Date or Service_Date for date/period filtering.  \n- No explicit indication of cash vs accrual basis\u2014choose by whether you use Invoice_Date (accrual) or payment records (not present).  \n",
    "other_table_columns": {
      "ServiceTypes": [
        "Description",
        "Average_Time_Taken",
        "Time_Range"
      ],
      "CustomerFeedbacks": [
        "Feedback_ID",
        "Service_ID",
        "Rating",
        "Comments",
        "Review_Date"
      ],
      "Appointments": [
        "Appointment_ID",
        "Customer_ID",
        "Vehicle_ID",
        "Service_Type_ID",
        "Appointment_Date",
        "Schedule_Date"
      ],
      "EmployeeData": [
        "Employee_ID",
        "Name",
        "Role",
        "Service_Type_ID",
        "Hire_Date",
        "Leave_Date"
      ],
      "CustomerData": [
        "Customer_ID",
        "Name",
        "Contact_Information",
        "Join_Date",
        "Vehicle_ID",
        "Vehicle_Make",
        "Vehicle_Model"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier for each account, allowing distinct reference and access to individual account records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "and 22 more..."
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "The complete and descriptive name of the account, ensuring clarity in its purpose and type, unique for each account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "PST Expense (BC)",
              "Sales",
              "Uncategorized Income",
              "Billable Expense Income",
              "Sales of Product Income",
              "and 22 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorizes the account into distinct types such as liability, asset, income, or expense, aiding in the identification of its general financial nature.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Other Current Liability",
              "Income",
              "Expense",
              "Other Current Asset",
              "Accounts Receivable",
              "Cost of Goods Sold",
              "Equity"
            ]
          },
          "name": {
            "name": "name",
            "description": "The common name used for identifying the account in reports and queries, unique to each record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "PST Expense (BC)",
              "Sales",
              "Uncategorized Income",
              "Billable Expense Income",
              "Sales of Product Income",
              "and 22 more..."
            ]
          },
          "classification": {
            "name": "classification",
            "description": "Specifies the financial classification of the account, such as Asset or Liability, essential for accurate financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Liability",
              "Expense",
              "Revenue",
              "Asset",
              "Equity"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides a more detailed categorization of the account, offering additional context about its specific functions or types.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GlobalTaxPayable",
              "SalesOfProductIncome",
              "GlobalTaxSuspense",
              "GlobalTaxExpense",
              "UndepositedFunds",
              "SuppliesMaterialsCogs",
              "RetainedEarnings",
              "OtherMiscellaneousServiceCost",
              "DiscountsRefundsGiven",
              "AccountsReceivable",
              "OtherCurrentAssets",
              "Inventory",
              "SuppliesMaterials"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp indicating the date and time when the account record was first created, useful for historical tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-18 21:10:47+00:00",
              "2021-03-19 07:44:58+00:00",
              "2021-03-19 10:01:30+00:00",
              "2021-03-19 08:00:28+00:00",
              "2021-03-19 07:46:28+00:00",
              "and 5 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp reflecting the last modification date and time of the account record, important for maintaining data integrity.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-18 21:10:47+00:00",
              "2021-03-19 10:01:30+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-03-19 07:44:58+00:00",
              "2021-03-19 07:46:28+00:00",
              "and 8 more..."
            ]
          },
          "balance_with_sub_accounts": {
            "name": "balance_with_sub_accounts",
            "description": "The total balance of the account, including any associated sub-accounts, indicating net credit or debit status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292",
              "-182.25",
              "-255.15"
            ]
          },
          "balance": {
            "name": "balance",
            "description": "Represents the current balance of the account itself, providing insight into its financial standing without considering sub-accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292",
              "-182.25",
              "-255.15"
            ]
          }
        }
      },
      "invoice": {
        "table_name": "invoice",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique identifier for each invoice, ensuring distinct reference within the database.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp marking the creation date and time of the invoice, essential for historical tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-01 10:05:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-07-16 08:30:03+00:00",
              "2021-07-16 06:00:03+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp indicating the last modification date and time of the invoice record, ensuring data integrity.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-01 10:05:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-07-16 08:30:03+00:00",
              "2021-07-16 06:00:03+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The total charge on the invoice, encompassing all fees and taxes, indicating the financial total owed.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "96.32",
              "and 6 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The date on which the transaction related to the invoice occurred, providing context for the billing.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10",
              "2021-09-01",
              "2021-07-13",
              "2021-07-19",
              "2021-08-02",
              "and 2 more..."
            ]
          }
        }
      },
      "invoice_line": {
        "table_name": "invoice_line",
        "fields": {
          "invoice_id": {
            "name": "invoice_id",
            "description": "A unique identifier linking this line item to its parent invoice, establishing a one-to-many relationship between invoices and their line items.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The monetary value of the line item, represented as a numeric field capable of handling large values.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 21 more..."
            ]
          },
          "detail_type": {
            "name": "detail_type",
            "description": "A string indicating the category of the line item, such as a standard sale, subtotal, or discount, providing context for its role in the invoice.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "SalesItemLineDetail",
              "SubTotalLineDetail",
              "DiscountLineDetail"
            ]
          },
          "sales_item_item_id": {
            "name": "sales_item_item_id",
            "description": "A string identifying the specific sales item associated with this line, which may be absent for a portion of records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "4",
              "5",
              "6",
              "7",
              "8",
              "9"
            ]
          }
        }
      },
      "item": {
        "table_name": "item",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique identifier for each item in the catalog, serving as the primary key that ensures each record is distinct.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9"
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "A detailed classification of the item, providing clarity on the specific product or service being offered.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Other",
              "Paintless Dent Repair",
              "Ceramic Coating",
              "Hours",
              "Sales",
              "Vinyl Wrap",
              "Window Tint",
              "Paint Protection Film"
            ]
          },
          "name": {
            "name": "name",
            "description": "A shorter or alternative name for the item, which may be used for display purposes or internal reference.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Other",
              "Paintless Dent Repair",
              "Ceramic Coating",
              "Hours",
              "Sales",
              "Vinyl Wrap",
              "Window Tint",
              "Paint Protection Film"
            ]
          },
          "type": {
            "name": "type",
            "description": "Indicates whether the item is a service or a non-inventory product, aiding in the categorization of offerings.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "NonInventory",
              "Service"
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nTo calculate gross profit for a QuickBooks Profit and Loss report, we need line items for income and cost of goods sold, which are represented by accounts. Gross profit is generally Total Income minus Cost of Goods Sold (COGS). Relevant columns are account classification (Revenue/Expense/COGS), account balances, and date/period fields from transactions (invoices, etc.). Business rules must address calculation logic, date filtering, and accounting basis.\n\n  \n**Table Columns:**  \n- account:  \n  columns: id, fully_qualified_name, account_type, name, classification, account_sub_type, balance_with_sub_accounts, balance, created_at, updated_at  \n- invoice:  \n  columns: id, created_at, updated_at, total_amount, transaction_date  \n- invoice_line:  \n  columns: invoice_id, amount, detail_type, sales_item_item_id  \n- item:  \n  columns: id, fully_qualified_name, name, type  \n  \n\n**Business Rules to Include:**  \n- Interpret gross profit as (Total Income - Cost of Goods Sold), using account.classification or account.account_sub_type to distinguish line types.  \n- Apply date filtering using invoice.transaction_date or invoice.created_at.  \n- Specify if calculation uses cash or accrual basis, as this affects which transactions are included.  \n",
    "other_table_columns": {
      "item": [
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [],
      "estimate_line": [],
      "journal_entry_tax_line": [],
      "term": [
        "id",
        "name",
        "active",
        "type",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [],
      "credit_card_payment_txn": [],
      "tax_code": [
        "id",
        "description",
        "name",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_order",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [],
      "transfer": [],
      "vendor": [],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "created_at",
        "updated_at",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [],
      "deposit_line": [],
      "tax_agency": [
        "id",
        "display_name",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [],
      "account": [
        "_fivetran_synced"
      ],
      "address": [
        "id",
        "line_1",
        "city",
        "postal_code",
        "country",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [],
      "class": [
        "id",
        "fully_qualified_name",
        "name",
        "active",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bill_payment_line": [],
      "payment_line": [],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [],
      "bill_line": [],
      "payment": [],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "doc_number",
        "balance",
        "sync_token",
        "total_tax",
        "shipping_address_id",
        "billing_address_id",
        "due_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "purchase": [],
      "customer": [
        "id",
        "family_name",
        "fully_qualified_name",
        "given_name",
        "company_name",
        "display_name",
        "print_on_check_name",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "created_at",
        "updated_at",
        "email",
        "phone_number",
        "shipping_address_id",
        "bill_address_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "index",
        "id",
        "line_num",
        "description",
        "sales_item_tax_code_id",
        "_fivetran_synced"
      ],
      "credit_memo_line": [],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "journal_entry_line": [],
      "payment_method": [
        "id",
        "name",
        "type",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [],
      "deposit": []
    }
  },
  {
    "data_source_name": "QuickBooks_Analytics",
    "selected_table_column_metadata": {
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The date for which the profit and loss data is recorded, crucial for tracking daily financial performance.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-01",
              "2021-11-01",
              "2022-01-01",
              "2022-04-01",
              "2023-12-01",
              "and 47 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the reporting period for which the profit and loss data is aggregated.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-01",
              "2021-11-01",
              "2022-01-01",
              "2022-04-01",
              "2023-12-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The concluding date of the reporting period for which the profit and loss data is reported.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-31",
              "2021-11-30",
              "2022-01-31",
              "2022-04-30",
              "2023-12-31",
              "and 47 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "Categorizes the account type specifically as 'Revenue', aiding in grouping income-related records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Revenue"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The name of the parent account linked to this record, which typically indicates the source of revenue such as 'Discounts given' or 'Sales'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Discounts given",
              "Sales"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Indicates the account classification as 'Income', differentiating it from other account types for financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides further classification of the account, indicating specific categories like 'DiscountsRefundsGiven' or 'SalesOfProductIncome'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "DiscountsRefundsGiven",
              "SalesOfProductIncome"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique identifier for the account associated with this record, allowing for distinct tracking of financial entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "28"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The name of the account, typically reflecting the specific revenue source such as 'Discounts given' or 'Sales'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Discounts given",
              "Sales"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Represents the financial amount associated with this record, capturing both income and losses in a numeric format.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 1 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Represents a financial amount similar to 'amount', potentially adjusted for currency conversion or other formats.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 1 more..."
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "Indicates the hierarchical order of the account, consistently reflecting the same level of account classification.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1"
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nTo calculate gross profit for QuickBooks Profit and Loss data, we need line items for revenue and cost of goods sold, plus fields that define reporting periods. The table `quickbooks__profit_and_loss` contains all relevant financial line items, including Gross Income, Cost of Goods Sold, and Total Income, as well as date fields. Primary/foreign key columns are not needed here since this table is self-contained for P&L reporting.  \n\n  \n**Table Columns:**   \n- table: quickbooks__profit_and_loss  \n  columns: calendar_date, period_first_day, period_last_day, account_class, parent_account_name, account_type, account_sub_type, account_id, account_name, amount, converted_amount, account_ordinal  \n  \n  \n**Business Rules:**  \n- Use `amount` for financial values; filter by `account_name` or `parent_account_name` for Gross Profit, Gross Income, Cost of Goods Sold, and Total Income line items.  \n- Use `calendar_date`, `period_first_day`, and `period_last_day` for date-based filtering.  \n- Confirm if data is reported on cash or accrual basis (not shown in columns; clarify using business context or metadata).  \n- Gross Profit calculation: Gross Profit = Gross Income - Cost of Goods Sold.  \n",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "transaction_source",
        "transaction_id",
        "transaction_line_id",
        "doc_number",
        "transaction_type",
        "transaction_date",
        "item_id",
        "account_id",
        "account_name",
        "account_sub_type",
        "customer_id",
        "customer_name",
        "description",
        "amount",
        "converted_amount",
        "total_amount",
        "total_converted_amount"
      ],
      "quickbooks__cash_flow_statement": [
        "cash_flow_period",
        "account_class",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_id",
        "account_name",
        "cash_ending_period",
        "cash_converted_ending_period",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal",
        "cash_beginning_period",
        "cash_net_period",
        "cash_converted_beginning_period",
        "cash_converted_net_period"
      ],
      "quickbooks__balance_sheet": [
        "calendar_date",
        "period_first_day",
        "period_last_day",
        "account_class",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_id",
        "account_name",
        "amount",
        "converted_amount",
        "account_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_type",
        "transaction_id",
        "doc_number",
        "transaction_with",
        "customer_vendor_name",
        "customer_vendor_balance",
        "customer_vendor_address_city",
        "total_amount",
        "total_converted_amount",
        "current_balance",
        "due_date"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_id",
        "account_number",
        "account_name",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "financial_statement_helper",
        "date_year",
        "period_first_day",
        "period_last_day",
        "period_net_change",
        "period_beginning_balance",
        "period_ending_balance",
        "period_net_converted_change",
        "period_beginning_converted_balance",
        "period_ending_converted_balance",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "unique_id",
        "transaction_id",
        "transaction_index",
        "transaction_date",
        "customer_id",
        "amount",
        "account_id",
        "account_name",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "financial_statement_helper",
        "account_current_balance",
        "account_class",
        "transaction_type",
        "transaction_source",
        "account_transaction_type",
        "created_at",
        "updated_at",
        "adjusted_amount",
        "adjusted_converted_amount",
        "running_balance",
        "running_converted_balance"
      ]
    }
  }
]
````