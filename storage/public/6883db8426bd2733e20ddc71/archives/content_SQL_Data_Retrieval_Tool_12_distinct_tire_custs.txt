SQL Query:

````
SELECT
  year,
  month_number,
  month_name,
  COUNT(DISTINCT Customer_ID) AS distinct_customer_tire_invoices
FROM (
  SELECT
    EXTRACT(YEAR FROM Invoice_Date) AS year,
    EXTRACT(MONTH FROM Invoice_Date) AS month_number,
    MONTHNAME(Invoice_Date) AS month_name,
    Customer_ID
  FROM Invoices
  WHERE Service_Type_ID = 1
    AND Invoice_Date >= '2023-01-01'
    AND Invoice_Date <= '2025-12-31'
) AS sub
GROUP BY year, month_number, month_name
ORDER BY year ASC, month_number ASC;
````

Data retrieved successfully:

|   year |   month_number | month_name   |   distinct_customer_tire_invoices |
|-------:|---------------:|:-------------|----------------------------------:|
|   2023 |              1 | January      |                               617 |
|   2023 |              2 | February     |                               409 |
|   2023 |              3 | March        |                               443 |
|   2023 |              4 | April        |                               402 |
|   2023 |              5 | May          |                               451 |
|   2023 |              6 | June         |                               436 |
|   2023 |              7 | July         |                               452 |
|   2023 |              8 | August       |                               456 |
|   2023 |              9 | September    |                               435 |
|   2023 |             10 | October      |                               463 |
|   2023 |             11 | November     |                               435 |
|   2023 |             12 | December     |                               433 |
|   2024 |              1 | January      |                               428 |
|   2024 |              2 | February     |                               443 |
|   2024 |              3 | March        |                               480 |
|   2024 |              4 | April        |                               443 |
|   2024 |              5 | May          |                               479 |
|   2024 |              6 | June         |                               465 |
|   2024 |              7 | July         |                               462 |
|   2024 |              8 | August       |                               490 |
|   2024 |              9 | September    |                               458 |
|   2024 |             10 | October      |                               479 |
|   2024 |             11 | November     |                               438 |
|   2024 |             12 | December     |                               503 |
|   2025 |              1 | January      |                               487 |
|   2025 |              2 | February     |                               488 |
|   2025 |              3 | March        |                               520 |
|   2025 |              4 | April        |                               502 |
|   2025 |              5 | May          |                               532 |
|   2025 |              6 | June         |                               491 |
|   2025 |              7 | July         |                               392 |