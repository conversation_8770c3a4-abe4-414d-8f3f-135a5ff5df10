Python code:

```python
import pandas as pd
from metalake import load_data

# Set chat_id and data file name
data_file_name = "invoice_outstanding_balance.dat"
chat_id = "688987d57efb283b7b03134b"

# Load the full dataset (already filtered as per policy)
df = load_data(chat_id, data_file_name)

# Handle empty DataFrame gracefully
if df.empty:
    print("No outstanding balance data is available.")
else:
    # Save the full DataFrame to CSV
    csv_path = "files/total_outstanding_balance.csv"
    df.to_csv(csv_path, index=False)
    
    # Save the full DataFrame to .dat (using tab delimiter for .dat)
    dat_path = "files/total_outstanding_balance.dat"
    df.to_csv(dat_path, sep='\t', index=False)
    
    # Display up to 50 rows as markdown (max 50, but this will be just 1 row)
    print(df.head(50).to_markdown(index=False))
    if len(df) > 50:
        print("\n[Output truncated to first 50 rows]")

````

Output:

|   total_outstanding_balance |
|----------------------------:|
|                        7292 |

