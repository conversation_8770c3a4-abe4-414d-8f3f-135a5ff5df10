SQL Query:

````
SELECT service_month, COUNT(*) AS tire_services_count
FROM (
  SELECT STR_TO_DATE(CONCAT(YEAR(Service_Date), '-', LPAD(MONTH(Service_Date), 2, '0'), '-01'), '%Y-%m-%d') AS service_month
  FROM ServiceRecords
  WHERE Service_Type_ID = 1
    AND Service_Date >= '2025-01-01'
    AND Service_Date <= '2025-12-31'
) AS monthly_services
GROUP BY service_month
ORDER BY service_month ASC;
````

Data retrieved successfully:

| service_month            |   tire_services_count |
|:-------------------------|----------------------:|
| 2025-01-01T00:00:00.000Z |                   492 |
| 2025-02-01T00:00:00.000Z |                   497 |
| 2025-03-01T00:00:00.000Z |                   526 |
| 2025-04-01T00:00:00.000Z |                   511 |
| 2025-05-01T00:00:00.000Z |                   539 |
| 2025-06-01T00:00:00.000Z |                   501 |
| 2025-07-01T00:00:00.000Z |                   396 |