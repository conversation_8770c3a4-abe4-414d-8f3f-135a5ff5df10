SQL Query:

````
SELECT service_month, COUNT(*) AS total_services_count
FROM (
  SELECT STR_TO_DATE(CONCAT(YEAR(Service_Date), '-', LPAD(MONTH(Service_Date), 2, '0'), '-01'), '%Y-%m-%d') AS service_month
  FROM ServiceRecords
  WHERE Service_Date >= '2025-06-01' AND Service_Date <= '2025-12-31'
) AS monthly_totals
GROUP BY service_month
ORDER BY service_month ASC;
````

Data retrieved successfully:

| service_month            |   total_services_count |
|:-------------------------|-----------------------:|
| 2025-06-01T00:00:00.000Z |                   4338 |
| 2025-07-01T00:00:00.000Z |                   3525 |