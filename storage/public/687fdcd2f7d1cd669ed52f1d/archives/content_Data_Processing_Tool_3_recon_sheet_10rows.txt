Python code:

```python
from metalake import load_data
import pandas as pd

# Input parameters
data_file_name = "RECONCILIATION.dat"
chat_id = "687fdfecf7d1cd669ed52f8a"

# Load the full, pre-filtered reconciliation worksheet
df = load_data(chat_id, data_file_name)

# Handle empty DataFrame gracefully
if df.empty:
    print("No reconciliation data was found. Please check your filters or data availability.")
else:
    # Display column names and first 10 rows as markdown
    print("### Column Names:")
    print("| " + " | ".join(df.columns) + " |")
    print("|" + "|".join(["---"] * len(df.columns)) + "|")
    print("\n### First 10 Rows:")
    print(df.head(10).to_markdown(index=False))

````

Output:

### Column Names:
| Date | Description | Charges | Credit | Value | Receipt provided | Province | GST/HST | PST | GST/HST% | PST% | Is this related to an AP vendor account/ PO | COGS or General | Expense Description | GL Account |  GST/HST-Input Tax Credit  |  Pre-Tax Expense  | Div | Status | Links to Field Servio |  |
|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|---|

### First 10 Rows:
| Date                | Description              | Charges   | Credit   |   Value | Receipt provided   | Province   | GST/HST   | PST   |   GST/HST% | PST%   | Is this related to an AP vendor account/ PO   | COGS or General   | Expense Description   | GL Account   |  GST/HST-Input Tax Credit    |  Pre-Tax Expense    | Div   | Status   | Links to Field Servio       |    |
|:--------------------|:-------------------------|:----------|:---------|--------:|:-------------------|:-----------|:----------|:------|-----------:|:-------|:----------------------------------------------|:------------------|:----------------------|:-------------|:-----------------------------|:--------------------|:------|:---------|:----------------------------|:---|
| 2025-06-30 00:00:00 | PRITCHARD ENGINEERING    | 134.4     |          |  134.4  | Y                  | MB         | Y         | N     |       0.05 | 0.07   | Y                                             |                   |                       | POSTED       | 0.0                          | 134.4               | 100.0 |          | Vendor Bill: 156051-0       |    |
| 2025-06-29 00:00:00 | BEAUTIFUL.AI             | 63.48     |          |   63.48 |                    |            |           |       |       0.05 |        |                                               |                   |                       |              |                              |                     |       |          |                             |    |
| 2025-06-27 00:00:00 | COMFORT INN WINNIPEG A   | 886.06    |          |  886.06 |                    |            |           |       |       0.05 |        |                                               |                   |                       |              |                              |                     |       |          |                             |    |
| 2025-06-26 00:00:00 | RIVER OAKS GOLF          | 96.73     |          |   96.73 |                    |            |           |       |       0.05 |        |                                               |                   |                       |              |                              |                     |       |          |                             |    |
| 2025-06-26 00:00:00 | RIVER OAKS GOLF          | 287.84    |          |  287.84 |                    |            |           |       |       0.05 |        |                                               |                   |                       |              |                              |                     |       |          |                             |    |
| 2025-06-25 00:00:00 | MANITOULIN TRANSPORT INC | 960.78    |          |  960.78 | Y                  | MB         | Y         | N     |       0.05 | 0.07   | Y                                             |                   |                       | POSTED       | 0.0                          | 960.78              | 100.0 |          | Vendor Bill: 3303933603     |    |
| 2025-06-25 00:00:00 | GREAT CANADIAN OIL CHA   | 180.88    |          |  180.88 |                    |            |           |       |       0.05 |        |                                               |                   |                       |              |                              |                     |       |          |                             |    |
| 2025-06-24 00:00:00 | AMZN Mktp CA             |           | 18.47    |  -18.47 |                    |            |           |       |       0.05 |        |                                               |                   |                       |              |                              |                     |       |          |                             |    |
| 2025-06-23 00:00:00 | APPRUV.COM               | 810.39    |          |  810.39 | Y                  | SK         | N         | N     |       0.05 | 0.06   | Y                                             |                   |                       | POSTED       | 0.0                          | 810.39              | 200.0 |          | Vendor Bill: 980239110334-1 |    |
| 2025-06-21 00:00:00 | UBER *TRIP HELP.UBER.COM | 93.74     |          |   93.74 | Y                  | MB         | N         | N     |       0.05 | 0.07   | N                                             | GA                | Travel                | 8000/        | 0.0                          | 93.74               | 100.0 |          |                             |    |

