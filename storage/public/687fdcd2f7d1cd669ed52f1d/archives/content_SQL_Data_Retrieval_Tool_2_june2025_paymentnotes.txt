SQL Query:

````
SELECT DISTINCT TOP 100 CAST(Payment_PaymentNotes AS NVARCHAR(MAX)) AS Payment_PaymentNotes
FROM Payments
WHERE LOWER(CAST(Payment_PaymentNotes AS NVARCHAR(MAX))) LIKE '%3774%'
  AND Payment_PaymentDate >= '2025-06-01'
  AND Payment_PaymentDate < '2025-07-01';
````

Data retrieved successfully:

| Payment_PaymentNotes                                   |
|:-------------------------------------------------------|
| CHE#003774                                             |
| TD Visa Allocations 3774 Cody Tanner - Jun 01 - Jun 30 |