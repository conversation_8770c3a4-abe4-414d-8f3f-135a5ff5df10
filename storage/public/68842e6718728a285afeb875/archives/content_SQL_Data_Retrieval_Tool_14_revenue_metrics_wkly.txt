SQL Query:

````
SELECT
  week_start,
  SUM(I.Total_Charge) AS total_revenue,
  COUNT(*) AS job_count,
  ROUND(SUM(I.Total_Charge) / NULLIF(COUNT(*), 0), 2) AS avg_revenue_per_job
FROM (
  SELECT
    SR.Service_ID,
    SR.Employee_ID,
    SR.Service_Date,
    DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) AS week_start
  FROM ServiceRecords SR
  INNER JOIN EmployeeData ED ON SR.Employee_ID = ED.Employee_ID
  WHERE
    LOWER(ED.Role) LIKE 'technician'
    AND (ED.Leave_Date IS NULL OR ED.Leave_Date >= SR.Service_Date)
    AND DATE_SUB(SR.Service_Date, INTERVAL WEEKDAY(SR.Service_Date) DAY) IN (
      DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 2 WEEK),
      DATE_SUB(DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY), INTERVAL 1 WEEK)
    )
) AS sub
INNER JOIN Invoices I ON sub.Service_ID = I.Service_ID
GROUP BY week_start
ORDER BY week_start ASC;
````

Data retrieved successfully:

| week_start               |   total_revenue |   job_count |   avg_revenue_per_job |
|:-------------------------|----------------:|------------:|----------------------:|
| 2025-07-07T00:00:00.000Z |         64694.4 |        1021 |                 63.36 |
| 2025-07-14T00:00:00.000Z |         63376.5 |        1011 |                 62.69 |