SQL Query:

````
SELECT
  DATE_FORMAT(Appointment_Date, '%Y-%m-01') AS month_start,
  COUNT(*) AS appointment_count_2025
FROM Appointments
WHERE CAST(Appointment_Date AS DATE) >= '2025-01-01'
  AND CAST(Appointment_Date AS DATE) <= CURRENT_DATE
GROUP BY DATE_FORMAT(Appointment_Date, '%Y-%m-01')
ORDER BY month_start ASC;
````

Data retrieved successfully:

| month_start   |   appointment_count_2025 |
|:--------------|-------------------------:|
| 2025-01-01    |                     4495 |
| 2025-02-01    |                     4099 |
| 2025-03-01    |                     4635 |
| 2025-04-01    |                     4374 |
| 2025-05-01    |                     4597 |
| 2025-06-01    |                     4369 |
| 2025-07-01    |                     3470 |