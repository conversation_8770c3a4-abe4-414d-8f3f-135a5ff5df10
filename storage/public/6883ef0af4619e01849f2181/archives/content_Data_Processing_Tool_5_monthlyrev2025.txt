Python code:

```python
from metalake import load_data
import pandas as pd
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt

chat_id = "6883efe2f4619e01849f21f6"
data_file = "invoices_2025_monthly_revenue.dat"

# 1. Load the full, pre-filtered dataset
df = load_data(chat_id, data_file)

if df.empty:
    print("No data available to export or plot.")
else:
    # 2. Export full result set to CSV
    csv_path = "files/monthly_revenue_2025.csv"
    df.to_csv(csv_path, index=False)

    # 3. Display up to 50 rows as markdown table
    display_rows = min(50, len(df))
    table = df.head(display_rows).to_markdown(index=False)
    print(table)
    if len(df) > 50:
        print(f"\nNote: Table truncated to first 50 rows out of {len(df)}.")

    # 4. Plot the Monthly Revenue Trend
    plt.figure(figsize=(10,6))
    plt.plot(df['Month'], df['Total_Revenue'], marker='o')
    plt.title("Monthly Revenue Trend – 2025")
    plt.xlabel("Month")
    plt.ylabel("Total Revenue")
    plt.grid(True, linestyle='--', alpha=0.6)
    plt.tight_layout()
    image_path = "files/monthly_revenue_2025.png"
    plt.savefig(image_path)
    plt.close()

````

Output:

| Month   |   Total_Revenue |
|:--------|----------------:|
| 2025-01 |          289592 |
| 2025-02 |          262110 |
| 2025-03 |          287058 |
| 2025-04 |          283328 |
| 2025-05 |          293763 |
| 2025-06 |          271276 |
| 2025-07 |          223458 |

