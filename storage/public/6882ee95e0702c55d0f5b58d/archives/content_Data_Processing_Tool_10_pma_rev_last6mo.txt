Python code:

```python
import pandas as pd
from metalake import load_data
import os

chat_id = "6882eac05607b9ce5b16b720"
data_file_name = "monthly_recognised_revenue_pma_6mo.dat"
output_csv = "files/pma_revenue_last6_months.csv"
output_dat = "files/pma_revenue_last6_months.dat"

# 1. Load the full, pre-filtered dataset
df = load_data(chat_id, data_file_name)

if df.empty:
    print("No recognised PMA contract revenue data is available for the last six months.")
else:
    # 2. Sort by MonthStart ascending (oldest to newest)
    df_sorted = df.sort_values("MonthStart", ascending=True)
    # 3. Retain only the last six rows (most recent six months)
    df_last6 = df_sorted.tail(6).copy()
    # 4. Format currency to two decimals
    df_last6["TotalRevenue"] = df_last6["TotalRevenue"].map(lambda x: f"{x:,.2f}")
    # 5. Output CSV
    df_last6.to_csv(output_csv, index=False)
    # 6. Output .dat file (same as CSV but with .dat extension)
    df_last6.to_csv(output_dat, index=False)
    # 7. Display up to 50 rows as markdown table
    print(df_last6.head(50).to_markdown(index=False))
    if len(df_last6) > 50:
        print(f"\nNote: Table truncated to first 50 of {len(df_last6)} rows.")
````

Output:

| MonthStart               | TotalRevenue   |
|:-------------------------|:---------------|
| 2025-02-01T00:00:00.000Z | 205,115.78     |
| 2025-03-01T00:00:00.000Z | 218,457.79     |
| 2025-04-01T00:00:00.000Z | 267,810.23     |
| 2025-05-01T00:00:00.000Z | 224,927.53     |
| 2025-06-01T00:00:00.000Z | 249,156.62     |
| 2025-07-01T00:00:00.000Z | 228,460.14     |

