Python code:

```python
from metalake import load_data
import pandas as pd

# Load the full, pre-filtered dataset
chat_id = "68828c3c56cf54f65632ff0b"
data_file = "vendorbills_overdue_by_month.dat"
df = load_data(chat_id, data_file)

# Handle empty DataFrame gracefully
if df.empty:
    print("No overdue vendor bill data available for the requested period.")
else:
    # Sort by due_month ascending
    df_sorted = df.sort_values('due_month').reset_index(drop=True)

    # Select required columns and save to CSV
    output_path = 'files/overdue_vendor_bills.csv'
    df_sorted[['due_month', 'overdue_amount']].to_csv(output_path, index=False)

    # Display up to 50 rows as a markdown table
    display_limit = min(50, len(df_sorted))
    print(df_sorted.head(display_limit).to_markdown(index=False))
    if len(df_sorted) > 50:
        print(f"\nNote: Only the first 50 of {len(df_sorted)} rows are displayed.")
````

Output:

| due_month   |   overdue_amount |
|:------------|-----------------:|
| 2025-02     |  16279.6         |
| 2025-03     |  86664.5         |
| 2025-04     | 148607           |
| 2025-05     | 186600           |
| 2025-06     |      3.05992e+06 |
| 2025-07     | 659285           |

