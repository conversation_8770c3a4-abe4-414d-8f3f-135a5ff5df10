Python code:

```python
from metalake import load_data
import pandas as pd

# Load the data
dat_file = "overdue_vendor_bills.dat"
chat_id = "6882fc622611ed2d2f54bcee"
df = load_data(chat_id, dat_file)

# Handle empty DataFrame
def print_no_data():
    print("No overdue vendor bill data available for KPI calculation.")

if df.empty:
    print_no_data()
else:
    # Ensure correct data types for calculation
    df = df.copy()
    df['due_month'] = pd.to_datetime(df['due_month'], format='%Y-%m')
    df['overdue_amount'] = pd.to_numeric(df['overdue_amount'], errors='coerce')
    df = df.dropna(subset=['due_month', 'overdue_amount'])
    df_sorted = df.sort_values('due_month').reset_index(drop=True)

    if len(df_sorted) < 2:
        print("Not enough data to calculate month-over-month KPI.")
    else:
        currentValue = df_sorted.iloc[-1]['overdue_amount']
        previousValue = df_sorted.iloc[-2]['overdue_amount']

        # Compute percent change
        if previousValue == 0:
            pctChange = None
        else:
            pctChange = round((currentValue - previousValue) / previousValue * 100, 1)

        if pctChange is None:
            statement = "Previous value is zero; percent change not defined."
        else:
            trend = "increased" if pctChange > 0 else ("decreased" if pctChange < 0 else "did not change")
            statement = f"Overdue vendor bills {trend} {abs(pctChange)}% MoM."

        # Build KPI table
        kpi_rows = [
            {"field": "currentValue", "value": f"${currentValue:,.2f}"},
            {"field": "previousValue", "value": f"${previousValue:,.2f}"},
            {"field": "pctChange", "value": f"{pctChange if pctChange is not None else 'N/A'}"},
            {"field": "currencySign", "value": "$"},
            {"field": "statement", "value": statement}
        ]
        kpi_df = pd.DataFrame(kpi_rows)

        # Save the KPI table
        output_path = 'files/overdue_vendor_bills_kpi.csv'
        kpi_df.to_csv(output_path, index=False)
        print(kpi_df.to_markdown(index=False))

````

Output:

| field         | value                                     |
|:--------------|:------------------------------------------|
| currentValue  | $659,284.79                               |
| previousValue | $3,059,915.10                             |
| pctChange     | -78.5                                     |
| currencySign  | $                                         |
| statement     | Overdue vendor bills decreased 78.5% MoM. |

