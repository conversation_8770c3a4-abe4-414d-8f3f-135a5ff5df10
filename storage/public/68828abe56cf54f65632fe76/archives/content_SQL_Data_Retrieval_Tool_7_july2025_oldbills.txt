SQL Query:

````
SELECT
  SUM(p.Payment_PaymentAmount) AS total_payments_clearing_old_bills,
  COUNT(DISTINCT p.Payment_BillID) AS bills_cleared_count
FROM Payments p
INNER JOIN VendorBills vb ON vb.VendorBills_BillId = p.Payment_BillID
WHERE p.Payment_PaymentDate >= '2025-07-01'
  AND p.Payment_PaymentDate < '2025-08-01'
  AND p.Payment_Status = 'Processed'
  AND p.Payment_PaymentAmount > 0
  AND vb.VendorBills_DueDate < '2025-07-01'
  AND vb.VendorBills_Balance > 0;
````

Data retrieved successfully:

| total_payments_clearing_old_bills   |   bills_cleared_count |
|:------------------------------------|----------------------:|
|                                     |                     0 |