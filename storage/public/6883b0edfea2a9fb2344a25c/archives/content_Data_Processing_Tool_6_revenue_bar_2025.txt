Python code:

```python
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt
import pandas as pd
from metalake import load_data

# Inputs
data_file_name = "invoices_monthly_2025.dat"
chat_id = "6883793ee0e584fe878d88de"

# Load the full dataset
df = load_data(chat_id, data_file_name)

# Handle empty dataframe gracefully
if df.empty:
    print("No data available to plot monthly revenue for 2025.")
else:
    # Ensure dates are parsed correctly
    df["month_start"] = pd.to_datetime(df["month_start"])
    # Sort by month for correct plotting order
    df = df.sort_values("month_start")

    # Format x-axis as abbreviated month names
    month_labels = df["month_start"].dt.strftime("%b")

    # Plot
    fig, ax = plt.subplots(figsize=(8, 5))
    ax.bar(month_labels, df["monthly_revenue"], color="blue")
    ax.set_title("Monthly Revenue – 2025")
    ax.set_xlabel("Month")
    ax.set_ylabel("Revenue")
    ax.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, _: f"${x:,.2f}"))
    plt.tight_layout()
    plt.savefig("files/monthly_revenue_2025.png")
    plt.close()
    print("Bar chart saved as files/monthly_revenue_2025.png.")

````

Output:

Bar chart saved as files/monthly_revenue_2025.png.

