Metadata of all data sources:

````
[
  {
    "data_source_name": "superlube",
    "selected_table_column_metadata": {},
    "reasoning": "  \n**Reasoning:**  \nThe provided schema does not include any tables or columns that reference QuickBooks data, accounts, journal entries, transactions, or ledger details. Instead, it contains service, employee, customer, appointment, and salary information specific to an automotive or service business context. No relevant tables or columns match the user\u2019s QuickBooks-related request.\n\n  \n**Table Columns:**  \n*No relevant tables or columns available for QuickBooks data (accounts, journal entries, transactions, ledger details) in the provided schema.*",
    "other_table_columns": {}
  },
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier for each account, allowing distinct reference and access to individual account records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "and 22 more..."
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "The complete and descriptive name of the account, ensuring clarity in its purpose and type, unique for each account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "PST Expense (BC)",
              "Sales",
              "Uncategorized Income",
              "Billable Expense Income",
              "Sales of Product Income",
              "and 22 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorizes the account into distinct types such as liability, asset, income, or expense, aiding in the identification of its general financial nature.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Other Current Liability",
              "Income",
              "Expense",
              "Other Current Asset",
              "Accounts Receivable",
              "Cost of Goods Sold",
              "Equity"
            ]
          },
          "name": {
            "name": "name",
            "description": "The common name used for identifying the account in reports and queries, unique to each record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "PST Expense (BC)",
              "Sales",
              "Uncategorized Income",
              "Billable Expense Income",
              "Sales of Product Income",
              "and 22 more..."
            ]
          },
          "classification": {
            "name": "classification",
            "description": "Specifies the financial classification of the account, such as Asset or Liability, essential for accurate financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Liability",
              "Expense",
              "Revenue",
              "Asset",
              "Equity"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides a more detailed categorization of the account, offering additional context about its specific functions or types.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GlobalTaxPayable",
              "SalesOfProductIncome",
              "GlobalTaxSuspense",
              "GlobalTaxExpense",
              "UndepositedFunds",
              "SuppliesMaterialsCogs",
              "RetainedEarnings",
              "OtherMiscellaneousServiceCost",
              "DiscountsRefundsGiven",
              "AccountsReceivable",
              "OtherCurrentAssets",
              "Inventory",
              "SuppliesMaterials"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp indicating the date and time when the account record was first created, useful for historical tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-18 21:10:47+00:00",
              "2021-03-19 07:44:58+00:00",
              "2021-03-19 10:01:30+00:00",
              "2021-03-19 08:00:28+00:00",
              "2021-03-19 07:46:28+00:00",
              "and 5 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp reflecting the last modification date and time of the account record, important for maintaining data integrity.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-18 21:10:47+00:00",
              "2021-03-19 10:01:30+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-03-19 07:44:58+00:00",
              "2021-03-19 07:46:28+00:00",
              "and 8 more..."
            ]
          },
          "balance_with_sub_accounts": {
            "name": "balance_with_sub_accounts",
            "description": "The total balance of the account, including any associated sub-accounts, indicating net credit or debit status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292",
              "-182.25",
              "-255.15"
            ]
          },
          "balance": {
            "name": "balance",
            "description": "Represents the current balance of the account itself, providing insight into its financial standing without considering sub-accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292",
              "-182.25",
              "-255.15"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "A timestamp showing the last synchronization of the account record with the Fivetran data integration tool, important for ensuring data freshness.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-07-29 22:01:04.625000+00:00",
              "2025-07-29 15:47:28.483000+00:00",
              "2025-07-29 15:47:28.455000+00:00",
              "2025-07-29 15:47:28.460000+00:00",
              "2025-07-29 15:47:28.464000+00:00",
              "and 21 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n\n\nThe question requests metadata for tables storing QuickBooks financial/ledger data: accounts, journal entries, transactions, and ledger details. Based on the ER diagram and table metadata, only the `account` table contains relevant structured data (accounts, balances, account types). No metadata is available for `journal_entry`, `journal_entry_line`, or transaction/ledger tables, as their columns are not provided. Thus, only `account` and its columns are relevant.  \n\n\n**Table Columns:**  \n   - table: account  \n      columns: id, fully_qualified_name, account_type, name, classification, account_sub_type, created_at, updated_at, balance_with_sub_accounts, balance, _fivetran_synced  \n  \n",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [],
      "estimate_line": [],
      "journal_entry_tax_line": [],
      "term": [
        "id",
        "name",
        "active",
        "type",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [],
      "credit_card_payment_txn": [],
      "tax_code": [
        "id",
        "description",
        "name",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_order",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [],
      "transfer": [],
      "vendor": [],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "created_at",
        "updated_at",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [],
      "deposit_line": [],
      "tax_agency": [
        "id",
        "display_name",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [],
      "address": [
        "id",
        "line_1",
        "city",
        "postal_code",
        "country",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [],
      "class": [
        "id",
        "fully_qualified_name",
        "name",
        "active",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bill_payment_line": [],
      "payment_line": [],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [],
      "bill_line": [],
      "payment": [],
      "sales_receipt_line_bundle": [],
      "invoice": [
        "id",
        "doc_number",
        "balance",
        "sync_token",
        "created_at",
        "updated_at",
        "total_tax",
        "shipping_address_id",
        "billing_address_id",
        "due_date",
        "total_amount",
        "transaction_date",
        "customer_id",
        "_fivetran_synced"
      ],
      "purchase": [],
      "customer": [
        "id",
        "family_name",
        "fully_qualified_name",
        "given_name",
        "company_name",
        "display_name",
        "print_on_check_name",
        "sync_token",
        "balance_with_jobs",
        "balance",
        "created_at",
        "updated_at",
        "email",
        "phone_number",
        "shipping_address_id",
        "bill_address_id",
        "_fivetran_synced"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "invoice_id",
        "index",
        "id",
        "line_num",
        "description",
        "amount",
        "detail_type",
        "sales_item_item_id",
        "sales_item_tax_code_id",
        "_fivetran_synced"
      ],
      "credit_memo_line": [],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "journal_entry_line": [],
      "payment_method": [
        "id",
        "name",
        "type",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [],
      "deposit": []
    }
  },
  {
    "data_source_name": "QuickBooks_Analytics",
    "selected_table_column_metadata": {
      "quickbooks__expenses_sales_enhanced": {
        "table_name": "quickbooks__expenses_sales_enhanced",
        "fields": {
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique identifier for each transaction, ensuring the distinct identification of individual sales records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "transaction_line_id": {
            "name": "transaction_line_id",
            "description": "Represents the specific line item number within a transaction, allowing for precise tracking of each item sold.",
            "dataType": "INT64",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "1",
              "2",
              "3",
              "4",
              "and 22 more..."
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "Contains the associated document number for the transaction, facilitating easy reference to related invoices or documents.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1116",
              "1118",
              "1129",
              "2553",
              "2593",
              "2602",
              "2605",
              "2607",
              "2609",
              "2612",
              "2613"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "Records the date when the transaction took place, enabling analysis of sales trends over time.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10",
              "2021-07-13",
              "2021-09-01",
              "2021-08-02",
              "2021-04-28",
              "and 2 more..."
            ]
          },
          "item_id": {
            "name": "item_id",
            "description": "Represents the unique identifier for the sold item, allowing for tracking and reporting on specific products.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "4",
              "5",
              "6",
              "7",
              "8",
              "9"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Links each transaction to a specific account, consistently holding the value '6' for categorization purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "Indicates the account name associated with the transaction, always labeled as 'Sales' to reinforce transaction categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Sales"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Details the type of account involved, consistently marked as 'SalesOfProductIncome', clarifying the nature of income generated.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "SalesOfProductIncome"
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Provides the identifier for the customer involved in the transaction, facilitating customer-focused reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "4",
              "5"
            ]
          },
          "customer_name": {
            "name": "customer_name",
            "description": "Contains the name of the customer related to the transaction, enabling straightforward identification of customers.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Carl manit CST",
              "ABC Company",
              "Tommy Thumb"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Represents the monetary value associated with the transaction line item, crucial for financial analysis and reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 12 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Indicates the amount converted into a different currency, if applicable, supporting multi-currency transaction reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 12 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Represents the total amount for the entire transaction, summing all line items for comprehensive financial reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "30.24",
              "and 6 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Signifies the total amount converted into another currency, aiding in financial reporting across various currencies.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "30.24",
              "and 6 more..."
            ]
          }
        }
      },
      "quickbooks__cash_flow_statement": {
        "table_name": "quickbooks__cash_flow_statement",
        "fields": {
          "cash_flow_period": {
            "name": "cash_flow_period",
            "description": "The specific date for which the cash flow statement entry applies, serving as a key reference for each record.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-08-01",
              "2024-01-01",
              "2024-10-01",
              "2022-08-01",
              "2025-02-01",
              "and 47 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "Categorizes accounts as either 'Equity' or 'Asset', essential for understanding their contribution to the cash flow statement.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Equity",
              "Asset"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "Indicates the higher-level account associated with the entry, often defaulting to 'Accounts Receivable (A/R)', relevant for receivable accounts.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Specifies the type of account, such as 'Equity' or 'Accounts Receivable', aiding in differentiating account types in the cash flow statement.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Equity",
              "Accounts Receivable"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides further detail on the account type, typically including classifications like 'RetainedEarnings' or 'AccountsReceivable', facilitating deeper financial analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "RetainedEarnings",
              "AccountsReceivable"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Uniquely identifies each account within the cash flow statement, critical for establishing relationships with other tables.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "27",
              "9999"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "Holds the name of the account, such as 'Net Income Adjustment' or 'Accounts Receivable (A/R)', helping to identify specific accounts in the cash flow statement.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Net Income Adjustment",
              "Accounts Receivable (A/R)"
            ]
          },
          "cash_ending_period": {
            "name": "cash_ending_period",
            "description": "Represents the total cash available at the end of the period, essential for assessing the cash position.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "cash_converted_ending_period": {
            "name": "cash_converted_ending_period",
            "description": "Indicates the adjusted cash amount at the end of the period, reflecting potential accounting adjustments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "account_unique_id": {
            "name": "account_unique_id",
            "description": "Contains a unique identifier for each record, allowing for precise tracking and identification of entries within the cash flow statement.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "06845afdabb0e83a0e2c6b582f36b75a",
              "91356420db1018f78d23c7c125faea94",
              "48f07e5ed9a3dce2e973d9214a2b3e91",
              "64410122f213e134e8a2e7ad968efba4",
              "f8754a7a920281448ef8f2720382dbab",
              "and 99 more..."
            ]
          },
          "cash_beginning_period": {
            "name": "cash_beginning_period",
            "description": "Shows the cash amount at the start of the period, important for understanding cash flow dynamics.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "cash_net_period": {
            "name": "cash_net_period",
            "description": "Reflects the net cash flow during the period, crucial for evaluating cash inflow and outflow.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "288",
              "1223",
              "2000",
              "3343.6"
            ]
          },
          "cash_converted_beginning_period": {
            "name": "cash_converted_beginning_period",
            "description": "Indicates the adjusted cash amount at the start of the period, facilitating comparisons against expected cash positions.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "cash_converted_net_period": {
            "name": "cash_converted_net_period",
            "description": "Represents the adjusted net cash flow during the period, aiding in understanding effective cash flow after adjustments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "288",
              "1223",
              "2000",
              "3343.6"
            ]
          }
        }
      },
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The date for which the profit and loss data is recorded, crucial for tracking daily financial performance.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-01",
              "2021-11-01",
              "2022-01-01",
              "2022-04-01",
              "2023-12-01",
              "and 47 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the reporting period for which the profit and loss data is aggregated.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-01",
              "2021-11-01",
              "2022-01-01",
              "2022-04-01",
              "2023-12-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The concluding date of the reporting period for which the profit and loss data is reported.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-31",
              "2021-11-30",
              "2022-01-31",
              "2022-04-30",
              "2023-12-31",
              "and 47 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "Categorizes the account type specifically as 'Revenue', aiding in grouping income-related records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Revenue"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The name of the parent account linked to this record, which typically indicates the source of revenue such as 'Discounts given' or 'Sales'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Discounts given",
              "Sales"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Indicates the account classification as 'Income', differentiating it from other account types for financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides further classification of the account, indicating specific categories like 'DiscountsRefundsGiven' or 'SalesOfProductIncome'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "DiscountsRefundsGiven",
              "SalesOfProductIncome"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique identifier for the account associated with this record, allowing for distinct tracking of financial entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "28"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The name of the account, typically reflecting the specific revenue source such as 'Discounts given' or 'Sales'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Discounts given",
              "Sales"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Represents the financial amount associated with this record, capturing both income and losses in a numeric format.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 1 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Represents a financial amount similar to 'amount', potentially adjusted for currency conversion or other formats.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 1 more..."
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "Indicates the hierarchical order of the account, consistently reflecting the same level of account classification.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1"
            ]
          }
        }
      },
      "quickbooks__balance_sheet": {
        "table_name": "quickbooks__balance_sheet",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The specific date for which the balance sheet data is recorded, used to track the company's financial position on that day.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-06-01",
              "2022-12-01",
              "2021-06-01",
              "2022-08-01",
              "2024-07-01",
              "and 47 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The start date of the financial period covered by the balance sheet entry, providing context for the associated financial data.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-06-01",
              "2022-12-01",
              "2021-06-01",
              "2022-08-01",
              "2024-07-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The end date of the financial period represented in the balance sheet record, indicating the duration of the financial data being analyzed.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-06-30",
              "2022-12-31",
              "2021-06-30",
              "2022-08-31",
              "2024-07-31",
              "and 47 more..."
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "A categorization of the account as either 'Asset' or 'Equity', aiding in the understanding of the account's nature for financial analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Equity"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The name of the parent account linked to the balance sheet entry, typically providing broader context, though often missing in records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "A further classification of the account as 'Accounts Receivable' or 'Equity', helping analysts differentiate between account types in financial assessments.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable",
              "Equity"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "An additional categorization of accounts, specifying whether they are 'AccountsReceivable' or 'RetainedEarnings' for detailed financial analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "AccountsReceivable",
              "RetainedEarnings"
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique identifier for each account within the balance sheet, essential for distinguishing accounts during data queries and analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "27",
              "9999"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The descriptive name of the account, providing clarity on the financial data associated with each account in the balance sheet.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)",
              "Net Income Adjustment"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The financial value associated with the account for the specified date, critical for evaluating the monetary state of accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "The potentially adjusted financial value of the account for different reporting standards or currencies, important for understanding account valuation.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "The order of the accounts within the balance sheet, facilitating logical organization and readability of the financial data.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3"
            ]
          }
        }
      },
      "quickbooks__ap_ar_enhanced": {
        "table_name": "quickbooks__ap_ar_enhanced",
        "fields": {
          "transaction_type": {
            "name": "transaction_type",
            "description": "Indicates the nature of the transaction, consistently labeled as 'invoice', to categorize records specific to invoice-related activities.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique string identifier assigned to each transaction, ensuring distinct identification and reference for data integrity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "A unique document identifier for each invoice, facilitating tracking and management of individual invoice records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1116",
              "1118",
              "1129",
              "2553",
              "2593",
              "2602",
              "2605",
              "2607",
              "2609",
              "2612",
              "2613"
            ]
          },
          "customer_vendor_name": {
            "name": "customer_vendor_name",
            "description": "The name of the customer or vendor associated with the transaction, essential for identifying the parties involved and for reporting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Carl manit CST",
              "ABC Company",
              "Tommy Thumb"
            ]
          },
          "customer_vendor_balance": {
            "name": "customer_vendor_balance",
            "description": "Reflects the financial balance for the customer or vendor at the time of the transaction, indicating amounts owed or available credit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2562.56",
              "3347.36",
              "1382.08"
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Captures the total invoice amount, a critical numeric value for accurate financial reporting and revenue tracking.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "89.6",
              "and 6 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Represents the total amount converted to a standard currency, important for organizations handling multiple currencies.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "89.6",
              "and 6 more..."
            ]
          },
          "current_balance": {
            "name": "current_balance",
            "description": "Indicates the current financial balance of the customer or vendor at the time of the transaction, crucial for account management.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "89.6",
              "and 6 more..."
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "Specifies the payment due date for the invoice, vital for monitoring payment schedules and cash flow management.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-10-10",
              "2021-08-12",
              "2021-10-01",
              "2021-09-01",
              "2021-08-18",
              "and 2 more..."
            ]
          }
        }
      },
      "quickbooks__general_ledger_by_period": {
        "table_name": "quickbooks__general_ledger_by_period",
        "fields": {
          "account_id": {
            "name": "account_id",
            "description": "A unique string identifier for each account in the general ledger, representing various account types.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "27",
              "28",
              "9999"
            ]
          },
          "account_number": {
            "name": "account_number",
            "description": "The associated account number for transactions, often defaulting to '9999-00' for many records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "9999-00"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The descriptive name of the account as listed in the general ledger, such as 'Accounts Receivable' or 'Sales'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)",
              "Net Income Adjustment",
              "Sales",
              "Discounts given"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The name of the parent account to which this account is linked, which may be absent for some records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)",
              "Sales",
              "Discounts given"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The classification of the account indicating its category for financial reporting, such as 'Income' or 'Equity'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Income",
              "Equity",
              "Accounts Receivable"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "A more detailed classification of the account providing insights into its specific nature, such as 'SalesOfProductIncome'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "AccountsReceivable",
              "RetainedEarnings",
              "SalesOfProductIncome",
              "DiscountsRefundsGiven"
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "The broader classification of the account that defines its role in financial statements, like 'Asset' or 'Revenue'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Revenue",
              "Equity",
              "Asset"
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the financial period represented in this record, usually unique for smaller groups of records.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-04-01",
              "2021-11-01",
              "2022-10-01",
              "2022-12-01",
              "2022-09-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The ending date of the financial period represented in this record, typically unique for smaller groups of records.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-04-30",
              "2021-11-30",
              "2022-10-31",
              "2022-12-31",
              "2022-09-30",
              "and 47 more..."
            ]
          },
          "period_net_change": {
            "name": "period_net_change",
            "description": "The net change in the account balance over the financial period, which can be positive or negative.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 2 more..."
            ]
          },
          "period_beginning_balance": {
            "name": "period_beginning_balance",
            "description": "The balance of the account at the start of the financial period, which may occasionally be missing.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "period_ending_balance": {
            "name": "period_ending_balance",
            "description": "The balance of the account at the end of the financial period, providing a summary of account status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "period_net_converted_change": {
            "name": "period_net_converted_change",
            "description": "The net change in the account balance for the period, converted into another currency or format.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 2 more..."
            ]
          },
          "period_beginning_converted_balance": {
            "name": "period_beginning_converted_balance",
            "description": "The converted balance of the account at the beginning of the financial period, which may be missing in some records.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "period_ending_converted_balance": {
            "name": "period_ending_converted_balance",
            "description": "The converted balance of the account at the end of the financial period, reflecting the final status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "account_ordinal": {
            "name": "account_ordinal",
            "description": "An integer representing the order of accounts, typically with values under 100, indicating their sequence.",
            "dataType": "INT64",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "3"
            ]
          }
        }
      },
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "unique_id": {
            "name": "unique_id",
            "description": "A unique string identifier for each record in the general ledger, ensuring distinct reference for every entry.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "23a45771e15fcb4f1f65b4820fc7b6b1",
              "86732109980bb963e6882bface89b485",
              "e40355c76f0a117ba0a6cbb55e1f3930",
              "28b68dfe8ab493fa05745def68b7b3ad",
              "ea1e4c74db4a78056991657d9bb770ae",
              "and 93 more..."
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique identifier for the transaction tied to each record, facilitating connections between related entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The specific date when the transaction occurred, represented in a date format, allowing for chronological tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10",
              "2021-07-13",
              "2021-09-01",
              "2021-08-02",
              "2021-04-28",
              "and 2 more..."
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "A string identifier for the customer linked to the transaction, enabling customer-specific transaction analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "4",
              "5"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The monetary value of the transaction, represented as a bignumeric type, reflecting the financial impact on the ledger.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 13 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A string identifier for the account associated with the transaction, used for account-specific tracking and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "27",
              "28"
            ]
          },
          "account_name": {
            "name": "account_name",
            "description": "The name of the account related to the transaction, providing clarity on the financial category involved.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)",
              "Sales",
              "Discounts given"
            ]
          },
          "parent_account_name": {
            "name": "parent_account_name",
            "description": "The name of the parent account in the hierarchy, potentially mirroring the account_name for clarity in account relationships.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable (A/R)",
              "Sales",
              "Discounts given"
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "The classification of the account, indicating its category such as 'Assets' or 'Income', essential for financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Accounts Receivable",
              "Income"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "A more detailed classification of the account, providing specific types for nuanced financial categorization.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "AccountsReceivable",
              "SalesOfProductIncome",
              "DiscountsRefundsGiven"
            ]
          },
          "financial_statement_helper": {
            "name": "financial_statement_helper",
            "description": "Indicates whether the account is part of a balance sheet or income statement, guiding financial reporting orientation.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "balance_sheet",
              "income_statement"
            ]
          },
          "account_current_balance": {
            "name": "account_current_balance",
            "description": "The current balance of the account, expressed as a bignumeric, showing the financial standing at the time of the transaction.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292"
            ]
          },
          "account_class": {
            "name": "account_class",
            "description": "The classification type of the account, such as 'Asset' or 'Revenue', aiding in financial analysis and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Asset",
              "Revenue"
            ]
          },
          "transaction_type": {
            "name": "transaction_type",
            "description": "Indicates whether the transaction is a 'debit' or 'credit', crucial for understanding the nature of the financial impact.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "transaction_source": {
            "name": "transaction_source",
            "description": "The origin of the transaction, detailing where it was initiated, with common sources like 'invoice' or 'invoice discount'.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice",
              "invoice discount"
            ]
          },
          "account_transaction_type": {
            "name": "account_transaction_type",
            "description": "Reflects the nature of the transaction for the account, indicating whether it is a 'debit' or 'credit', similar to transaction_type.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "debit",
              "credit"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp indicating when the record was created, essential for tracking the history of entries.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10 11:00:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-08-18 11:23:51+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp showing when the record was last modified, useful for auditing changes over time.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10 11:00:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-08-18 11:23:51+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "adjusted_amount": {
            "name": "adjusted_amount",
            "description": "The financial amount adjusted for the transaction, which may be positive or negative, reflecting corrections made.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 13 more..."
            ]
          },
          "adjusted_converted_amount": {
            "name": "adjusted_converted_amount",
            "description": "The converted amount reflecting any adjustments made, also potentially containing both positive and negative values.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 13 more..."
            ]
          },
          "running_balance": {
            "name": "running_balance",
            "description": "The cumulative total of transactions up to the current record, showing ongoing financial status that may vary.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1000",
              "2000",
              "3234",
              "4534",
              "4646",
              "and 91 more..."
            ]
          },
          "running_converted_balance": {
            "name": "running_converted_balance",
            "description": "The converted cumulative balance indicating the total after adjustments, also varying between positive and negative values.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1000",
              "2000",
              "3234",
              "4534",
              "4646",
              "and 91 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nI identified all tables with names prefixed by \"quickbooks__\" as storing QuickBooks data. For each table, I selected metadata columns relevant to accounts, journal entries, transactions, and ledger details. I included key columns such as account IDs, names, numbers, transaction dates, debits, credits, and balances, along with columns needed for joins (e.g., account_id, transaction_id). No explicit business rules for interpreting balances are defined in the metadata.\n\n---\n\n**Table Columns:**  \n\n- table: quickbooks__expenses_sales_enhanced  \n  columns: transaction_id, transaction_line_id, doc_number, transaction_date, item_id, account_id, account_name, account_sub_type, customer_id, customer_name, amount, converted_amount, total_amount, total_converted_amount\n\n- table: quickbooks__cash_flow_statement  \n  columns: cash_flow_period, account_id, account_name, parent_account_name, account_type, account_sub_type, account_class, cash_beginning_period, cash_ending_period, cash_net_period, cash_converted_beginning_period, cash_converted_ending_period, cash_converted_net_period, account_unique_id\n\n- table: quickbooks__profit_and_loss  \n  columns: calendar_date, period_first_day, period_last_day, account_id, account_name, parent_account_name, account_type, account_sub_type, account_class, amount, converted_amount, account_ordinal\n\n- table: quickbooks__balance_sheet  \n  columns: calendar_date, period_first_day, period_last_day, account_id, account_name, parent_account_name, account_type, account_sub_type, account_class, amount, converted_amount, account_ordinal\n\n- table: quickbooks__ap_ar_enhanced  \n  columns: transaction_id, doc_number, transaction_type, customer_vendor_name, customer_vendor_balance, total_amount, total_converted_amount, current_balance, due_date\n\n- table: quickbooks__general_ledger_by_period  \n  columns: account_id, account_name, parent_account_name, account_type, account_sub_type, account_class, account_number, period_first_day, period_last_day, period_net_change, period_beginning_balance, period_ending_balance, period_net_converted_change, period_beginning_converted_balance, period_ending_converted_balance, account_ordinal\n\n- table: quickbooks__general_ledger  \n  columns: unique_id, transaction_id, transaction_date, customer_id, amount, account_id, account_name, parent_account_name, account_type, account_sub_type, financial_statement_helper, account_current_balance, account_class, transaction_type, transaction_source, account_transaction_type, created_at, updated_at, adjusted_amount, adjusted_converted_amount, running_balance, running_converted_balance\n\n---\n\n**Business Rules:**  \nNo explicit business rules for interpreting balances are defined in the provided metadata for these tables.\n\n---",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "transaction_source",
        "transaction_type",
        "description"
      ],
      "quickbooks__cash_flow_statement": [
        "account_number",
        "cash_flow_type",
        "cash_flow_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_with",
        "customer_vendor_address_city"
      ],
      "quickbooks__general_ledger_by_period": [
        "financial_statement_helper",
        "date_year"
      ],
      "quickbooks__general_ledger": [
        "transaction_index"
      ]
    }
  }
]
````