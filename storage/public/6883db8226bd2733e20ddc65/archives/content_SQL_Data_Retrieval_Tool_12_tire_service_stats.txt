SQL Query:

````
SELECT
  year,
  month_number,
  month_name,
  COUNT(*) AS tire_service_appointments
FROM (
  SELECT
    EXTRACT(YEAR FROM Appointment_Date) AS year,
    EXTRACT(MONTH FROM Appointment_Date) AS month_number,
    MONTHNAME(Appointment_Date) AS month_name
  FROM Appointments
  WHERE Service_Type_ID = 1
    AND Appointment_Date >= '2023-01-01'
    AND Appointment_Date <= '2025-12-31'
) AS sub
GROUP BY year, month_number, month_name
ORDER BY year ASC, month_number ASC;
````

Data retrieved successfully:

|   year |   month_number | month_name   |   tire_service_appointments |
|-------:|---------------:|:-------------|----------------------------:|
|   2023 |              1 | January      |                         442 |
|   2023 |              2 | February     |                         403 |
|   2023 |              3 | March        |                         457 |
|   2023 |              4 | April        |                         419 |
|   2023 |              5 | May          |                         460 |
|   2023 |              6 | June         |                         437 |
|   2023 |              7 | July         |                         460 |
|   2023 |              8 | August       |                         453 |
|   2023 |              9 | September    |                         457 |
|   2023 |             10 | October      |                         464 |
|   2023 |             11 | November     |                         436 |
|   2023 |             12 | December     |                         448 |
|   2024 |              1 | January      |                         453 |
|   2024 |              2 | February     |                         438 |
|   2024 |              3 | March        |                         480 |
|   2024 |              4 | April        |                         477 |
|   2024 |              5 | May          |                         489 |
|   2024 |              6 | June         |                         450 |
|   2024 |              7 | July         |                         478 |
|   2024 |              8 | August       |                         507 |
|   2024 |              9 | September    |                         472 |
|   2024 |             10 | October      |                         463 |
|   2024 |             11 | November     |                         470 |
|   2024 |             12 | December     |                         482 |
|   2025 |              1 | January      |                         521 |
|   2025 |              2 | February     |                         489 |
|   2025 |              3 | March        |                         530 |
|   2025 |              4 | April        |                         513 |
|   2025 |              5 | May          |                         534 |
|   2025 |              6 | June         |                         513 |
|   2025 |              7 | July         |                         394 |