SQL Query:

````
SELECT
  DATEFROMPARTS(YEAR(I.InvoiceSummary_InvoiceDate), MONTH(I.InvoiceSummary_InvoiceDate), 1) AS MonthStart,
  CAST(C.Contract_Type AS VARCHAR(MAX)) AS ContractType,
  COUNT(*) AS InvoiceCount,
  SUM(I.InvoiceSummary_InvoiceAmount) AS TotalRevenue,
  ROUND(AVG(CAST(I.InvoiceSummary_InvoiceAmount AS FLOAT)), 2) AS AvgInvoice
FROM Contracts AS C
INNER JOIN InvoiceSummary AS I ON C.Contract_Number = I.InvoiceSummary_ContractNumber
WHERE CAST(C.Contract_Type AS VARCHAR(MAX)) IN ('PMA - GEN','PMA-COM')
  AND I.InvoiceSummary_InvoiceType = 'Invoice'
  AND I.InvoiceSummary_InvoiceStatus IN ('Sent','Paid in Full','Partial Payment Received','Approved','Ready for Accounting Review')
  AND I.InvoiceSummary_InvoiceDate >= '2025-06-01'
  AND I.InvoiceSummary_InvoiceDate < '2025-08-01'
GROUP BY DATEFROMPARTS(YEAR(I.InvoiceSummary_InvoiceDate), MONTH(I.InvoiceSummary_InvoiceDate), 1), CAST(C.Contract_Type AS VARCHAR(MAX))
ORDER BY MonthStart ASC, ContractType ASC;
````

Data retrieved successfully:

| MonthStart               | ContractType   |   InvoiceCount |   TotalRevenue |   AvgInvoice |
|:-------------------------|:---------------|---------------:|---------------:|-------------:|
| 2025-06-01T00:00:00.000Z | PMA - GEN      |             81 |       101159   |      1248.88 |
| 2025-06-01T00:00:00.000Z | PMA-COM        |             70 |       147998   |      2114.25 |
| 2025-07-01T00:00:00.000Z | PMA - GEN      |             37 |        80266.8 |      2169.37 |
| 2025-07-01T00:00:00.000Z | PMA-COM        |             56 |       148193   |      2646.31 |