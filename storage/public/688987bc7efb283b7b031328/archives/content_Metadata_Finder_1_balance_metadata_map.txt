Metadata of all data sources:

````
[
  {
    "data_source_name": "superlube",
    "selected_table_column_metadata": {
      "Invoices": {
        "table_name": "Invoices",
        "fields": {
          "Invoice_ID": {
            "name": "Invoice_ID",
            "description": "A unique integer identifier for each invoice in the table, serving as the primary key to distinctly reference each record.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 271030 more..."
            ]
          },
          "Service_Type_ID": {
            "name": "Service_Type_ID",
            "description": "An integer that categorizes the type of service billed in the invoice, linking to the ServiceTypes table and limited to values under 100.",
            "dataType": "int",
            "is_unstructured": false,
            "All distinct values": [
              "1",
              "2",
              "3",
              "4"
            ]
          },
          "Customer_ID": {
            "name": "Customer_ID",
            "description": "An integer that identifies the customer related to the invoice, establishing a relationship with the CustomerData table, indicating multiple invoices per customer.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "4",
              "6",
              "7",
              "and 17423 more..."
            ]
          },
          "Service_ID": {
            "name": "Service_ID",
            "description": "A unique integer identifier for the specific service rendered, serving as a foreign key to the ServiceRecords table, ensuring each service is distinctly referenced.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 271030 more..."
            ]
          },
          "Invoice_Date": {
            "name": "Invoice_Date",
            "description": "The date when the invoice was issued, crucial for tracking service delivery timing and for financial reporting.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2019-01-06",
              "2019-01-09",
              "2019-01-10",
              "2019-01-08",
              "2019-01-13",
              "and 2352 more..."
            ]
          },
          "Total_Charge": {
            "name": "Total_Charge",
            "description": "A floating-point number indicating the total billed amount for services on the invoice, essential for revenue tracking and financial analysis.",
            "dataType": "float",
            "is_unstructured": false,
            "Subset of values": [
              "22.5",
              "22.5001",
              "22.5002",
              "22.5003",
              "22.5004",
              "and 266698 more..."
            ]
          }
        }
      },
      "Salaries": {
        "table_name": "Salaries",
        "fields": {
          "Salary_ID": {
            "name": "Salary_ID",
            "description": "A unique integer identifier for each salary record, serving as the primary key for the Salaries table to ensure distinct tracking of salary entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "5",
              "and 2708 more..."
            ]
          },
          "Employee_ID": {
            "name": "Employee_ID",
            "description": "An integer that uniquely identifies the employee associated with the salary record, allowing for multiple entries per employee over time to reflect salary history.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "2",
              "3",
              "4",
              "6",
              "and 64 more..."
            ]
          },
          "Pay_Date": {
            "name": "Pay_Date",
            "description": "The date on which the salary payment was issued, essential for payroll management and tracking historical payment schedules.",
            "dataType": "date",
            "is_unstructured": false,
            "Subset of values": [
              "2019-01-31",
              "2019-02-28",
              "2019-03-31",
              "2019-04-30",
              "2019-05-31",
              "and 72 more..."
            ]
          },
          "Amount": {
            "name": "Amount",
            "description": "A floating-point number representing the total salary amount paid to the employee on the corresponding Pay_Date, reflecting individual salary payments.",
            "dataType": "float",
            "is_unstructured": false,
            "Subset of values": [
              "2500",
              "2800",
              "3000",
              "3200",
              "3500",
              "and 1 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \n\n\nI identified tables and columns by searching for fields indicative of \"balance\" or financial status, such as amounts, charges, or totals, and considered those tracking monetary values (e.g., Invoices, Salaries). I also included columns representing relevant dates for snapshot or real-time balance isolation (e.g., Invoice_Date, Pay_Date). No business rules for balance updates were found in the metadata.  \n\n\n**Table Columns:**  \n   - table: Invoices  \n      columns: Invoice_ID, Customer_ID, Service_ID, Service_Type_ID, Invoice_Date, Total_Charge  \n   - table: Salaries  \n      columns: Salary_ID, Employee_ID, Pay_Date, Amount  \n\n\n**Full Metadata:**  \n*Refer to the original \"Table columns and business rules\" for full descriptions of all fields in the Invoices and Salaries tables, as well as the ER diagram provided above. All relevant metadata regarding data types, descriptions, and constraints for the selected columns are included there.*  \n\n\n**ER Diagram:**  \n*Refer to the ER diagram JSON structure provided above for all relationships and primary/foreign keys relevant to Invoices and Salaries, including their links to CustomerData and EmployeeData.*",
    "other_table_columns": {
      "ServiceTypes": [
        "Service_Type_ID",
        "Description",
        "base_charge",
        "Average_Time_Taken",
        "Time_Range"
      ],
      "CustomerFeedbacks": [
        "Feedback_ID",
        "Service_ID",
        "Rating",
        "Comments",
        "Review_Date"
      ],
      "Appointments": [
        "Appointment_ID",
        "Customer_ID",
        "Vehicle_ID",
        "Service_Type_ID",
        "Appointment_Date",
        "Schedule_Date"
      ],
      "ServiceRecords": [
        "Service_ID",
        "Customer_ID",
        "Employee_ID",
        "Service_Type_ID",
        "Time_Taken",
        "Service_Date"
      ],
      "EmployeeData": [
        "Employee_ID",
        "Name",
        "Role",
        "Service_Type_ID",
        "Hire_Date",
        "Leave_Date"
      ],
      "CustomerData": [
        "Customer_ID",
        "Name",
        "Contact_Information",
        "Join_Date",
        "Vehicle_ID",
        "Vehicle_Make",
        "Vehicle_Model"
      ]
    }
  },
  {
    "data_source_name": "QuickBooks_Raw",
    "selected_table_column_metadata": {
      "account": {
        "table_name": "account",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique string identifier for each account, allowing distinct reference and access to individual account records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "and 22 more..."
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "The complete and descriptive name of the account, ensuring clarity in its purpose and type, unique for each account.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "PST Expense (BC)",
              "Sales",
              "Uncategorized Income",
              "Billable Expense Income",
              "Sales of Product Income",
              "and 22 more..."
            ]
          },
          "account_type": {
            "name": "account_type",
            "description": "Categorizes the account into distinct types such as liability, asset, income, or expense, aiding in the identification of its general financial nature.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Other Current Liability",
              "Income",
              "Expense",
              "Other Current Asset",
              "Accounts Receivable",
              "Cost of Goods Sold",
              "Equity"
            ]
          },
          "name": {
            "name": "name",
            "description": "The common name used for identifying the account in reports and queries, unique to each record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "Subset of values": [
              "PST Expense (BC)",
              "Sales",
              "Uncategorized Income",
              "Billable Expense Income",
              "Sales of Product Income",
              "and 22 more..."
            ]
          },
          "classification": {
            "name": "classification",
            "description": "Specifies the financial classification of the account, such as Asset or Liability, essential for accurate financial reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Liability",
              "Expense",
              "Revenue",
              "Asset",
              "Equity"
            ]
          },
          "account_sub_type": {
            "name": "account_sub_type",
            "description": "Provides a more detailed categorization of the account, offering additional context about its specific functions or types.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "GlobalTaxPayable",
              "SalesOfProductIncome",
              "GlobalTaxSuspense",
              "GlobalTaxExpense",
              "UndepositedFunds",
              "SuppliesMaterialsCogs",
              "RetainedEarnings",
              "OtherMiscellaneousServiceCost",
              "DiscountsRefundsGiven",
              "AccountsReceivable",
              "OtherCurrentAssets",
              "Inventory",
              "SuppliesMaterials"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp indicating the date and time when the account record was first created, useful for historical tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-18 21:10:47+00:00",
              "2021-03-19 07:44:58+00:00",
              "2021-03-19 10:01:30+00:00",
              "2021-03-19 08:00:28+00:00",
              "2021-03-19 07:46:28+00:00",
              "and 5 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp reflecting the last modification date and time of the account record, important for maintaining data integrity.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-18 21:10:47+00:00",
              "2021-03-19 10:01:30+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-03-19 07:44:58+00:00",
              "2021-03-19 07:46:28+00:00",
              "and 8 more..."
            ]
          },
          "balance_with_sub_accounts": {
            "name": "balance_with_sub_accounts",
            "description": "The total balance of the account, including any associated sub-accounts, indicating net credit or debit status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292",
              "-182.25",
              "-255.15"
            ]
          },
          "balance": {
            "name": "balance",
            "description": "Represents the current balance of the account itself, providing insight into its financial standing without considering sub-accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292",
              "-182.25",
              "-255.15"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "A timestamp showing the last synchronization of the account record with the Fivetran data integration tool, important for ensuring data freshness.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-07-29 22:01:04.625000+00:00",
              "2025-07-29 15:47:28.483000+00:00",
              "2025-07-29 15:47:28.455000+00:00",
              "2025-07-29 15:47:28.460000+00:00",
              "2025-07-29 15:47:28.464000+00:00",
              "and 21 more..."
            ]
          }
        }
      },
      "customer": {
        "table_name": "customer",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique identifier for each customer record, essential for distinguishing individual customers and maintaining data integrity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8",
              "9",
              "10",
              "11",
              "12"
            ]
          },
          "family_name": {
            "name": "family_name",
            "description": "The surname of the customer, which may be absent for some records, helping to identify customers with common last names.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Thumb",
              "Green"
            ]
          },
          "fully_qualified_name": {
            "name": "fully_qualified_name",
            "description": "A complete name that combines the customer's first and last names, ensuring unique identification for each record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "DCCHail Winnipeg",
              "Pickled Peppers Bodyshop",
              "Tommy Thumb",
              "ABC Company",
              "Nobo",
              "Sally Green",
              "Carl manit CST",
              "Cash Customer - SK",
              "VPS SK",
              "ZOOMi Technologies",
              "Uptown Bodyshop"
            ]
          },
          "given_name": {
            "name": "given_name",
            "description": "The first name of the customer, which may also be missing for certain records, providing further identification alongside the family name.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Tommy",
              "Sally"
            ]
          },
          "company_name": {
            "name": "company_name",
            "description": "The name of the company associated with the customer, which can be absent for some entries, aiding in categorizing business customers.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "VPS SK",
              "ABC Company",
              "Pickled Peppers Bodyshop",
              "ZOOMi Technologies",
              "Cash Customer - SK",
              "Uptown Bodyshop",
              "DCCHail Winnipeg",
              "Nobo"
            ]
          },
          "display_name": {
            "name": "display_name",
            "description": "A user-friendly and unique name for representing the customer in user interfaces, enhancing the customer experience.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "DCCHail Winnipeg",
              "Pickled Peppers Bodyshop",
              "Tommy Thumb",
              "ABC Company",
              "Nobo",
              "Sally Green",
              "Carl manit CST",
              "Cash Customer - SK",
              "VPS SK",
              "ZOOMi Technologies",
              "Uptown Bodyshop"
            ]
          },
          "print_on_check_name": {
            "name": "print_on_check_name",
            "description": "The name that will appear on financial documents such as checks, ensuring accurate representation for payment processing.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "DCCHail Winnipeg",
              "Pickled Peppers Bodyshop",
              "Tommy Thumb",
              "ABC Company",
              "Nobo",
              "Sally Green",
              "Carl manit CST",
              "Cash Customer - SK",
              "VPS SK",
              "ZOOMi Technologies",
              "Uptown Bodyshop"
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "A status token indicating the synchronization state of the customer record across systems, facilitating data consistency.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1",
              "2"
            ]
          },
          "balance_with_jobs": {
            "name": "balance_with_jobs",
            "description": "The customer's balance accounting for jobs, providing insight into financial standing related to specific tasks or projects.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "3347.36",
              "2562.56",
              "1382.08"
            ]
          },
          "balance": {
            "name": "balance",
            "description": "The overall account balance of the customer, crucial for financial assessments and transaction processing.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "3347.36",
              "2562.56",
              "1382.08"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp marking the creation time of the customer record, serving as a historical reference for data management.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-03-30 16:50:10+00:00",
              "2021-03-24 21:16:42+00:00",
              "2021-03-23 20:09:19+00:00",
              "2021-03-23 19:47:13+00:00",
              "2021-03-30 16:57:02+00:00",
              "and 6 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp indicating the last modification of the record, essential for tracking changes and data updates.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-04-08 08:19:47+00:00",
              "2021-03-25 10:10:02+00:00",
              "2021-07-16 06:00:03+00:00",
              "2021-03-30 16:57:02+00:00",
              "2021-08-29 19:01:33+00:00",
              "and 4 more..."
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "A timestamp indicating the last synchronization of the record with the Fivetran system, ensuring data consistency across platforms.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-07-29 15:47:31.616000+00:00",
              "2025-07-29 15:47:31.606000+00:00",
              "2025-07-29 15:47:31.605000+00:00",
              "2025-07-29 15:47:31.604000+00:00",
              "2025-07-29 15:47:31.617000+00:00",
              "and 6 more..."
            ]
          }
        }
      },
      "invoice": {
        "table_name": "invoice",
        "fields": {
          "id": {
            "name": "id",
            "description": "A unique identifier for each invoice, ensuring distinct reference within the database.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "A unique document number assigned to the invoice, crucial for external tracking and referencing.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1116",
              "1118",
              "1129",
              "2553",
              "2593",
              "2602",
              "2605",
              "2607",
              "2609",
              "2612",
              "2613"
            ]
          },
          "balance": {
            "name": "balance",
            "description": "The outstanding balance remaining on the invoice, indicating the amount yet to be paid.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "96.32",
              "and 6 more..."
            ]
          },
          "sync_token": {
            "name": "sync_token",
            "description": "A token utilized for synchronization purposes, reflecting the version of the invoice record.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "1"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp marking the creation date and time of the invoice, essential for historical tracking.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-01 10:05:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-07-16 08:30:03+00:00",
              "2021-07-16 06:00:03+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp indicating the last modification date and time of the invoice record, ensuring data integrity.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-01 10:05:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-07-16 08:30:03+00:00",
              "2021-07-16 06:00:03+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "total_tax": {
            "name": "total_tax",
            "description": "The total tax amount applicable to the invoice, reflecting the financial impact of taxes included.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "9",
              "108",
              "132",
              "10.32",
              "and 5 more..."
            ]
          },
          "shipping_address_id": {
            "name": "shipping_address_id",
            "description": "A reference to the ID of the shipping address associated with the invoice, used for delivery purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "20",
              "22"
            ]
          },
          "billing_address_id": {
            "name": "billing_address_id",
            "description": "A reference to the ID of the billing address linked to the invoice, used for payment processing.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "20",
              "22"
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "The date by which the payment for the invoice is expected, serving as a payment deadline.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-10-10",
              "2021-10-01",
              "2021-08-12",
              "2021-08-18",
              "2021-09-01",
              "and 2 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "The total charge on the invoice, encompassing all fees and taxes, indicating the financial total owed.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "96.32",
              "and 6 more..."
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The date on which the transaction related to the invoice occurred, providing context for the billing.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10",
              "2021-09-01",
              "2021-07-13",
              "2021-07-19",
              "2021-08-02",
              "and 2 more..."
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "A reference to the ID of the customer associated with the invoice, linking it to customer information.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "4",
              "5"
            ]
          },
          "_fivetran_synced": {
            "name": "_fivetran_synced",
            "description": "A timestamp indicating the last synchronization event with Fivetran, ensuring data consistency.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2025-07-29 15:47:34.287000+00:00",
              "2025-07-29 15:47:34.251000+00:00",
              "2025-07-29 15:47:34.262000+00:00",
              "2025-07-29 15:47:34.219000+00:00",
              "2025-07-29 15:47:34.302000+00:00",
              "and 6 more..."
            ]
          }
        }
      }
    },
    "reasoning": "  \n**Reasoning:**  \nTo answer which tables and columns store real-time or snapshot balance information, I looked for fields explicitly labeled as \"balance\" or \"balance_with_jobs\" or similar, and included related date/time or status fields that help identify the latest or most up-to-date records. I also checked for business rules about balance updates and included all relevant primary keys for joining. The ER diagram and full metadata are included as requested.\n\n  \n**Table Columns:**  \n- table: account  \n  columns: id, fully_qualified_name, account_type, name, classification, account_sub_type, created_at, updated_at, balance_with_sub_accounts, balance, _fivetran_synced\n\n- table: customer  \n  columns: id, family_name, fully_qualified_name, given_name, company_name, display_name, print_on_check_name, sync_token, balance_with_jobs, balance, created_at, updated_at, _fivetran_synced\n\n- table: invoice  \n  columns: id, doc_number, balance, sync_token, created_at, updated_at, total_tax, shipping_address_id, billing_address_id, due_date, total_amount, transaction_date, customer_id, _fivetran_synced\n\n  \n**Business Rules:**  \n- account: No explicit business rules provided on balance calculation or updates.\n- customer: No explicit business rules provided on how balances are updated.\n- invoice: No explicit business rules provided on how invoice balance is updated.\n\n  \n**Full Metadata:**  \nProvided in the original input (see table columns and descriptions above for complete metadata).\n\n  \n**ER Diagram:**  \nProvided in the previous input; it details all primary/foreign keys and relationships relevant to the selected tables.",
    "other_table_columns": {
      "item": [
        "id",
        "fully_qualified_name",
        "name",
        "type",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "employee": [],
      "sales_receipt_line": [],
      "purchase_order_tax_line": [],
      "invoice_line_bundle": [],
      "vendor_credit_line": [],
      "time_activity": [],
      "bill": [],
      "estimate_line": [],
      "journal_entry_tax_line": [],
      "term": [
        "id",
        "name",
        "active",
        "type",
        "due_days",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "purchase_tax_line": [],
      "credit_card_payment_txn": [],
      "tax_code": [
        "id",
        "description",
        "name",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "tax_rate_detail": [
        "tax_code_id",
        "tax_rate_id",
        "type",
        "tax_order",
        "_fivetran_synced"
      ],
      "bill_linked_txn": [],
      "transfer": [],
      "vendor": [],
      "purchase_order_linked_txn": [],
      "currency": [
        "id",
        "name",
        "_fivetran_synced"
      ],
      "tax_rate": [
        "id",
        "name",
        "special_tax_type",
        "rate_value",
        "description",
        "display_type",
        "effective_tax_rate",
        "created_at",
        "updated_at",
        "tax_agency_id",
        "_fivetran_synced"
      ],
      "purchase_order_line": [],
      "sales_receipt": [],
      "refund_receipt": [],
      "invoice_linked_txn": [],
      "deposit_line": [],
      "tax_agency": [
        "id",
        "display_name",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bundle": [],
      "sales_receipt_tax_line": [],
      "bundle_item": [],
      "refund_receipt_line_bundle": [],
      "bill_payment": [],
      "address": [
        "id",
        "line_1",
        "city",
        "postal_code",
        "country",
        "country_sub_division_code",
        "_fivetran_synced"
      ],
      "purchase_order": [],
      "credit_memo": [],
      "class": [
        "id",
        "fully_qualified_name",
        "name",
        "active",
        "created_at",
        "updated_at",
        "_fivetran_synced"
      ],
      "bill_payment_line": [],
      "payment_line": [],
      "budget": [],
      "estimate_line_bundle": [],
      "department": [],
      "credit_memo_line_bundle": [],
      "budget_detail": [],
      "journal_entry": [],
      "bill_line": [],
      "payment": [],
      "sales_receipt_line_bundle": [],
      "purchase": [],
      "customer": [
        "email",
        "phone_number",
        "shipping_address_id",
        "bill_address_id"
      ],
      "estimate": [],
      "refund_receipt_line": [],
      "invoice_line": [
        "invoice_id",
        "index",
        "id",
        "line_num",
        "description",
        "amount",
        "detail_type",
        "sales_item_item_id",
        "sales_item_tax_code_id",
        "_fivetran_synced"
      ],
      "credit_memo_line": [],
      "estimate_linked_txn": [],
      "vendor_credit": [],
      "estimate_tax_line": [],
      "journal_entry_line": [],
      "payment_method": [
        "id",
        "name",
        "type",
        "_fivetran_synced"
      ],
      "invoice_tax_line": [
        "invoice_id",
        "index",
        "amount",
        "percent_based",
        "net_amount_taxable",
        "tax_percent",
        "tax_rate_id",
        "_fivetran_synced"
      ],
      "refund_receipt_tax_line": [],
      "purchase_line": [],
      "deposit": []
    }
  },
  {
    "data_source_name": "QuickBooks_Analytics",
    "selected_table_column_metadata": {
      "quickbooks__ap_ar_enhanced": {
        "table_name": "quickbooks__ap_ar_enhanced",
        "fields": {
          "transaction_type": {
            "name": "transaction_type",
            "description": "Indicates the nature of the transaction, consistently labeled as 'invoice', to categorize records specific to invoice-related activities.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "invoice"
            ]
          },
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique string identifier assigned to each transaction, ensuring distinct identification and reference for data integrity.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "doc_number": {
            "name": "doc_number",
            "description": "A unique document identifier for each invoice, facilitating tracking and management of individual invoice records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "1116",
              "1118",
              "1129",
              "2553",
              "2593",
              "2602",
              "2605",
              "2607",
              "2609",
              "2612",
              "2613"
            ]
          },
          "customer_vendor_name": {
            "name": "customer_vendor_name",
            "description": "The name of the customer or vendor associated with the transaction, essential for identifying the parties involved and for reporting purposes.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "Carl manit CST",
              "ABC Company",
              "Tommy Thumb"
            ]
          },
          "customer_vendor_balance": {
            "name": "customer_vendor_balance",
            "description": "Reflects the financial balance for the customer or vendor at the time of the transaction, indicating amounts owed or available credit.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2562.56",
              "3347.36",
              "1382.08"
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Captures the total invoice amount, a critical numeric value for accurate financial reporting and revenue tracking.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "89.6",
              "and 6 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Represents the total amount converted to a standard currency, important for organizations handling multiple currencies.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "89.6",
              "and 6 more..."
            ]
          },
          "current_balance": {
            "name": "current_balance",
            "description": "Indicates the current financial balance of the customer or vendor at the time of the transaction, crucial for account management.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "89.6",
              "and 6 more..."
            ]
          },
          "due_date": {
            "name": "due_date",
            "description": "Specifies the payment due date for the invoice, vital for monitoring payment schedules and cash flow management.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-10-10",
              "2021-08-12",
              "2021-10-01",
              "2021-09-01",
              "2021-08-18",
              "and 2 more..."
            ]
          }
        }
      },
      "quickbooks__general_ledger_by_period": {
        "table_name": "quickbooks__general_ledger_by_period",
        "fields": {
          "account_id": {
            "name": "account_id",
            "description": "A unique string identifier for each account in the general ledger, representing various account types.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "27",
              "28",
              "9999"
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the financial period represented in this record, usually unique for smaller groups of records.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-04-01",
              "2021-11-01",
              "2022-10-01",
              "2022-12-01",
              "2022-09-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The ending date of the financial period represented in this record, typically unique for smaller groups of records.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-04-30",
              "2021-11-30",
              "2022-10-31",
              "2022-12-31",
              "2022-09-30",
              "and 47 more..."
            ]
          },
          "period_net_change": {
            "name": "period_net_change",
            "description": "The net change in the account balance over the financial period, which can be positive or negative.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 2 more..."
            ]
          },
          "period_beginning_balance": {
            "name": "period_beginning_balance",
            "description": "The balance of the account at the start of the financial period, which may occasionally be missing.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "period_ending_balance": {
            "name": "period_ending_balance",
            "description": "The balance of the account at the end of the financial period, providing a summary of account status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "period_net_converted_change": {
            "name": "period_net_converted_change",
            "description": "The net change in the account balance for the period, converted into another currency or format.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 2 more..."
            ]
          },
          "period_beginning_converted_balance": {
            "name": "period_beginning_converted_balance",
            "description": "The converted balance of the account at the beginning of the financial period, which may be missing in some records.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "period_ending_converted_balance": {
            "name": "period_ending_converted_balance",
            "description": "The converted balance of the account at the end of the financial period, reflecting the final status.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          }
        }
      },
      "quickbooks__general_ledger": {
        "table_name": "quickbooks__general_ledger",
        "fields": {
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique identifier for the transaction tied to each record, facilitating connections between related entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "The specific date when the transaction occurred, represented in a date format, allowing for chronological tracking.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10",
              "2021-07-13",
              "2021-09-01",
              "2021-08-02",
              "2021-04-28",
              "and 2 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A string identifier for the account associated with the transaction, used for account-specific tracking and reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "27",
              "28"
            ]
          },
          "account_current_balance": {
            "name": "account_current_balance",
            "description": "The current balance of the account, expressed as a bignumeric, showing the financial standing at the time of the transaction.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "7292"
            ]
          },
          "created_at": {
            "name": "created_at",
            "description": "A timestamp indicating when the record was created, essential for tracking the history of entries.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10 11:00:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-08-18 11:23:51+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "updated_at": {
            "name": "updated_at",
            "description": "A timestamp showing when the record was last modified, useful for auditing changes over time.",
            "dataType": "TIMESTAMP",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10 11:00:02+00:00",
              "2021-07-16 08:23:25+00:00",
              "2021-09-10 11:20:02+00:00",
              "2021-08-18 11:23:51+00:00",
              "2021-09-10 10:55:02+00:00",
              "and 6 more..."
            ]
          },
          "running_balance": {
            "name": "running_balance",
            "description": "The cumulative total of transactions up to the current record, showing ongoing financial status that may vary.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1000",
              "2000",
              "3234",
              "4534",
              "4646",
              "and 91 more..."
            ]
          },
          "running_converted_balance": {
            "name": "running_converted_balance",
            "description": "The converted cumulative balance indicating the total after adjustments, also varying between positive and negative values.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1000",
              "2000",
              "3234",
              "4534",
              "4646",
              "and 91 more..."
            ]
          }
        }
      },
      "quickbooks__balance_sheet": {
        "table_name": "quickbooks__balance_sheet",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The specific date for which the balance sheet data is recorded, used to track the company's financial position on that day.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-06-01",
              "2022-12-01",
              "2021-06-01",
              "2022-08-01",
              "2024-07-01",
              "and 47 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The start date of the financial period covered by the balance sheet entry, providing context for the associated financial data.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-06-01",
              "2022-12-01",
              "2021-06-01",
              "2022-08-01",
              "2024-07-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The end date of the financial period represented in the balance sheet record, indicating the duration of the financial data being analyzed.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-06-30",
              "2022-12-31",
              "2021-06-30",
              "2022-08-31",
              "2024-07-31",
              "and 47 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique identifier for each account within the balance sheet, essential for distinguishing accounts during data queries and analysis.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "27",
              "9999"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "The financial value associated with the account for the specified date, critical for evaluating the monetary state of accounts.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "The potentially adjusted financial value of the account for different reporting standards or currencies, important for understanding account valuation.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          }
        }
      },
      "quickbooks__cash_flow_statement": {
        "table_name": "quickbooks__cash_flow_statement",
        "fields": {
          "cash_flow_period": {
            "name": "cash_flow_period",
            "description": "The specific date for which the cash flow statement entry applies, serving as a key reference for each record.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2024-08-01",
              "2024-01-01",
              "2024-10-01",
              "2022-08-01",
              "2025-02-01",
              "and 47 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "Uniquely identifies each account within the cash flow statement, critical for establishing relationships with other tables.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "27",
              "9999"
            ]
          },
          "cash_ending_period": {
            "name": "cash_ending_period",
            "description": "Represents the total cash available at the end of the period, essential for assessing the cash position.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "cash_converted_ending_period": {
            "name": "cash_converted_ending_period",
            "description": "Indicates the adjusted cash amount at the end of the period, reflecting potential accounting adjustments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "2000",
              "6854.6",
              "5343.6",
              "6566.6"
            ]
          },
          "cash_beginning_period": {
            "name": "cash_beginning_period",
            "description": "Shows the cash amount at the start of the period, important for understanding cash flow dynamics.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "cash_net_period": {
            "name": "cash_net_period",
            "description": "Reflects the net cash flow during the period, crucial for evaluating cash inflow and outflow.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "288",
              "1223",
              "2000",
              "3343.6"
            ]
          },
          "cash_converted_beginning_period": {
            "name": "cash_converted_beginning_period",
            "description": "Indicates the adjusted cash amount at the start of the period, facilitating comparisons against expected cash positions.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "2000",
              "6854.6",
              "6566.6",
              "5343.6"
            ]
          },
          "cash_converted_net_period": {
            "name": "cash_converted_net_period",
            "description": "Represents the adjusted net cash flow during the period, aiding in understanding effective cash flow after adjustments.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "All distinct values": [
              "0",
              "288",
              "1223",
              "2000",
              "3343.6"
            ]
          }
        }
      },
      "quickbooks__profit_and_loss": {
        "table_name": "quickbooks__profit_and_loss",
        "fields": {
          "calendar_date": {
            "name": "calendar_date",
            "description": "The date for which the profit and loss data is recorded, crucial for tracking daily financial performance.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-01",
              "2021-11-01",
              "2022-01-01",
              "2022-04-01",
              "2023-12-01",
              "and 47 more..."
            ]
          },
          "period_first_day": {
            "name": "period_first_day",
            "description": "The starting date of the reporting period for which the profit and loss data is aggregated.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-01",
              "2021-11-01",
              "2022-01-01",
              "2022-04-01",
              "2023-12-01",
              "and 47 more..."
            ]
          },
          "period_last_day": {
            "name": "period_last_day",
            "description": "The concluding date of the reporting period for which the profit and loss data is reported.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-05-31",
              "2021-11-30",
              "2022-01-31",
              "2022-04-30",
              "2023-12-31",
              "and 47 more..."
            ]
          },
          "account_id": {
            "name": "account_id",
            "description": "A unique identifier for the account associated with this record, allowing for distinct tracking of financial entries.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "6",
              "28"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Represents the financial amount associated with this record, capturing both income and losses in a numeric format.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 1 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Represents a financial amount similar to 'amount', potentially adjusted for currency conversion or other formats.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "0",
              "288",
              "1223",
              "2000",
              "3646",
              "and 1 more..."
            ]
          }
        }
      },
      "quickbooks__expenses_sales_enhanced": {
        "table_name": "quickbooks__expenses_sales_enhanced",
        "fields": {
          "transaction_id": {
            "name": "transaction_id",
            "description": "A unique identifier for each transaction, ensuring the distinct identification of individual sales records.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "88",
              "89",
              "90",
              "91",
              "92",
              "93",
              "94",
              "95",
              "96",
              "97",
              "98"
            ]
          },
          "transaction_date": {
            "name": "transaction_date",
            "description": "Records the date when the transaction took place, enabling analysis of sales trends over time.",
            "dataType": "DATE",
            "is_unstructured": false,
            "Subset of values": [
              "2021-09-10",
              "2021-07-13",
              "2021-09-01",
              "2021-08-02",
              "2021-04-28",
              "and 2 more..."
            ]
          },
          "customer_id": {
            "name": "customer_id",
            "description": "Provides the identifier for the customer involved in the transaction, facilitating customer-focused reporting.",
            "dataType": "STRING",
            "is_unstructured": false,
            "All distinct values": [
              "2",
              "4",
              "5"
            ]
          },
          "amount": {
            "name": "amount",
            "description": "Represents the monetary value associated with the transaction line item, crucial for financial analysis and reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 12 more..."
            ]
          },
          "converted_amount": {
            "name": "converted_amount",
            "description": "Indicates the amount converted into a different currency, if applicable, supporting multi-currency transaction reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "1",
              "5",
              "9",
              "10",
              "11",
              "and 12 more..."
            ]
          },
          "total_amount": {
            "name": "total_amount",
            "description": "Represents the total amount for the entire transaction, summing all line items for comprehensive financial reporting.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "30.24",
              "and 6 more..."
            ]
          },
          "total_converted_amount": {
            "name": "total_converted_amount",
            "description": "Signifies the total amount converted into another currency, aiding in financial reporting across various currencies.",
            "dataType": "BIGNUMERIC",
            "is_unstructured": false,
            "Subset of values": [
              "84",
              "1008",
              "1232",
              "2000",
              "30.24",
              "and 6 more..."
            ]
          }
        }
      }
    },
    "reasoning": "**Reasoning:**  \n\n\nTo identify all tables and columns storing real-time or snapshot balances, I looked for columns with names or descriptions such as \"balance,\" \"current balance,\" \"ending balance,\" \"amount,\" or \"net balance.\" I also included any associated date/time or period fields that allow isolation of the latest or period-specific balances. All relevant business rules and metadata, as well as keys required for context or joins, are included.\n\n\n---\n\n**Table Columns:**  \n\n\n- table: quickbooks__ap_ar_enhanced  \n  columns: customer_vendor_balance, current_balance, total_amount, total_converted_amount, due_date, transaction_id, doc_number, customer_vendor_name, transaction_type\n\n- table: quickbooks__general_ledger_by_period  \n  columns: period_beginning_balance, period_ending_balance, period_net_change, period_net_converted_change, period_beginning_converted_balance, period_ending_converted_balance, account_id, period_first_day, period_last_day\n\n- table: quickbooks__general_ledger  \n  columns: account_current_balance, running_balance, running_converted_balance, transaction_id, account_id, transaction_date, created_at, updated_at\n\n- table: quickbooks__balance_sheet  \n  columns: amount, converted_amount, account_id, calendar_date, period_first_day, period_last_day\n\n- table: quickbooks__cash_flow_statement  \n  columns: cash_beginning_period, cash_ending_period, cash_net_period, cash_converted_beginning_period, cash_converted_ending_period, cash_converted_net_period, cash_flow_period, account_id\n\n- table: quickbooks__profit_and_loss  \n  columns: amount, converted_amount, account_id, calendar_date, period_first_day, period_last_day\n\n- table: quickbooks__expenses_sales_enhanced  \n  columns: amount, converted_amount, total_amount, total_converted_amount, transaction_id, transaction_date, customer_id\n\n---\n\n**Business Rules:**  \n\n\nThere are no table-specific business rules provided regarding how balances are updated. General implied logic, based on column descriptions:\n- Balances in summary tables (e.g., `quickbooks__balance_sheet`, `quickbooks__cash_flow_statement`, `quickbooks__profit_and_loss`, `quickbooks__general_ledger_by_period`) are typically snapshots as of a date or period.\n- Real-time or running balances (e.g., `account_current_balance`, `running_balance` in `quickbooks__general_ledger`) are updated as each transaction is posted.\n- Date fields (`transaction_date`, `calendar_date`, `period_first_day`, `period_last_day`, `created_at`, `updated_at`) are needed to isolate the latest or relevant balance.\n\n---\n\n**ER Diagram:**  \n\n\n```json\n{\n  \"quickbooks__ap_ar_enhanced\": {\n    \"primary_key\": [\"transaction_id\"],\n    \"foreign_keys\": []\n  },\n  \"quickbooks__expenses_sales_enhanced\": {\n    \"primary_key\": [\"transaction_id\", \"transaction_line_id\"],\n    \"foreign_keys\": [\n      {\"column\": \"customer_id\", \"references\": \"customer.id\"},\n      {\"column\": \"account_id\", \"references\": \"account.id\"}\n    ]\n  },\n  \"quickbooks__general_ledger\": {\n    \"primary_key\": [\"unique_id\"],\n    \"foreign_keys\": [\n      {\"column\": \"transaction_id\", \"references\": \"quickbooks__ap_ar_enhanced.transaction_id\"},\n      {\"column\": \"customer_id\", \"references\": \"customer.id\"},\n      {\"column\": \"account_id\", \"references\": \"account.id\"}\n    ]\n  },\n  \"quickbooks__general_ledger_by_period\": {\n    \"primary_key\": [\"account_id\", \"period_first_day\", \"period_last_day\"],\n    \"foreign_keys\": [\n      {\"column\": \"account_id\", \"references\": \"account.id\"}\n    ]\n  },\n  \"quickbooks__balance_sheet\": {\n    \"primary_key\": [\"account_id\", \"calendar_date\"],\n    \"foreign_keys\": [\n      {\"column\": \"account_id\", \"references\": \"account.id\"}\n    ]\n  },\n  \"quickbooks__profit_and_loss\": {\n    \"primary_key\": [\"account_id\", \"calendar_date\"],\n    \"foreign_keys\": [\n      {\"column\": \"account_id\", \"references\": \"account.id\"}\n    ]\n  },\n  \"quickbooks__cash_flow_statement\": {\n    \"primary_key\": [\"account_id\", \"cash_flow_period\"],\n    \"foreign_keys\": [\n      {\"column\": \"account_id\", \"references\": \"account.id\"}\n    ]\n  }\n}\n```\n\n---",
    "other_table_columns": {
      "quickbooks__expenses_sales_enhanced": [
        "transaction_source",
        "transaction_line_id",
        "doc_number",
        "transaction_type",
        "item_id",
        "account_id",
        "account_name",
        "account_sub_type",
        "customer_name",
        "description"
      ],
      "quickbooks__cash_flow_statement": [
        "account_class",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_number",
        "account_name",
        "account_unique_id",
        "cash_flow_type",
        "cash_flow_ordinal"
      ],
      "quickbooks__profit_and_loss": [
        "account_class",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_name",
        "account_ordinal"
      ],
      "quickbooks__balance_sheet": [
        "account_class",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_name",
        "account_ordinal"
      ],
      "quickbooks__ap_ar_enhanced": [
        "transaction_with",
        "customer_vendor_address_city"
      ],
      "quickbooks__general_ledger_by_period": [
        "account_number",
        "account_name",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "account_class",
        "financial_statement_helper",
        "date_year",
        "account_ordinal"
      ],
      "quickbooks__general_ledger": [
        "unique_id",
        "transaction_index",
        "customer_id",
        "amount",
        "account_name",
        "parent_account_name",
        "account_type",
        "account_sub_type",
        "financial_statement_helper",
        "account_class",
        "transaction_type",
        "transaction_source",
        "account_transaction_type",
        "adjusted_amount",
        "adjusted_converted_amount"
      ]
    }
  }
]
````