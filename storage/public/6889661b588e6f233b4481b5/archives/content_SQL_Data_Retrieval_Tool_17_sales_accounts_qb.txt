SQL Query:

````
SELECT id, name, fully_qualified_name, account_type, classification, balance
FROM account
WHERE name LIKE '%Sales%' OR fully_qualified_name LIKE '%Sales%'
ORDER BY name ASC;
````

Data retrieved successfully:

|   id | name                    | fully_qualified_name    | account_type   | classification   |   balance |
|-----:|:------------------------|:------------------------|:---------------|:-----------------|----------:|
|    6 | Sales                   | Sales                   | Income         | Revenue          |         0 |
|   24 | Sales of Product Income | Sales of Product Income | Income         | Revenue          |         0 |