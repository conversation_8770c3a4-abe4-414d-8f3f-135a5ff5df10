Python code:

```python
from metalake import load_data
import pandas as pd
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt

# 1. Load the full, pre-filtered data
df = load_data("68828b7d56cf54f65632fee1", "vendorbills_overdue_by_month.dat")

# 2. Handle empty DataFrame
if df.empty:
    print("No overdue vendor bill data available for the requested period.")
else:
    # 3. Sort by due_month ascending
    df_sorted = df.sort_values('due_month').reset_index(drop=True)

    # 4. Save the sorted DataFrame to CSV
    csv_path = 'files/overdue_vendor_bills.csv'
    df_sorted[['due_month', 'overdue_amount']].to_csv(csv_path, index=False)

    # 5. Display up to 50 rows in markdown table
    display_rows = min(50, len(df_sorted))
    print(df_sorted.head(display_rows).to_markdown(index=False))
    if len(df_sorted) > 50:
        print(f"\nNote: Only the first 50 of {len(df_sorted)} rows are displayed.")

    # 6. Create bar chart
    plt.figure(figsize=(10, 6))
    bars = plt.bar(df_sorted['due_month'], df_sorted['overdue_amount'], color='steelblue')
    plt.title("Monthly Overdue Vendor Bill Amounts (Last 6 Months)")
    plt.xlabel("Due Month")
    plt.ylabel("Overdue Amount")
    plt.xticks(rotation=30, ha='right')

    # 7. Add labels on bars (with comma formatting, no scientific notation)
    for bar, amount in zip(bars, df_sorted['overdue_amount']):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height(),
                 f"{amount:,.2f}", ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    image_path = 'files/overdue_vendor_bills_bar.png'
    plt.savefig(image_path, dpi=150)
    plt.close()
````

Output:

| due_month   |   overdue_amount |
|:------------|-----------------:|
| 2025-02     |  16279.6         |
| 2025-03     |  86664.5         |
| 2025-04     | 148607           |
| 2025-05     | 186600           |
| 2025-06     |      3.05992e+06 |
| 2025-07     | 659285           |

