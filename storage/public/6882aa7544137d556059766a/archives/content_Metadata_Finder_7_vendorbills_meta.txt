Metadata of all data sources:

````
[
  {
    "data_source_name": "FieldServio",
    "selected_table_column_metadata": {
      "VendorBills": {
        "table_name": "VendorBills",
        "fields": {
          "VendorBills_BillId": {
            "name": "VendorBills_BillId",
            "description": "A unique integer identifier for each bill record, ensuring distinct referencing of entries.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1486017",
              "1500576",
              "1502251",
              "1511492",
              "1530883",
              "and 6063 more..."
            ]
          },
          "VendorBills_Total": {
            "name": "VendorBills_Total",
            "description": "A decimal field representing the overall amount of the bill, accommodating both positive and negative values for credits or adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_AmountPaid": {
            "name": "VendorBills_AmountPaid",
            "description": "A decimal field recording the amount paid towards the bill, which can be positive for payments or negative for refunds.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_Balance": {
            "name": "VendorBills_Balance",
            "description": "The outstanding balance on the bill after payments, which can be either positive or negative, indicating amounts still owed.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyTotal": {
            "name": "VendorBills_CurrencyTotal",
            "description": "This decimal field captures the total amount of the bill in the specified currency, allowing for multi-currency transactions.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencySubTotal": {
            "name": "VendorBills_CurrencySubTotal",
            "description": "Reflects the subtotal amount in the specified currency, accommodating both positive and negative values.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyTax": {
            "name": "VendorBills_CurrencyTax",
            "description": "Indicates the tax amount in the specified currency, similar to the TaxAmount field, and can also be positive or negative.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyPaid": {
            "name": "VendorBills_CurrencyPaid",
            "description": "Captures the amount paid in the specified currency, allowing for both positive payments and negative adjustments.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_CurrencyBalance": {
            "name": "VendorBills_CurrencyBalance",
            "description": "Represents the remaining balance in the specified currency after accounting for payments, which can be positive or negative.",
            "dataType": "decimal",
            "is_unstructured": false
          },
          "VendorBills_BillDate": {
            "name": "VendorBills_BillDate",
            "description": "A datetime field recording the date when the bill was issued, crucial for financial tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_DueDate": {
            "name": "VendorBills_DueDate",
            "description": "A datetime field showing the payment due date for the bill, important for managing accounts payable.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "VendorBills_SupplierID": {
            "name": "VendorBills_SupplierID",
            "description": "An integer foreign key linking to the SupplierInformation table, indicating the supplier associated with the bill.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "81061",
              "81073",
              "81079",
              "81111",
              "81113",
              "and 372 more..."
            ]
          },
          "VendorBills_SupplierName": {
            "name": "VendorBills_SupplierName",
            "description": "A varchar field holding the name of the supplier related to the bill, typically unique within groups of records.",
            "dataType": "varchar",
            "is_unstructured": false,
            "Subset of values": [
              "Ingersoll-Rand (USD)",
              "Generac Power Systems, Inc.",
              "Geo. H. Young & Co. Ltd.",
              "Tom Beggs Agencies",
              "Oil Mart Ltd",
              "and 369 more..."
            ]
          },
          "VendorBills_Status": {
            "name": "VendorBills_Status",
            "description": "A varchar field representing the current processing state of the bill, with values such as 'Paid in Full', 'Approved', or 'Pending Approval'.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Paid in Full",
              "Approved",
              "Pending Approval",
              "Voided"
            ]
          },
          "VendorBills_Office": {
            "name": "VendorBills_Office",
            "description": "A varchar field describing the office related to the vendor bill, potentially categorical.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "MB Office",
              "SK Office",
              "Shared Services"
            ]
          }
        }
      },
      "Payments": {
        "table_name": "Payments",
        "fields": {
          "Payment_ID": {
            "name": "Payment_ID",
            "description": "A unique identifier for each payment entry, ensuring distinct reference for every transaction within the Payments table.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "3073833",
              "3123445",
              "3149130",
              "3167630",
              "3228503",
              "and 14086 more..."
            ]
          },
          "Payment_EntityType": {
            "name": "Payment_EntityType",
            "description": "Categorizes the payment type, with possible values including 'Bill', 'Invoice', 'Customer Credit', 'Other', and 'A/R Deposit', clarifying the nature of the payment.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Bill",
              "Invoice",
              "Customer Credit",
              "Other",
              "A/R Deposit"
            ]
          },
          "Payment_BillID": {
            "name": "Payment_BillID",
            "description": "Indicates the ID of the corresponding bill for the payment; populated for less than half of the records, typically unique for pairs of records.",
            "dataType": "int",
            "is_unstructured": false,
            "Subset of values": [
              "1529163",
              "1540163",
              "1541672",
              "1556201",
              "1557223",
              "and 5517 more..."
            ]
          },
          "Payment_PaymentAmount": {
            "name": "Payment_PaymentAmount",
            "description": "Records the total amount of the payment as a monetary value, which may include both positive and negative figures reflecting adjustments.",
            "dataType": "money",
            "is_unstructured": false
          },
          "Payment_PaymentDate": {
            "name": "Payment_PaymentDate",
            "description": "Denotes the actual date on which the payment was processed, essential for financial tracking and reporting.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_ProcessedDate": {
            "name": "Payment_ProcessedDate",
            "description": "Captures the date when the payment was fully processed, critical for auditing purposes and payment completion tracking.",
            "dataType": "datetime",
            "is_unstructured": false
          },
          "Payment_TypeName": {
            "name": "Payment_TypeName",
            "description": "Classifies the payment method, with options including 'Check', 'Manual', 'EFT', 'Visa', 'MasterCard', and more, reflecting the various payment processing methods.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Manual",
              "EFT",
              "Credit Offset",
              "Check",
              "Exchange Rate Adjustment",
              "Visa",
              "MasterCard",
              "Cash"
            ]
          },
          "Payment_Status": {
            "name": "Payment_Status",
            "description": "Indicates the current state of the payment, with most records marked as 'Processed' and a small percentage as 'Voided', offering insight into payment completion.",
            "dataType": "varchar",
            "is_unstructured": false,
            "All distinct values": [
              "Processed",
              "Voided"
            ]
          }
        }
      }
    },
    "reasoning": "**Reasoning:**  \n\n\nTo analyze overdue vendor bills and payment clearing, we need `VendorBills` for bill details and `Payments` for payment records. Specifically, the selected columns are directly requested for both tables. The relationship is via `VendorBills_BillId` (PK) and `Payment_BillID` (FK). Status columns are included for business rules/status definitions as requested.\n\n  \n\n**Table Columns:**  \n\n\n- table: VendorBills  \n  columns: VendorBills_BillId, VendorBills_SupplierID, VendorBills_SupplierName, VendorBills_Office, VendorBills_BillDate, VendorBills_DueDate, VendorBills_Total, VendorBills_AmountPaid, VendorBills_Balance, VendorBills_Status, VendorBills_CurrencyTotal, VendorBills_CurrencySubTotal, VendorBills_CurrencyTax, VendorBills_CurrencyPaid, VendorBills_CurrencyBalance  \n\n- table: Payments  \n  columns: Payment_ID, Payment_EntityType, Payment_BillID, Payment_TypeName, Payment_PaymentDate, Payment_ProcessedDate, Payment_PaymentAmount, Payment_Status  \n\n\n\n**Business rules/Status-code definitions:**  \n\n\n- VendorBills_Status:  \n  Values include 'Paid in Full', 'Approved', 'Pending Approval', 'Voided'.  \n  - 'Paid in Full': Bill fully paid  \n  - 'Approved': Bill approved but not necessarily paid  \n  - 'Pending Approval': Awaiting approval  \n  - 'Voided': Bill canceled/invalidated  \n\n- Payment_Status:  \n  Values include 'Processed', 'Voided'.  \n  - 'Processed': Payment completed  \n  - 'Voided': Payment canceled/invalidated  \n\n  \n\n**PK/FK relationships:**  \n\n- VendorBills_BillId (PK in VendorBills)  \n- Payment_BillID (FK in Payments referencing VendorBills_BillId)"
  }
]
````