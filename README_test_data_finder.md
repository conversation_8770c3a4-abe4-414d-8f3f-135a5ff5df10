# DataFinderAgent Test Program

## Overview

This test program (`test_data_finder.py`) comprehensively tests the `DataFinderAgent.execute_task()` functionality with a specific focus on vendor bills metadata retrieval.

## Task Instruction Tested

The test program validates the agent's ability to handle this specific task instruction:

> "Provide metadata for retrieving vendor bills and their payment status. Identify all tables and columns that store vendor bills, their current status (e.g., pending, paid), payment dates, amounts, vendor identifiers, and due dates. Include relevant business rules for interpreting bill status and payment completion."

## Features Tested

### 1. Successful Metadata Retrieval
- Tests retrieval from multiple mock data sources (FinanceDB, AccountingSystem)
- Validates table and column metadata structure
- Verifies business rules extraction

### 2. Error Handling
- Tests behavior when some data sources fail
- Validates proper error recording in results
- Ensures partial success scenarios work correctly

### 3. ER Diagram Support
- Tests ER diagram retrieval when requested via `additional_data` parameter
- Validates proper inclusion in result output

### 4. Business Rules Processing
- Tests extraction and formatting of business rules
- Validates JSON structure and content
- Ensures rules are properly displayed in output

## Mock Data Sources

### FinanceDB
- **vendor_bills** table: bill_id, vendor_id, bill_amount, bill_date, due_date, status, payment_date
- **vendor_payments** table: payment_id, bill_id, payment_amount, payment_date, payment_method
- **Business Rules**: Status interpretation, payment completion logic

### AccountingSystem
- **accounts_payable** table: ap_id, vendor_code, invoice_number, invoice_amount, payment_status, payment_due_date
- **Business Rules**: Payment status values and meanings

### EmptyDB
- Used for testing failure scenarios
- Returns error responses to test error handling

## Usage

### Run All Tests
```bash
python test_data_finder.py
```

This will:
1. Execute 4 comprehensive unit tests
2. Display detailed test results
3. Show a demonstration of the vendor bill metadata retrieval with sample output

### Test Cases Included

1. **test_execute_task_success**: Tests successful execution with multiple data sources
2. **test_execute_task_with_er_diagrams**: Tests ER diagram retrieval functionality
3. **test_execute_task_with_failures**: Tests error handling when some data sources fail
4. **test_business_rules_extraction**: Tests business rules extraction and formatting

## Expected Output

The test program produces:
- Unit test results with pass/fail status
- Test execution summary
- Detailed demonstration showing:
  - Formatted display output
  - Complete metadata JSON structure
  - Business rules JSON array

## Dependencies

The test uses Python's built-in `unittest` and `unittest.mock` modules, so no additional dependencies are required beyond the existing project structure.

## Integration

This test program can be integrated into CI/CD pipelines or used for manual testing during development. All tests use mocks to avoid dependencies on actual database connections or external services.
