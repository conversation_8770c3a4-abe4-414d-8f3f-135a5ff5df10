#!/usr/bin/env python3
"""
Test script to verify backward compatibility for legacy userId field in _add_users_to_insight_dashboard.
This script tests that dashboards with the old userId field are properly migrated to userIdList
without losing the original user ID.
"""

import os
import sys
from bson import ObjectId
from datetime import datetime, timezone

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.insight_dashboard_service import InsightDashboardService
from databases.mongo_manager import MongoDBmanager
from utils.constant import UserType


class MockUser:
    """Mock user object for testing"""
    def __init__(self, user_id, name, email):
        self.id = user_id
        self.name = name
        self.email = email
        self.isSelected = True


def test_legacy_userid_compatibility():
    """Test backward compatibility for legacy userId field."""
    
    print("Testing backward compatibility for legacy userId field...")
    
    # Initialize the service
    insight_service = InsightDashboardService()
    
    # Test data
    legacy_user_id = "507f1f77bcf86cd799439011"
    new_user1_id = "507f1f77bcf86cd799439012"
    new_user2_id = "507f1f77bcf86cd799439013"
    
    # Test 1: Create a dashboard with legacy userId field (no userIdList)
    print("\n1. Creating dashboard with legacy userId field...")
    
    legacy_dashboard = {
        "conversationId": ObjectId("507f1f77bcf86cd799439015"),
        "sessionId": ObjectId("507f1f77bcf86cd799439016"),
        "userId": ObjectId(legacy_user_id),  # Legacy field - single ObjectId
        "userName": "Legacy User",
        "userEmail": "<EMAIL>",
        "initialTitle": "Legacy Dashboard",
        "frequencyInfo": {"frequency": "daily", "trigger": ""},
        "csv_data_with_user_input": [],
        "isRendered": False,
        "renderedMainGraph": "",
        "createdAt": datetime.now(timezone.utc),
        "updatedAt": datetime.now(timezone.utc),
        "status": 1,
        "stats": {},
        "kpiParam": "legacy_kpi"
    }
    
    # Insert legacy test document
    result = insight_service.insight_dashboard_collection.insert_one(legacy_dashboard)
    dashboard_id = result.inserted_id
    print(f"✓ Created legacy dashboard with ID: {dashboard_id}")
    print(f"  Legacy userId: {legacy_user_id}")
    
    # Verify the legacy user can access the dashboard before migration
    print("\n2. Verifying legacy user access before migration...")
    response = insight_service.get_insight_list_to_dashboard(
        legacy_user_id, UserType.USER_TYPE_AUDITOR.value
    )
    
    legacy_user_can_access_before = False
    if response.success:
        for insight in response.data:
            if insight["insightDashboardId"] == str(dashboard_id):
                legacy_user_can_access_before = True
                break
    
    if legacy_user_can_access_before:
        print("✓ Legacy user can access dashboard before migration")
    else:
        print("✗ Legacy user cannot access dashboard before migration")
    
    # Test 3: Add new users to the legacy dashboard
    print("\n3. Adding new users to legacy dashboard...")
    
    # Retrieve the dashboard object
    dashboard_obj = insight_service.insight_dashboard_collection.get_one_document({"_id": dashboard_id})
    
    # Verify it has userId field and no userIdList
    if "userId" in dashboard_obj and "userIdList" not in dashboard_obj:
        print("✓ Dashboard has legacy userId field and no userIdList")
    else:
        print("✗ Dashboard structure is not as expected")
        print(f"  Has userId: {'userId' in dashboard_obj}")
        print(f"  Has userIdList: {'userIdList' in dashboard_obj}")
    
    # Create mock users to add
    new_users = [
        MockUser(new_user1_id, "New User 1", "<EMAIL>"),
        MockUser(new_user2_id, "New User 2", "<EMAIL>")
    ]
    
    user_info = {
        "id": legacy_user_id,
        "name": "Legacy User",
        "email": "<EMAIL>"
    }
    
    # Add users to dashboard (this should trigger the migration)
    insight_service._add_users_to_insight_dashboard(
        dashboard_obj, new_users, [], user_info
    )
    
    print(f"✓ Added {len(new_users)} new users to legacy dashboard")
    
    # Test 4: Verify the migration worked correctly
    print("\n4. Verifying migration to userIdList...")
    
    # Retrieve the updated dashboard
    updated_dashboard = insight_service.insight_dashboard_collection.get_one_document({"_id": dashboard_id})
    
    # Check that userId field is removed and userIdList is created
    has_userid = "userId" in updated_dashboard
    has_useridlist = "userIdList" in updated_dashboard
    
    if not has_userid and has_useridlist:
        print("✓ Migration successful: userId removed, userIdList created")
    else:
        print("✗ Migration failed:")
        print(f"  Still has userId: {has_userid}")
        print(f"  Has userIdList: {has_useridlist}")
    
    # Check that all expected users are in userIdList
    if has_useridlist:
        user_ids_in_list = [str(uid) for uid in updated_dashboard["userIdList"]]
        expected_users = [legacy_user_id, new_user1_id, new_user2_id]
        
        print(f"  userIdList: {user_ids_in_list}")
        
        if all(uid in user_ids_in_list for uid in expected_users) and len(user_ids_in_list) == 3:
            print("✓ All expected users present in userIdList")
        else:
            print("✗ Missing or extra users in userIdList")
            print(f"  Expected: {expected_users}")
            print(f"  Got: {user_ids_in_list}")
    
    # Test 5: Verify all users can access the dashboard after migration
    print("\n5. Verifying user access after migration...")
    
    all_users = [legacy_user_id, new_user1_id, new_user2_id]
    access_results = {}
    
    for test_user_id in all_users:
        response = insight_service.get_insight_list_to_dashboard(
            test_user_id, UserType.USER_TYPE_AUDITOR.value
        )
        
        can_access = False
        if response.success:
            for insight in response.data:
                if insight["insightDashboardId"] == str(dashboard_id):
                    can_access = True
                    break
        
        access_results[test_user_id] = can_access
        user_label = "Legacy user" if test_user_id == legacy_user_id else f"New user {test_user_id[-4:]}"
        
        if can_access:
            print(f"✓ {user_label} can access the dashboard")
        else:
            print(f"✗ {user_label} cannot access the dashboard")
    
    # Test 6: Verify no duplicate dashboards were created
    print("\n6. Verifying no duplicate dashboards...")
    
    all_dashboards = insight_service.insight_dashboard_collection.get_documents({
        "initialTitle": "Legacy Dashboard"
    })
    
    if len(all_dashboards) == 1:
        print("✓ Only one dashboard exists (no duplicates created)")
    else:
        print(f"✗ Found {len(all_dashboards)} dashboards (expected 1)")
    
    # Test 7: Test adding more users to the already migrated dashboard
    print("\n7. Testing adding users to already migrated dashboard...")
    
    additional_user_id = "507f1f77bcf86cd799439014"
    additional_user = [MockUser(additional_user_id, "Additional User", "<EMAIL>")]
    
    # Get the updated dashboard object
    migrated_dashboard_obj = insight_service.insight_dashboard_collection.get_one_document({"_id": dashboard_id})
    
    # Add another user
    insight_service._add_users_to_insight_dashboard(
        migrated_dashboard_obj, additional_user, [], user_info
    )
    
    # Verify the additional user was added
    final_dashboard = insight_service.insight_dashboard_collection.get_one_document({"_id": dashboard_id})
    final_user_ids = [str(uid) for uid in final_dashboard["userIdList"]]
    
    if additional_user_id in final_user_ids and len(final_user_ids) == 4:
        print("✓ Additional user successfully added to migrated dashboard")
        print(f"  Final userIdList: {final_user_ids}")
    else:
        print("✗ Failed to add additional user to migrated dashboard")
        print(f"  Final userIdList: {final_user_ids}")
    
    # Cleanup: Remove test dashboard
    print("\n8. Cleaning up test data...")
    mongo_manager = MongoDBmanager("InsightDashboard")
    mongo_manager.remove_by_id(dashboard_id)
    print("✓ Test dashboard removed")
    
    print("\n✅ Legacy userId compatibility tests completed!")


if __name__ == "__main__":
    try:
        test_legacy_userid_compatibility()
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
