#!/usr/bin/env python3
"""
Test program for DataFinderAgent's execute_task() functionality.

This comprehensive test program validates the DataFinderAgent's ability to find metadata
for vendor bills and their payment status, including tables, columns,
business rules, and payment completion logic.

FEATURES TESTED:
- Successful metadata retrieval from multiple data sources
- Proper handling of failed data source queries
- ER diagram retrieval when requested
- Business rules extraction and formatting
- JSON output structure validation
- Display output formatting

TASK INSTRUCTION TESTED:
"Provide metadata for retrieving vendor bills and their payment status.
Identify all tables and columns that store vendor bills, their current status
(e.g., pending, paid), payment dates, amounts, vendor identifiers, and due dates.
Include relevant business rules for interpreting bill status and payment completion."

MOCK DATA SOURCES:
- FinanceDB: Contains vendor_bills and vendor_payments tables
- AccountingSystem: Contains accounts_payable table
- EmptyDB: Used for testing failure scenarios

USAGE:
Run this script directly to execute all tests and see a demonstration:
    python test_data_finder.py

The program will:
1. Run 4 comprehensive unit tests
2. Display test results and summary
3. Show a detailed demonstration of the vendor bill metadata retrieval
"""

import json
import os
import sys
import unittest
from unittest.mock import Mock, MagicMock, patch
from typing import Dict, List, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.tools.data_finder.data_finder_agent import DataFinderAgent
from services.tools.base_tool import ToolOutput
from models.conversation import Conversation
from models.session import Session


class MockConversation:
    """Mock Conversation class for testing"""

    def __init__(self, chat_id="test_chat_123", session_id="test_session_456"):
        self.chat_id = chat_id
        self.active_session = Mock()
        self.active_session.session_id = session_id


class MockSQLDataFinderAgent:
    """Mock SQLDataFinderAgent for testing"""

    def __init__(self, data_source_name: str, model: str):
        self.data_source_name = data_source_name
        self.model = model
        self.data_dictionary_service = Mock()

    def execute_agent(self, input_data: Dict, conversation, session_id: str, chat_log) -> Dict:
        """Mock execute_agent method that returns vendor bill metadata"""
        user_question = input_data.get("user_question", "")

        # Simulate different responses based on data source
        if self.data_source_name == "FinanceDB":
            return {
                "is_success": True,
                "table_metadata": {
                    "vendor_bills": {
                        "table_name": "vendor_bills",
                        "fields": {
                            "bill_id": {"name": "bill_id", "type": "INTEGER", "description": "Unique bill identifier"},
                            "vendor_id": {"name": "vendor_id", "type": "INTEGER", "description": "Vendor identifier"},
                            "bill_amount": {"name": "bill_amount", "type": "DECIMAL", "description": "Bill amount"},
                            "bill_date": {"name": "bill_date", "type": "DATE", "description": "Bill creation date"},
                            "due_date": {"name": "due_date", "type": "DATE", "description": "Payment due date"},
                            "status": {
                                "name": "status",
                                "type": "VARCHAR",
                                "description": "Bill status (pending, paid, overdue)",
                            },
                            "payment_date": {
                                "name": "payment_date",
                                "type": "DATE",
                                "description": "Actual payment date",
                            },
                        },
                    },
                    "vendor_payments": {
                        "table_name": "vendor_payments",
                        "fields": {
                            "payment_id": {
                                "name": "payment_id",
                                "type": "INTEGER",
                                "description": "Payment identifier",
                            },
                            "bill_id": {
                                "name": "bill_id",
                                "type": "INTEGER",
                                "description": "Related bill identifier",
                            },
                            "payment_amount": {
                                "name": "payment_amount",
                                "type": "DECIMAL",
                                "description": "Payment amount",
                            },
                            "payment_date": {"name": "payment_date", "type": "DATE", "description": "Payment date"},
                            "payment_method": {
                                "name": "payment_method",
                                "type": "VARCHAR",
                                "description": "Payment method",
                            },
                        },
                    },
                },
                "other_table_columns": {
                    "vendors": {
                        "table_name": "vendors",
                        "fields": {
                            "vendor_id": {"name": "vendor_id", "type": "INTEGER"},
                            "vendor_name": {"name": "vendor_name", "type": "VARCHAR"},
                            "vendor_status": {"name": "vendor_status", "type": "VARCHAR"},
                        },
                    }
                },
                "business_rules": [
                    "Bills with status 'pending' are awaiting payment",
                    "Bills with status 'paid' have been fully settled",
                    "Bills with status 'overdue' are past their due date without payment",
                    "Payment completion is determined by matching bill_amount with total payment_amount",
                    "Multiple partial payments can be made against a single bill",
                ],
                "reasoning": "Found vendor bill tables with payment status tracking and related payment records",
            }
        elif self.data_source_name == "AccountingSystem":
            return {
                "is_success": True,
                "table_metadata": {
                    "accounts_payable": {
                        "table_name": "accounts_payable",
                        "fields": {
                            "ap_id": {"name": "ap_id", "type": "INTEGER", "description": "Accounts payable ID"},
                            "vendor_code": {"name": "vendor_code", "type": "VARCHAR", "description": "Vendor code"},
                            "invoice_number": {
                                "name": "invoice_number",
                                "type": "VARCHAR",
                                "description": "Invoice number",
                            },
                            "invoice_amount": {
                                "name": "invoice_amount",
                                "type": "DECIMAL",
                                "description": "Invoice amount",
                            },
                            "payment_status": {
                                "name": "payment_status",
                                "type": "VARCHAR",
                                "description": "Payment status",
                            },
                            "payment_due_date": {
                                "name": "payment_due_date",
                                "type": "DATE",
                                "description": "Due date",
                            },
                        },
                    }
                },
                "business_rules": [
                    "Payment status values: 'OPEN', 'PAID', 'PARTIAL', 'CANCELLED'",
                    "OPEN status indicates unpaid invoices",
                    "PAID status indicates fully paid invoices",
                ],
                "reasoning": "Found accounts payable data with payment status information",
            }
        else:
            return {
                "is_success": False,
                "error_message": f"No vendor bill data found in {self.data_source_name}",
                "reasoning": f"Data source {self.data_source_name} does not contain vendor bill information",
            }


class TestDataFinderAgent(unittest.TestCase):
    """Test cases for DataFinderAgent"""

    def setUp(self):
        """Set up test fixtures"""
        self.task_instruction = (
            "Provide metadata for retrieving vendor bills and their payment status. "
            "Identify all tables and columns that store vendor bills, their current status "
            "(e.g., pending, paid), payment dates, amounts, vendor identifiers, and due dates. "
            "Include relevant business rules for interpreting bill status and payment completion."
        )

    @patch("services.tools.data_finder.data_finder_agent.DataDictionaryService")
    @patch("services.tools.data_finder.data_finder_agent.SQLDataFinderAgent", MockSQLDataFinderAgent)
    @patch("services.tools.data_finder.data_finder_agent.get_debug_logger")
    def test_execute_task_success(self, mock_logger, mock_data_dict_service):
        """Test successful execution of data finding task"""

        # Mock DataDictionaryService
        mock_service_instance = Mock()
        mock_service_instance.get_data_sources_list.return_value = ["FinanceDB", "AccountingSystem"]
        mock_data_dict_service.return_value = mock_service_instance

        # Mock logger
        mock_logger.return_value = Mock()

        # Create DataFinderAgent instance
        agent = DataFinderAgent()

        # Create mock conversation
        conversation = MockConversation()

        # Execute the task
        result = agent.execute_task(
            conversation=conversation,
            data_source_name="",  # Not used in current implementation
            data_file_name_list=[],  # Not used in current implementation
            task_instruction=self.task_instruction,
            base_log_path="test_logs/",
            additional_data={},
        )

        # Verify result type
        self.assertIsInstance(result, ToolOutput)

        # Verify display output contains expected information
        self.assertIn("Data Source: FinanceDB", result.display_output)
        self.assertIn("Data Source: AccountingSystem", result.display_output)
        self.assertIn("vendor_bills", result.display_output)
        self.assertIn("accounts_payable", result.display_output)
        self.assertIn("Business Rules:", result.display_output)

        # Verify result text list structure
        self.assertEqual(len(result.result_text_list), 2)

        # Check business rules result
        business_rules_result = result.result_text_list[0]
        self.assertIn("Business Rules:", business_rules_result[0])
        self.assertFalse(business_rules_result[1])  # Not archivable

        # Check metadata result
        metadata_result = result.result_text_list[1]
        self.assertIn("Metadata of all data sources:", metadata_result[0])
        self.assertTrue(metadata_result[1])  # Archivable

        # Parse and verify the metadata JSON
        metadata_json_start = metadata_result[0].find("````\n") + 5
        metadata_json_end = metadata_result[0].rfind("\n````")
        metadata_json = metadata_result[0][metadata_json_start:metadata_json_end]
        metadata_list = json.loads(metadata_json)

        # Verify metadata structure
        self.assertEqual(len(metadata_list), 2)

        # Check FinanceDB metadata
        finance_metadata = next(m for m in metadata_list if m["data_source_name"] == "FinanceDB")
        self.assertIn("selected_table_column_metadata", finance_metadata)
        self.assertIn("vendor_bills", finance_metadata["selected_table_column_metadata"])
        self.assertIn("vendor_payments", finance_metadata["selected_table_column_metadata"])

        # Check AccountingSystem metadata
        accounting_metadata = next(m for m in metadata_list if m["data_source_name"] == "AccountingSystem")
        self.assertIn("selected_table_column_metadata", accounting_metadata)
        self.assertIn("accounts_payable", accounting_metadata["selected_table_column_metadata"])

    @patch("services.tools.data_finder.data_finder_agent.DataDictionaryService")
    @patch("services.tools.data_finder.data_finder_agent.SQLDataFinderAgent", MockSQLDataFinderAgent)
    @patch("services.tools.data_finder.data_finder_agent.get_debug_logger")
    def test_execute_task_with_er_diagrams(self, mock_logger, mock_data_dict_service):
        """Test execution with ER diagram request"""

        # Mock DataDictionaryService
        mock_service_instance = Mock()
        mock_service_instance.get_data_sources_list.return_value = ["FinanceDB"]
        mock_data_dict_service.return_value = mock_service_instance

        # Mock logger
        mock_logger.return_value = Mock()

        # Create DataFinderAgent instance
        agent = DataFinderAgent()

        # Mock ER diagram retrieval
        for sub_agent in agent.metadata_finder_sub_agents.values():
            sub_agent.data_dictionary_service.get_er_diagram_for_data_source.return_value = {
                "tables": ["vendor_bills", "vendor_payments"],
                "relationships": [{"from": "vendor_bills", "to": "vendor_payments", "type": "one-to-many"}],
            }

        # Create mock conversation
        conversation = MockConversation()

        # Execute with ER diagram request
        result = agent.execute_task(
            conversation=conversation,
            data_source_name="",
            data_file_name_list=[],
            task_instruction=self.task_instruction,
            base_log_path="test_logs/",
            additional_data={"is_initial_metadata_pending": True},
        )

        # Verify ER diagram is included in results
        self.assertEqual(len(result.result_text_list), 3)
        er_diagram_result = result.result_text_list[2]
        self.assertIn("ER Diagrams of all data sources:", er_diagram_result[0])
        self.assertFalse(er_diagram_result[1])  # Not archivable

    @patch("services.tools.data_finder.data_finder_agent.DataDictionaryService")
    @patch("services.tools.data_finder.data_finder_agent.get_debug_logger")
    def test_execute_task_with_failures(self, mock_logger, mock_data_dict_service):
        """Test execution when some data sources fail"""

        # Mock DataDictionaryService
        mock_service_instance = Mock()
        mock_service_instance.get_data_sources_list.return_value = ["FinanceDB", "EmptyDB"]
        mock_data_dict_service.return_value = mock_service_instance

        # Mock logger
        mock_logger.return_value = Mock()

        # Create custom mock that returns failure for EmptyDB
        def create_mock_agent(data_source_name: str, model: str):
            mock_agent = Mock()
            mock_agent.data_dictionary_service = Mock()

            if data_source_name == "EmptyDB":
                mock_agent.execute_agent.return_value = {
                    "is_success": False,
                    "error_message": "No vendor bill data found",
                    "reasoning": "Data source does not contain vendor bill information",
                }
            else:
                # Use the successful response from MockSQLDataFinderAgent
                real_mock = MockSQLDataFinderAgent(data_source_name, model)
                mock_agent.execute_agent.side_effect = real_mock.execute_agent

            return mock_agent

        with patch("services.tools.data_finder.data_finder_agent.SQLDataFinderAgent", create_mock_agent):
            # Create DataFinderAgent instance
            agent = DataFinderAgent()

            # Create mock conversation
            conversation = MockConversation()

            # Execute the task
            result = agent.execute_task(
                conversation=conversation,
                data_source_name="",
                data_file_name_list=[],
                task_instruction=self.task_instruction,
                base_log_path="test_logs/",
                additional_data={},
            )

            # Verify result type
            self.assertIsInstance(result, ToolOutput)

            # Parse metadata to check for error handling
            metadata_result = result.result_text_list[1]
            metadata_json_start = metadata_result[0].find("````\n") + 5
            metadata_json_end = metadata_result[0].rfind("\n````")
            metadata_json = metadata_result[0][metadata_json_start:metadata_json_end]
            metadata_list = json.loads(metadata_json)

            # Should have 2 entries (one success, one failure)
            self.assertEqual(len(metadata_list), 2)

            # Check that failure is properly recorded
            empty_db_metadata = next(m for m in metadata_list if m["data_source_name"] == "EmptyDB")
            self.assertIn("error", empty_db_metadata)
            self.assertNotIn("selected_table_column_metadata", empty_db_metadata)

    def test_business_rules_extraction(self):
        """Test that business rules are properly extracted and formatted"""

        with patch("services.tools.data_finder.data_finder_agent.DataDictionaryService") as mock_data_dict_service:
            with patch("services.tools.data_finder.data_finder_agent.SQLDataFinderAgent", MockSQLDataFinderAgent):
                with patch("services.tools.data_finder.data_finder_agent.get_debug_logger"):

                    # Mock DataDictionaryService
                    mock_service_instance = Mock()
                    mock_service_instance.get_data_sources_list.return_value = ["FinanceDB"]
                    mock_data_dict_service.return_value = mock_service_instance

                    # Create DataFinderAgent instance
                    agent = DataFinderAgent()

                    # Create mock conversation
                    conversation = MockConversation()

                    # Execute the task
                    result = agent.execute_task(
                        conversation=conversation,
                        data_source_name="",
                        data_file_name_list=[],
                        task_instruction=self.task_instruction,
                        base_log_path="test_logs/",
                        additional_data={},
                    )

                    # Check business rules in display output
                    self.assertIn("Bills with status 'pending' are awaiting payment", result.display_output)
                    self.assertIn("Payment completion is determined by matching bill_amount", result.display_output)

                    # Check business rules in result text
                    business_rules_result = result.result_text_list[0]
                    business_rules_json_start = business_rules_result[0].find("````\n") + 5
                    business_rules_json_end = business_rules_result[0].rfind("\n````")
                    business_rules_json = business_rules_result[0][business_rules_json_start:business_rules_json_end]
                    business_rules_list = json.loads(business_rules_json)

                    # Verify business rules content
                    self.assertIsInstance(business_rules_list, list)
                    self.assertGreater(len(business_rules_list), 0)
                    self.assertIn("Bills with status 'pending' are awaiting payment", business_rules_list)


def demonstrate_vendor_bill_task():
    """Demonstrate the DataFinderAgent with the specific vendor bill task"""
    print("=" * 80)
    print("DEMONSTRATION: VENDOR BILLS METADATA RETRIEVAL")
    print("=" * 80)
    print()

    task_instruction = (
        "Provide metadata for retrieving vendor bills and their payment status. "
        "Identify all tables and columns that store vendor bills, their current status "
        "(e.g., pending, paid), payment dates, amounts, vendor identifiers, and due dates. "
        "Include relevant business rules for interpreting bill status and payment completion."
    )

    print("Task Instruction:")
    print(f'"{task_instruction}"')
    print()

    with patch("services.tools.data_finder.data_finder_agent.DataDictionaryService") as mock_data_dict_service:
        with patch("services.tools.data_finder.data_finder_agent.SQLDataFinderAgent", MockSQLDataFinderAgent):
            with patch("services.tools.data_finder.data_finder_agent.get_debug_logger"):

                # Mock DataDictionaryService
                mock_service_instance = Mock()
                mock_service_instance.get_data_sources_list.return_value = ["FinanceDB", "AccountingSystem"]
                mock_data_dict_service.return_value = mock_service_instance

                # Create DataFinderAgent instance
                agent = DataFinderAgent()

                # Create mock conversation
                conversation = MockConversation()

                # Execute the task
                result = agent.execute_task(
                    conversation=conversation,
                    data_source_name="",
                    data_file_name_list=[],
                    task_instruction=task_instruction,
                    base_log_path="demo_logs/",
                    additional_data={},
                )

                print("RESULT DISPLAY OUTPUT:")
                print("-" * 40)
                print(result.display_output)
                print()

                print("DETAILED METADATA (JSON):")
                print("-" * 40)
                metadata_result = result.result_text_list[1]
                metadata_json_start = metadata_result[0].find("````\n") + 5
                metadata_json_end = metadata_result[0].rfind("\n````")
                metadata_json = metadata_result[0][metadata_json_start:metadata_json_end]
                metadata_dict = json.loads(metadata_json)
                print(json.dumps(metadata_dict, indent=2))
                print()

                print("BUSINESS RULES (JSON):")
                print("-" * 40)
                business_rules_result = result.result_text_list[0]
                business_rules_json_start = business_rules_result[0].find("````\n") + 5
                business_rules_json_end = business_rules_result[0].rfind("\n````")
                business_rules_json = business_rules_result[0][business_rules_json_start:business_rules_json_end]
                business_rules_list = json.loads(business_rules_json)
                print(json.dumps(business_rules_list, indent=2))
                print()


def run_test_with_output():
    """Run the test and display detailed output"""
    print("=" * 80)
    print("TESTING DATAFINDER AGENT - VENDOR BILLS METADATA RETRIEVAL")
    print("=" * 80)
    print()

    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDataFinderAgent)

    # Run tests with detailed output
    runner = unittest.TextTestRunner(verbosity=2, stream=sys.stdout)
    result = runner.run(suite)

    print()
    print("=" * 80)
    print("TEST EXECUTION SUMMARY")
    print("=" * 80)
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")

    if result.failures:
        print("\nFAILURES:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")

    if result.errors:
        print("\nERRORS:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")

    print()
    return result.wasSuccessful()


if __name__ == "__main__":
    # Run the unit tests
    success = run_test_with_output()

    if success:
        print("\n" + "=" * 80)
        print("ALL TESTS PASSED! Running demonstration...")
        print("=" * 80)
        print()

        # Run the demonstration
        demonstrate_vendor_bill_task()

    sys.exit(0 if success else 1)
