"""
* Copyright (c) 2023 ZOOMi Technologies Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* Conversation model class

* @class Conversation
* @description Conversation encapsulated model class to get and set data use getter and setters
* <AUTHOR>

"""

import copy
from datetime import datetime, timezone
import traceback
import uuid
from bson import ObjectId
from pymongo import InsertOne
from databases.mongo_manager import MongoDBmanager
from utils.misc_utils import get_data_block_visibility_list
from models.db_models.upload_session import UploadInfo
from services.execution_service import ExecutionService
from models.session import Session
from copy import deepcopy
import tiktoken
import os
from dotenv import load_dotenv
from typing import List
import pymongo
from utils.logger import get_debug_logger

from utils.constant import (
    LLM_MAX_LIMIT_INVOKES_PER_SESSION_CHAT_AGENT,
    LLM_MAX_LIMIT_INVOKES_PER_SESSION_INSIGHT_AGENT,
    SESSION_MAX_DATA_REVIEW_FAIL_COUNT,
    AnalysisStep,
    ChatAppUserMessages,
    ConversationConclusion,
    ConversationLLMStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    ExecutionMode,
    FrontendBlockDataMessages,
    FrontendBlockStatus,
    FrontendBlockType,
    FrontendTabContentType,
    LLMAgentType,
    MessageContentType,
    SectionStatus,
    ConversationStatus,
    SectionType,
    DataReviewStage,
    AgentState,
    SessionStatus,
    StreamChunkOperation,
)


class Conversation:

    def __init__(
        self,
        conversation_id,
        session_id,
        question,
        user_id,
        is_continue_prev=False,  # Kelum - 22/05/2024 - Not used now with addition of FollowupHandlerAgent
        agent_type: LLMAgentType = LLMAgentType.AGENT_TYPE_CHAT,
        file_upload_sessions: List[UploadInfo] = [],
    ):

        # Private attributes with a convention of using double underscores
        self.__active_session: Session
        self.__history_messages = []
        self.__initial_history_message_before_current_session = []
        self.__bulk_write_insert_operations = []
        self.__conversation_id = conversation_id
        self.__session_id = session_id
        self.__llm_system_prompt = ""
        self.__user_id = user_id
        self.__is_continue_prev = False  # is_continue_prev  # Kelum - 22/05/2024 - Not initially set now with addition of FollowupHandlerAgent
        self.data_source_types = []  # List of data source types from which instructions loaded so far
        # self.initial_instruction_source_types = (
        #    []
        # )  # IF there are some special instruction sources (Eg: for goal tracking)
        self.sources = []  # List of data sources required
        self.tools = []  # List of tools required
        self.data_sections_list = []  # List of tables selected in data section selection
        self.python_code = ""  # python code given by llm
        self.agent_type = agent_type
        self.retrieval_logic = ""  # Data retrieval SQL list or logic explanation given by llm
        self.packages_list = []  # List of required Python packages given  by llm
        self.input_variables_list = []  # List of input variables for Test Run given by llm
        self.llm_code_with_logic = ""  # python code with logic explanation given by llm
        self.agent_task = ""  # Agent task given by llm
        # self.__session_history = self.load_session_history(conversation_id=conversation_id)
        self.initial_question = question
        self.__session_llm_invoke_count = 0  # No of times that LLM is invoked for the current active session (This is not initialized with history value)

        self.is_file_uploaded = False  # flag to check if file is uploaded
        self.is_conversation_title_required = False
        self.uploaded_files = []  # list of uploaded files
        self.latest_uploaded_files = []  # list of latest uploaded files
        self.user_initial_observations = {}
        self.user_inputs = []  # list of user questions and inputs

        # if this conversation already has sessions get the current token counts
        self.conversation_collection = MongoDBmanager("Conversations")
        self.data_retrieval_collection = MongoDBmanager("ConversationDataRetrieval")
        self.messages_collection = MongoDBmanager("Messages")
        self.data_blocks_collection = MongoDBmanager("DataBlocks")
        self.sessions_collection = MongoDBmanager("Sessions")

        conversation_ = self.conversation_collection.get_one_document(
            {
                "_id": ObjectId(self.__conversation_id),
            }
        )
        # Flag to keep last execution's result is success or error
        self.is_executing_error = False

        # Instructions on data retrieval from all necessary sources
        self.source_data_retrieval_instructions = ""
        # True if code review is not needed
        self.is_skip_code_review = False

        # variable to store the number of tokens used
        try:
            self.completion_tokens = conversation_["completion_tokens"]
            self.prompt_tokens = conversation_["prompt_tokens"]
            self.total_tokens = conversation_["total_tokens"]
        except:
            self.completion_tokens = 0
            self.prompt_tokens = 0
            self.total_tokens = 0

        # get the openai model name
        self.llm_name = os.getenv("MODEL")

        self.__agent_id = None  # associated agent id for the conversation
        # get count tokens env variable
        self.token_counting_on = os.getenv("COUNT_TOKENS")
        if self.token_counting_on == "True":
            # use tiktoken with openAI stream
            if self.llm_name.startswith("gpt-4o"):
                self.encoding = tiktoken.encoding_for_model("gpt-4")
            else:
                self.encoding = tiktoken.encoding_for_model(self.llm_name)
        # Flag to avoid displaying the retrying prompt when loading from massage history.
        self.retry = False
        # Flag to identify wether we need to stop sending to frontend
        self.stop_stream = False

        # Execution environment for running Python codes (code verification in case of Agent and getting answer in case of chat)
        # Global variables for execution are maintained in the conversation
        self.execution_env = ExecutionService(ExecutionMode.TEST.value)
        # List of global variables available at any moment
        self.global_var_list = []
        # Load the memory for execution environment from previously dumped pickle if exists
        self.global_var_list = self.execution_env.load_up_env(conversation_id)
        # True if the last message is a user feedback (@feedback)
        self.is_user_feedback = False
        # Files generated in REACT cycles in case of insight generation
        self.insight_image_file_paths = []  # .png
        self.insight_data_file_paths = []  # .csv
        self.initial_insight_image_file_names_before_current_session = []
        self.initial_insight_data_file_names_before_current_session = []
        # In case this conversation was initiated within another one, then this is the parent conversation
        self.parent_conversation_id = conversation_id
        # No of failures so far on data locating
        self.data_locate_fail_count = 0
        # Each time the textual data reviewer rejects answer in this conversation, this count is incremented
        self.data_retrieve_fail_count = 0
        # Each time the  visual reviewer rejects answer in this conversation, this count is incremented
        self.visual_fail_count = 0
        # Last retrieved data without data issues detected by data reviewer, both markdown text and csv file name included in the format {"markdown": markdown_text, "csv": csv_file_name}
        self.last_retrieved_reviewer_accepted_data = None
        # All data availability checks done for this conversation (List of query and results)
        self.data_availability_check_info_list = []
        # Status of data retrieval
        self.data_retrieval_status = ConversationConclusion.CANT_FIND_DATA
        # The raw answer as text typically set by python execution output for the current question
        self.current_question_raw_answer = ""
        self.data_reviewer_feedback_history = []
        self.data_retrieval_attempts = 0
        # Conversation Id with previous unstructured data label mapping (to re-use it in the next action of react cycle)
        self.previous_unstructured_data_label_mapping_conversation_id = None
        self.fresh_retry_attempt = 0
        self.is_revision_required = False
        # The data review stage that is currently active
        self.current_data_review_stage = DataReviewStage.TEXTUAL_REVIEW_STAGE
        # Data query info for document and sql data sources
        self.document_query_info = ""
        self.sql_table_info = []
        # Current data retrieval SQL
        self.current_data_retrieve_sql = ""
        # Current state of conversation in the agent flow
        self.agent_state: AgentState = AgentState.DATA_FINDING
        # Map containing the LLM history for each state - key value dictionary from AgentState to dictionary containing invoked time (count) as key and list of messages as value
        self.agent_state_follow_up_history: dict[AgentState, dict[int, list[dict]]] = (
            {}
        )  # Loaded from DB for follow up questions
        self.agent_state_current_session_history: dict[AgentState, dict[int, list[dict]]] = (
            {}
        )  # Kept in memory for current session
        # Set of reference to data used by python code within this conversation
        self.data_reference_set = set()
        # Set of attachment file names added to the conversation
        self.attachment_file_names = set()

        self.__file_upload_sessions = file_upload_sessions  # list of file upload sessions

        self.__log_file_full_path = None

    def get_message_count(self):
        return len(self.__history_messages)

    @property
    def chat_id(self):
        return self.__conversation_id

    @property
    def user_id(self):
        return self.__user_id

    @property
    def active_session(self):
        """
        Description: Getter method for accessing the active session instance.

        Returns:
            - Session: The currently active session instance.

        Note:
            This property provides read-only access to the active session.
        """
        return self.__active_session

    @active_session.setter
    def active_session(self, session_instance):
        """
        Description: Setter method for updating the active session instance.

        Parameters:
            - session_instance: Session, the new session instance to be set as active.

        Note:
            This setter method allows updating the active session instance.
        """
        self.__active_session = session_instance

    @property
    def file_upload_sessions(self):
        return self.__file_upload_sessions

    @file_upload_sessions.setter
    def file_upload_sessions(self, file_upload_sessions):
        if file_upload_sessions:
            self.__file_upload_sessions = file_upload_sessions
        else:
            self.__file_upload_sessions = []

    @property
    def log_file_full_path(self):
        return self.__log_file_full_path

    @log_file_full_path.setter
    def log_file_full_path(self, log_file_full_path):
        self.__log_file_full_path = log_file_full_path

    @property
    def message_history(self):
        """
        Description: Getter method for accessing the session history array.

        Returns:
            - list: The array containing the session history entries.

        Note:
            This property provides read-only access to the session history.
        """
        return self.__history_messages

    @message_history.setter
    def message_history(self, messages_array):
        """
        Description: Setter method for updating the session history array.

        Parameters:
            - messages_array: list, the new array of session history entries.

        Note:
            This setter method allows updating the entire session history array.
            Each message should have 'role', 'content', and 'is_llm_agent_input' fields.
        """
        # Ensure all messages have the is_llm_agent_input field
        for message in messages_array:
            if "is_llm_agent_input" not in message:
                message["is_llm_agent_input"] = True  # Default to True for backward compatibility

        self.__history_messages = messages_array
        self.__initial_history_message_before_current_session = copy.deepcopy(messages_array)

    @property
    def llm_system_prompt(self):
        return self.__llm_system_prompt

    @llm_system_prompt.setter
    def llm_system_prompt(self, updated_prompt):
        self.__llm_system_prompt = updated_prompt

    @property
    def is_continue_previous(self):
        return self.__is_continue_prev

    @is_continue_previous.setter
    def is_continue_previous(self, flag):
        self.__is_continue_prev = flag

    @property
    def stop_stream(self):
        return self.__stop_stream

    @stop_stream.setter
    def stop_stream(self, flag):
        if flag == True:
            self.active_session.session_status = SessionStatus.COMPLETED
            self.persist_and_stream_handler(
                block_id=f"{self.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data="\n\n",
                is_last_block=True,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                block_status=FrontendBlockStatus.COMPLETED,
            )
        self.__stop_stream = flag

    def stop_stream_when_user_stopped(self):

        # save conversation
        # need to get chat log for different agent types to pass to save method
        # conversation.log_file_full_path is not none take it othrwise atke server log
        if self.log_file_full_path:
            chat_log = get_debug_logger(self.__conversation_id, self.log_file_full_path)
        else:
            chat_log = get_debug_logger(self.__conversation_id, f"./logs/server.log")
        chat_log.info(
            f"Conversation.stop_stream_when_user_stopped | Conversation stopped by user. Saving conversation to db. Conversation ID: {self.__conversation_id}"
        )
        self.save(chat_log)

        # set conversation status to stopped in db
        self.conversation_collection.update_one(
            {"_id": ObjectId(self.__conversation_id)},
            {"$set": {"status": ConversationStatus.STOPPED.value}},
        )

        # set session status to stopped in db
        self.sessions_collection.update_one(
            {"_id": ObjectId(self.active_session.session_id)},
            {"$set": {"status": SessionStatus.STOPPED.value}},
        )

        # NOTE: need to set stop_stream True before setting user_stopped_stream True.
        # because in the persist_and_stream_handler method and Conversation.save method, we check if the user_stopped_stream is True.
        # if we set user_stopped_stream True first, then the stop signal will not be sent to the frontend.
        # step 1: save conversation and session status to db
        # step 2: set the stop stream flag to true. (this will send stop signal to the frontend)
        # step 3: set the user stopped stream flag to true.
        self.stop_stream = True
        self.active_session.user_stopped_stream = True

    def reset_to_retry(self):
        self.fresh_retry_attempt += 1

        # reset required class variables
        self.llm_system_prompt = ""
        self.data_source_types = []
        self.sources = []
        self.tools = []
        self.data_sections_list = []
        self.python_code = ""
        self.retrieval_logic = ""
        self.packages_list = []
        self.input_variables_list = []
        self.llm_code_with_logic = ""
        self.agent_task = ""

        self.__session_llm_invoke_count = 0

        self.user_inputs = []

        self.is_executing_error = False
        self.source_data_retrieval_instructions = ""
        self.is_skip_code_review = False

        self.retry = False
        self.__stop_stream = False

        self.insight_image_file_paths = []  # .png
        self.insight_data_file_paths = []  # .csv

        self.last_retrieved_reviewer_accepted_data = None
        self.data_availability_check_info_list = []

        self.data_retrieval_status = ConversationConclusion.CANT_FIND_DATA
        self.data_reviewer_feedback_history = []
        self.data_locate_fail_count = 0
        self.data_retrieval_attempts = 0
        self.current_data_review_stage = DataReviewStage.TEXTUAL_REVIEW_STAGE

        # reset session variables
        self.active_session.llm_status = ConversationLLMStatus.LLM_DATA_SECTION_LOCATING  # The initial stage
        self.active_session.session_answer_list = []
        self.active_session.data_sources_info_list = []

        # reuse initial history array to support follow up questions
        self.message_history = copy.deepcopy(self.__initial_history_message_before_current_session)
        self.insight_image_file_paths = copy.deepcopy(self.initial_insight_image_file_names_before_current_session)
        self.insight_data_file_paths = copy.deepcopy(self.initial_insight_data_file_names_before_current_session)

        self.document_query_info = ""
        self.sql_table_info = []
        self.current_data_retrieve_sql = ""

    """
    Description:
        Function to get notified when LLM is invoked in the conversation
        With this, the token usage per conversation can be monitored and also un necessary LLM invoking can be prevented
    Parameters:
        - llm_prompt: input prompt for the llm
        - llm_response: Response object returned from the invoke of LLM
        - chat_log: log file object 
        - method: llm response providing method. "stream" and "non-stream" modes 
    """

    def on_llm_invoked(self, llm_prompt, llm_response, chat_log, method="non-stream"):
        self.__session_llm_invoke_count += 1

        if self.token_counting_on == "True":
            try:
                if method == "stream":
                    self.count_tokens(llm_prompt, method="tiktoken", token_type=1)
                    self.count_tokens(llm_response, method="tiktoken", token_type=-1)
                else:
                    self.count_tokens(llm_prompt)
                    self.count_tokens(llm_response)
                self.log_token_counts(chat_log)
            except Exception as e:
                chat_log.error(f"Error in token counting: {e}")

    def on_data_reviewer_feedback(
        self,
        review_feedback_dict: dict,
        answer_data,
        chat_log,
        is_final_answer_processing_pending,
    ):
        """
        Description:
            This function is called when the data reviewer feedback is received and it will update the
            conversation status and notify the user with a message.
        Parameters:
            - review_feedback_dict: dictionary containing the feedback of the data reviewer
            - answer_data: data that was sent to the data reviewer
            - chat_log: log file object
            - is_final_answer_processing_pending: boolean indicating whether the final answer processing stage is required
        """
        is_data_reviewer_verified = False
        if review_feedback_dict["is_revision_required"]:
            review_type = "data"
            if review_feedback_dict["is_data_issues"]:
                self.data_retrieve_fail_count += 1
                self.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
                chat_log.debug(
                    f"conversation | on_data_reviewer_feedback | Failed to retrieve data for {self.data_retrieve_fail_count} attempt"
                )
            else:
                review_type = "visual"
                self.visual_fail_count += 1
                chat_log.debug(
                    f"conversation | on_data_reviewer_feedback | Visualization failed for {self.visual_fail_count} attempt. Received answer without data issues in {self.data_retrieve_fail_count} attempt"
                )
                self.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
                self.last_retrieved_reviewer_accepted_data = {
                    "markdown": answer_data,
                    "csv": self.insight_data_file_paths,
                }

            # Notify the user with message
            notify_text = (
                f"\n\n###### Answer rejected by {review_type} reviewer:\n\n"
                + str(review_feedback_dict["feedback"])
                + "\n\n"
            )
            is_data_reviewer_verified = False
        else:
            notify_text = "\n\n###### Answer Verified by the Data Reviewer.\n\n"
            is_data_reviewer_verified = True

        for index, answer in enumerate(self.active_session.session_answer_list):
            answer["is_data_reviewer_verified"] = is_data_reviewer_verified

        if self.agent_type != LLMAgentType.AGENT_TYPE_PRE_DATA:
            # self.active_session.add_section() #remove tis line to show notify text inside collapsed action block
            self.active_session.add_to_queue(notify_text, "assistant", ConversationStatus.IN_PROGRESS)
            self.add_to_history({"role": "assistant", "content": notify_text}, False, True)

    """
    Description:
        Checks whether next LLM invoke can be done considering usage
        If its already reached the max. LLM call count, then this request is not proceeded
    """

    def is_llm_limit_reached(self):
        # Limit depends on the agent type
        llm_limit = LLM_MAX_LIMIT_INVOKES_PER_SESSION_CHAT_AGENT
        if self.agent_type == LLMAgentType.AGENT_TYPE_INSIGHT:
            llm_limit = LLM_MAX_LIMIT_INVOKES_PER_SESSION_INSIGHT_AGENT
        return (
            self.__session_llm_invoke_count >= llm_limit
            or self.data_retrieve_fail_count > SESSION_MAX_DATA_REVIEW_FAIL_COUNT
        )

    @property
    def session_llm_invoke_count(self):
        return self.__session_llm_invoke_count

    """ 
    Use to save session history that mean save all messages to db
    @data               : data that save to db
    @is_llm_agent_input : use to identify is this data use for llm_agent_input or as front_end retrieval data
    @is_user_input      : if is user input use this data for both purpose
    """

    def add_to_history(
        self,
        data,
        is_llm_agent_input=True,
        is_front_end_data=False,
        isConclusion=False,
        is_required_for_follow_ups=False,
        llm_section_type=None,
    ):
        """
        Description: Adds data to the session history, and creates a message document for database insertion.

        Parameters:
            - data: dict, the data to be added to the session history.
            - is_llm_agent_input: bool, indicates whether the data is from the LLM agent (True) or user input (False).
            - is_front_end_data: bool, indicates whether the data is for front-end display.
            - isConclusion: bool, indicates whether this message is a conclusion.
            - is_required_for_follow_ups: bool, indicates whether this message is required for follow-up questions.
            - llm_section_type: str, indicates the section type for LLM agent input. Used in case we don't want to add a section actually to frontend
        """
        if is_front_end_data:
            if self.active_session.section_status == SectionStatus.IN_PROGRESS.value:
                _section_status = SectionStatus.COMPLETED.value
            else:
                _section_status = self.active_session.section_status
        else:
            _section_status = None

        # use for determining the initial question of the conversation and save initial question with isInitialQuestion : true flag
        is_initial_question = False
        if data and data.get("role", "") == "user" and data.get("content", "") == self.initial_question:
            is_initial_question = True
        # Give priority to llm_section_type if present
        section_type = (
            llm_section_type
            if llm_section_type
            else (self.active_session.section_type if is_front_end_data and self.active_session.section_type else None)
        )

        message = {
            "conversationId": ObjectId(self.__conversation_id),
            "userId": ObjectId(self.__user_id),
            "sessionId": str(self.__session_id),
            "senderType": data["role"] if "role" in data else "assistant",
            "senderId": ObjectId(self.__user_id) if data["role"] == "user" else None,
            "content": data["content"],
            "createdAt": datetime.now(timezone.utc),
            "isChild": False,
            # "timestamp": int(datetime.utcnow().timestamp()),
            "attachments": [],
            "contentType": (data["contentType"] if "contentType" in data else MessageContentType.TEXT.value),
            "is_llm_agent_input": is_llm_agent_input,
            "is_front_end_data": is_front_end_data,
            "sectionId": self.active_session.section_id if is_front_end_data else None,
            "sectionType": section_type,
            "sectionStatus": _section_status,
            "isConclusion": isConclusion,
            "jsonReport": data.get("jsonReport"),
            "isInitialQuestion": is_initial_question,
            "is_required_for_follow_ups": is_required_for_follow_ups,
        }

        self.__bulk_write_insert_operations.append(InsertOne(deepcopy(message)))

        if is_llm_agent_input:
            # Add is_llm_agent_input flag to the history message
            history_message = data.copy()
            history_message["is_llm_agent_input"] = is_llm_agent_input
            self.__history_messages.append(history_message)

    def remove_last_history_elem(self, is_llm_agent_input=True):
        self.__bulk_write_insert_operations.pop()
        if is_llm_agent_input:
            self.__history_messages.pop()

    def mark_session_latest_action_as_is_required_for_follow_ups(self):
        for item in reversed(self.__bulk_write_insert_operations):  # Iterate from last to first
            if (
                self.active_session.session_llm_code_with_logic
                and self.active_session.session_llm_code_with_logic in item._doc.get("content")
            ):
                item._doc["is_required_for_follow_ups"] = True
                break

    def save(self, logger, isIntermediateSave=False):
        # The 'isIntermediateSave' flag was added to prevent updating the Insight conversation object's status
        # after every hypothesis process. It is set isIntermediateSave flag to True. Note: active session object cant use here since
        # it was shared by other agents
        """
        Description: This function used for four tasks:
                        1. Synchronizes the session history with the database by performing bulk write operations.
                        2. Put the conversation token count to the DB
                        3. Dump the execution env memory to files
                        4. Save agent state history to the database

        Raises:
            - Exception: If there is an unexpected error during the bulk write operation.
        """
        if self.__active_session.user_stopped_stream:
            logger.info(
                f"Save Chat History | Conversation.save | {self.__conversation_id} | Conversation is stopped by user. Skipping save since it will be saved by stop_stream_when_user_stopped method"
            )
            return

        # Dump global variables
        variable_names = self.execution_env.dump_env(self.__conversation_id)
        logger.debug(f"Saving the conversation {self.__conversation_id}, dumping variables: {variable_names}")
        try:
            logger.info(
                f"Save Chat History | Conversation.save | {self.__conversation_id} |Synchronizing the session history with the database. Length: {len(self.__bulk_write_insert_operations)}"
            )
            message_collection = MongoDBmanager("Messages")
            message_collection.bulk_write(self.__bulk_write_insert_operations)
            # Clear the bulk messages queue
            self.__bulk_write_insert_operations.clear()

            # Purpose: Search the conversation list by its questions in addition to chat title
            # Every time before conversation save, Create searchKey and save/update that field to conversation collection
            conversation_last = self.conversation_collection.get_one_document(
                {"_id": ObjectId(self.__conversation_id)}
            )
            messages_pipeline = [
                {
                    "$match": {
                        "conversationId": ObjectId(self.__conversation_id),
                        "senderType": "user",
                        "is_front_end_data": True,
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "questions": {
                            "$push": {
                                "$cond": {
                                    "if": {"$isArray": "$content"},
                                    "then": "Attachment",
                                    "else": "$content",
                                }
                            }
                        },
                    }
                },
            ]
            # searchKey Generation
            messages_result = list(message_collection.aggregate(messages_pipeline))
            questions_list = messages_result[0]["questions"] if messages_result else []
            conversation_name = conversation_last.get("name", "") if conversation_last else ""
            search_string = conversation_name + " " + " ".join(questions_list)

            # Converting the keys in agent history to strings to match the DB format
            agent_history_keys_to_string = {
                state.value: {str(key): value for key, value in state_history.items()}
                for state, state_history in self.agent_state_follow_up_history.items()
            }

            # if file exists then mark them as {isNew: False} since they should be in conversation history now
            # update conversation status to COMPLETED and add search string
            update_conversation = {
                # "status": ConversationStatus.COMPLETED.value,
                "searchString": search_string,
                "dataSections": self.data_sections_list,
                "isViewed": False,  # make conversation unread
                "updatedAt": datetime.now(timezone.utc),  # update conversation update time for every save call
                "agentStateHistory": agent_history_keys_to_string,  # Update the agent state history,
                "prompt_tokens": self.prompt_tokens,
                "completion_tokens": self.completion_tokens,
                "total_tokens": self.total_tokens,
                "dataReferenceList": list(self.data_reference_set),
                "agent_type": self.agent_type.value,
                "attachmentFileNames": list(self.attachment_file_names),
            }
            if isIntermediateSave:
                update_conversation["status"] = ConversationStatus.IN_PROGRESS.value
            else:
                update_conversation["status"] = ConversationStatus.COMPLETED.value

            if self.llm_code_with_logic:
                update_conversation["llmCodeWithLogic"] = self.llm_code_with_logic

            self.conversation_collection.find_one_update(
                {
                    "_id": ObjectId(self.__conversation_id),
                },
                update_conversation,
                [],
            )
            # if file exists then mark them as {isNew: False} since they should be in conversation history now
            # TODO: Check whether this is required now
            self.conversation_collection.find_one_update(
                {
                    "_id": ObjectId(self.__conversation_id),
                    "uploadedFiles": {"$exists": True},
                },
                {"uploadedFiles.$[].isNew": False},
                [],
            )

            # # Save the session state to the DB
            # self.active_session.save_session_agent_state(
            #     self.__active_session.session_id, agent_history_keys_to_string
            # )

        except Exception as e:
            logger.warn(
                f"Save Chat History | Conversation.save | {self.__conversation_id} | Unexpected error: {e}: \n {traceback.format_exc()}"
            )
            raise Exception(ChatAppUserMessages.ERROR_OCCUR_WHILE_BULK_WRITE.value)

    def save_matched_document_elements(self, document_elements: list, logger):
        """
        Save the matched document elements to the DB against the conversation in the collection "ConversationDataRetrieval" in the format:
        {
            "conversationId": "",
            "documentKey": "unique_name",
            "title": "Title_1",
            "file_type": "pdf",
            "elements": [element_1, element_2]
        }
        """
        logger.debug(f"save_matched_document_elements: {document_elements}")
        # For each document in the "document_elements" list, upsert records in the "ConversationDataRetrieval" collection
        try:
            for document in document_elements:
                self.data_retrieval_collection.upsert(
                    {
                        "conversationId": ObjectId(self.__conversation_id),
                        "documentKey": document["unique_name"],
                    },
                    {
                        "conversationId": ObjectId(self.__conversation_id),
                        "documentKey": document["unique_name"],
                        "title": document["title"],
                        "file_type": document["file_type"],
                        "elements": document["elements"],
                    },
                )
        except Exception as e:
            logger.warn(
                f"Save Doc Elements | Conversation.save_matched_document_elements | {self.__conversation_id} | Unexpected error: {e}"
            )

    def get_doc_element_meta_info(self, document_key: str, logger):
        """
        Get the metadata and elements list for the specified document key from the DB against the conversation in the collection "ConversationDataRetrieval".
        Returns:
        {
            "unique_name": "document_key",
            "title": "Title_1",
            "file_type": "pdf",
            "elements": [element_1, element_2]
        }
        """
        try:
            doc_elements_db_obj = self.data_retrieval_collection.get_one_document(
                {
                    "conversationId": ObjectId(self.__conversation_id),
                    "documentKey": document_key,
                }
            )

            if doc_elements_db_obj:
                return {
                    "unique_name": document_key,
                    "title": doc_elements_db_obj["title"],
                    "file_type": doc_elements_db_obj["file_type"],
                    "named_entities": doc_elements_db_obj.get("namedEntities", []),
                    "elements": doc_elements_db_obj["elements"],
                }
            else:
                return None

        except Exception as e:
            logger.warn(
                f"Get Doc Elements | Conversation.get_element_list_for_doc | {self.__conversation_id} | Unexpected error: {e}"
            )

        return None

    def clear_message_history(self):
        self.__history_messages.clear()

    """
    description:Extract details of the token count             
    note : addition of 7 special tokens 
           https://community.openai.com/t/what-is-the-reason-for-adding-total-7-tokens/337002/6 

    parameter  :
                response : response of the llm
                logger : logger file to log token count
                method : 'openai' - use the token count given by the openai 
                         'tiktoken' - use tiktoken library to count tokens, when using stream mode
                token_type : when using tiktoken identify if it is input prompt or a model response
    return: 
                no return              
    """

    def count_tokens(self, response, method="openai", token_type=1):
        if method == "openai":
            self.total_tokens += response.usage.total_tokens
            self.completion_tokens = response.usage.completion_tokens
            self.prompt_tokens = response.usage.prompt_tokens
        elif method == "tiktoken":
            self.count_tokens_tiktoken(response, token_type)

    def count_tokens_tiktoken(self, response, token_type=1):
        token_count = 0
        if isinstance(response, str):
            token_count = len(self.encoding.encode(response))
        elif isinstance(response, list):
            # get the tokens for the conversation.message_history
            for msg in response:
                token_count += len(self.encoding.encode(msg["content"])) + 7  # add special tokens

        self.total_tokens += token_count
        if token_type == 1:
            self.prompt_tokens += token_count
        elif token_type == -1:
            self.completion_tokens += token_count

    def log_token_counts(self, logger):
        logger.info(
            f"SessionID : {self.__session_id} | Prompt Tokens : {self.prompt_tokens} | Compleation Tokens : {self.completion_tokens} | Total Tokens : {self.total_tokens}"
        )

    @property
    def agent_id(self):
        """
        Description: Getter method for accessing the agent ID.

        Returns:
            - __agent_id: agent id associated with the conversation.

        Note:
            This property provides read-only access to the __agent_id.
        """
        return self.__agent_id

    @agent_id.setter
    def agent_id(self, id):
        """
        Description: Setter method for setting the agent id.

        Parameters:
            - id: agent id associated with the conversation.

        Note:
            This setter method allows setting the agent id of the conversation.
        """
        self.__agent_id = ObjectId(id)

    def set_active_session_llm_status(self, status):
        self.active_session.llm_status = status

    def publish_answer(self, is_final_answer=False):
        """
        Description:
            Publish answers to the chat window. If is_final_answer is set, then
            add the section type as ANSWER and update the history, otherwise
            add the answers to the queue for the active session.

        Parameters:
            - is_final_answer: boolean indicating whether this is the final
                               answer or not.

        Note:
            If is_final_answer is set, then the section type is updated to
            ANSWER and the history is updated. If not, then the answers are
            added to the queue for the active session.
        """
        if is_final_answer:
            self.active_session.add_section(SectionType.ANSWER.value)
            self.mark_session_latest_action_as_is_required_for_follow_ups()
            # save answer list to session
            self.sessions_collection.update_one(
                {"_id": ObjectId(self.active_session.session_id)},
                {
                    "$set": {
                        "session_answer_list": [
                            {"content": answer["content"], "content_type": answer["content_type"]}
                            for answer in self.active_session.session_answer_list
                        ]
                    }
                },
            )

        for index, answer in enumerate(self.active_session.session_answer_list):
            # Set required for followup only if this is text answer, not other media types
            if is_final_answer:
                self.add_to_history(
                    {
                        "role": "assistant",
                        "content": answer["content"],
                        "contentType": answer["content_type"],
                    },
                    True,
                    True,
                    is_required_for_follow_ups=(answer["content_type"] == MessageContentType.TEXT.value),
                )
            else:
                # add new line before content to make UX better in case of not final answer (shows in collapsed )
                if answer["content_type"] == MessageContentType.TEXT.value:
                    answer["content"] = "\n\n" + answer["content"]

            self.active_session.add_to_queue(
                answer["content"],
                "assistant",
                ConversationStatus.IN_PROGRESS,
                answer["content_type"],
            )

            if is_final_answer:

                if answer["content_type"] == MessageContentType.MEDIA.value:
                    block_id = f"{self.active_session.session_id}.{FrontendBlockType.MEDIA.suffix_value}.{index}"
                    block_type = FrontendBlockType.MEDIA
                else:
                    block_id = f"{self.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}.{index}"
                    block_type = FrontendBlockType.MARKDOWN

                self.persist_and_stream_handler(
                    block_id=block_id,
                    block_type=block_type,
                    block_data=answer["content"],
                    block_status=FrontendBlockStatus.COMPLETED,
                    block_tab_types=[FrontendTabContentType.FINAL_CONCLUSION],
                    handler_type=DataBlockHandlerType.PERSIST,
                )

        if is_final_answer:
            self.section_status = SectionStatus.COMPLETED.value

    def persist_and_stream_handler(
        self,
        block_id,
        block_data,
        block_tab_types: List[FrontendTabContentType],
        block_type: FrontendBlockType = FrontendBlockType.MARKDOWN,
        block_status: FrontendBlockStatus = FrontendBlockStatus.IN_PROGRESS,
        handler_type: DataBlockHandlerType = DataBlockHandlerType.PERSIST_AND_STREAM,
        operation: StreamChunkOperation = StreamChunkOperation.ADD,
        is_last_block: bool = False,
        user_visibility: DataBlockVisibility = DataBlockVisibility.ALL_USERS,
    ):
        """
        Persist the frontend blocks to the DB and stream them to the frontend.
        Frontend blocks are the blocks that are displayed to the user in the frontend.
        There are Many types of frontend blocks and required data type for each block type.
        This method is used to persist the frontend blocks to the DB and stream them to the frontend.
        The handler type is used to determine the type of the handler in the backend.
        The operation is used to determine the operation of the block in the frontend.
        The is_last_block is used to determine whether the block is the last block of the stream.

        Parameters:
            - block_id: str, the id of the block.
            - block_data: str, the data of the block.
            - block_tab_types: List[FrontendTabContentType], the tab types of the block which data will be shown.
            - block_type: FrontendBlockType, the type of the block.
            - block_status: FrontendBlockStatus, the status of the block.
            - handler_type: DataBlockHandlerType, the type of the handler.
            - operation: StreamChunkOperation, the operation of the block.
            - is_last_block: bool, whether the block is the last block.
        """

        if self.active_session.user_stopped_stream:
            return

        enriched_block_data = {
            "session_id": self.active_session.session_id,
            "conversation_id": str(self.active_session.conversation_id),
            "status": self.active_session.session_status.value,  # status of the main session (session can have multiple streams- ex: supplementary chat session between user and agent). Status should be kept as "in progress" in case of supplementary chat session.
            "session_type": self.active_session.session_type.value,
            "final": is_last_block,  # used to determine whether the stream is completed
            "user_visibility": user_visibility.value,
            "blocks": [
                {
                    "block_id": block_id,
                    "block_type": block_type.type_value,
                    "tab_types": [tab_type.tab_type_value for tab_type in block_tab_types],
                    "operation": operation.value,
                    "status": block_status.value,
                    "data": block_data,
                }
            ],
        }

        if handler_type == DataBlockHandlerType.PERSIST_AND_STREAM or handler_type == DataBlockHandlerType.PERSIST:
            # make conversation_id ObjectId
            enriched_block_data["conversation_id"] = ObjectId(enriched_block_data["conversation_id"])
            enriched_block_data["session_id"] = ObjectId(enriched_block_data["session_id"])
            self.data_blocks_collection.insert_one(enriched_block_data)

        if handler_type == DataBlockHandlerType.PERSIST_AND_STREAM or handler_type == DataBlockHandlerType.STREAM:
            if "_id" in enriched_block_data:
                del enriched_block_data["_id"]
            # make conversation_id string
            enriched_block_data["conversation_id"] = str(enriched_block_data["conversation_id"])
            enriched_block_data["session_id"] = str(enriched_block_data["session_id"])

            if user_visibility.value in self.active_session.data_block_visibility:
                self.active_session.enqueue(enriched_block_data)

    def get_all_questions(self, logger):
        """
        Get all questions from the DB against the conversation in the collection "Messages".
        """
        try:
            query = [
                {
                    "$match": {
                        "conversationId": ObjectId(self.__conversation_id),
                        "senderType": "user",
                        "is_front_end_data": True,
                        "isChild": False,  # Not require pre-data analysis messages here
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "questions": {
                            "$push": {
                                "$cond": {
                                    "if": {"$isArray": "$content"},
                                    "then": "Attachment",
                                    "else": "$content",
                                }
                            }
                        },
                    }
                },
            ]
            res = self.messages_collection.aggregate(query)

            if res:
                return list(res)[0]["questions"]

        except Exception as e:
            logger.warn(
                f"Get All Questions | Conversation.get_all_questions | {self.__conversation_id} | Unexpected error: {e}"
            )

        return []

    def active_session_send_analysis_step(
        self,
        step: AnalysisStep,
        status: FrontendBlockStatus = FrontendBlockStatus.IN_PROGRESS,
    ):
        if self.agent_type == LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP:
            return
        # self.active_session.send_analysis_step(step, status)

        if step == AnalysisStep.PREVIOUS_STEP:  # in case of we need to change status of previous/last step
            self.persist_and_stream_handler(
                block_id=f"{self.active_session.session_id}.{FrontendBlockType.ANALYSIS_STEPS.suffix_value}.{self.active_session.analysis_step_id}",
                block_type=FrontendBlockType.ANALYSIS_STEPS,
                block_data=self.active_session.recent_analysis_step.description,
                block_status=status,
                operation=StreamChunkOperation.REPLACE,
                block_tab_types=[FrontendTabContentType.ANALYSIS_STEPS],
            )
            return

        if self.active_session.analysis_step_id is None:
            self.active_session.analysis_step_id = 1
            self.active_session.recent_analysis_step = step
            self.active_session.recent_analysis_step_status = status
        elif self.active_session.recent_analysis_step.identifier != step.identifier:
            # complete previous step in case of switching to a new step if previous step is in in progress
            if self.active_session.recent_analysis_step_status == FrontendBlockStatus.IN_PROGRESS:
                self.persist_and_stream_handler(
                    block_id=f"{self.active_session.session_id}.{FrontendBlockType.ANALYSIS_STEPS.suffix_value}.{self.active_session.analysis_step_id}",
                    block_type=FrontendBlockType.ANALYSIS_STEPS,
                    block_data=self.active_session.recent_analysis_step.description,
                    block_status=FrontendBlockStatus.COMPLETED,
                    operation=StreamChunkOperation.REPLACE,
                    block_tab_types=[FrontendTabContentType.ANALYSIS_STEPS],
                )
            if step.identifier == AnalysisStep.COMPLETED.identifier:
                # AnalysisStep.COMPLETED is only used to complete last analysis step
                return

            self.active_session.analysis_step_id += 1
            self.active_session.recent_analysis_step = step
            self.active_session.recent_analysis_step_status = status

        self.persist_and_stream_handler(
            block_id=f"{self.active_session.session_id}.{FrontendBlockType.ANALYSIS_STEPS.suffix_value}.{self.active_session.analysis_step_id}",
            block_type=FrontendBlockType.ANALYSIS_STEPS,
            block_data=step.description,
            block_status=FrontendBlockStatus.IN_PROGRESS,
            block_tab_types=[FrontendTabContentType.ANALYSIS_STEPS],
        )

    def get_llm_input_history(self, section_type_list: List[str]):
        llm_input_history = []
        for message in self.__history_messages:
            if (
                "is_llm_agent_input" in message
                and message["is_llm_agent_input"]
                and "sectionType" in message
                and message["sectionType"] in section_type_list
            ):
                llm_input_history.append(str(message["sectionType"]) + " : " + str(message["content"]))
        return llm_input_history

    def complete_session_in_db(self):
        self.sessions_collection.update_one(
            {
                "_id": ObjectId(self.active_session.session_id),
                "conversation_id": ObjectId(self.chat_id),
            },
            {"$set": {"status": SessionStatus.COMPLETED.value}},
        )

    def get_merged_session_blocks(self):
        """
        Get merged data blocks for the current session.

        Returns:
            list: Merged data blocks for the current session
        """
        try:
            # Get all data blocks for the current session
            data_blocks_lists = self.data_blocks_collection.get_documents(
                {"session_id": self.active_session.session_id},
                sort_criteria_list=[("_id", pymongo.ASCENDING)],
            )

            all_blocks = []
            for data_blocks_list in data_blocks_lists:
                blocks = data_blocks_list.get("blocks", [])
                if blocks:
                    all_blocks.extend(blocks)

            # Merge blocks using the Session utility method
            if all_blocks:
                return Session.merge_data_blocks(all_blocks)

            return []

        except Exception as e:
            return []
