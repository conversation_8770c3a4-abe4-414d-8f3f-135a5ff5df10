"""
* Copyright (c) 2023 ZOOMi Technologies Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* Session model class

* @class Session
* @description Session class use to maintained queue data. This class contain add data to queue and get data from queue
* <AUTHOR>

"""

import datetime
import json
import os
from queue import Empty, Queue
import time
import uuid

from bson import ObjectId
from utils.misc_utils import get_data_block_visibility_list
from models.models import FormattedDataSource
from utils.constant import (
    LLM_PROVIDER_AZURE_OPENAI,
    AgentState,
    AnalysisStep,
    ConversationLLMStatus,
    ConversationStatus,
    FrontendBlockType,
    FrontendTabContentType,
    InsightStep,
    SessionStatus,
    SessionType,
    StreamChunkOperation,
    SubSectionType,
    SectionStatus,
    SectionType,
    UserReaction,
)
from utils.logger import get_debug_logger
from dotenv import load_dotenv
from databases.mongo_manager import MongoDBmanager

load_dotenv()
FRONTEND_URL = os.getenv("FRONTEND_URL")


class Session:
    def __init__(
        self, _conversation_id, _session_id, queue_handle=None, session_type: SessionType = SessionType.ANALYSIS
    ):
        """
        Initializes a new Session instance.

        Parameters:
            _conversation_id (str): The unique identifier for the conversation.
            _session_id (str): The unique identifier for the session.
            queue_handle (Queue, optional): A queue handle to be used for the queue within the session. If a valid queue is passed, its re-used, otherwise a new queue is created. Defaults to None.

        Attributes:
            __llm_status (ConversationLLMStatus): The initial LLM status.
            __section_id (str): The identifier for the current section.
            __section_type (SectionType): The type of the current section.
            __section_status (SectionStatus): The status of the current section, initially set to show.
            queue (Queue): A thread-safe queue for session data.
            end_stream (bool): Flag indicating if the stream has ended.
            user_stopped_stream (bool): Flag indicating if the user stopped the stream.
            conversation_id (str): Stores the conversation ID.
            session_id (str): Stores the session ID.
            llm_status (ConversationLLMStatus): The current LLM status, starting at LLM_DATA_SECTION_LOCATING.
            logger (Logger): Logger for session-specific events.
        """

        self.__llm_status = None
        self.__section_id = None
        self.__section_type = None
        self.__section_status = None  # used to keep display mode (FE requires this), initial value is set to show since every session starts with a user input which need to be shown in the history

        self.__analysis_step_id = 0
        self.__recent_analysis_step: AnalysisStep = AnalysisStep.NOT_STARTED
        self.__recent_analysis_step_status = None

        self.__recent_insight_step_id = None
        self.__recent_insight_step = None
        self.__recent_insight_step_status = None
        self.__recent_insight_step_message = None

        if queue_handle:
            self.queue = queue_handle
        else:
            self.queue = Queue()  # Thread-safe queue

        self.__queue = Queue()  # v2 implementation queue

        self.end_stream = False
        self.user_stopped_stream = False
        # self.conversation_id = _conversation_id
        # self.session_id = _session_id
        self.llm_status: ConversationLLMStatus = ConversationLLMStatus.LLM_DATA_SECTION_LOCATING  # The initial stage

        self.session_answer_list = []
        self.data_sources_info_list = []

        self.session_llm_code_with_logic = ""

        # self.logger = get_debug_logger(f'Stream data', pathlib.Path.joinpath(
        #     pathlib.Path(__file__).parent.resolve(), f'../logs/chat_{conversation_id}.log'))
        self.logger = get_debug_logger(f"session_{_conversation_id}", f"./logs/session_{_conversation_id}.log")

        """
        Session v2 implementation
        """
        self.__session_id = None
        self.__session_status = None
        self.__session_type = None
        self.__conversation_id = None
        self.__session_used_data_sources_all: list[FormattedDataSource] = []  # all data sources used in the session
        self.__session_used_tabs_all: list[FrontendTabContentType] = []  # all tabs used in the session

        self.session_id = _session_id
        self.session_status = SessionStatus.IN_PROGRESS
        self.session_type = session_type
        self.conversation_id = _conversation_id
        self.session_start_time = datetime.datetime.now(datetime.timezone.utc)

        self.dashboard_pin_request = None
        self.__user_info = None
        self.__data_block_visibility = []

        self.sessions_collection = MongoDBmanager("Sessions")

    @property
    def llm_status(self):
        return self.__llm_status

    @llm_status.setter
    def llm_status(self, __status):
        self.__llm_status = __status

    @property
    def section_id(self):
        return self.__section_id

    @section_id.setter
    def section_id(self, __id):
        self.__section_id = __id

    @property
    def section_type(self):
        return self.__section_type

    @section_type.setter
    def section_type(self, __type):
        self.__section_type = __type

    @property
    def section_status(self):
        return self.__section_status

    @section_status.setter
    def section_status(self, __status):
        self.__section_status = __status
        self.add_to_queue(
            "",
            "assistant",
            ConversationStatus.IN_PROGRESS,
        )

    @property
    def session_used_data_sources_all(self):
        return self.__session_used_data_sources_all

    def update_session_used_data_sources_all(self, __data_source: FormattedDataSource):
        if __data_source not in self.__session_used_data_sources_all:
            self.__session_used_data_sources_all.append(__data_source)
            return True
        return False

    @property
    def user_info(self):
        return self.__user_info

    @user_info.setter
    def user_info(self, user_info):
        self.__data_block_visibility = get_data_block_visibility_list(user_info["userType"])
        self.__user_info = user_info

    @property
    def data_block_visibility(self):
        return self.__data_block_visibility

    @property
    def session_used_tabs_all(self):
        return self.__session_used_tabs_all

    def update_session_used_tabs_all(self, __tab: FrontendTabContentType):
        if __tab not in self.__session_used_tabs_all:
            self.__session_used_tabs_all.append(__tab)
            return True
        return False

    @property
    def dashboard_pin_request(self):
        return self.__dashboard_pin_request

    @dashboard_pin_request.setter
    def dashboard_pin_request(self, __dashboard_pin_request):
        self.__dashboard_pin_request = __dashboard_pin_request

    def add_section(self, _section_type=SectionType.DEFAULT.value):
        """
        Adds a new section to the current session.

        Parameters:
            _section_type (str): The type of section to add. Defaults to SectionType.DEFAULT.value.

        Returns:
            None
        """
        # if requested new section type is same as current section type, do nothing
        if self.__section_type == _section_type:
            return
        # set current section status to completed before start new section
        if self.__section_status == SectionStatus.IN_PROGRESS.value:
            self.section_status = SectionStatus.COMPLETED.value
        self.__section_id = str(uuid.uuid4())
        self.__section_type = _section_type
        # set new section status to in progress
        self.section_status = SectionStatus.IN_PROGRESS.value

    def send_analysis_step(
        self,
        step: AnalysisStep = None,
        status: SectionStatus = SectionStatus.IN_PROGRESS,
    ):
        if step is None:  # step is none in case of we need to change status of previous step
            self.add_to_queue(
                self.__recent_analysis_step.description,
                "orchestrator",
                ConversationStatus.IN_PROGRESS,
                section_info={
                    "sectionId": self.__analysis_step_id,
                    "sectionType": "step",
                    "sectionStatus": status.value,
                },
            )
            return

        if self.__analysis_step_id is None:
            self.__analysis_step_id = str(uuid.uuid4())
            self.__recent_analysis_step = step
            self.__recent_analysis_step_status = status
        elif self.__recent_analysis_step.identifier != step.identifier:
            # complete previous step in case of switching to a new step if previous step is in in progress
            if self.__recent_analysis_step_status == SectionStatus.IN_PROGRESS:
                self.add_to_queue(
                    self.__recent_analysis_step.description,
                    "orchestrator",
                    ConversationStatus.IN_PROGRESS,
                    section_info={
                        "sectionId": self.__analysis_step_id,
                        "sectionType": "step",
                        "sectionStatus": SectionStatus.COMPLETED.value,
                    },
                )

            if step.identifier == AnalysisStep.COMPLETED.identifier:
                return

            self.__analysis_step_id = str(uuid.uuid4())
            self.__recent_analysis_step = step
            self.__recent_analysis_step_status = status

        _section_id = self.__analysis_step_id

        self.add_to_queue(
            self.__recent_analysis_step.description,
            "orchestrator",
            ConversationStatus.IN_PROGRESS,
            section_info={
                "sectionId": _section_id,
                "sectionType": "step",
                "sectionStatus": self.__recent_analysis_step_status.value,
            },
        )

    def send_insight_step(
        self,
        step: InsightStep = None,
        step_id: str = None,
        status: SectionStatus = SectionStatus.IN_PROGRESS,
        message: str = None,
    ):

        if (
            step is None and step_id is None and message is None
        ):  # in case of we need to change status of previous step
            self.add_to_queue(
                self.__recent_insight_step_message,
                "orchestrator",
                ConversationStatus.IN_PROGRESS,
                section_info={
                    "sectionId": self.__recent_insight_step_id,
                    "sectionType": "step",
                    "sectionStatus": status.value,
                },
            )
            return

        self.add_to_queue(
            message if message else step.description,
            "orchestrator",
            ConversationStatus.IN_PROGRESS,
            section_info={
                "sectionId": step_id,
                "sectionType": "step",
                "sectionStatus": status.value,
            },
        )
        self.__recent_insight_step_id = step_id
        self.__recent_insight_step = step
        self.__recent_insight_step_status = status
        self.__recent_insight_step_message = message if message else step.description

    def clone_session(self):
        """
        Description: Clone session except llm status variable
        Returns:
            Cloned session
        """
        new_session = Session(
            _conversation_id=self.conversation_id,
            _session_id=self.session_id,
            queue_handle=self.queue,
        )
        new_session.section_id = self.section_id
        new_session.section_type = self.section_type
        new_session.section_status = self.section_status
        new_session.end_stream = self.end_stream
        new_session.user_stopped_stream = self.user_stopped_stream

        return new_session

    def add_to_queue(
        self,
        data_display,
        role,
        status: ConversationStatus,
        content_type="text",
        section_info=None,
    ):
        """
        Description: Adds data to the streaming queue to be displayed in Frontend
        Parameters:
            data_display: String to show
            role: eg: assistant
            status: Status of session - important for frontend to decide whether to close the connection
        """
        self.logger.debug(
            f"CONVERSATION_ID: {self.conversation_id} | SESSION_ID: {self.session_id} | Push data to queue: {data_display}"
        )

        self.queue.put(
            {
                "role": role,
                "sectionId": self.__section_id if section_info is None else section_info["sectionId"],
                "sectionType": self.__section_type if section_info is None else section_info["sectionType"],
                "sectionStatus": self.__section_status if section_info is None else section_info["sectionStatus"],
                "content": data_display,
                "contentType": content_type,
                "id": str(self.conversation_id),
                "sessionId": self.session_id,
                "status": status.value,
            }
        )

    def get_stream(self, session_id):
        """
        Description: Generates a stream of messages for server-sent events (SSE) to be sent to the client.

        Parameters:
            - session_id: str, the unique identifier for the current session.

        Yields:
            - str: SSE-formatted messages containing data, events, and ping messages.

        Note:
            This generator function continuously checks the streaming queue for new data and yields SSE-formatted messages
            to be sent to the client. It includes regular messages with conversation data, ping events to maintain
            continuous communication, and a completion event when the streaming ends.

        Raises:
            - Exception: If an unexpected error occurs during the streaming process.
        """

        try:
            count = 0
            while not self.end_stream and not self.user_stopped_stream:
                # logger.debug(f'SESSION_ID: {session_id} | Current queue size: {self.queue.qsize()}')
                # every 0.01s check the queue is empty or not
                # if queue is not empty then get data from streaming queue and push to streaming channel
                if not self.queue.empty():
                    data = self.queue.get()
                    if "status" in data and data["status"] == ConversationStatus.COMPLETED.value:
                        self.end_stream = True

                    self.logger.debug(
                        f"CONVERSATION_ID: {self.conversation_id} | SESSION_ID: {session_id} | Get data from queue: {data}"
                    )
                    # yield f"data: {json.dumps(data)}\n\n"
                    # print
                    yield f"event:message\ndata: {json.dumps(data)}\n\n"
                    count = 0
                else:
                    # every 30s push empty data message(ping event) to streaming channel to keep communication continuously
                    count += 1
                    if count == 3000:
                        yield f"event:ping\ndata: {json.dumps({})}\n\n"
                        count = 0
                    time.sleep(0.01)

            # Send a final message when the loop ends (complete event)
            # yield f"event:completed\ndata: {json.dumps({'status': 'completed'})}\n\n"

            if self.user_stopped_stream:

                # handle if any in-progress analysis step exist
                if self.__recent_analysis_step_status == SectionStatus.IN_PROGRESS:
                    _data = {
                        "role": "orchestrator",
                        "sectionId": self.__analysis_step_id,
                        "sectionType": "step",
                        "sectionStatus": (
                            SectionStatus.STOPPED.value if self.user_stopped_stream else SectionStatus.COMPLETED.value
                        ),
                        "content": self.__recent_analysis_step.description,
                        "contentType": "text",
                        "id": str(self.conversation_id),
                        "status": ConversationStatus.IN_PROGRESS.value,
                    }

                # handle if any in-progress insight step exist
                if self.__recent_insight_step_status == SectionStatus.IN_PROGRESS:
                    _data = {
                        "role": "orchestrator",
                        "sectionId": self.__recent_insight_step_id,
                        "sectionType": "step",
                        "sectionStatus": (
                            SectionStatus.STOPPED.value if self.user_stopped_stream else SectionStatus.COMPLETED.value
                        ),
                        "content": self.__recent_insight_step_message,
                        "contentType": "text",
                        "id": str(self.conversation_id),
                        "status": ConversationStatus.IN_PROGRESS.value,
                    }

                self.logger.info(
                    f"Session.get_stream() | CONVERSATION_ID: {self.conversation_id} | SESSION_ID: {session_id} | User stopped stream"
                )
                _data = {
                    "role": "assistant",
                    "sectionId": self.__section_id,
                    "sectionType": self.__section_type,
                    "sectionStatus": self.__section_status,
                    "content": "",
                    "contentType": "text",
                    "id": str(self.conversation_id),
                    "status": ConversationStatus.COMPLETED.value,
                }
                yield f"event:message\ndata: {json.dumps(_data)}\n\n"

        except Exception as e:
            self.logger.error(f"An exception occurred: {e}")
            # Send an error message if an exception occurs
            yield f"event:error\ndata: {json.dumps({'message': str(e)})}\n\n"

    """
    Session v2 implementation
    """

    # Getter for session_id
    @property
    def session_id(self):
        return self.__session_id

    # Setter for session_id
    @session_id.setter
    def session_id(self, value):
        self.__session_id = value

    # Getter for session_status
    @property
    def session_status(self) -> ConversationStatus:
        return self.__session_status

    # Setter for session_status
    @session_status.setter
    def session_status(self, value):
        self.__session_status = value

    # Getter for session_type
    @property
    def session_type(self) -> SessionType:
        return self.__session_type

    # Setter for session_type
    @session_type.setter
    def session_type(self, value):
        self.__session_type = value

    @property
    def conversation_id(self):
        return self.__conversation_id

    # Setter for conversation_id
    @conversation_id.setter
    def conversation_id(self, value):
        self.__conversation_id = value

    # Getter for analysis_step_id
    @property
    def analysis_step_id(self):
        return self.__analysis_step_id

    # Setter for analysis_step_id
    @analysis_step_id.setter
    def analysis_step_id(self, value):
        self.__analysis_step_id = value

    # Getter for recent_analysis_step
    @property
    def recent_analysis_step(self) -> AnalysisStep:
        return self.__recent_analysis_step

    # Setter for recent_analysis_step
    @recent_analysis_step.setter
    def recent_analysis_step(self, value: AnalysisStep):
        self.__recent_analysis_step = value

    # Getter for recent_analysis_step_status
    @property
    def recent_analysis_step_status(self):
        return self.__recent_analysis_step_status

    # Setter for recent_analysis_step_status
    @recent_analysis_step_status.setter
    def recent_analysis_step_status(self, value):
        self.__recent_analysis_step_status = value

    def enqueue(
        self,
        data_block,
    ):
        """Add an data to the queue."""
        self.__queue.put(data_block)

    def dequeue(self):
        """Remove and return an item from the queue. Raises exception if empty."""
        count = 0
        while True:
            if not self.__queue.empty():
                data = self.__queue.get()
                if data["final"]:
                    yield f"event:end\ndata: {json.dumps(data)}\n\n"
                    break
                yield f"event:message\ndata: {json.dumps(data)}\n\n"
                count = 0
            else:
                count += 1
                if count == 3000:
                    yield f"event:ping\ndata: {json.dumps({})}\n\n"
                    count = 0
                time.sleep(0.01)

    def clear_queue(self):
        # with self.__queue.mutex:  # Lock to prevent concurrent access
        while True:
            try:
                self.__queue.get_nowait()  # Non-blocking dequeue
            except Empty:
                break

    @staticmethod
    def merge_data_blocks(
        blocks_list,
        session_id: str,
        session_status: SessionStatus = SessionStatus.COMPLETED.value,
        user_reaction: UserReaction = UserReaction.PENDING.value,
    ):
        """
        Merge data blocks based on block_id, handling ADD and REPLACE operations.

        Args:
            blocks_list (list): List of data blocks to merge

        Returns:
            list: Merged data blocks with unique block_ids
        """
        if not blocks_list:
            return []

        merged_blocks = {}

        for block in blocks_list:
            try:
                block_id = block.get("block_id")
                if not block_id:
                    continue  # Skip blocks without block_id

                block_operation = block.get("operation")
                block_data = block.get("data")
                block_status = block.get("status")
                block_tab_types = block.get("tab_types", [])

                if block_id in merged_blocks:
                    # Merge with existing block
                    existing_block = merged_blocks[block_id]

                    if block_operation == StreamChunkOperation.ADD.value:
                        # Handle ADD operation based on data type
                        if isinstance(block_data, str) and isinstance(existing_block.get("data"), str):
                            # For string data, concatenate
                            existing_block["data"] += block_data
                        elif isinstance(block_data, list) and isinstance(existing_block.get("data"), list):
                            # For list data, extend
                            existing_block["data"].extend(block_data)
                        elif isinstance(block_data, dict) and isinstance(existing_block.get("data"), dict):
                            # For dict data, update
                            existing_block["data"].update(block_data)
                        else:
                            # If types don't match, replace with new data
                            existing_block["data"] = block_data
                    elif block_operation == StreamChunkOperation.REPLACE.value:
                        # Handle REPLACE operation - replace data completely
                        existing_block["data"] = block_data
                    else:
                        # Unknown operation, replace with new data
                        existing_block["data"] = block_data

                    # Always update status and tab_types to the latest
                    existing_block["status"] = block_status
                    existing_block["tab_types"] = block_tab_types

                    # if block type is Tab Content
                    if block.get("block_type") == FrontendBlockType.TAB_CONTENT.type_value:
                        # sort the tabs based on the order of precedence
                        order_precedence = [
                            FrontendTabContentType.ANSWER.tab_type_value,
                            FrontendTabContentType.SOURCES.tab_type_value,
                            FrontendTabContentType.TASKS.tab_type_value,
                        ]
                        _tab_data = sorted(
                            existing_block["data"],
                            key=lambda x: (
                                order_precedence.index(x["id"])
                                if x["id"] in order_precedence
                                else len(order_precedence)
                            ),
                        )
                        # update tab data with session status (because the tab data are not updated even session completed), we can remove this by adding replace operation for tab content block when session completed
                        for tab in _tab_data:
                            tab["status"] = session_status

                        existing_block["data"] = _tab_data

                else:
                    # Create new block entry
                    merged_blocks[block_id] = {
                        "block_id": block_id,
                        "block_type": block.get("block_type"),
                        "operation": block_operation,
                        "status": block_status,
                        "data": block_data,
                        "tab_types": block_tab_types,
                    }
            except Exception as e:
                # Log error but continue processing other blocks
                print(f"Error processing block {block.get('block_id', 'unknown')}: {e}")
                continue

        # if session is in progress / streaming ongoing
        # in this case, task tab should be hidden, content of the task tab should be shown in answer tab
        if session_status == SessionStatus.IN_PROGRESS.value:
            # remove task tab from tab_types
            for block in merged_blocks.values():
                if FrontendTabContentType.TASKS.tab_type_value in block["tab_types"]:
                    block["tab_types"].remove(FrontendTabContentType.TASKS.tab_type_value)
                    if FrontendTabContentType.ANSWER.tab_type_value not in block["tab_types"]:
                        block["tab_types"].append(FrontendTabContentType.ANSWER.tab_type_value)

                # if block type is tab content
                if block["block_type"] == FrontendBlockType.TAB_CONTENT.type_value:
                    # remove task tab from data
                    _tab_data = [
                        tab for tab in block["data"] if tab["id"] != FrontendTabContentType.TASKS.tab_type_value
                    ]
                    block["data"] = _tab_data
                    # if answer tab is not in tab_types, then add it
                    if FrontendTabContentType.ANSWER.tab_type_value not in block["tab_types"]:
                        block["tab_types"].append(FrontendTabContentType.ANSWER.tab_type_value)
        else:
            #  add default user reaction block if not exist
            user_reaction_block_id = f"{session_id}.{FrontendBlockType.USER_REACTION.suffix_value}"

            if user_reaction_block_id not in merged_blocks:
                merged_blocks[user_reaction_block_id] = {
                    "block_id": user_reaction_block_id,
                    "block_type": FrontendBlockType.USER_REACTION.type_value,
                    "operation": StreamChunkOperation.ADD.value,
                    "status": session_status,
                    "data": {
                        "reaction": user_reaction,
                    },
                    "tab_types": [
                        FrontendTabContentType.FINAL_CONCLUSION.tab_type_value,
                    ],
                }

        return list(merged_blocks.values())

    def save_session_agent_state(self, session_id: str, agent_states: dict[str, dict[str, list[dict]]]):
        """
        Description: Save the current state of the agents to "Sessions" collection. This is essential to identify the thought chain of the master agent in each session in the conversation
        Parameters:
            session_id: session id
            agent_states: current state of the agents
        Returns:
            True if DB update is successful, False otherwise
        """
        try:
            # Note: Currently only the master agent (Complex analysis) state is saved
            master_agent_state = agent_states.get(AgentState.COMPLEX_REASONING.value, {})
            if not master_agent_state or not master_agent_state.keys():
                return True
            master_agent_state_latest = master_agent_state[max(master_agent_state.keys())]
            self.sessions_collection.find_one_update(
                {"_id": ObjectId(session_id)},
                {"$set": {"master_agent_state": master_agent_state_latest}},
                [],
            )
            return True
        except Exception as e:
            print(f"Error updating agent state for session {session_id}: {e}")
            return False
