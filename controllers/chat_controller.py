"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
* ChatController use for Handle the request

* @class ChatController
* @description This controller use for Handle the request related to the chat. Ex: create new chat, maintain chat and retrieve chat data
* <AUTHOR>
"""

import json
import signal
import traceback
from typing import Union
import layernext
import os
import pathlib

# import threading
from fastapi import Depends, HTTPException, Query
from fastapi.responses import StreamingResponse
from pydantic import BaseModel
from fastapi_utils.cbv import cbv
from fastapi_utils.inferring_router import InferringRouter
import datetime
from models.schemas.responses import GeneralResponse
from services.orchestration_agent.orchestration_agent import OrchestrationAgent

# from services.orchestration_agent.iterative_problem_solver_agent import IterativeProblemSolverAgent
from utils.misc_utils import make_json_compatible
from services.insight_app.insight_report_service import InsightReportService
from services.global_memory_service import GlobalMemoryService
from models.models import (
    ConversationAddSharedUsersRequest,
    CreateConversationRequest,
    GetConversationListRequest,
    InsightReportSharedUsersRequest,
    StartConversationRequest,
    StopConversationRequest,
    UpdateInsightReportSectionRequest,
    DeleteInsightReportSectionRequest,
    ConversationRemoveSharedUsersRequest,
)
from fastapi.responses import FileResponse
from fastapi.responses import StreamingResponse
from services.insight_app.insight_generator_service import LlmInsightAgent
from services.llm_chat_service import LlmChatAgent
from services.conversation_service import ConversationService
from models.conversation import Conversation
from models.session import Session
from services.llm_master_agent_service import LlmMasterAgent
from models.models import FavouriteUpdateRequest
from services.user_service import UserService
from utils.constant import (
    MAX_SESSIONS_PER_CONVERSATION,
    ChatAppUserMessages,
    ConversationLLMStatus,
    ConversationStatus,
    ConversationType,
    FrontendBlockType,
    FrontendTabContentType,
    LLMAgentType,
    SectionStatus,
    SessionType,
    StreamChunkOperation,
    UserReaction,
)
from utils.dependency_provider import (
    get_agent_service,
    get_conversation_service,
    get_current_user,
    get_global_memory,
    get_insight_report_service,
    get_llm_chat_agent,
    get_llm_insight_agent,
    get_master_agent,
    get_super_admin_user,
)
from utils.logger import get_debug_logger
from utils.package_install_utils import PackageInstallUtils
import concurrent.futures
from openai import OpenAIError
from dotenv import load_dotenv
from fastapi import Header

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))
logger = get_debug_logger(
    "chat_controller",
    pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log"),
)


process_id_log = get_debug_logger(
    "process_id", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/process_id.log")
)

load_dotenv()
GENERATE_QUESTION_SUGGESTIONS_FOR_USER = os.getenv("GENERATE_QUESTION_SUGGESTIONS_FOR_USER", "disabled")

router = InferringRouter()


@cbv(router)
class ChatController:
    def __init__(
        self,
        chat_agent: LlmChatAgent = Depends(get_llm_chat_agent),
        master_agent: OrchestrationAgent = Depends(get_master_agent),
        insight_agent: LlmInsightAgent = Depends(get_llm_insight_agent),
        agent_service: LlmMasterAgent = Depends(get_agent_service),
        conversation_service: ConversationService = Depends(get_conversation_service),
        insight_report_service: InsightReportService = Depends(get_insight_report_service),
        # iterative_problem_solver_agent: IterativeProblemSolverAgent = Depends(get_iterative_problem_solver_agent),
    ):
        logger.debug("Initiate Chat Controller")
        self.chat_agent = chat_agent
        self.master_agent = master_agent
        # self.iterative_problem_solver_agent = iterative_problem_solver_agent
        self.insight_agent = insight_agent
        self.conversation_service = conversation_service
        self.agent_service = agent_service
        self.insight_report_service = insight_report_service
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)

    """
    Description: Initiates a new conversation with the language model agent (LLM) based on the user's request.

    Parameters:
        - request: StartConversationRequest, contains information about the conversation initiation request.
        - user_info: dict, user information obtained from the authentication token.

    Returns:
        - StreamingResponse: A streaming response providing real-time updates from the conversation.

    Raises:
        - HTTPException: If there is an issue with starting the conversation, it raises a 500 Internal Server Error.
            - status_code: HTTP status code indicating the issue.
            - detail: Error message providing details about the issue.
    """

    @router.post("/api/conversation")
    async def llm_request(
        self,
        request: StartConversationRequest,
        global_memory: GlobalMemoryService = Depends(get_global_memory),
        user_info: dict = Depends(get_current_user),
    ):
        logger.info(f"ChatController.llm_request | Start chat")
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)
        conversation_id = request.id

        if conversation_id is None or conversation_id == "":
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.CONVERSATION_ID_REQUIRED.value)

        conversation_db_obj = self.conversation_service.get_conversation_data(conversation_id)

        if not conversation_db_obj:
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

        is_deleted: bool = True if conversation_db_obj.get("status", "") == ConversationStatus.DELETED.value else False
        if is_deleted:
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.CHAT_DELETED.value)

        # request.reconnect is true when user view the conversation page again from history which is already in progress
        # when frontend stream reconnection is automatically triggered from frontend library, the request.reconnect flag is not set.
        # In such case, we need to check if the conversation is in progress (ex: network issue happen in client side and reconnect)
        if request.reconnect or conversation_db_obj.get("status") == ConversationStatus.IN_PROGRESS.value:
            if conversation_id not in global_memory.active_conversations:
                raise HTTPException(
                    status_code=406, detail=ChatAppUserMessages.CHAT_CAN_NOT_RECONNECT_CONVERSATION_NOT_IN_MEMORY.value
                )
            # NOTE: is_reset_required is a temporary solution, we cannot handle case of network reconnection retry happen even when in a manual(from history) reconnect attempt
            return await self.reconnect_conversation(
                conversation_id, global_memory, is_reset_required=True if request.reconnect else False
            )

        # Send session limit exceed error if no of sessions in the conversation is already reached to 5
        curr_session_count = len(conversation_db_obj.get("sessionIdList", []))
        if curr_session_count >= MAX_SESSIONS_PER_CONVERSATION:
            logger.warn(
                f"Session count already reached to {curr_session_count} in the conversation  {conversation_id}"
            )
            raise HTTPException(status_code=429, detail=ChatAppUserMessages.SESSION_LIMIT_EXCEEDED.value)

        if (
            conversation_db_obj.get("status") == ConversationStatus.QUEUED.value
            and request.insight_board_id is not None
            and request.insight_board_id != ""
        ):
            self.conversation_service.attach_conversation_as_insight_followup(
                request.insight_board_id, conversation_id, request.content
            )

        request_session_type = request.session_type

        try:
            # Extract data from request
            prompt = request.content.strip()  # Trim the string to ignore the leading spaces if there
            logger.info(f"Question: {prompt}")

            agent_type = None
            raw_agent_type = conversation_db_obj.get("type", LLMAgentType.AGENT_TYPE_CHAT.value)

            # Check if raw_agent_type is a valid LLMAgentType value
            if any(raw_agent_type == member.value for member in LLMAgentType):
                agent_type = LLMAgentType(raw_agent_type)
            # Handle legacy ConversationType values
            elif raw_agent_type == ConversationType.INSIGHT_GEN_CONVERSATION.value:
                agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
            elif raw_agent_type == ConversationType.CHAT_BOT_CONVERSATION.value:
                # Can be chat to complex
                agent_type = LLMAgentType(conversation_db_obj.get("agent_type", LLMAgentType.AGENT_TYPE_CHAT.value))
            elif raw_agent_type == ConversationType.INSIGHT_FOLLOW_UP_CONVERSATION.value:
                agent_type = LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP
            # Default case
            else:
                agent_type = LLMAgentType.AGENT_TYPE_CHAT

            if agent_type is None:
                logger.warning(f"chat_controller.llm_request | Invalid agent type: {raw_agent_type}")
                raise HTTPException(status_code=406, detail=ChatAppUserMessages.INVALID_CONVERSATION_TYPE.value)

            is_user_feedback = False
            user_question = prompt

            # @insight not allowed for insight follow up
            if agent_type == LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP:
                if prompt.lower().startswith("@insight"):
                    logger.warning(f"chat_controller.llm_request | New Insight not allowed for insight follow up")
                    raise HTTPException(status_code=406, detail="New Insight not allowed for insight follow up")

            if not (
                prompt.lower().startswith("@insight")
                or prompt.lower().startswith("@suggestion")
                or prompt.lower().startswith("@feedback")
                or prompt.lower().startswith("@thought_feedback")
            ):
                # generating next suggestions for this user
                if GENERATE_QUESTION_SUGGESTIONS_FOR_USER == "enabled":
                    self.user_service = UserService(user_info["id"])
                    self.thread_pool.submit(self.user_service.generate_suggested_questions, question=prompt)

            # Handle insight prompt
            if prompt.lower().startswith("@insight"):
                conversation_type = ConversationType.INSIGHT_GEN_CONVERSATION.value
                self.conversation_service.set_conversation_type(conversation_id, conversation_type)
                agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
                prompt = {"query": user_question, "initial_data": ""}
                conversation_db_obj["type"] = ConversationType.INSIGHT_GEN_CONVERSATION.value
                insight_report_id = self.insight_report_service.create_insight_report_draft(
                    insight_id=conversation_id,
                    userId=user_info["id"],
                    user_email=user_info["email"],
                    query=prompt["query"],
                )
                logger.info(
                    f"Insight report id: {insight_report_id} is drafted for conversation_id/insight_id: {conversation_id}"
                )
            # Handle feedback prompt
            elif prompt.lower().startswith("@feedback"):
                is_user_feedback = True
            # For testing: Handle insight_feedback prompt
            elif prompt.lower().startswith("@thought_feedback"):
                # Break the hypothesis_key, observation_key and comment by :
                prompt = prompt.replace("@thought_feedback", "")
                hypothesis_id, thought_index, comment = prompt.split(":")
                conversation_type = ConversationType.INSIGHT_GEN_CONVERSATION.value
                self.conversation_service.set_conversation_type(conversation_id, conversation_type)
                agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
                prompt = {
                    "query": "",
                    "initial_data": "",
                    "feedback": {
                        "hypothesis_id": hypothesis_id,
                        "thought_chain_index": thought_index,
                        "comment": comment,
                    },
                }
                conversation_db_obj["type"] = ConversationType.INSIGHT_GEN_CONVERSATION.value

            user_id = user_info["id"]

            # Convert request_session_type string to SessionType enum
            request_session_type_enum = SessionType.ANALYSIS  # Default value
            if request_session_type:
                for session_type in SessionType:
                    if session_type.value == request_session_type:
                        request_session_type_enum = session_type
                        break

            if request_session_type_enum == SessionType.USER_REACTION:
                user_question = "Thanks for the feedback, we are updating the knowledge..."
                # save the user reaction to the provided session id
                self.conversation_service.save_session_user_reaction(conversation_id, request.reactionInfo)

                # if user reaction is thumbs down, then return since we don't have implementation yet for it #TODO:
                if request.reactionInfo.reaction == UserReaction.THUMBS_DOWN.value:
                    raise HTTPException(status_code=406, detail="User reaction thumbs down is not supported yet")

            elif request_session_type_enum == SessionType.ADD_TO_DASHBOARD:
                user_question = "We are updating the insight dashboard..."
                self.conversation_service.save_add_to_dashboard_session(conversation_id, request.dashboardInsightInfo)

            # create new session
            session_db_res = self.conversation_service.initialize_session_in_db(
                conversation_id, request_session_type, user_id, user_question
            )
            if not session_db_res.success:
                raise HTTPException(status_code=406, detail=session_db_res.message)

            session_id = session_db_res.data
            logger.info(
                f"ChatController.llm_request | New session created: {session_id} for conversation: {conversation_id}"
            )
            session_instance = Session(conversation_id, session_id, session_type=request_session_type_enum)

            # get session history related to conversation
            history_messages = self.conversation_service.load_message_history(conversation_id, user_id)

            # create new conversation and pass it to llm agent thread created
            conversation_instance = Conversation(
                conversation_id,
                session_id,
                user_question,
                user_id,
                len(history_messages) > 0,
                agent_type,
                request.uploads,
            )

            global_memory.active_conversations[str(conversation_id)] = conversation_instance

            # update conversation title flag for followed up insight question
            if conversation_db_obj.get("type") == ConversationType.INSIGHT_GEN_CONVERSATION.value:
                if not conversation_db_obj.get("name", "").startswith("@ Insight"):
                    conversation_instance.is_conversation_title_required = True

            self.conversation_service.set_conversation_data(conversation_db_obj, conversation_instance)
            conversation_instance.active_session = session_instance
            conversation_instance.message_history = history_messages
            conversation_instance.active_session.user_info = user_info
            if request_session_type_enum == SessionType.ADD_TO_DASHBOARD:
                conversation_instance.active_session.dashboard_pin_request = request.dashboardInsightInfo
            # Feedback is valid only for followup questions
            if len(history_messages) > 0:
                conversation_instance.is_user_feedback = is_user_feedback

            # update conversation db
            self.conversation_service.update_conversation(conversation_instance, session_id, user_id)

            # handle empty content request which hit when file upload
            if request.isFileUpload and not prompt:
                msg = "Please feel free to ask any questions you might have regarding the content of your uploaded documents. I am here to assist you with any information or clarifications you need"
                conversation_instance.active_session.add_section()
                conversation_instance.add_to_history({"role": "assistant", "content": str(msg)}, False, True)
                conversation_instance.active_session.add_to_queue(msg, "assistant", ConversationStatus.IN_PROGRESS)
                conversation_instance.set_active_session_llm_status(ConversationLLMStatus.LLM_FINISHED)
                conversation_instance.is_conversation_title_required = False

            # if is_needed_adding_insight_data:
            #     self.conversation_service.add_insight_data_to_history(conversation_instance, request.insight_board_id)

            base_log_path = ""
            # TODO : Request should be first handled by the orchestration (master) agent
            # if agent_type == LLMAgentType.AGENT_TYPE_INSIGHT:
            #     agent_target = self.insight_agent.run
            #     base_log_path = "insight_" + conversation_id + "/"
            #     result = self.thread_pool.map(
            #         agent_target, [prompt], [conversation_instance], [session_id], [base_log_path]
            #     )
            #     return StreamingResponse(session_instance.get_stream(session_id), media_type="text/event-stream")

            # conversation_instance.persist_and_stream_handler(
            #     block_id=f"{conversation_instance.active_session.session_id}.{FrontendBlockType.USER_QUESTION.suffix_value}",
            #     block_data=user_question,
            #     block_type=FrontendBlockType.USER_QUESTION,
            #     block_tab_types=[FrontendTabContentType.QUERY],
            # )

            self.thread_pool.map(self.master_agent.run, [conversation_instance], [base_log_path], [user_info])
            # return StreamingResponse(session_instance.get_stream(session_id), media_type="text/event-stream")
            return StreamingResponse(session_instance.dequeue(), media_type="text/event-stream")

            # agent_target = self.chat_agent.run
            # if agent_type == LLMAgentType.AGENT_TYPE_INSIGHT:
            #     agent_target = self.insight_agent.run
            #     base_log_path = "insight_" + conversation_id + "/"

            # result = self.thread_pool.map(
            #     agent_target, [prompt], [conversation_instance], [session_id], [base_log_path]
            # )

            # """
            # agent_thread = threading.Thread(
            #     target=agent_target,
            #     args=(prompt, conversation_instance, session_id),
            # )
            # agent_thread.start()
            # """
            # # return streaming data to front end
            # return StreamingResponse(session_instance.get_stream(session_id), media_type="text/event-stream")

        except Exception as e:
            logger.error(f" Chat | ChatController.llm_request | N/A | Error: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail=str(e))

    @router.get("/api/conversation/{conversation_id}/reconnect")
    async def reconnect_conversation(
        self,
        conversation_id: str,
        global_memory: GlobalMemoryService = Depends(get_global_memory),
        is_reset_required: bool = False,
    ):
        """
        Description: Reconnects the stream of a conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - global_memory: GlobalMemoryService, the global memory service.
        """
        if conversation_id in global_memory.active_conversations:
            conversation_instance: Conversation = global_memory.active_conversations[conversation_id]

            # NOTE: is_reset_required is a temporary solution, we cannot handle case of network reconnection retry happen even when in a manual(from history) reconnect attempt
            if is_reset_required:
                conversation_instance.active_session.clear_queue()  # clear queue since existing queue is sent from the history API

            return StreamingResponse(
                conversation_instance.active_session.dequeue(),
                media_type="text/event-stream",
            )
        else:
            logger.error(
                f"ChatController.reconnect_conversation | Chat_id {conversation_id} | Unable to reconnect conversation. Conversation not found in global_memory"
            )
            raise HTTPException(
                status_code=406, detail=ChatAppUserMessages.CHAT_CAN_NOT_RECONNECT_CONVERSATION_NOT_IN_MEMORY.value
            )

    @router.post("/api/conversation/stop")
    async def stop_active_conversation(
        self,
        request: StopConversationRequest,
        global_memory: GlobalMemoryService = Depends(get_global_memory),
        user_info: dict = Depends(get_current_user),
    ):
        conversation_id = request.id
        logger.info(f"Stop signal from user: {user_info} to stop active session for conversation: {conversation_id}")
        if conversation_id in global_memory.active_conversations:

            conversation_instance = global_memory.active_conversations[conversation_id]

            global_memory.active_conversations[conversation_id].active_session.section_status = (
                SectionStatus.STOPPED.value
            )

            global_memory.active_conversations[conversation_id].stop_stream_when_user_stopped()

            # kill the running python processes
            session = global_memory.active_conversations[conversation_id].active_session
            if hasattr(session, "process_ids"):
                for process_id in session.process_ids:
                    process_id_log.info(
                        f"ChatController | chat_id {conversation_id} | Process ID: {process_id} | Action: killed from session"
                    )
                    logger.info(f"ChatController | Chat_id {conversation_id} | Killing process: {process_id}")
                    os.kill(process_id, signal.SIGTERM)

                    # remove from the active session
                    session.process_ids.remove(process_id)

            # log the active session ids and active conversations
            process_id_log.info(f"ChatController | Active sessions: {global_memory.active_conversations.keys()}")

            # if conversation is an insight, then mark it as user stopped
            if conversation_instance.agent_type == LLMAgentType.AGENT_TYPE_INSIGHT:
                self.conversation_service.stop_insight(str(conversation_id))

            self.conversation_service.stop_conversation(
                str(conversation_id)
            )  # marked conversation is stopped in conversation collection
            return {"success": True}
        else:
            logger.error(
                f"ChatController.stop_active_conversation | Chat_id {conversation_id} | Unable to stop conversation. Conversation not found in global_memory"
            )
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)

    @router.post("/api/conversation/create")
    async def create_conversation(
        self, request: CreateConversationRequest, user_info: dict = Depends(get_current_user)
    ):
        """
        Description: Creates a new chat conversation.

        Parameters:
            - request: CreateConversationRequest, contains parent conversation id.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: Result of creating a new conversation, containing relevant details like success or not.
        """

        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        # Check if parent conversation id is provided. If so we create a child conversation and return
        if request.parent_conversation_id:
            # Load the parent conversation instance
            parent_conversation_instance = self.conversation_service.load_conversation(
                request.parent_conversation_id, user_info["id"]
            )
            if not parent_conversation_instance:
                logger.error(
                    f"ChatController.create_conversation | Parent conversation not found: {request.parent_conversation_id}"
                )
                raise HTTPException(status_code=406, detail=ChatAppUserMessages.CHAT_CAN_NOT_FIND.value)
            child_conversation, session_id = self.conversation_service.create_conversation_and_session(
                user_info["id"],
                parent_conversation_instance.user_inputs[0],
                LLMAgentType.AGENT_TYPE_CHAT,
                parent_conversation=parent_conversation_instance,
                is_hide_from_end_user=True,
                is_copy_agent_states=True,
            )
            logger.info(
                f"ChatController.create_conversation | Created child conversation: {child_conversation.chat_id} for parent conversation: {request.parent_conversation_id}"
            )
            # Save the child conversation in DB so it will have the parent's history
            child_conversation.save(logger)
            # change conversation status again to queued to mark it as fresh
            self.conversation_service.update_conversation_status(
                child_conversation.chat_id, ConversationStatus.QUEUED.value
            )

            return {"isSuccess": True, "conversationId": child_conversation.chat_id, "sessionId": session_id}

        userId = user_info["id"]
        user_name = user_info.get("name", "")
        res = self.conversation_service.add_new_conversation(
            userId, user_name, ConversationType.CHAT_BOT_CONVERSATION.value
        )
        return res

    @router.post("/api/conversation/list")
    async def get_conversation_list(
        self,
        request: GetConversationListRequest,
        user_info: dict = Depends(get_current_user),
        timeZoneOffset: int = Header(default=0),
    ):
        """
        Description: Retrieves a list of chat conversations based on the specified request parameters.

        Parameters:
            - request: GetConversationListRequest, contains parameters for retrieving conversation list.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - list: List of chat conversations with relevant details.
        """

        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_info = user_info

        pageIndex = request.pageIndex
        pageSize = request.pageSize
        searchKey = request.searchKey or ""
        isFavourite = request.isFavourite if request.isFavourite is not None else False
        date = request.date or {}
        status = request.status
        conversationType = request.conversationType

        res = self.conversation_service.get_conversation_list(
            pageIndex, pageSize, searchKey, isFavourite, user_info, date, status, conversationType, timeZoneOffset
        )
        return res

    @router.get("/api/conversation/{insight_id}/insight-report/shared-users")
    async def get_insight_report_shared_users(
        self,
        insight_id: str,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Retrieves the list of users with shared status for a given insight report.

        Parameters:
            - insight_id: str, the unique identifier for the insight report.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary containing the list of users with shared status.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        team_id = user_info["teamId"]
        user_id = user_info["id"]

        res = self.conversation_service.get_insight_report_shared_users(insight_id, team_id, user_id)
        return res

    @router.post("/api/conversation/{insight_id}/insight-report/shared-users/update")
    async def update_insight_report_shared_users(
        self,
        insight_id: str,
        shared_users_list: InsightReportSharedUsersRequest,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Updates the list of shared users for a given insight report.

        Parameters:
            - insight_id: str, the unique identifier for the insight report.
            - shared_users_list: InsightReportSharedUsersRequest, the list of users to share the report with.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary with success status and message.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        result = self.conversation_service._add_insight_report_shared_users(
            insight_id, shared_users_list.users, user_info
        )

        if result.success:
            return result.data
        else:
            raise HTTPException(status_code=406, detail=result["message"])

    @router.post("/api/conversation/{insight_id}/insight-report/update-section")
    async def update_insight_report_section(
        self,
        insight_id: str,
        update_data: UpdateInsightReportSectionRequest,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Updates a specific section in the insight report.

        Parameters:
            - insight_id: str, the unique identifier for the insight.
            - update_data: UpdateInsightReportSectionRequest, the data to update in the report.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary with success status and message.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        result = self.insight_report_service.update_insight_report_section(insight_id, update_data)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=406, detail=result["message"])

    @router.post("/api/conversation/{insight_id}/insight-report/delete-section")
    async def delete_insight_report_section(
        self,
        insight_id: str,
        delete_data: DeleteInsightReportSectionRequest,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Deletes or resets a specific section in the insight report.

        Parameters:
            - insight_id: str, the unique identifier for the insight.
            - delete_data: DeleteInsightReportSectionRequest, the data specifying what to delete.
                - id: Section ID (e.g., ReportSectionId.KEY_FINDINGS, ReportSectionId.ACTIONABLE_RECOMMENDATIONS)
                - subId: Subsection ID (e.g., ReportSubSectionId.SHORT_DESCRIPTION, ReportSubSectionId.MAIN_FACTS)
                - index: Index of item to delete in array (-1 to reset entire array or field)
                - mainIndex: For visualizations: index of the main visualization object
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary with success status and message.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        result = self.insight_report_service.delete_insight_report_section(insight_id, delete_data)

        if result["success"]:
            return result
        else:
            raise HTTPException(status_code=406, detail=result["message"])

    @router.get("/api/conversation/{conversation_type}/{id}/message/history")
    async def get_message_list(
        self, conversation_type: Union[int, str], id: str, user_info: dict = Depends(get_current_user)
    ):
        """
        Description: Retrieves the message history for a given conversation.

        Parameters:
            - id: str, the unique identifier (chat id or agent id)
            - user_info: dict, user information obtained from the authentication token.
            - conversation_type: ConversationType, the type of conversation

        Returns:
            - list: A formatted list of messages in the conversation's history.
                Each message in the list is a dictionary with relevant details.
        """
        # if conversation type is digit, covert it to int
        if int(conversation_type):
            conversation_type = int(conversation_type)

        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        if (
            conversation_type == ConversationType.INSIGHT_FOLLOW_UP_CONVERSATION.value
        ):  # ConversationType is deprecated
            conversation_type = LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP.value

        if conversation_type == ConversationType.CHAT_BOT_CONVERSATION.value:
            conversation_id = id
        elif conversation_type == ConversationType.AGENT_SETUP_CONVERSATION.value:
            agent = self.agent_service.get_agent(id)
            conversation_id = agent["conversationId"]
        elif conversation_type == LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP.value:
            conversation_id = id
        else:
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.INVALID_CONVERSATION_TYPE.value)

        if not conversation_id:
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.INVALID_CONVERSATION_ID.value)

        res = self.conversation_service.get_message_list(
            conversation_id, user_info["id"], userType=user_info.get("userType")
        )
        if res and isinstance(res, list) and len(res) > 0:
            self.conversation_service.set_conversation_viewed(conversation_id, True)  # conversation mark as viewed
            return self.conversation_service.format_message_list(res)
        else:
            return []

    # v2 api for getting the history blocks
    @router.get("/api/conversation/{conversation_id}/history")
    async def get_conversation_history_blocks(
        self,
        conversation_id: str,
        sid: str = Query(None, description="Optional session ID to filter history blocks"),
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Retrieves the history blocks for a given conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - session_id: str, optional session ID to filter history blocks for a specific session.
            - user_info: dict, user information obtained from the authentication token.
        """
        res = self.conversation_service.get_conversation_history_blocks(
            conversation_id, user_info.get("id"), user_info.get("userType"), sid
        )

        if res.success:
            return res.data
        else:
            raise HTTPException(status_code=406, detail=res.message)

    @router.get("/api/conversation/{conversation_id}/history/v2")
    async def get_conversation_history_blocks_v2(
        self,
        conversation_id: str,
        sid: str = Query(None, description="Optional session ID to filter history blocks"),
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Retrieves the history blocks for a given conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - session_id: str, optional session ID to filter history blocks for a specific session.
            - user_info: dict, user information obtained from the authentication token.
        """
        return self.conversation_service.get_conversation_history_blocks_v2(
            conversation_id, user_info.get("id"), user_info.get("userType"), sid
        )

    @router.get("/api/conversation/{id}/message/insightReport")
    async def get_insight_report(self, id: str, user_info: dict = Depends(get_current_user)):
        """
        Description: Retrieves the Insight Report for a given conversation.

        Parameters:
            - id: str, the unique identifier for the conversation.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary representing the Insight report.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)
        return self.conversation_service.get_insight_report(id, user_info.get("id"), user_info.get("userType"))

    @router.get("/api/conversation/{id}/insight-report")
    async def get_insight_report_v2(self, id: str, user_info: dict = Depends(get_current_user)):
        """
        Description: Retrieves the Insight Report for a given conversation/insight.

        Parameters:
            - id: str, the unique identifier for the conversation/insight.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary representing the Insight report.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)
        self.conversation_service.set_conversation_viewed(id, True)
        res = self.insight_report_service.get_insight_report_v2(id, user_info.get("id"), user_info.get("userType"))

        return make_json_compatible(res)

    @router.get("/api/conversation/{id}/hypothesis-report")
    async def get_hypothesis_report(self, id: str, user_info: dict = Depends(get_current_user)):
        """
        Description: Retrieves the Hypothesis report for a given hypothesis.

        Parameters:
            - id: str, the unique identifier for the hypothesis.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary representing the Hypothesis report.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"get_hypothesis_report | Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        res = self.conversation_service.get_hypothesis_report(id, user_info.get("id"), user_info.get("userType"))

        return make_json_compatible(res)

    @router.get("/api/conversation/{conversation_id}/title")
    async def get_conversation_title(self, conversation_id: str, user_info: dict = Depends(get_current_user)):
        """
        Description: get a chat conversation title based on the conversation id.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - json object with title.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)
        user_id = user_info["id"]
        logger.info(f"Get conversation title start for conversation id {conversation_id} and user {user_id}")

        conversation_db_obj = self.conversation_service.get_conversation_data(conversation_id)

        if not conversation_db_obj:
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.INVALID_CONVERSATION_ID.value)

        conversation_title = conversation_db_obj.get("name", "")
        conversation_type = conversation_db_obj.get("type", 1)
        conversation_is_favourite = conversation_db_obj.get("isFavourite", False)

        hypothesis_list = []
        question = None

        # Remove the @ Insight | pipeline for insight report viewing in history tab
        if conversation_type == ConversationType.INSIGHT_GEN_CONVERSATION.value and conversation_title.startswith(
            "@ Insight | "
        ):
            conversation_title = conversation_title.replace("@ Insight | ", "", 1)
            hypothesis_list = self.conversation_service.get_hypothesis_list(
                conversation_id, user_id, user_info.get("userType")
            )
            # insight report record to get insight query
            insight_report = self.insight_report_service.get_insight_report(
                conversation_id, user_id, user_info.get("userType")
            )
            if insight_report:
                question = insight_report.get("query", "Not Available...")
                question = question.replace("@Insight | ", "", 1)

        if conversation_type == LLMAgentType.AGENT_TYPE_INSIGHT_FOLLOW_UP.value:
            conversation_type = ConversationType.INSIGHT_FOLLOW_UP_CONVERSATION.value  # ConversationType is deprecated

        return make_json_compatible(
            {
                "title": conversation_title,
                "type": conversation_type,
                "isFavourite": conversation_is_favourite,
                "hypotheses": hypothesis_list,
                "question": question,
            }
        )

    @router.get("/api/conversation/{conversation_id}/delete")
    async def delete_conversation(self, conversation_id: str, user_info: dict = Depends(get_current_user)):
        """
        Description: Deletes a chat conversation based on the conversation id.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary indicating the success or failure of the operation.
                - isSuccess: True if the conversation was successfully deleted, False otherwise.
        """

        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)
        user_id = user_info["id"]
        logger.info(f"Conversation delete start for conversation id {conversation_id} and user {user_id}")

        return self.conversation_service.delete_conversation(conversation_id)

    @router.patch("/api/conversation/{conversation_id}/favourite")
    async def update_is_favourite(
        self, conversation_id: str, body: FavouriteUpdateRequest, user_info: dict = Depends(get_current_user)
    ):
        """
        Description: Updates the 'isFavourite' field of a conversation based on the conversation id.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - is_favourite: bool, the new value for the 'isFavourite' field (true/false).
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary indicating the success or failure of the operation.
                - is_success: True if the operation was successful, False otherwise.
        """

        # Validate user info and get user ID
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_id = user_info["id"]
        logger.info(
            f"Conversation 'isFavourite' update start for conversation id {conversation_id} and user {user_id}"
        )

        # Call service to update isFavourite
        return self.conversation_service.update_is_favourite(conversation_id, body.is_favourite)

    @router.post("/api/conversation/{conversation_id}/share")
    async def share_conversation(self, conversation_id: str, user_info: dict = Depends(get_current_user)):
        """
        Description: Creates a new chat conversation.

        Parameters:
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: Result of creating a new conversation, containing relevant details like success or not.
        """

        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(
                f"Share conversation | ChatController.share_conversation | N/A | {ChatAppUserMessages.TEAM_NOT_EXIST.value}"
            )
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_id = user_info["id"]

        logger.info(
            f"Share conversation | ChatController.share_conversation | {user_id} | Share conversation {conversation_id} request by user {user_id}"
        )

        response = self.conversation_service.share_conversation(conversation_id, user_id, user_info["userType"])
        if response.success == True:
            return response.data
        else:
            raise HTTPException(status_code=406, detail=response.message)

    @router.post("/api/conversation/{conversation_id}/shared-users/add")
    async def add_conversation_shared_users(
        self,
        conversation_id: str,
        request: ConversationAddSharedUsersRequest,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Adds new users to the shared_users list of a conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - request: ConversationSharedUsersRequest, contains the list of user IDs to add.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary with success status and message.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_id = user_info["id"]
        logger.info(
            f"Add shared users | ChatController.add_conversation_shared_users | {user_id} | Add shared users to conversation {conversation_id} request by user {user_id}"
        )

        result = self.conversation_service.add_conversation_shared_users(conversation_id, request.user_ids, user_info)

        if result.success:
            return result.data
        else:
            raise HTTPException(status_code=406, detail=result.message)

    @router.post("/api/conversation/{conversation_id}/shared-users/remove")
    async def remove_conversation_shared_users(
        self,
        conversation_id: str,
        request: ConversationRemoveSharedUsersRequest,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Removes users from the shared_users list of a conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - request: ConversationRemoveSharedUsersRequest, contains the list of user IDs to remove.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary with success status and message.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_id = user_info["id"]
        logger.info(
            f"Remove shared users | ChatController.remove_conversation_shared_users | {user_id} | Remove shared users from conversation {conversation_id} request by user {user_id}"
        )

        result = self.conversation_service.remove_conversation_shared_users(
            conversation_id, request.user_ids, user_info
        )

        if result.success:
            return result.data
        else:
            raise HTTPException(status_code=406, detail=result.message)

    @router.get("/api/conversation/{conversation_id}/shared-users")
    async def get_conversation_shared_users(
        self,
        conversation_id: str,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Retrieves the list of users with shared status for a given conversation.

        Parameters:
            - conversation_id: str, the unique identifier for the conversation.
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - dict: A dictionary containing the list of users with shared status.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        team_id = user_info["teamId"]
        user_id = user_info["id"]

        res = self.conversation_service.get_conversation_shared_users(conversation_id, team_id, user_id)
        if res.success:
            return res.data
        else:
            raise HTTPException(status_code=406, detail=res.message)

    @router.get("/api/conversation/{conversation_id}/download_log_files")
    async def download_log_files(self, conversation_id: str, user_info: dict = Depends(get_super_admin_user)):
        """
        Description: Function to download log files when the chat_id is given

        Parameters:
            - conversation_id: str
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - zip file - zip file containing two logs files from chat and metalake
        """

        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(
                f"Download log files | ChatController.download_log_files | N/A | {ChatAppUserMessages.TEAM_NOT_EXIST.value}"
            )
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_id = user_info["id"]

        logger.info(
            f"Download log files | ChatController.download_log_files | {user_id} | Download logs for conversation {conversation_id} request by user {user_id}"
        )

        response = self.conversation_service.download_log_files(conversation_id=conversation_id)

        if response.success == True:
            return StreamingResponse(
                content=response.data.get("zip_file_stream"),
                media_type="application/zip",
                headers={"Content-Disposition": f"attachment; filename=chat_{conversation_id}.zip"},
            )
        else:
            raise HTTPException(status_code=406, detail=response.message)

    @router.get("/api/conversation/suggested_questions")
    async def get_suggested_questions(self, user_info: dict = Depends(get_current_user)):
        """
        Description: Retrieves suggested questions for the user.

        Parameters:
            - user_info: dict, user information obtained from the authentication token.

        Returns:
            - list: A list of suggested questions if the operation was successful.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(f"Could not find the team")
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        user_id = user_info["id"]
        user_service = UserService(user_id)

        response = user_service.get_suggested_questions()

        if response.success == True:
            return response.data
        else:
            raise HTTPException(status_code=406, detail=response.message)

    @router.get("/api/conversation/recent_analysis")
    async def get_recent_analysis(
        self,
        user_info: dict = Depends(get_current_user),
    ):
        """
        Description: Retrieves the recent conversation analysis.

        Returns:
            - dict: A dictionary of recent conversation analysis if the operation was successful.
        """
        if not user_info or "teamId" not in user_info or user_info["teamId"] is None:
            logger.error(
                f"get recent analysis| ChatController.get_recent_analysis | N/A | {ChatAppUserMessages.TEAM_NOT_EXIST.value}"
            )
            raise HTTPException(status_code=406, detail=ChatAppUserMessages.TEAM_NOT_EXIST.value)

        response = self.conversation_service.get_recent_analysis(user_info)
        if response.success == True:
            return response.data
        else:
            raise HTTPException(status_code=406, detail=response.message)

    # Used just for testing
    def prepare_test_attachments(self, conversation_id):
        import shutil
        import os
        import pathlib

        # Skip if the temp folder doesn't exist
        if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../storage/temp")):
            return
        # source location: storage/temp
        # destination location: storage/public/{conversation_instance.chat_id}/attachments
        source_path = pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../storage/temp")
        destination_path = pathlib.Path.joinpath(
            pathlib.Path(__file__).parent.resolve(), f"../storage/public/{conversation_id}/attachments"
        )
        if not os.path.exists(destination_path):
            os.makedirs(destination_path)
        for file_name in os.listdir(source_path):
            shutil.copy(os.path.join(source_path, file_name), os.path.join(destination_path, file_name))
