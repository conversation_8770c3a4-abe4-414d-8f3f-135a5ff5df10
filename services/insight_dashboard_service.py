"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class InsightDashboardService
* @description This class is responsible for handling the Insight Dashboard related operations.
* <AUTHOR>
"""

import copy
from datetime import datetime, timedelta, timezone
import json
from logging import Logger
import os
import pathlib
import traceback
from bson import ObjectId
import pymongo
import pytz
import pandas as pd
from services.insight_app.insight_board_manager_service import InsightBoardManagerService
from services.orchestration_agent.iterative_problem_solver_agent import IterativeProblemSolverAgent
from utils.misc_utils import csv_to_markdown, format_number, get_month_number
from utils.schedule import ScheduleTimeCalculator
from services.global_memory_service import GlobalMemoryService
from services.insight_app.insight_generator_service import LlmInsightAgent
from services.insight_app.insight_report_service import InsightReportService
from models.conversation import Conversation
from models.session import Session
from services.llm_chat_service import LlmChatAgent
from utils.constant import (
    MASTER_REPORT_JSON_TEMPLATE,
    ConversationType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    InsightReportStatus,
    LLMAgentType,
    MessageContentType,
    SessionType,
    UserType,
)
from databases.mongo_manager import MongoDBmanager
from services.conversation_service import ConversationService
from services.sso_user_service import SsoUserService
from models.schemas.responses import GeneralResponse
import concurrent.futures

from utils.logger import get_debug_logger

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))

main_logger = get_debug_logger(
    "insight_dashboard_service", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log")
)


class InsightDashboardService:
    def __init__(self):

        self.model_name = os.getenv("MODEL")

        self.llm_chat_agent = LlmChatAgent()
        self.llm_insight_agent = LlmInsightAgent(True)
        self.insight_report_service = InsightReportService()
        self.insight_board_manager_service = InsightBoardManagerService(self.model_name)
        self.conversation_service = ConversationService()
        self.global_memory = GlobalMemoryService()
        self.insight_dashboard_collection = MongoDBmanager("InsightDashboard")
        self.insight_dashboard_history_collection = MongoDBmanager("InsightDashboardHistory")
        self.hypothesis_collection = MongoDBmanager("Hypothesis")
        self.sessions_collection = MongoDBmanager("Sessions")
        self.insight_report_collection = MongoDBmanager("InsightReport")
        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        self.sso_user_service = SsoUserService()

        self.timezone_string = str(os.getenv("TIME_ZONE", "America/Winnipeg"))
        self.timezone = pytz.timezone(self.timezone_string)
        self.problem_solver_agent = IterativeProblemSolverAgent(
            ""
        )  # Used for followup when rendering main graph fails
        # Load system instruction for kpi further analysis
        with open(
            "instructions/orchestration_agent/iterative_problem_solver/kpi_analysis_instructions.txt",
            "r",
            encoding="utf-8",
        ) as file:
            kpi_analysis_system_instruction = file.read()
            self.kpi_analysis_agent = IterativeProblemSolverAgent("", system_prompt=kpi_analysis_system_instruction)

    def add_analysis_to_dashboard_check(
        self,
        conversation_id,
        session_id,
        user_info,
    ) -> GeneralResponse[dict]:
        """
        Check if an analysis is eligible to be added to the Insight Dashboard

        Args:
            conversation_id (str): The unique identifier of the conversation
            session_id (str): The unique identifier of the session within the conversation
            user_info (dict): Dictionary containing user information with 'id' and 'name' keys

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains isEligibleToPin and message if successful
                - message (str): Error message if operation failed
        """

        # find conversation
        conversation_db_obj = self.conversation_service.get_conversation_data(conversation_id)

        is_eligible_to_pin = True
        response_message = "success"

        if conversation_db_obj is None:
            main_logger.error(
                f"InsightDashboardService.add_analysis_to_dashboard_check | Conversation not found for id: {conversation_id}"
            )
            is_eligible_to_pin = False
            response_message = "Unable to find the conversation."

            return GeneralResponse(
                success=True,
                message=response_message,
                data={"isEligibleToPin": is_eligible_to_pin, "message": response_message},
            )

        # get sessions list from session collection
        sessions_list = self.sessions_collection.get_documents(
            {"conversation_id": ObjectId(conversation_id)},
            sort_criteria_list=[("_id", pymongo.ASCENDING)],
        )

        if len(sessions_list) == 0:
            main_logger.error(
                f"InsightDashboardService.add_analysis_to_dashboard_check | Sessions not found for conversation id: {conversation_id}"
            )
            is_eligible_to_pin = False
            response_message = "Unable to find the sessions."

            return GeneralResponse(
                success=True,
                message=response_message,
                data={"isEligibleToPin": is_eligible_to_pin, "message": response_message},
            )

        # check at least one media type image content exist in session answer list
        is_image_content_exist = False
        for session in sessions_list:
            session_answer_list = session.get("session_answer_list", [])
            for answer in session_answer_list:
                if answer.get("content_type") == MessageContentType.MEDIA.value:
                    for content in answer.get("content", []):
                        if content.get("mediaType") == "image":
                            is_image_content_exist = True
                            break  # break the inner loop
                if is_image_content_exist:
                    break  # break the middle loop
            if is_image_content_exist:
                break  # break the outer loop

            # if given session id == current session id, then break since we only consider up to given session id
            if str(session["_id"]) == session_id:
                break

        if not is_image_content_exist:
            main_logger.error(
                f"InsightDashboardService.add_analysis_to_dashboard_check | No image content found for conversation id: {conversation_id}"
            )
            is_eligible_to_pin = False
            response_message = "Only questions with visual insights can be added to the Insights Board. Please try asking a question that generates a visual output."

        return GeneralResponse(
            success=True,
            message=response_message,
            data={"isEligibleToPin": is_eligible_to_pin, "message": response_message},
        )

    def add_analysis_to_dashboard(
        self,
        parent_conversation: Conversation,
        pinned_session_id,
        frequencyInfo,
        user_info,
    ) -> GeneralResponse[dict]:
        """Add an analysis to the insight dashboard

        Args:
            conversation_id (str): The unique identifier of the conversation
            session_id (str): The unique identifier of the session within the conversation
            audience (str): The target audience for this analysis
            frequency (str): How often this analysis should be updated (e.g., 'daily', 'weekly')
            user_info (dict): Dictionary containing user information with 'id' and 'name' keys

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains conversationId, sessionId, and graphs if successful
                - message (str): Error message if operation failed
        """
        try:
            parent_conversation.persist_and_stream_handler(
                block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.ADD_TO_DASHBOARD_TASK.value,
                block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
            )

            conversation_id = parent_conversation.chat_id
            main_logger.info(
                f"InsightDashboardService.add_analysis_to_dashboard | Adding analysis to dashboard for conversation id: {conversation_id}"
            )
            # Create log handle for insight board for this conversation id
            base_log_path = "insight_board_" + conversation_id + "/"
            if not os.path.exists(pathlib.Path(f"./logs/{base_log_path}")):
                os.makedirs(pathlib.Path(f"./logs/{base_log_path}"))
            insight_log = get_debug_logger(
                f"insight_board_{conversation_id}",
                f"./logs/{base_log_path}generate_insight_dashboard.log",
            )

            pinned_session_db_obj = self.sessions_collection.get_one_document(
                {"_id": ObjectId(pinned_session_id)},
            )

            if pinned_session_db_obj is None:
                insight_log.error(
                    f"InsightDashboardService.add_analysis_to_dashboard | Pinned session not found for id: {pinned_session_id}"
                )
                return GeneralResponse(success=False, message="Unable to find the pinned session.")

            csv_data_with_user_input = []

            # Re-run the question if answer generated more than 12 hours ago
            if "updated_at" in pinned_session_db_obj and pinned_session_db_obj["updated_at"].replace(
                tzinfo=timezone.utc
            ) < (datetime.now(timezone.utc) - timedelta(hours=12)):
                insight_log.info(
                    f"InsightDashboardService.add_analysis_to_dashboard | Answer generated more than 12 hours ago. Re-generating answer for session id: {pinned_session_id}"
                )
                conversation_instance = self.conversation_service.load_conversation(conversation_id)
                if conversation_instance is None:
                    insight_log.error(
                        f"InsightDashboardService.add_analysis_to_dashboard | Conversation not found for id: {conversation_id}"
                    )
                    return GeneralResponse(success=False, message="Unable to find the conversation.")

                parent_conversation.persist_and_stream_handler(
                    block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.REFRESH_DASHBOARD_TASK.value,
                    block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
                )

                conversation_instance.is_continue_previous = True
                problem_solver_result = self.problem_solver_agent.execute_agent(
                    {
                        "user_request": "Regenerate the answer with data as of now in order to get the up-to-date values for KPI."
                    },
                    conversation_instance,
                    pinned_session_id,
                    insight_log,
                )
                if not problem_solver_result["is_success"]:
                    insight_log.error(
                        f"InsightDashboardService.add_analysis_to_dashboard | Failed to refresh answer for session id: {pinned_session_id}, message: {problem_solver_result['message']}"
                    )
                    return GeneralResponse(success=False, message="Failed to refresh answer")
                # Save the conversation to retain in DB for the followups
                conversation_instance.save(insight_log)
                # Get tabular data by reading CSV files as markdown for each table
                csv_data_list = self._get_csv_info_from_session_answer_list(
                    conversation_id, problem_solver_result["final_answer_list"]
                )
                csv_data_with_user_input.append(
                    {"user_input": parent_conversation.user_inputs[0], "csv_file_list": csv_data_list}
                )

            else:

                session_list = self.sessions_collection.get_documents(
                    {
                        "conversation_id": ObjectId(conversation_id),
                        "type": {"$in": [SessionType.ANALYSIS.value, SessionType.COMPLEX_ANALYSIS.value]},
                    },
                    sort_criteria_list=[("_id", pymongo.ASCENDING)],
                )

                if len(session_list) == 0:
                    insight_log.error(
                        f"InsightDashboardService.add_analysis_to_dashboard | Sessions not found for conversation id: {conversation_id}"
                    )
                    return GeneralResponse(success=False, message="Unable to find the sessions.")

                for session in session_list:
                    session_answer_list = session.get("session_answer_list", [])
                    user_input = session.get("user_input", "")
                    csv_file_list = self._get_csv_info_from_session_answer_list(conversation_id, session_answer_list)
                    csv_data_with_user_input.append({"user_input": user_input, "csv_file_list": csv_file_list})

            # generate visualization graph component
            render_result = self._render_main_graph(csv_data_with_user_input, frequencyInfo, insight_log)
            # In case is_rendered is false, but followup question is present then re-try by sending followup question to the problem solver agent and then re-do the extraction
            # If is_rendered is false and followup question is not present, then log error and proceed
            if not render_result["is_rendered"] and render_result["followup_question"]:
                # Trigger followup from iterative problem solver agent and then re-do the extraction
                insight_log.debug(
                    f"InsightDashboardService.add_analysis_to_dashboard | Retrying main graph rendering with followup question: {render_result['followup_question']}"
                )
                parent_conversation.persist_and_stream_handler(
                    block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data="Retrying insight board generation...\n\n" + render_result["followup_question"],
                    block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )
                followup_render_result = self._render_main_graph_with_followup_question(
                    conversation_id,
                    pinned_session_id,
                    csv_data_with_user_input,
                    frequencyInfo,
                    render_result["followup_question"],
                    insight_log,
                )
                if followup_render_result["is_rendered"]:
                    insight_log.debug(
                        f"InsightDashboardService.add_analysis_to_dashboard | Successfully rendered main graph with followup question: {render_result['followup_question']}"
                    )
                    render_result = followup_render_result
                else:
                    insight_log.debug(
                        f"InsightDashboardService.add_analysis_to_dashboard | Failed to render main graph with followup question: {render_result['followup_question']}"
                    )
                    return GeneralResponse(success=False, message="Failed to render main graph with followup.")
            elif not render_result["is_rendered"]:
                insight_log.debug(
                    f"InsightDashboardService.add_analysis_to_dashboard | Failed to render main graph for session id: {pinned_session_id}"
                )
                return GeneralResponse(success=False, message="Failed to render main graph")

            kpi_param = render_result["kpi_param"]
            # Show the KPI name to the user
            parent_conversation.persist_and_stream_handler(
                block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=f"KPI: {kpi_param}",
                block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
            # create dashboard insight
            pinned_analysis = {
                "conversationId": ObjectId(conversation_id),
                "sessionId": ObjectId(pinned_session_id),
                "userIdList": [ObjectId(user_info["id"])],
                "userInfo": user_info,
                "userName": user_info["name"],
                "userEmail": user_info["email"],
                "initialTitle": kpi_param,
                "frequencyInfo": frequencyInfo,
                "csv_data_with_user_input": csv_data_with_user_input,
                "isRendered": render_result["is_rendered"],
                "renderedMainGraph": render_result["rendered_main_graph"],
                "createdAt": datetime.now(timezone.utc),
                "updatedAt": datetime.now(timezone.utc),
                "status": InsightReportStatus.DRAFT.value,
                "stats": render_result["change_dict"],
                "kpiParam": kpi_param,
            }
            insight_log.debug(
                f"InsightDashboardService.add_analysis_to_dashboard | Main graph rendered: {json.dumps(render_result['rendered_main_graph'], indent=2)}"
            )
            # Send stream message to user to notify that further kpi analysis is in progress
            parent_conversation.persist_and_stream_handler(
                block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.FURTHER_ANALYSIS_IN_PROGRESS.value,
                block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
            )
            res = self.insight_dashboard_collection.insert_one(pinned_analysis)
            dashboard_insight_id = str(res.inserted_id)
            insight_result = self.generate_insight_for_analysis(dashboard_insight_id, parent_conversation)

            if insight_result.success:
                # Update the status to finalized
                self._update_insight_dashboard_status(dashboard_insight_id, InsightReportStatus.FINALIZED)
                parent_conversation.persist_and_stream_handler(
                    block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.ADD_TO_DASHBOARD_SUCCESS.value,
                    block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
                )

            else:
                # Update the status to failed
                self._update_insight_dashboard_status(dashboard_insight_id, InsightReportStatus.FAILED)
                parent_conversation.persist_and_stream_handler(
                    block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.ADD_TO_DASHBOARD_FAILED.value,
                    block_tab_types=[FrontendTabContentType.TASKS, FrontendTabContentType.ANSWER],
                )

            navigation_url = f"{os.getenv('FRONTEND_URL')}/insight-board?id={str(dashboard_insight_id)}"
            markdown_navigation_url = f"[Click here to view the insight board]({navigation_url})"

            parent_conversation.persist_and_stream_handler(
                block_id=f"{parent_conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}.{str(dashboard_insight_id)}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=markdown_navigation_url,
                block_tab_types=[
                    FrontendTabContentType.FINAL_CONCLUSION,
                ],
            )

            return GeneralResponse(
                success=True,
                message="Analysis added to dashboard",
                data={"dashboardInsightId": dashboard_insight_id},
            )

        except Exception as e:
            insight_log.error(
                f"InsightDashboardService.add_analysis_to_dashboard | Error while adding analysis to dashboard: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def _update_insight_dashboard_status(self, dashboard_insight_id: str, status: InsightReportStatus):
        self.insight_dashboard_collection.update_one(
            {"_id": ObjectId(dashboard_insight_id)},
            {
                "$set": {
                    "status": status.value,
                    "updatedAt": datetime.now(timezone.utc),
                }
            },
        )

    def _render_main_graph(self, csv_data_with_user_input: list, frequencyInfo: dict, insight_log):
        insight_log.debug(
            f"InsightDashboardService._render_main_graph | Rendering main graph for csv_data_with_user_input: {csv_data_with_user_input}, frequencyInfo: {frequencyInfo}"
        )
        # generate visualization graph component
        insight_board_main_graph_render_response = self.insight_board_manager_service.insight_board_main_graph_render(
            csv_data_with_user_input, frequencyInfo
        )

        rendered_main_graph = {}
        followup_question = ""
        change_dict = {
            "currentValue": None,
            "previousValue": None,
            "pctChange": None,
            "currencySign": None,
            "statement": None,
        }
        kpi_param = ""
        if insight_board_main_graph_render_response.success:
            insight_board_main_graph_render_response_data = insight_board_main_graph_render_response.data
            if insight_board_main_graph_render_response_data.get("is_rendered", False):
                kpi_param = insight_board_main_graph_render_response_data.get("kpi_name", "")
                rendered_main_graph = insight_board_main_graph_render_response_data.get("visualization", {})
                # get visualization summary
                summary_values_dict = insight_board_main_graph_render_response_data.get("visualization_summary", {})

                current_value = summary_values_dict.get("current_value")
                previous_value = summary_values_dict.get("previous_value")

                # Check if values exist and are numeric
                if current_value is not None and previous_value is not None:
                    try:
                        current_value = float(current_value)
                        previous_value = float(previous_value)

                        # Handle division by zero
                        if previous_value != 0:
                            change_dict["pctChange"] = ((current_value - previous_value) / previous_value) * 100

                        change_dict["currentValue"] = current_value
                        change_dict["previousValue"] = previous_value
                        change_dict["currencySign"] = summary_values_dict.get("currency_sign")
                        change_dict["statement"] = summary_values_dict.get("statement")

                    except (ValueError, TypeError):
                        # Handle non-numeric values
                        insight_log.warning(
                            f"Non-numeric values encountered: current={current_value}, previous={previous_value}"
                        )
            else:
                # Send a followup to the original question with suggestion that renderable table data and graph should be generated according to desired frequency of KPI evaluation
                followup_question = (
                    insight_board_main_graph_render_response_data.get("reason")
                    + "\nProvide a response that can be rendered as a table and a visualization chart according to the desired frequency of KPI evaluation.\nDesired frequency of KPI evaluation: "
                    + json.dumps(frequencyInfo, indent=2)
                )
                insight_log.warning(
                    f"InsightDashboardService._render_main_graph | Failed to render main graph for csv_data_with_user_input: {csv_data_with_user_input},\
                     desired frequency: {json.dumps(frequencyInfo, indent=2)}, reason: {insight_board_main_graph_render_response_data.get('reason', '')}\
                        \nFollowup question: {followup_question}"
                )

        else:
            insight_log.error(
                f"InsightDashboardService.add_analysis_to_dashboard | Failed to render main graph, message: {insight_board_main_graph_render_response.message}"
            )
        return {
            "is_rendered": rendered_main_graph != {},
            "rendered_main_graph": rendered_main_graph,
            "change_dict": change_dict,
            "kpi_param": kpi_param,
            "followup_question": followup_question,
        }

    def _render_main_graph_with_followup_question(
        self,
        conversation_id: str,
        session_id: str,
        csv_data_with_user_input: list,
        frequencyInfo: dict,
        followup_question: str,
        chat_log,
    ):
        # Trigger followup from iterative problem solver agent
        # Create conversation and session instances from given conversation id and session id for followup using global_memory
        if conversation_id in self.global_memory.active_conversations:
            conversation = self.global_memory.active_conversations[conversation_id]
        else:
            conversation = self.conversation_service.load_conversation(conversation_id)
            self.global_memory.active_conversations[conversation_id] = conversation
            conversation.active_session = Session(conversation_id, session_id)
        conversation.is_continue_previous = True
        chat_log.debug(
            f"InsightDashboardService._render_main_graph_with_followup_question | Executing followup question: {followup_question}"
        )
        problem_solver_result = self.problem_solver_agent.execute_agent(
            {"user_request": followup_question}, conversation, session_id, chat_log
        )
        if problem_solver_result["is_success"]:
            # Save the conversation to retain in DB for the followups
            conversation.save(chat_log)
            # Append the csv data list with the "final_answer_list" in problem_solver_result
            followup_csv_list = self._get_csv_info_from_session_answer_list(
                conversation.chat_id, problem_solver_result["final_answer_list"]
            )
            csv_data_with_user_input.append({"user_input": followup_question, "csv_file_list": followup_csv_list})
            # Re-do the extraction with followup question response
            return self._render_main_graph(csv_data_with_user_input, frequencyInfo, chat_log)
        else:
            chat_log.error(
                f"InsightDashboardService._render_main_graph_with_followup_question | Failed to render main graph with followup question: {followup_question}, reason: {problem_solver_result.message}"
            )
            return {
                "is_rendered": False,
                "rendered_main_graph": {},
                "change_dict": {},
                "kpi_param": "",
                "followup_question": "",
            }

    def get_insight_Dashboard_report_pinned_users(
        self, insight_dashboard_id, team_id, user_id
    ) -> GeneralResponse[dict]:
        """Get the list of all users with adding the pinned status of the insight board report for a given insight dashboard ID

        Args:
            insight_dashboard_id (str): The unique identifier for the insight dashboard
            team_id (str): The unique identifier for the user
            user_id (str): The unique identifier for the user

        Returns:
            dict: A dictionary representing the list of all users with adding the pinned status of the insight board report
        """
        user_types = [UserType.USER_TYPE_AUDITOR.value, UserType.USER_TYPE_TEAM_ADMIN.value]
        # user_types = [1, 3]
        insight_dashboard_obj = self.insight_dashboard_collection.get_one_document(
            {"_id": ObjectId(insight_dashboard_id)}
        )

        if insight_dashboard_obj is None:
            main_logger.error(
                f"InsightDashboardService.get_insight_Dashboard_report_pinned_users | Insight not available for id: {insight_dashboard_id}"
            )
            return GeneralResponse(success=False, message="Insight not available")

        # Get shared users from the insight dashboard object
        shared_users = insight_dashboard_obj.get("shared_users", [])

        try:
            # Call the SSO user service to get the user list
            user_list = self.sso_user_service.get_user_list(team_id, user_types)

            removed_current_user_list = [user for user in user_list if user.get("id") != user_id]

            # Print users for testing
            main_logger.info(
                f"InsightDashboardService.get_insight_Dashboard_report_pinned_users |Retrieved {len(user_list)} users from SSO server"
            )

            # Add pinned status to each user
            for user in removed_current_user_list:
                main_logger.info(
                    f"InsightDashboardService.get_insight_Dashboard_report_pinned_users | User: {user.get('name')} ({user.get('email')})"
                )
                user_id_str = ObjectId(user.get("id"))
                user["isSelected"] = user_id_str in shared_users

            return GeneralResponse(
                success=True, message="Users retrieved successfully", data={"users": removed_current_user_list}
            )

        except Exception as e:
            main_logger.error(
                f"InsightDashboardService.get_insight_Dashboard_report_pinned_users |Error retrieving users from SSO: {str(e)}"
            )
            return GeneralResponse(success=False, message=f"Failed to retrieve users: {str(e)}")

    def pinned_insight_dashboard_report_to_users(self, insight_dashboard_id, pinned_user_list, user_info):
        """Pin the insight dashboard report to the selected users

        Args:
            insight_dashboard_id (str): The unique identifier for the insight dashboard
            pinned_user_list (list): The list of users to pin the report to
            user_info (dict): The user information

        Returns:
            dict: A dictionary representing the result of the operation
        """
        try:
            insight_dashboard_obj = self.insight_dashboard_collection.get_one_document(
                {"_id": ObjectId(insight_dashboard_id)}
            )

            if insight_dashboard_obj is None:
                main_logger.error(
                    f"InsightDashboardService.pinned_insight_dashboard_report_to_users | Insight not available for id: {insight_dashboard_id}"
                )
                return GeneralResponse(success=False, message="Insight not available")

            # Get existing shared users from the insight dashboard object
            existing_shared_users = insight_dashboard_obj.get("shared_users", [])

            # Extract user IDs from the pinned_user_list
            new_pinned_user_ids = [ObjectId(user.id) for user in pinned_user_list.users if user.isSelected]

            # Find newly added users and removed users
            newly_added_user_ids = [user_id for user_id in new_pinned_user_ids if user_id not in existing_shared_users]
            removed_user_ids = [user_id for user_id in existing_shared_users if user_id not in new_pinned_user_ids]

            if not newly_added_user_ids and not removed_user_ids:
                return GeneralResponse(success=True, message="No changes in pinned users", data={"success": True})

            if newly_added_user_ids:
                # Get user details for newly added users
                newly_added_users = [
                    user
                    for user in pinned_user_list.users
                    if user.isSelected and ObjectId(user.id) in newly_added_user_ids
                ]

                self._add_users_to_insight_dashboard(
                    insight_dashboard_obj, newly_added_users, new_pinned_user_ids, user_info
                )

            # Handle removed users //TODO
            # if removed_user_ids:
            #     self._remove_users_from_insight_dashboard(insight_dashboard_obj, removed_user_ids)

            # Update the shared_users list in the original insight dashboard object
            self.insight_dashboard_collection.update_one(
                {"_id": ObjectId(insight_dashboard_id)},
                {"$set": {"shared_users": new_pinned_user_ids, "updatedAt": datetime.now(timezone.utc)}},
            )

            return GeneralResponse(success=True, message="Users pinned successfully", data={"success": True})

        except Exception as e:
            main_logger.error(
                f"InsightDashboardService.pinned_insight_dashboard_report_to_users | Error: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def _add_users_to_insight_dashboard(
        self, insight_dashboard_obj, newly_added_users, new_pinned_user_ids, user_info
    ):
        """Add users to the insight dashboard by adding their IDs to the userIdList array

        Args:
            insight_dashboard_obj (dict): The original insight dashboard object
            newly_added_users (list): List of user objects to add
            new_pinned_user_ids (list): List of all pinned user IDs
            user_info (dict): Information about the user who is sharing the dashboard
        """
        try:
            insight_dashboard_id = ObjectId(insight_dashboard_obj["_id"])

            # Handle backward compatibility: get existing users from both userIdList and legacy userId
            current_user_id_list = insight_dashboard_obj.get("userIdList", [])

            # If dashboard has legacy userId field, include it in the userIdList
            if "userId" in insight_dashboard_obj and insight_dashboard_obj["userId"]:
                legacy_user_id = insight_dashboard_obj["userId"]
                if legacy_user_id not in current_user_id_list:
                    current_user_id_list.append(legacy_user_id)

            # Add new user IDs to the existing userIdList
            new_user_ids = [ObjectId(user.id) for user in newly_added_users]
            updated_user_id_list = current_user_id_list + new_user_ids

            # Remove duplicates while preserving order
            seen = set()
            unique_user_id_list = []
            for user_id in updated_user_id_list:
                if user_id not in seen:
                    seen.add(user_id)
                    unique_user_id_list.append(user_id)

            # Update the dashboard with the new userIdList and remove legacy userId field
            update_fields = {"userIdList": unique_user_id_list, "updatedAt": datetime.now(timezone.utc)}

            # Remove the legacy userId field if it exists
            unset_fields = {}
            if "userId" in insight_dashboard_obj:
                unset_fields["userId"] = ""

            update_operation = {"$set": update_fields}
            if unset_fields:
                update_operation["$unset"] = unset_fields

            self.insight_dashboard_collection.update_one({"_id": insight_dashboard_id}, update_operation)

            main_logger.info(
                f"InsightDashboardService._add_users_to_insight_dashboard | Added {len(new_user_ids)} users to dashboard {insight_dashboard_id}"
            )

            # Update the shared_users field in the insightReport object if it exists
            if "insightId" in insight_dashboard_obj and insight_dashboard_obj["insightId"]:
                insight_report_id = str(insight_dashboard_obj["insightId"])
                self.conversation_service._add_insight_report_shared_users(
                    insight_report_id, newly_added_users, user_info
                )

        except Exception as e:
            main_logger.error(
                f"InsightDashboardService._add_users_to_insight_dashboard | Error: {traceback.format_exc()}"
            )
            raise e

    def generate_insight_for_analysis(
        self, dashboard_insight_id: str, parent_conversation: Conversation
    ) -> GeneralResponse[dict]:
        """Generate an insight for a specific analysis

        Args:
            dashboard_insight_id (str): The unique identifier of the dashboard insight

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains insightId if successful
                - message (str): Error message if operation failed
        """
        base_log_path = "insight_board_" + parent_conversation.chat_id + "/"
        if not os.path.exists(f"./logs/{base_log_path}"):
            os.makedirs(f"./logs/{base_log_path}")
        insight_log = get_debug_logger(
            f"insight_{parent_conversation.chat_id}",
            f"./logs/{base_log_path}generate_insight_dashboard.log",
        )
        insight_log.info(
            f"InsightDashboardService.generate_insight_for_analysis | Generating insight for analysis for dashboard_insight_id: {dashboard_insight_id}"
        )
        try:

            # retrieve dashboard insight details
            dashboard_insight = self.insight_dashboard_collection.get_one_document(
                {"_id": ObjectId(dashboard_insight_id)}
            )

            if not dashboard_insight:
                insight_log.error(
                    f"InsightDashboardService.generate_insight_for_analysis | Dashboard insight not found for id: {dashboard_insight_id}"
                )
                return GeneralResponse(success=False, message="Dashboard insight not found")

            # reset insight dashboard object in db
            current_run_at = datetime.now(self.timezone)

            next_run_at = None
            next_run_at_response = self.get_next_insight_run_at(dashboard_insight["frequencyInfo"])
            if next_run_at_response.success:
                next_run_at = next_run_at_response.data["nextRunAt"]
            else:
                insight_log.error(
                    f"InsightDashboardService.generate_insight_for_analysis | Failed to get next run at for dashboard_insight_id: {dashboard_insight_id}, message: {next_run_at_response.message}"
                )

            self.insight_dashboard_collection.update_one(
                {"_id": ObjectId(dashboard_insight_id)},
                {
                    "$unset": {
                        "insightId": "",
                        "insightReportId": "",
                        "insightQuery": "",
                        "followUps": "",
                        "shared_users": "",
                    },
                    "$set": {
                        "updatedAt": datetime.now(timezone.utc),
                        "currentRunAt": current_run_at,
                        "nextRunAt": next_run_at,
                        "timezone": self.timezone_string,
                        "status": InsightReportStatus.QUEUED.value,
                    },
                },
            )

            # if sessionAnswerGeneratedAt 12 hour before, execute code and get answer again, otherwise use existing answer
            if "updatedAt" in dashboard_insight and dashboard_insight["updatedAt"].replace(tzinfo=timezone.utc) < (
                datetime.now(timezone.utc) - timedelta(hours=12)
            ):
                res = self.refresh_insight_dashboard(parent_conversation, dashboard_insight)

                if not res.success:
                    insight_log.error(
                        f"InsightDashboardService.generate_insight_for_analysis | Failed to refresh insight dashboard for dashboard_insight_id: {dashboard_insight_id}, message: {res.message}"
                    )
                    return GeneralResponse(success=False, message="Failed to refresh insight dashboard")

            kpi_param = dashboard_insight["kpiParam"]
            # invoke insight generation
            kpi_further_analysis_res = self.invoke_kpi_further_analysis(
                dashboard_insight["stats"] if "stats" in dashboard_insight else {},
                dashboard_insight_id,
                parent_conversation,
            )

            if kpi_further_analysis_res.success:
                # Extract key insights
                key_insight_extracts = (
                    self.insight_board_manager_service.extract_insights_from_further_analysis_result(
                        kpi_param,
                        kpi_further_analysis_res.data["result"],
                        kpi_further_analysis_res.data["tabular_data"],
                        kpi_further_analysis_res.data["thought_chain"],
                        insight_log,
                    )
                )
                if key_insight_extracts.success:
                    render_key_insights_and_content_res = self._render_key_insights_and_content(
                        key_insight_extracts.data, dashboard_insight_id, insight_log
                    )
                    if render_key_insights_and_content_res.success:
                        # Update key insights and observation list to the insight dashboard
                        dashboard_insight["keyInsights"] = key_insight_extracts.data["key_insights"]
                        dashboard_insight["observationList"] = key_insight_extracts.data["observation_list"]
                        dashboard_insight["status"] = InsightReportStatus.FINALIZED.value
                        new_findings = self.on_insight_dashboard_generated(
                            dashboard_insight,
                            kpi_further_analysis_res.data["insightReportId"],
                            key_insight_extracts.data,
                            insight_log,
                        )
                        return GeneralResponse(success=True, data=render_key_insights_and_content_res.data)
                    else:
                        insight_log.error(
                            f"InsightDashboardService.generate_insight_for_analysis | Failed to render key insights and content for dashboard_insight_id: {dashboard_insight_id}, message: {render_key_insights_and_content_res.message}"
                        )
                        return GeneralResponse(success=False, message="Failed to render key insights and content")
                else:
                    insight_log.error(
                        f"InsightDashboardService.generate_insight_for_analysis | Failed to extract key insights from further analysis result for dashboard_insight_id: {dashboard_insight_id}, message: {key_insight_extracts.message}"
                    )
                    return GeneralResponse(success=False, message="Failed to extract key insights")
            else:
                insight_log.error(
                    f"InsightDashboardService.generate_insight_for_analysis | Failed to invoke insight generation for dashboard_insight_id: {dashboard_insight_id}, message: {kpi_further_analysis_res.message}"
                )
                return GeneralResponse(success=False, message="Failed to start insight generation")
        except Exception as e:
            insight_log.error(
                f"InsightDashboardService.generate_insight_for_analysis | Error while generating insight for analysis: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def on_insight_dashboard_generated(
        self, dashboard_insight: dict, insight_report_id: str, key_insight_extracts: dict, insight_log: Logger
    ):
        """
        Description: First save the key insights and observation list to the insight dashboard collection with new findings
                Check the key findings of the given insight dashboard and also the previously sent findings and notify the user if there is any new finding.
                Called when the insight dashboard is generated
        Params:
            dashboard_insight (dict): The original insight dashboard object
            key_insight_extracts (dict): The key insights and observation list extracted from the further analysis result
        Returns:
            bool: True if there is any new finding, False otherwise
        """
        dashboard_insight_id = dashboard_insight["_id"]
        kpi_param = dashboard_insight["kpiParam"]
        kpi_frequency = dashboard_insight["frequencyInfo"]
        kpi_status = dashboard_insight["stats"]
        insight_board_user_ids = dashboard_insight["userIdList"]
        user_info = dashboard_insight["userInfo"]
        # Get 5 most recent previous findings sent by "InsightDashboardHistory" for same dashboard insight id
        previous_findings = self.insight_dashboard_history_collection.get_documents(
            {"insightDashboardId": ObjectId(dashboard_insight_id)},
            sort_criteria_list=[("updatedAt", pymongo.ASCENDING)],
            limit=5,
            field_filter={"keyInsights": 1, "newFindings": 1, "updatedAt": 1},
        )
        insight_log.debug(
            f"InsightDashboardService.on_insight_dashboard_generated | Identifying new findings for dashboard_insight_id: {dashboard_insight_id} for KPI: {kpi_param}, \
                frequency: {kpi_frequency}, status: {kpi_status}, report id: {insight_report_id}, user list: {insight_board_user_ids}, previous findings count : {len(previous_findings)}"
        )

        new_findings_res = self.insight_board_manager_service.identify_significant_new_key_findings(
            kpi_param, kpi_frequency, kpi_status, key_insight_extracts["key_insights"], previous_findings, insight_log
        )
        new_findings_list = []
        if new_findings_res.success:
            new_findings_list = new_findings_res.data["new_key_findings"]
        # Save the key insights and observation list to the insight dashboard collection with new findings
        self.insight_dashboard_collection.update_one(
            {"_id": ObjectId(dashboard_insight_id)},
            {
                "$set": {
                    "keyInsights": key_insight_extracts["key_insights"],
                    "observationList": key_insight_extracts["observation_list"],
                    "newFindings": new_findings_list,
                }
            },
        )
        dashboard_insight["newFindings"] = new_findings_list
        # Save the key insights and observation list to the insight dashboard history collection
        dashboard_insight_history = copy.deepcopy(dashboard_insight)
        if "_id" in dashboard_insight_history:
            dashboard_insight_history["insightDashboardId"] = dashboard_insight_history["_id"]
            del dashboard_insight_history["_id"]
        self.insight_dashboard_history_collection.insert_one(dashboard_insight_history)

        # Notify the shared users if there is any new finding
        if new_findings_list:
            insight_log.info(
                f"InsightDashboardService.on_insight_dashboard_generated | New findings identified for dashboard_insight_id: {dashboard_insight_id}, findings: {new_findings_list}"
            )
            # Send the email to all created and shared users
            self.conversation_service._add_insight_report_shared_users(
                insight_report_id, insight_board_user_ids, user_info
            )
            return True
        else:
            insight_log.info(
                f"InsightDashboardService.on_insight_dashboard_generated | No new findings identified for dashboard_insight_id: {dashboard_insight_id}"
            )
            return False

    def _render_key_insights_and_content(
        self, key_insight_extracts: dict, dashboard_insight_id: str, insight_log: Logger
    ):
        """
        Render the key insights and observation list for the insight dashboard
        Rendering format:
        {
        "keyFindings": {
            "mainFacts": [
                {
                    "id": 1,
                    "fact": "The vast majority of the 2023 revenue decline was caused by a 61.5% drop in Tyre Change service volume."
                },
                {
                    "id": 2,
                    "fact": "Revenue losses were not due to customer dissatisfaction, pricing changes, or increased competition."
                },
                {
                    "id": 3,
                    "fact": "Recurring service volume (oil changes, car washes) remained stable after early 2023, ruling out frequency decline as a cause."
                }
            ],
            "visualizations": [
                {
                    "data_point_id": "1",
                    "title": "<Observation title>",
                    "description": "<Observation description>",
                    "items": [
                        {
                            "type": "table",
                            "fileName": "table_data1.csv",
                            "data": {
                                "title": "<table title>",
                                "headers": [
                                    "<column 1>",
                                    "<column 2>"
                                ],
                                "rows": [
                                    [
                                        "<row 1, column 1>",
                                        "<row 1, column 2>"
                                    ],
                                    [
                                        "<row 2, column 1>",
                                        "<row 2, column 2>"
                                    ]
                                ]
                            },
                            "src": "<base_url>/api/file/public/<conversation_id>/download?file_name=table_data1.csv",
                            "isTruncated": false
                        },
                        {
                            "type": "image",
                            "fileName": "image_data1.png",
                            "src": "<base_url>/api/file/public/<conversation_id>/download?file_name=image_data1.png",
                            "altText": "<image description>"
                        }
                    ]
                }
                ]
            }
        }
        Params:
            key_insight_extracts (dict): The key insights and observation list extracted from the further analysis result
            {
                "key_insights": ["insight1", "insight2"],
                "observation_list": [{
                        "observation_title": "<observation title>",
                        "observation_description": "<observation description>",
                        "observation_items":[
                            {
                                "type": "table",
                                "title": "table title",
                                "file_name": "table_file.csv",
                            },
                            {
                                "type": "image",
                                "title": "image title",
                                "file_name": "image_file.png",
                            }
                        ]
                    }
                ]
            }
            dashboard_insight_id (str): The unique identifier of the dashboard insight
        Returns:
         formatted Rendering
        """
        insight_log.debug(
            f"InsightDashboardService._render_key_insights_and_content | Rendering key insights and observation list for dashboard_insight_id: {dashboard_insight_id}\nKey insights: {key_insight_extracts['key_insights']}. Observation list: {key_insight_extracts['observation_list']}"
        )
        try:
            base_url = os.getenv("FRONTEND_URL")
            # Render key insights
            key_insights = key_insight_extracts["key_insights"]
            observation_list = key_insight_extracts["observation_list"]

            # Get dashboard insight to retrieve conversation_id for file URLs
            dashboard_insight = self.insight_dashboard_collection.get_one_document(
                {"_id": ObjectId(dashboard_insight_id)}
            )
            conversation_id = str(dashboard_insight["insightId"]) if dashboard_insight.get("insightId") else ""

            # Build mainFacts from key insights
            main_facts = []
            for i, insight in enumerate(key_insights, 1):
                main_facts.append({"id": i, "fact": insight})

            # Build visualizations from observation list
            visualizations = []
            for i, observation in enumerate(observation_list, 1):
                observation_items = []

                for item in observation.get("observation_items", []):
                    item_type = item.get("type", "")
                    file_name = item.get("file_name", "")
                    title = item.get("title", "")

                    if item_type == "table":
                        # For tables, we need to read the CSV file and convert to the required format
                        file_path = f"storage/public/{conversation_id}/files/{file_name}"
                        if os.path.exists(file_path):
                            try:
                                df = pd.read_csv(file_path)

                                # Convert DataFrame to required format
                                headers = df.columns.tolist()
                                all_rows = df.values.tolist()

                                # Limit to first 50 rows
                                is_truncated = len(all_rows) > 50
                                rows = all_rows[:50] if is_truncated else all_rows

                                table_item = {
                                    "type": "table",
                                    "fileName": file_name,
                                    "data": {"title": title, "headers": headers, "rows": rows},
                                    "src": f"{base_url}/api/file/public/{conversation_id}/download?file_name={file_name}",
                                    "isTruncated": is_truncated,
                                }
                                observation_items.append(table_item)
                            except Exception as e:
                                insight_log.error(f"Error reading CSV file {file_path}: {str(e)}")
                                # Add fallback table item without data
                                table_item = {
                                    "type": "table",
                                    "fileName": file_name,
                                    "data": {"title": title, "headers": [], "rows": []},
                                    "src": f"{base_url}/api/file/public/{conversation_id}/download?file_name={file_name}",
                                    "isTruncated": False,
                                }
                                observation_items.append(table_item)
                        else:
                            insight_log.error(f"CSV file not found: {file_path}")
                            # Add fallback table item
                            table_item = {
                                "type": "table",
                                "fileName": file_name,
                                "data": {"title": title, "headers": [], "rows": []},
                                "src": f"{base_url}/api/file/public/{conversation_id}/download?file_name={file_name}",
                                "isTruncated": False,
                            }
                            observation_items.append(table_item)

                    elif item_type == "image":
                        # For images, create the image item
                        image_item = {
                            "type": "image",
                            "fileName": file_name,
                            "src": f"{base_url}/api/file/public/{conversation_id}/download?file_name={file_name}",
                            "altText": title,
                        }
                        observation_items.append(image_item)

                # Create visualization object for this observation
                visualization = {
                    "data_point_id": str(i),
                    "title": observation.get("observation_title", ""),
                    "description": observation.get("observation_description", ""),
                    "items": observation_items,
                }
                visualizations.append(visualization)

            # Build the final report structure
            report = {"keyFindings": {"mainFacts": main_facts, "visualizations": visualizations}}

            # save report in insight report collection
            self.insight_report_collection.update_one(
                {"_id": ObjectId(str(dashboard_insight.get("insightReportId", "")))},
                {
                    "$set": {
                        "report": report,
                        "status": InsightReportStatus.FINALIZED.value,
                        "updatedAt": datetime.now(timezone.utc),
                    }
                },
            )

            return GeneralResponse(success=True, data=report)

        except Exception as e:
            insight_log.error(
                f"InsightDashboardService._render_key_insights_and_content | Error while rendering key insights and observation list: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message="Failed to render key insights and observation list")

    def handle_insight_generation_result(self, future: concurrent.futures.Future, dashboard_insight_id: str):
        """Handle the result of the insight generation once it's ready"""

        def callback(fut: concurrent.futures.Future):
            try:
                # Get the result of the generate_insight_for_analysis function
                result: GeneralResponse[dict] = fut.result()  # This won't block since it's called after completion
                # Update the database with the result
                if result.success:
                    main_logger.info(
                        f"InsightDashboardService.handle_insight_generation_result | Insight generated for dashboard_insight_id: {dashboard_insight_id}"
                    )
                    self.insight_dashboard_collection.update_one(
                        {"_id": ObjectId(dashboard_insight_id)},
                        {
                            "$set": {
                                "status": InsightReportStatus.FINALIZED.value,
                                "updatedAt": datetime.now(timezone.utc),
                            }
                        },
                    )
                else:
                    main_logger.error(
                        f"InsightDashboardService.handle_insight_generation_result | Failed to generate insight for dashboard_insight_id: {dashboard_insight_id}, message: {result.message}"
                    )
                    self.insight_dashboard_collection.update_one(
                        {"_id": ObjectId(dashboard_insight_id)},
                        {
                            "$set": {
                                "status": InsightReportStatus.FAILED.value,
                                "updatedAt": datetime.now(timezone.utc),
                            }
                        },
                    )
            except Exception as e:
                # Handle any errors that occurred during insight generation
                main_logger.error(
                    f"InsightDashboardService.handle_insight_generation_result | Error while handling insight generation result: {traceback.format_exc()}"
                )
                self.insight_dashboard_collection.update_one(
                    {"_id": ObjectId(dashboard_insight_id)},
                    {
                        "$set": {
                            "status": InsightReportStatus.FAILED.value,
                            "updatedAt": datetime.now(timezone.utc),
                        }
                    },
                )

        # Add a callback to the Future to process the result when done
        future.add_done_callback(callback)

    def define_insight_query_for_analysis(self, dashboard_insight_id: str):
        """
        Define the insight query for a specific analysis
        Args:
            dashboard_insight_id (str): The unique identifier of the dashboard insight
        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains insight query if successful
                - message (str): Error message if operation failed
        """

        try:
            # retrieve dashboard insight details
            dashboard_insight = self.insight_dashboard_collection.get_one_document(
                {"_id": ObjectId(dashboard_insight_id)}
            )

            # call llm to define insight

            user_question = dashboard_insight["sessionUserInput"]
            analysis_frequency = dashboard_insight["frequencyInfo"]["frequency"]
            kpi_data = ""

            for answer in dashboard_insight["sessionAnswer"]:
                if answer["contentType"] == "text":
                    kpi_data += answer["content"] + "\n\n"

            kpi_data = kpi_data.strip()
            if not kpi_data:
                main_logger.error(
                    f"InsightDashboardService.define_insight_query_for_analysis | No KPI data found for dashboard_insight_id: {dashboard_insight_id}"
                )
                return GeneralResponse(success=False, message="No KPI data found")

            insight_query = self.insight_board_manager_service.run(
                user_question,
                kpi_data,
                analysis_frequency,
            )

            if insight_query.success:
                return GeneralResponse(success=True, data=insight_query.data)
            else:
                main_logger.error(
                    f"InsightDashboardService.define_insight_query_for_analysis | Failed to define insight query for dashboard_insight_id: {dashboard_insight_id}, message: {insight_query.message}"
                )
                return GeneralResponse(success=False, message="Failed to define insight query")
        except Exception as e:
            main_logger.error(
                f"InsightDashboardService.define_insight_query_for_analysis | Error while defining insight query: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def invoke_insight_generation(
        self, insight_query: str, initial_data: str, dashboard_insight_id: str
    ) -> GeneralResponse[dict]:
        """Invoke insight generation for a specific analysis

        Args:
            insight_query (str): The query for generating insights
            initial_data (str): The initial data for generating insights
            dashboard_insight_id (str): The unique identifier of the dashboard insight

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains insightId if successful
                - message (str): Error message if operation failed
        """
        try:
            # retrieve dashboard insight details
            dashboard_insight = self.insight_dashboard_collection.get_one_document(
                {"_id": ObjectId(dashboard_insight_id)}
            )
            user_id = str(dashboard_insight["userIdList"][0])
            user_name = dashboard_insight["userName"]
            user_email = dashboard_insight["userEmail"]

            conversation_id = None
            res = self.conversation_service.add_new_conversation(
                user_id, user_name, ConversationType.INSIGHT_GEN_CONVERSATION.value
            )
            if res.get("isSuccess", False):
                conversation_id = res["conversationId"]
            else:
                main_logger.error(
                    f"InsightDashboardService.invoke_insight_generation | Failed to create conversation for dashboard_insight_id: {dashboard_insight_id}"
                )
                return GeneralResponse(success=False, message="Failed to create conversation")

            conversation_db_obj = self.conversation_service.get_conversation_data(conversation_id)

            if conversation_db_obj is None:
                main_logger.error(
                    f"InsightDashboardService.invoke_insight_generation | Conversation not found for conversation_id: {conversation_id}"
                )
                return {
                    "success": False,
                    "message": "Conversation not found",
                }

            conversation_type = ConversationType.INSIGHT_GEN_CONVERSATION.value
            self.conversation_service.set_conversation_type(conversation_id, conversation_type)
            agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
            prompt = {"query": insight_query, "initial_data": initial_data}
            conversation_db_obj["type"] = conversation_type
            insight_report_id = self.insight_report_service.create_insight_report_draft(
                insight_id=conversation_id,
                userId=user_id,
                user_email=user_email,
                query=prompt["query"],
            )
            main_logger.info(
                f"InsightDashboardService.invoke_insight_generation | Insight report id: {insight_report_id} is drafted for conversation_id/insight_id: {conversation_id}"
            )

            # create new session
            session_id = str(
                datetime.now(timezone.utc).timestamp()
            )  # this session_id will be used for ordering sessions. So always increment is needed
            session_instance = Session(conversation_id, session_id)

            # get session history related to conversation
            history_messages = self.conversation_service.load_message_history(conversation_id, user_id)

            # create new conversation and pass it to llm agent thread created
            conversation_instance = Conversation(
                conversation_id, session_id, insight_query, user_id, len(history_messages) > 0, agent_type
            )

            self.global_memory.active_conversations[str(conversation_id)] = conversation_instance

            self.conversation_service.set_conversation_data(conversation_db_obj, conversation_instance)
            conversation_instance.active_session = session_instance
            conversation_instance.message_history = history_messages

            # update conversation db
            self.conversation_service.update_conversation(conversation_instance, session_id, user_id)

            base_log_path = "insight_" + conversation_id + "/"

            # update db with conversation id and insight report id
            self.insight_dashboard_collection.update_one(
                {"_id": ObjectId(dashboard_insight_id)},
                {
                    "$set": {
                        "insightId": ObjectId(conversation_id),
                        "insightReportId": ObjectId(insight_report_id),
                        "updatedAt": datetime.now(timezone.utc),
                    }
                },
            )

            # self.thread_pool.map(
            #     self.llm_insight_agent.run, [prompt], [conversation_instance], [session_id], [base_log_path]
            # )

            self.llm_insight_agent.run(prompt, conversation_instance, session_id, base_log_path)

            return GeneralResponse(
                success=True,
                message="Insight generation started",
                data={
                    "insightId": conversation_id,
                },
            )
        except Exception as e:
            main_logger.error(
                f"InsightDashboardService.invoke_insight_generation | Error while invoking insight generation: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def invoke_kpi_further_analysis(
        self, current_kpi_status: dict, dashboard_insight_id: str, parent_conversation: Conversation
    ) -> GeneralResponse[dict]:
        """Invoke insight generation for a specific analysis

        Args:
            original_user_question (str): The query for generating insights
            initial_data (str): The initial data for generating insights
            dashboard_insight_id (str): The unique identifier of the dashboard insight

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains insightId if successful
                - message (str): Error message if operation failed
        """
        # # Testing
        # return GeneralResponse(
        #     success=True,
        #     message="Insight generated",
        #     data={
        #         "result": '**Monthly Overdue Vendor Bill Amounts (Last 6 Months)**\n\nThe table below lists total overdue vendor-bill balances for each of the last six months. A bill is considered overdue when its due date is before today, it still carries a positive balance, and its status is neither “Paid in Full” nor “Voided.”\n\n<table type="markdown" name="overdue_vendor_bills.csv">Monthly overdue vendor bill amounts</table>\n\n**Visual Overview**\n\nThe bar chart highlights the sharp rise in overdue balances that peaked in June 2025, followed by a partial decline in July.\n\n<file type="png" name="overdue_vendor_bills_bar.png">Monthly overdue vendor bill bar chart</file>\n\nYou can download the detailed data below:\n\n<file type="csv" name="overdue_vendor_bills.csv">Download overdue_vendor_bills.csv</file>',
        #         "insightId": "",
        #         "tabular_data": [
        #             {
        #                 "first_10_rows": "| vendor                              |    june_overdue |   july_overdue |            delta |   pct_change |\n|:------------------------------------|----------------:|---------------:|-----------------:|-------------:|\n| IMI International Manufaturing Inc. |     2.83129e+06 |              0 |     -2.83129e+06 |         -100 |\n| Shreem Systems & Solution Pvt. Ltd. | 38291.4         |              0 | -38291.4         |         -100 |\n| Minister of Finance (Saskatchewan)  | 33703.7         |              0 | -33703.7         |         -100 |\n| US Air Compressor Canada Inc.       | 25944.5         |              0 | -25944.5         |         -100 |\n| Minister of Finance (Manitoba)      | 23512.3         |              0 | -23512.3         |         -100 |\n| Powerex-Iwata Air Technology, Inc   | 12437           |              0 | -12437           |         -100 |\n| Pyramid Sheet Metal Ltd.            |  7612.5         |              0 |  -7612.5         |         -100 |\n| Telexperts (Manitoba) Ltd           |  6538.28        |              0 |  -6538.28        |         -100 |\n| St Charles Country Club             |  4352.49        |              0 |  -4352.49        |         -100 |\n| Ronnies Generator Service           |  4237.5         |              0 |  -4237.5         |         -100 |",
        #                 "record_count": 108,
        #             }
        #         ],
        #         "thought_chain": [],
        #     },
        # )
        ###
        kpi_analysis_log = get_debug_logger(
            f"chat_{parent_conversation.chat_id}",
            f"./logs/insight_board_{str(parent_conversation.chat_id)}/kpi_further_analysis.log",
        )
        kpi_analysis_log.info(
            f"InsightDashboardService.invoke_kpi_further_analysis | Invoking KPI further analysis for dashboard_insight_id: {dashboard_insight_id}"
        )
        try:
            # retrieve dashboard insight details
            dashboard_insight = self.insight_dashboard_collection.get_one_document(
                {"_id": ObjectId(dashboard_insight_id)}
            )
            # Get the first of userIdList as primary user
            user_id = str(dashboard_insight["userIdList"][0])
            user_email = dashboard_insight["userEmail"]
            kpi_name = dashboard_insight["kpiParam"]

            # Form the kpi analysis prompt using the original user question
            kpi_analysis_prompt = f"Please provide further analysis for the following KPI: {kpi_name}\n\nCurrent KPI status: {json.dumps(current_kpi_status)}"
            conversation_instance, session_id = self.conversation_service.create_conversation_and_session(
                user_id,
                kpi_analysis_prompt,
                LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT_REPORT_GENERATION,
                parent_session_instance=parent_conversation.active_session,
                parent_conversation=parent_conversation,
                is_copy_agent_states=True,
                is_clone_session=False,
            )
            conversation_id = conversation_instance.chat_id

            insight_report_id = self.insight_report_service.create_insight_report_draft(
                insight_id=conversation_id,
                userId=user_id,
                user_email=user_email,
                query=kpi_analysis_prompt,
            )
            kpi_analysis_log.info(
                f"InsightDashboardService.invoke_insight_generation | Insight report id: {insight_report_id} is drafted for conversation_id/insight_id: {conversation_id}"
            )

            base_log_path = "insight_board_" + conversation_id + "/"
            if not os.path.exists(pathlib.Path(f"./logs/{base_log_path}")):
                os.makedirs(pathlib.Path(f"./logs/{base_log_path}"))
            chat_log = get_debug_logger(
                f"chat_{conversation_id}",
                f"./logs/{base_log_path}chat_{conversation_id}.log",
            )
            # update db with conversation id and insight report id
            self.insight_dashboard_collection.update_one(
                {"_id": ObjectId(dashboard_insight_id)},
                {
                    "$set": {
                        "insightId": ObjectId(conversation_id),
                        "insightReportId": ObjectId(insight_report_id),
                        "updatedAt": datetime.now(timezone.utc),
                    }
                },
            )

            result_dict = self.kpi_analysis_agent.execute_agent(
                {"user_request": kpi_analysis_prompt, "current date": str(datetime.now().strftime("%Y-%m-%d"))},
                conversation_instance,
                conversation_instance.active_session.session_id,
                chat_log,
            )
            if result_dict["is_success"] == False:
                kpi_analysis_log.error(
                    f"InsightDashboardService.invoke_insight_generation | Failed to invoke kpi analysis agent for dashboard_insight_id: {dashboard_insight_id}, message: {result_dict['message']}"
                )
                return GeneralResponse(success=False, message=result_dict["message"])

            # Save the conversation to retain in DB for the followups
            conversation_instance.save(chat_log)

            # Get tabular data by reading CSV files as markdown for each table
            tabular_data = self._get_csv_info_from_session_answer_list(
                conversation_id, result_dict["final_answer_list"]
            )
            # Remove file_path from tabular data as it may mislead extraction
            for data in tabular_data:
                if data.get("file_path", ""):
                    del data["file_path"]

            kpi_analysis_log.info(
                f"InsightDashboardService.invoke_insight_generation | KPI further analysis generated for dashboard_insight_id: {dashboard_insight_id}\n{json.dumps(result_dict['final_answer'], indent=2)}\n\nTabular data:\n{json.dumps(tabular_data, indent=2)}"
            )
            return GeneralResponse(
                success=True,
                message="Insight generated",
                data={
                    "result": result_dict["final_answer"],
                    "tabular_data": tabular_data,
                    "thought_chain": result_dict["thought_chain"],
                    "insightId": conversation_id,
                    "insightReportId": insight_report_id,
                },
            )
        except Exception as e:
            kpi_analysis_log.error(
                f"InsightDashboardService.invoke_insight_generation | Error while invoking insight generation: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def refresh_insight_dashboard(
        self, parent_conversation: Conversation, current_dashboard_obj: dict
    ) -> GeneralResponse[dict]:
        """Refresh the insight dashboard for a specific dashboard insight

        Args:
            current_dashboard_obj (dict): The current dashboard insight object

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - data (dict): Contains answer if successful
                - message (str): Error message if operation failed
        """
        # Get chat log from conversation id
        chat_log = get_debug_logger(
            f"chat_{parent_conversation.chat_id}",
            f"./logs/insight_board_{str(parent_conversation.chat_id)}/refresh_insight_dashboard.log",
        )
        # Clone the current conversation to run the same user question as of now
        conversation_instance, session_id = self.conversation_service.create_conversation_and_session(
            parent_conversation.user_id,
            parent_conversation.user_inputs[0],
            parent_conversation.agent_type,
            parent_session_instance=parent_conversation.active_session,
            parent_conversation=parent_conversation,
            is_clone_session=True,
            is_copy_agent_states=True,
        )
        try:
            # Call problem solving agent to re-run the same user question as of now with data as of now
            problem_solver_result = self.problem_solver_agent.execute_agent(
                {
                    "user_request": "Please generate the answer again with data as of now to get the up-to-date values for KPI."
                },
                conversation_instance,
                session_id,
                chat_log,
            )
            if not problem_solver_result["is_success"]:
                chat_log.error(
                    f"InsightDashboardService.refresh_insight_dashboard | Failed to refresh insight dashboard for dashboard_insight_id: {current_dashboard_obj['_id']}, message: {problem_solver_result['message']}"
                )
                return GeneralResponse(success=False, message="Failed to refresh insight dashboard")

            # Save the conversation to retain in DB for the followups
            conversation_instance.save(chat_log)
            # Now do the extraction of KPI and rendering
            csv_data_list = self._get_csv_info_from_session_answer_list(
                conversation_instance.chat_id, problem_solver_result["final_answer_list"]
            )
            csv_data_with_user_input = [
                {"user_input": parent_conversation.user_inputs[0], "csv_file_list": csv_data_list}
            ]
            chat_log.debug(
                f"InsightDashboardService.refresh_insight_dashboard | CSV data with user input: {json.dumps(csv_data_with_user_input, indent=2)}"
            )
            render_result = self._render_main_graph(
                csv_data_with_user_input, current_dashboard_obj["frequencyInfo"], chat_log
            )
            if not render_result["is_rendered"]:
                chat_log.error(
                    f"InsightDashboardService.refresh_insight_dashboard | Failed to render main graph for dashboard_insight_id: {current_dashboard_obj['_id']}"
                )
                return GeneralResponse(success=False, message="Failed to render main graph")
            # Update the dashboard object with new rendered values
            dashboard_obj_update = {
                "csv_data_with_user_input": csv_data_with_user_input,
                "renderedMainGraph": render_result["rendered_main_graph"],
                "updatedAt": datetime.now(timezone.utc),
                "status": InsightReportStatus.DRAFT.value,
                "stats": render_result["change_dict"],
            }
            chat_log.info(
                f"InsightDashboardService.refresh_insight_dashboard | Main graph rendered for updated answer for dashboard_insight_id: {current_dashboard_obj['_id']}\n{json.dumps(render_result['rendered_main_graph'], indent=2)}"
            )
            self.insight_dashboard_collection.update_one(
                {"_id": ObjectId(current_dashboard_obj["_id"])}, {"$set": dashboard_obj_update}
            )
            # Update the object
            current_dashboard_obj.update(dashboard_obj_update)
            return GeneralResponse(success=True, data=dashboard_obj_update)

        except Exception as e:
            chat_log.error(
                f"InsightDashboardService.refresh_insight_dashboard | Error while refreshing insight dashboard: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def get_insight_list_to_dashboard(self, user_id: str, user_type: str) -> GeneralResponse[list]:
        """Get the list of insights available in the Insight Dashboard

        Args:
            user_id (str): The unique identifier of the user
            user_type (str): The type of the user

        Returns:
            dict: A dictionary containing the list of insights.
                The key 'insightList' holds a list of insights that are marked as added to the dashboard.
                If no insights are found or an error occurs, an empty list is returned.
        """
        try:
            pipeline = []

            # Apply user_id filter only if the user is not a Super Admin
            if user_type != UserType.USER_TYPE_SUPER_ADMIN.value:
                # Support both new userIdList field and legacy userId field for backward compatibility
                pipeline.append(
                    {
                        "$match": {
                            "$or": [
                                {"userIdList": {"$in": [ObjectId(user_id)]}},  # New field
                                {"userId": ObjectId(user_id)},  # Legacy field for backward compatibility
                            ]
                        }
                    }
                )

            pipeline.extend(
                [
                    {"$sort": {"updatedAt": -1}},
                    ##### No need to get title from insight ####
                    # {
                    #     "$lookup": {
                    #         "from": "Conversations",
                    #         "localField": "insightId",
                    #         "foreignField": "_id",
                    #         "as": "insightDetails",
                    #     }
                    # },
                ]
            )

            insight_list = self.insight_dashboard_collection.aggregate(pipeline)

            formatted_insight_list = []
            for insight in insight_list:

                # get title
                insight_title = insight["initialTitle"]
                ##### No need to get title from insight ####
                # if "insightDetails" in insight and isinstance(insight["insightDetails"], list):
                #     if len(insight["insightDetails"]) > 0:
                #         _insight_title = insight["insightDetails"][0].get("name")
                #         if _insight_title is not None:
                #             insight_title = _insight_title

                # if "@ Insight |" part is there, remove it
                if insight_title.startswith("@ Insight | "):
                    insight_title = insight_title.replace("@ Insight | ", "", 1)
                # remove leading and trailing spaces
                insight_title = insight_title.strip()

                formatted_insight_list.append(
                    {
                        "insightDashboardId": str(insight["_id"]),
                        "title": insight_title,
                        "updatedAt": insight["updatedAt"],
                        "frequency": insight["frequencyInfo"]["frequency"],
                    }
                )

            return GeneralResponse(success=True, data=formatted_insight_list)

        except Exception as e:
            main_logger.error(
                f"InsightDashboardService.get_insight_list_to_dashboard | Error while getting insight list: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=str(e))

    def get_insight_dashboard_report(self, insight_dashboard_id, user_id, user_type) -> GeneralResponse[dict]:
        """Get the Insight Dashboard report for a given insight dashboard ID

        Args:
            insight_dashboard_id (str): The unique identifier for the insight dashboard
            user_id (str): The unique identifier for the user
            user_type (str): The type of the user

        Returns:
            dict: A dictionary representing the Insight Dashboard report
        """

        insight_dashboard_obj = self.insight_dashboard_collection.get_one_document(
            {"_id": ObjectId(insight_dashboard_id)}
        )

        if insight_dashboard_obj is None:
            main_logger.error(
                f"InsightDashboardService.get_insight_dashboard_report | Insight not available for id: {insight_dashboard_id}"
            )
            return GeneralResponse(success=False, message="Insight not available")

        insight_id = insight_dashboard_obj.get("insightId", None)

        # get insight report
        insight_report = {}
        if insight_id is None:
            insight_report = {
                "_id": "",
                "query": "",
                "status": insight_dashboard_obj.get("status", InsightReportStatus.DRAFT.value),
                "reportId": "",
                "insightId": "",
                "report": MASTER_REPORT_JSON_TEMPLATE,
            }
        else:
            insight_report = self.insight_report_service.get_insight_report(insight_id, user_id, user_type)

        if "prompt" in insight_report:
            del insight_report["prompt"]

        # get key graph
        key_graph = {
            "src": "",
            "altText": "",
            "mediaUrl": "",
            "fileKey": "",
            "width": 0,
            "height": 0,
            "mediaType": "image",
            "fileName": "",
            "isMetaLakeRedirect": False,
        }

        if "sessionAnswerGraphs" in insight_dashboard_obj and isinstance(
            insight_dashboard_obj["sessionAnswerGraphs"], list
        ):
            if len(insight_dashboard_obj["sessionAnswerGraphs"]) > 0:
                key_graph = insight_dashboard_obj["sessionAnswerGraphs"][0]
                key_graph["src"] = key_graph["mediaUrl"]
                key_graph["altText"] = key_graph["fileName"]

        insight_report["keyGraph"] = key_graph

        # get title - title of the initial conversation
        insight_title = insight_dashboard_obj["initialTitle"]
        """ # Don't need to get title from insight
        if "insightId" in insight_dashboard_obj:
            conversation_id = insight_dashboard_obj["insightId"]
            conversation = self.conversation_service.get_conversation_data(conversation_id)
            if conversation is not None:
                _insight_title = conversation.get("name")
                if _insight_title is not None:
                    insight_title = _insight_title
        """

        # if "@ Insight |" part is there, remove it
        if insight_title.startswith("@ Insight | "):
            insight_title = insight_title.replace("@ Insight | ", "", 1)
        # remove leading and trailing spaces
        insight_title = insight_title.strip()

        insight_report["title"] = insight_title

        # add stats
        stats = insight_dashboard_obj.get(
            "stats",
            {
                "currentValue": None,
                "previousValue": None,
                "currencySign": None,
                "pctChange": None,
                "statement": None,
            },
        )

        # Format stats for frontend display
        formatted_stats = {
            "currentValue": (
                stats["currencySign"] + format_number(stats["currentValue"])
                if "currencySign" in stats and stats["currencySign"]
                else format_number(stats["currentValue"])
            ),
            "previousValue": (
                stats["currencySign"] + format_number(stats["previousValue"])
                if "currencySign" in stats and stats["currencySign"]
                else format_number(stats["previousValue"])
            ),
            "pctChange": format_number(stats["pctChange"]) + "%" if stats["pctChange"] is not None else None,
            "direction": (
                ("UP" if stats["pctChange"] > 0 else "DOWN" if stats["pctChange"] < 0 else None)
                if stats["pctChange"] is not None
                else None
            ),
            "statement": stats["statement"],
        }

        insight_report["stats"] = formatted_stats

        # attach follow ups
        insight_report["followUps"] = insight_dashboard_obj.get("followUps", [])

        # add rendered main graph
        insight_report["isRendered"] = insight_dashboard_obj.get("isRendered", False)
        insight_report["renderedMainGraph"] = insight_dashboard_obj.get("renderedMainGraph", {})

        # format rendered main graph as Frontend Requirement
        if insight_report["renderedMainGraph"]:
            if "xAxisTitle" in insight_report["renderedMainGraph"]:
                insight_report["renderedMainGraph"]["xAxis"] = {
                    "title": insight_report["renderedMainGraph"]["xAxisTitle"],
                    "display": True,
                }
                del insight_report["renderedMainGraph"]["xAxisTitle"]
            if "yAxisTitle" in insight_report["renderedMainGraph"]:
                insight_report["renderedMainGraph"]["yAxis"] = {
                    "title": insight_report["renderedMainGraph"]["yAxisTitle"],
                    "display": True,
                }
                del insight_report["renderedMainGraph"]["yAxisTitle"]
            insight_report["renderedMainGraph"]["widgetType"] = "chart"

        return GeneralResponse(success=True, data=insight_report)

    def get_next_insight_run_at(self, frequency_info: dict) -> GeneralResponse[dict]:
        """Get the next scheduled time for an insight run based on the provided frequency and trigger information

        Args:
            frequency_info (dict): A dictionary containing the frequency and trigger information

        Returns:
            dict: A dictionary containing the next scheduled time for the insight run
        """

        frequency = frequency_info.get("frequency", None)
        if frequency is None:
            return GeneralResponse(success=False, message="Frequency not provided")

        trigger = frequency_info.get("trigger", None)
        if trigger is None:
            return GeneralResponse(success=False, message="Trigger not provided")

        schedule_time_calculator = ScheduleTimeCalculator(self.timezone_string)

        _schedule_time = None

        trigger = str(trigger)

        if frequency == "daily":
            try:
                # Convert 12-hour format (e.g. "05:15 PM") to 24-hour format
                time_parts = trigger.strip().split(" ")
                if len(time_parts) != 2:
                    return GeneralResponse(success=False, message="Invalid time format")

                time, period = time_parts
                hour, minute = map(int, time.split(":"))

                if period.lower() == "pm" and hour != 12:
                    hour += 12
                elif period.lower() == "am" and hour == 12:
                    hour = 0

                if not (0 <= hour <= 23 and 0 <= minute <= 59):
                    return GeneralResponse(success=False, message="Invalid time values")

                _schedule_time = schedule_time_calculator.get_next_daily_time(hour, minute)
            except ValueError:
                return GeneralResponse(success=False, message="Invalid time format")

        elif frequency == "weekly":
            if trigger.lower() not in ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]:
                return GeneralResponse(success=False, message="Invalid trigger")
            _schedule_time = schedule_time_calculator.get_next_weekly_time(trigger.lower())

        elif frequency == "monthly":
            if not trigger.isdigit() or int(trigger) < 1 or int(trigger) > 31:
                return GeneralResponse(success=False, message="Invalid trigger")
            _schedule_time = schedule_time_calculator.get_next_monthly_time(int(trigger))

        elif frequency == "yearly":
            trigger = get_month_number(trigger)
            if trigger < 1 or trigger > 12:
                return GeneralResponse(success=False, message="Invalid trigger")
            _schedule_time = schedule_time_calculator.get_next_yearly_time(int(trigger))

        elif frequency == "quarterly":
            valid_triggers = ["start", "mid", "end"]
            trigger = trigger.lower().split(" ")[0]
            if trigger not in valid_triggers:
                return GeneralResponse(success=False, message="Invalid trigger")
            _schedule_time = schedule_time_calculator.get_next_quarterly_time(trigger)

        elif frequency == "none":
            return GeneralResponse(success=True, data={"nextRunAt": None})

        else:
            return GeneralResponse(success=False, message="Invalid frequency")

        if _schedule_time is None:
            return GeneralResponse(success=False, message="Invalid frequency")

        return GeneralResponse(success=True, data={"nextRunAt": _schedule_time})

    def remove_analysis_from_dashboard(self, dashboard_insight_id: str) -> GeneralResponse[dict]:
        """Remove an analysis from the Insight Dashboard

        Args:
            dashboard_insight_id (str): The unique identifier of the dashboard insight

        Returns:
            GeneralResponse[dict]: Response object containing:
                - success (bool): Whether the operation was successful
                - message (str): Success or error message
        """

        # copy to history before remove
        dashboard_insight = self.insight_dashboard_collection.get_one_document({"_id": ObjectId(dashboard_insight_id)})
        if dashboard_insight is None:
            main_logger.error(
                f"InsightDashboardService.remove_analysis_from_dashboard | Dashboard insight not found for id: {dashboard_insight_id}"
            )
            return GeneralResponse(success=False, message="Dashboard insight not found")
        dashboard_insight_history = copy.deepcopy(dashboard_insight)
        if "_id" in dashboard_insight_history:
            dashboard_insight_history["insightDashboardId"] = dashboard_insight_history["_id"]
            del dashboard_insight_history["_id"]
        self.insight_dashboard_history_collection.insert_one(dashboard_insight_history)

        # remove from dashboard
        res = self.insight_dashboard_collection.remove_by_id(ObjectId(dashboard_insight_id))
        if res.deleted_count == 0:
            main_logger.error(
                f"InsightDashboardService.remove_analysis_from_dashboard | Failed to remove dashboard insight id: {dashboard_insight_id}"
            )
            return GeneralResponse(success=False, message="Failed to remove dashboard insight")

        return GeneralResponse(success=True, message="Dashboard insight removed successfully", data={"success": True})

    def get_change_from_answer(self, user_question: str, answer_markdown_text: str):
        """
        Get the change from the answer markdown text by first finding the latest value and the previous value using LLM
        Args:
            user_question (str): The user question (asking to get a table with values aggregated by period such as week, month, or quarter)
            answer_markdown_text (str): The answer markdown text containing table data
        Returns:
            dict: A dictionary containing the change with current value, previous value and percentage change
        """
        try:
            latest_previous_value_res = self.insight_board_manager_service.llm_extract_current_and_previous_values(
                user_question, answer_markdown_text
            )
            if not latest_previous_value_res.success:
                main_logger.error(
                    f"InsightDashboardService.get_change_from_answer | Failed to extract current and previous values, message: {latest_previous_value_res.message}"
                )
                return GeneralResponse(success=False, message="Failed to extract current and previous values")

            values_dict = latest_previous_value_res.data

            change_stats = {
                "currentValue": None,
                "previousValue": None,
                "currencySign": None,
                "pctChange": None,
                "statement": None,
            }

            current_value = values_dict.get("current_value")
            previous_value = values_dict.get("previous_value")

            # Check if values exist and are numeric
            if current_value is not None and previous_value is not None:
                try:
                    current_value = float(current_value)
                    previous_value = float(previous_value)

                    # Handle division by zero
                    if previous_value != 0:
                        change_stats["pctChange"] = ((current_value - previous_value) / previous_value) * 100

                    change_stats["currentValue"] = current_value
                    change_stats["previousValue"] = previous_value
                    change_stats["currencySign"] = values_dict.get("currency_sign")
                    change_stats["statement"] = values_dict.get("statement")

                except (ValueError, TypeError):
                    # Handle non-numeric values
                    main_logger.error(
                        f"Non-numeric values encountered: current={current_value}, previous={previous_value}"
                    )
                    return GeneralResponse(success=False, message="Non-numeric values encountered")

            return GeneralResponse(success=True, data=change_stats)
        except Exception as e:
            main_logger.error(f"Error in get_change_from_answer: {traceback.format_exc()}")
            return GeneralResponse(success=False, message=str(e))

    def _get_csv_info_from_session_answer_list(self, conversation_id, session_answer_list):
        csv_file_list = []
        for answer in session_answer_list:
            if answer.get("content_type") == MessageContentType.MEDIA.value:
                for content in answer.get("content", []):
                    if content.get("mediaType") == "csv":
                        file_path = f"storage/public/{conversation_id}/files/{content['fileName']}"
                        if os.path.exists(file_path):
                            # Get markdown table from csv file
                            markdown_table, is_truncated, row_count = csv_to_markdown(file_path, 10)
                            csv_file_list.append(
                                {
                                    "file_path": file_path,
                                    "file_name": content["fileName"],
                                    "first_10_rows": markdown_table,
                                    "record_count": row_count,
                                }
                            )
                        else:
                            main_logger.error(
                                f"InsightDashboardService.add_analysis_to_dashboard | CSV file not found for file path: {file_path}"
                            )
        return csv_file_list
