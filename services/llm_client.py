"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class LLMClient
* @description This class is the abstract class which is a base for different LLM API clients
* <AUTHOR>
"""

from abc import ABC, abstractmethod
from logging import Logger
import os
import pathlib

from utils.constant import DataBlockHandlerType, DataBlockVisibility, FrontendBlockType, FrontendTabContentType
from models.conversation import Conversation
from utils.logger import get_debug_logger


if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))

logger = get_debug_logger(
    "llm_client", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/llm_client.log")
)


class LLMClient(ABC):
    def __init__(self):
        self.logger = logger
        # This env variable is used to convert the text output from reasoning models (o1 / o3) to structured output using gpt-4o
        self.llm_model_name_structured_output = os.environ.get("MODEL_JSON_OUTPUT")

    @abstractmethod
    def get_llm_response(
        self,
        agent,
        conversation: Conversation,
        chat_log: Logger,
        message_list: list,
        model: str,
        token_limit: int = None,
        response_format: dict = None,
        formatting_instructions: str = None,  # If this is given, then use the llm_model_name_structured_output to get structured output from unstructured output - this will enable primary LLM call freely give output without worrying about structure - this will increase the accuracy of the output
        **kwargs,
    ):
        pass

    @abstractmethod
    def get_provider_name(self):
        pass

    def chunk_stream(self, chunks, conversation: Conversation):
        collected_messages = []
        for i, chunk in enumerate(chunks):

            if conversation.active_session.user_stopped_stream:
                break

            chunk_message = None
            if len(chunk.choices) > 0:
                chunk_message = chunk.choices[0].delta.content
            if chunk_message:
                collected_messages.append(chunk_message)
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=chunk_message,
                    handler_type=DataBlockHandlerType.STREAM,
                    block_tab_types=[FrontendTabContentType.ANSWER],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )
        output_str = "".join(collected_messages)

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=output_str,
            block_tab_types=[FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )

        return output_str
