"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class SchedulerJobsService
* @description Contain functions to be executed by the scheduler
* <AUTHOR>
"""

import base64
from datetime import datetime, timezone
import os
import pathlib
import concurrent.futures
import time
import traceback
import pytz
import layernext
import requests
from databases.mongo_manager import MongoDBmanager
from utils.dependency_provider import (
    get_conversation_service,
    get_insight_dashboard_service,
    get_insight_report_service,
    get_llm_insight_agent,
    get_global_memory,
)
from utils.constant import (
    InsightReportStatus,
    SchedulerFrequency,
)
from utils.logger import get_debug_logger
from services.data_dictionary_service import DataDictionaryService

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))

logger = get_debug_logger(
    "schedule_jobs_service", pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log")
)


class SchedulerJobsService:

    def __init__(self):

        self.insight_agent = get_llm_insight_agent()
        self.conversation_service = get_conversation_service()
        self.insight_report_service = get_insight_report_service()
        self.global_memory_service = get_global_memory()
        self.insight_dashboard_service = get_insight_dashboard_service()

        self.insight_dashboard_collection = MongoDBmanager("InsightDashboard")
        self.system_data_collection = MongoDBmanager("SystemData")
        self.conversations_collection = MongoDBmanager("Conversations")

        self.timezone_string = str(os.getenv("TIME_ZONE", "America/Winnipeg"))
        self.timezone = pytz.timezone(self.timezone_string)

        self.thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)

    def dequeue_insights(self):
        """
        Runs every minute to dequeue insights from the database, and starts a new thread for insight generation.
        This method is called by the scheduler.
        """
        if self.insight_report_service.is_simultaneous_insight_generation_limit_reached():
            logger.info(f"dequeue_insights | simultaneous insight generation limit reached")
            return

        insight_to_dequeue = self.insight_report_service.dequeue_insight_generation()
        if not insight_to_dequeue:
            logger.info(f"dequeue_insights | no insight to dequeue")
            return

        conversation_id = str(insight_to_dequeue["insight_id"])

        logger.info(f"dequeue_insights | insight {str(insight_to_dequeue['insight_id'])} dequeueing...")

        agent_target = self.insight_agent.run
        base_log_path = "insight_" + conversation_id + "/"

        conversation_instance = self.conversation_service.load_conversation(conversation_id)

        if not conversation_instance:
            logger.error(f"dequeue_insights | N/A | No conversation found for conversation_id: {conversation_id}")
            return

        prompt = insight_to_dequeue.get("prompt", None)

        if not prompt:
            logger.error(f"dequeue_insights | N/A | No prompt found for insight_id: {conversation_id}")
            self.insight_report_service.change_insight_report_status(
                str(insight_to_dequeue["insight_id"]), InsightReportStatus.FAILED.value
            )
            return

        self.global_memory_service.active_conversations[str(conversation_id)] = conversation_instance

        result = self.thread_pool.map(
            agent_target,
            [prompt],
            [conversation_instance],
            [conversation_instance.active_session.session_id],
            [base_log_path],
            [True],
            [True if "feedback" in prompt else False],
        )

        return

    def start_update_data_dictionary(self, trigger_at):
        """
        If an update in the database of the data dictonry is detected then triggering a rewriting of the data dictionary txts
        rewriting the entire company folder

        """
        try:
            data_dictionary_service = DataDictionaryService()
            data_dictionary_service.trigger_data_dict_sync(trigger_at)

        except Exception as e:
            logger.warning(f"start_update_data_dictionary - Error: {str(e)}")

    def data_dictionary_scheduled_crawl(self):
        """
        Checks for updates in the data dictionary on a scheduled basis. If an update is detected,
        it triggers an update of the data dictionary.

        """
        try:
            trigger_at = datetime.now(timezone.utc)
            logger.info(
                f"SchedulerJobsService.data_dictionary_scheduled_crawl | data_dictionary_scheduled_crawl function Triggered at: {trigger_at}"
            )
            data_dictionary_sync_disabled = os.getenv("DATA_DICTIONARY_SYNC_DISABLED", "False")
            if data_dictionary_sync_disabled == "True":
                return

            execution_time = int(datetime.now(timezone.utc).timestamp() * 1000)
            path = "./data_dictionaries"
            company_name = os.getenv("COMPANY")
            company_folder = os.path.join(path, company_name)

            if os.path.exists(company_folder) and os.path.isdir(company_folder):

                # retrieve lastDataDictionarySyncedAt from database
                last_data_dictionary_synced = self.system_data_collection.get_one_document(
                    {}, {"lastDataDictionarySyncedAt": 1}
                )

                last_data_dictionary_synced_at = None
                if last_data_dictionary_synced and "lastDataDictionarySyncedAt" in last_data_dictionary_synced:
                    last_data_dictionary_synced_at = last_data_dictionary_synced["lastDataDictionarySyncedAt"]
                    # Ensure last_data_dictionary_synced_at has UTC timezone
                    if last_data_dictionary_synced_at.tzinfo is None:
                        last_data_dictionary_synced_at = last_data_dictionary_synced_at.replace(tzinfo=timezone.utc)

                logger.info(
                    f"SchedulerJobsService.data_dictionary_scheduled_crawl | Data Dictionary last synced time: {last_data_dictionary_synced_at}"
                )

                layernext_api_key = os.getenv("API_KEY")
                layernext_secret_key = os.getenv("SECRET_KEY")
                layernext_url = os.getenv("URL")
                layernext_client = layernext.LayerNextClient(layernext_api_key, layernext_secret_key, layernext_url)
                data_dictionary_modified_time = layernext_client.get_data_dictionary_modified_timestamp()

                # Convert string timestamp to datetime if it's not already
                if isinstance(data_dictionary_modified_time, str):
                    data_dictionary_modified_time = datetime.fromisoformat(
                        data_dictionary_modified_time.replace("Z", "+00:00")
                    )

                logger.info(
                    f"SchedulerJobsService.data_dictionary_scheduled_crawl | Data Dictionary last updated time: {data_dictionary_modified_time}"
                )

                if (
                    not last_data_dictionary_synced_at
                    or data_dictionary_modified_time > last_data_dictionary_synced_at
                ):
                    self.start_update_data_dictionary(trigger_at)
                    logger.info(
                        "SchedulerJobsService.data_dictionary_scheduled_crawl | Data Dictionary updated successfully: "
                        + str(execution_time)
                    )
                else:
                    logger.info(
                        "SchedulerJobsService.data_dictionary_scheduled_crawl | No update in data dictionary: "
                        + str(execution_time)
                    )
            else:
                self.start_update_data_dictionary(trigger_at)
                logger.info(
                    "SchedulerJobsService.data_dictionary_scheduled_crawl | Data Dictionary created successfully: "
                    + str(execution_time)
                )
        except Exception as e:
            logger.warning(
                f"data_dictionary_scheduled_crawl | Error in company folder creation: {traceback.format_exc()}"
            )

    def update_insight_dashboard(self):
        """
        Runs every day to check for insights that need to be generated .
        This method is called by the daily scheduler.
        """

        now = datetime.now(self.timezone)

        logger.info(
            f"SchedulerJobsService.update_insight_dashboard | Invoking execution of update_insight_dashboard at {now}"
        )

        insight_jobs = self.insight_dashboard_collection.get_documents({"nextRunAt": {"$lte": now}})

        for job in insight_jobs:
            logger.info(
                f"SchedulerJobsService.update_insight_dashboard | insight {str(job['_id'])} is starting for generation"
            )
            dashboard_insight_id = job["_id"]
            # Load the conversation for the insight board
            parent_conversation = self.conversation_service.load_conversation(str(job["conversationId"]))
            future = self.thread_pool.submit(
                self.insight_dashboard_service.generate_insight_for_analysis, dashboard_insight_id, parent_conversation
            )
            # Pass the future to a handler to process the result later
            self.insight_dashboard_service.handle_insight_generation_result(future, dashboard_insight_id)

            # sleep 1min before next job
            time.sleep(60)

        logger.info(
            f"SchedulerJobsService.update_insight_dashboard | Completed invoking execution of update_insight_dashboard at {datetime.now(self.timezone)} which started at {now}"
        )

    def send_usage_stats(self):
        """
        Sends usage stats to the server.

        """
        analysis_type_pipeline = [{"$match": {"type": 1, "isInternal": False}}, {"$count": "analysis_type_count"}]
        analysis_type_result = self.conversations_collection.aggregate(analysis_type_pipeline)
        analysis_type_count = analysis_type_result[0]["analysis_type_count"] if analysis_type_result else 0

        insight_type_pipeline = [{"$match": {"type": 3, "isInternal": False}}, {"$count": "insight_type_count"}]

        insight_type_result = self.conversations_collection.aggregate(insight_type_pipeline)
        insight_type_count = insight_type_result[0]["insight_type_count"] if insight_type_result else 0

        logger.info(f"Count of documents with type 1 and isInternal false: {analysis_type_count}")

        self.send_stats(analysis_type_count, insight_type_count, 0)

    def post_request(self, url, header, payload):

        try:
            response = requests.post(url=url, json=payload, headers=header)
            if response.status_code == 200 or response.status_code == 201:
                return response.json()
            else:
                return {"success": False}
        # Handle connection error
        except requests.exceptions.ConnectionError as e:
            logger.error("SchedulerJobsService.post_request | Connection error")
            return {"success": False}
        # Handle timeout error
        except requests.exceptions.Timeout as e:
            logger.error("SchedulerJobsService.post_request | Timeout error")
            return {"success": False}
        # Handle HTTP errors
        except requests.exceptions.HTTPError as e:
            logger.error("SchedulerJobsService.post_request | HTTP error")
            return {"success": False}
        except requests.exceptions.RequestException as e:
            logger.error(f"SchedulerJobsService.post_request | An unexpected request exception occurred: {format(e)}")
            traceback.print_exc()
            return {"success": False}
        except Exception as e1:
            logger.error(f"SchedulerJobsService.post_request | An unexpected exception occurred: {format(e1)}")
            traceback.print_exc()
            return {"success": False}

    def send_stats(self, number_of_analysis_questions, number_of_insight_questions, number_of_failed_questions):
        logger.info(
            f"SchedulerJobsService.send_stats | Invoking execution of send_stats at {datetime.now(self.timezone)}"
        )
        payload = {
            "numberOfAnalysisQuestions": number_of_analysis_questions,
            "numberOfInsightQuestions": number_of_insight_questions,
            "numberOfFailedQuestions": number_of_failed_questions,
        }

        send_stats_response = self.post_request(
            "https://cms.layernext.ai//api/customer/update/usage-stats", self.get_api_auth_header(), payload
        )

        logger.info(f"SchedulerJobsService.send_stats | send response {datetime.now(self.timezone)}")

        return send_stats_response

    def get_api_auth_header(self):
        logger.info(
            f"SchedulerJobsService.get_api_auth_header | Invoking execution of get_api_auth_header at {datetime.now(self.timezone)}"
        )
        # customer download attempt identification header
        # Get installation_uuid from environment variable
        installation_uuid = os.getenv("INSTALLATION_UUID")

        _key_secret_bytes = f"{installation_uuid}:{installation_uuid}".encode("ascii")
        _encoded_key_secret_bytes = base64.b64encode(_key_secret_bytes)
        encoded_key_secret = _encoded_key_secret_bytes.decode("ascii")
        api_auth_header = {"Authorization": "Basic " + encoded_key_secret}
        return api_auth_header
