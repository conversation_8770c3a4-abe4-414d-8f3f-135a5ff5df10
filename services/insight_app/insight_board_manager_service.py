"""
* Copyright (c) 2025 LayerNext, Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class InsightBoardManagerService
* @description This class is responsible for managing insight generation queries and value extraction operations.
* <AUTHOR> Chan<PERSON>, Chathura
"""

# When insight analysis is requested - this service will be called
# Call pre data analysis
# extract contextual data for the analysis/ define analysis framework
# fetch KPI data
# call kpi analysis
# generate insight query

# import data identification, loading data,
import base64
import json
from logging import Logger
import os
import pathlib
import traceback
from utils.misc_utils import csv_to_markdown
from models.schemas.responses import GeneralResponse
from services.base_agent_service import BaseAgent
from models.conversation import Conversation
import datetime
from utils.llm_utils import (
    insight_query_schema,
    insight_board_value_extract_schema,
    insight_board_main_graph_render_schema,
    insight_board_further_analysis_render_schema,
    insight_board_identify_new_key_findings_schema,
)
from utils.llm_utils import get_dict_from_json_or_python

from utils.logger import get_debug_logger

if not os.path.exists(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs")):
    os.makedirs(pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs"))

main_logger = get_debug_logger(
    "insight_dashboard_manager_service",
    pathlib.Path.joinpath(pathlib.Path(__file__).parent.resolve(), "../logs/server.log"),
)


class InsightBoardManagerService(BaseAgent):
    def __init__(self, model_name):
        super().__init__(model_name)
        self.model = model_name
        self.llm_model_visual_render = os.getenv("MODEL_VISUAL_RENDER", model_name)
        self.llm_model_key_finding_analyzer = os.getenv("MODEL_KEY_FINDING_ANALYZER", model_name)

        self.insight_query_generation_instructions = ""
        instruction_file = "instructions/insight_app/insight_query_generation_weekly_instructions.txt"
        with open(instruction_file, "r", encoding="utf-8") as file:
            self.insight_query_generation_instructions = str(file.read())

        self.insight_board_value_extract_instructions = ""
        instruction_file = "instructions/insight_app/insight_board_value_extract_instructions.txt"
        with open(instruction_file, "r", encoding="utf-8") as file:
            self.insight_board_value_extract_instructions = str(file.read())

        self.insight_board_main_graph_render_instructions = ""
        instruction_file = "instructions/insight_app/insight_board_main_graph_render_instructions.txt"
        with open(instruction_file, "r", encoding="utf-8") as file:
            self.insight_board_main_graph_render_instructions = str(file.read())
        self.insight_board_further_analysis_render_instructions = ""
        instruction_file = "instructions/insight_app/insight_board_further_analysis_render_instructions.txt"
        with open(instruction_file, "r", encoding="utf-8") as file:
            self.insight_board_further_analysis_render_instructions = str(file.read())

        self.insight_board_identify_new_key_findings_instructions = ""
        instruction_file = "instructions/insight_app/insight_board_identify_new_key_findings_instructions.txt"
        with open(instruction_file, "r", encoding="utf-8") as file:
            self.insight_board_identify_new_key_findings_instructions = str(file.read())

    def generate_insight_query(self, user_question, kpi_data, analysis_frequency):

        instructions = self.insight_query_generation_instructions
        instructions = instructions.replace("<<USER_QUESTION>>", user_question)
        # instructions = instructions.replace("<<CURRENT_DATETIME>>", "2024-09-30") # a test input
        instructions = instructions.replace(
            "<<CURRENT_DATETIME>>",
            str(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")),
        )
        instructions = instructions.replace("<<ANALYSIS_FREQUENCY>>", analysis_frequency)
        llm_prompt = []
        llm_prompt.append({"role": "user", "content": str(instructions)})
        llm_prompt.append({"role": "assistant", "content": f"KPI Data: {str(kpi_data)}"})
        llm_prompt.append(
            {
                "role": "assistant",
                "content": f"Business Overview: {str(self.data_dictionary_service.get_business_overview())}",
            }
        )
        try:
            response = self.client.get_llm_response(
                self,
                None,  # conversation
                main_logger,
                llm_prompt,
                self.llm_model_name,
                response_format=insight_query_schema,
            )
            if response is None:
                return GeneralResponse(success=False, message="LLM response is None for insight query generation")

            llm_output = response.choices[0].message.content
            output_dict = get_dict_from_json_or_python(llm_output)
            return GeneralResponse(success=True, data=output_dict)
        except Exception as e:
            main_logger.error(
                f" InsightBoardManagerService.generate_insight_query | Error in generating insight query: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=f"Error in LLM response: {str(e)}")

    def llm_extract_current_and_previous_values(
        self, user_question: str, markdown_table_data: str
    ) -> GeneralResponse[dict]:
        """
        Extract the current and previous values from the markdown table data using LLM
        Args:
            user_question (str): The user question (asking to get a table with values aggregated by period such as week, month, or quarter)
            markdown_table_data (str): The markdown table data containing the values
        Returns:
            dict: A dictionary containing the current and previous values
        """
        instructions = self.insight_board_value_extract_instructions
        llm_prompt = [
            {
                "role": "system",
                "content": instructions,
            },
            {
                "role": "user",
                "content": f"User Question: {user_question}",
            },
            {
                "role": "user",
                "content": f"Markdown Table Data: {markdown_table_data}",
            },
        ]
        try:
            main_logger.debug(f"llm_extract_current_and_previous_values | llm_prompt: {llm_prompt}")
            response = self.client.get_llm_response(
                self,
                None,  # conversation
                main_logger,
                llm_prompt,
                self.llm_model_visual_render,
                response_format=insight_board_value_extract_schema,
            )
            if response is None:
                return GeneralResponse(success=False, message="LLM response is None for extracting values")

            llm_output = response.choices[0].message.content
            main_logger.debug(f"llm_extract_current_and_previous_values | llm_output: {llm_output}")
            output_dict = get_dict_from_json_or_python(llm_output)
            return GeneralResponse(success=True, data=output_dict)
        except Exception as e:
            main_logger.error(
                f"InsightBoardManagerService.llm_extract_current_and_previous_values | Error in extracting values: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=f"Error in LLM response: {str(e)}")

    def run(self, user_question, kpi_data, analysis_frequency) -> GeneralResponse[str]:
        try:
            insight_query_dict = self.generate_insight_query(user_question, kpi_data, analysis_frequency)

            if insight_query_dict.success:
                insight_query = insight_query_dict.data.get("insight_query", "")
                if insight_query:
                    return GeneralResponse(success=True, data=insight_query)
                else:
                    return GeneralResponse(success=False, message="Failed to get insight query")
            else:
                return GeneralResponse(success=False, message=insight_query_dict.message)
        except Exception as e:
            main_logger.error(
                f"insight_board_manager_service.run | Error in generating insight query: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=f"Error in generating insight query: {str(e)}")

    def execute_agent(
        self,
        user_prompt,
        conversation: Conversation,
        session_id,
        chat_log,
        is_user_input=True,
        debug_loop=None,
        agent_type="chat_agent",
    ):
        return

    def before_session_stream_complete(self, conversation: Conversation, chat_log):
        return

    def insight_board_main_graph_render(
        self, csv_data_with_user_inputs: list, frequencyInfo: dict
    ) -> GeneralResponse[dict]:
        """
        Render the main graph for the insight board using LLM
        Args:
            user_question (str): The user question
            answer_data (str): The answer data containing the values
        Returns:
            dict: A dictionary containing the rendered graph data
        """
        instructions = self.insight_board_main_graph_render_instructions
        ui_graph_components = ""
        with open("instructions/insight_app/ui_graph_components.txt", "r", encoding="utf-8") as file:
            ui_graph_components = str(file.read())
        instructions = instructions.replace("<<UI_GRAPH_COMPONENTS>>", ui_graph_components)

        llm_prompt = [
            {
                "role": "system",
                "content": instructions,
            },
        ]

        # add user input and csv data as markdown table
        for csv_data_with_user_input in csv_data_with_user_inputs:
            llm_prompt.append(
                {
                    "role": "user",
                    "content": f"User Question: {csv_data_with_user_input['user_input']}\n",
                }
            )
            for csv_file in csv_data_with_user_input["csv_file_list"]:
                # read csv file data and convert to markdown table
                ROW_LIMIT = 500
                markdown_table, is_truncated, row_count = csv_to_markdown(csv_file["file_path"], ROW_LIMIT)
                llm_prompt.append(
                    {
                        "role": "user",
                        "content": (
                            f"Table data:\n{markdown_table}\n\n"
                            if not is_truncated
                            else f"Table data: {markdown_table}\n*Note: Table truncated to first {ROW_LIMIT} rows. There are {row_count - ROW_LIMIT} more rows in the table.*\n\n"
                        ),
                    }
                )

        llm_prompt.append(
            {
                "role": "user",
                "content": f"KPI Evaluation Frequency: {frequencyInfo['frequency']}",
            }
        )
        llm_prompt.append(
            {
                "role": "user",
                "content": f"Current Date: {datetime.datetime.now().strftime('%Y-%m-%d')}",
            }
        )

        try:
            response = self.client.get_llm_response(
                self,
                None,  # conversation
                main_logger,
                llm_prompt,
                self.llm_model_visual_render,
                response_format=insight_board_main_graph_render_schema,
            )
            if response is None:
                return GeneralResponse(success=False, message="LLM response is None for rendering main graph")

            llm_output = response.choices[0].message.content
            output_dict = get_dict_from_json_or_python(llm_output)
            return GeneralResponse(success=True, data=output_dict)
        except Exception as e:
            main_logger.error(
                f"InsightBoardManagerService.insight_board_main_graph_render | Error in rendering main graph: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=f"Error in LLM response: {str(e)}")

    def extract_insights_from_further_analysis_result(
        self,
        kpi_param: str,
        kpi_analysis_result: str,
        tabular_data: list,
        analysis_thought_chain: list,
        logger: Logger,
    ):
        """
        Extract key insights, charts and tables from the insight generation result using LLM and then build the data structure for remaining parts of the insight board. The output format is defined in the schema.
        Args:
            kpi_param (str): The KPI parameter
            kpi_analysis_result (str): The result of the further analysis
            tabular_data (list): The tabular data relevant to the analysis result
            analysis_thought_chain (list): The thought process of the AI agent that led to the analysis result
        Returns:
            dict: The rendered result
        """
        # LLM Input: Further analysis result
        # LLM Output: Key insights, observation list
        # {
        #     "key_insights": ["insight1", "insight2"],
        #     "observation_list": [{
        #            "observation_title": "<observation title>",
        #             "observation_description": "<observation description>",
        #             "observation_items":[
        #                 {
        #                     "type": "table",
        #                     "title": "table title",
        #                     "file_name": "table_file.csv",
        #                 },
        #                 {
        #                     "type": "image",
        #                     "title": "image title",
        #                     "file_name": "image_file.png",
        #                 }
        #             ]
        #         }
        #     ]
        # }
        prompts_list = [
            {
                "role": "system",
                "content": self.insight_board_further_analysis_render_instructions,
            },
            {
                "role": "user",
                "content": f"KPI: {kpi_param}",
            },
            {
                "role": "user",
                "content": f"KPI Evaluation Result Summary: {kpi_analysis_result}",
            },
            {
                "role": "user",
                "content": f"Output Tabular Data: {json.dumps(tabular_data)}",
            },
            {
                "role": "user",
                "content": f"Thought Process: {json.dumps(analysis_thought_chain)}",
            },
        ]
        try:
            response = self.client.get_llm_response(
                self,
                None,  # conversation
                logger,
                prompts_list,
                self.llm_model_visual_render,
                response_format=insight_board_further_analysis_render_schema,
            )
            if response is None:
                return GeneralResponse(success=False, message="LLM response is None for rendering further analysis")

            llm_output = response.choices[0].message.content
            output_dict = get_dict_from_json_or_python(llm_output)
            return GeneralResponse(success=True, data=output_dict)
        except Exception as e:
            logger.error(
                f"InsightBoardManagerService.render_further_analysis_result | Error in rendering further analysis: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=f"Error in LLM response: {str(e)}")

    def identify_significant_new_key_findings(
        self,
        kpi_param: str,
        kpi_frequency_info: dict,
        kpi_status: dict,
        current_key_insights: list,
        previous_key_insights_and_findings: list,
        logger: Logger,
    ):
        """
        Identify the significant new key findings from the current key insights and the previous key insights using LLM
        Args:
            kpi_param (str): The KPI parameter
            kpi_frequency_info (dict): The KPI frequency info
            kpi_status (dict): The KPI status
            current_key_insights (list): The current key insights
            previous_key_insights_and_findings (list): The previous key insights and new findings
        Returns:
            list: The significant new key findings as a list of strings
        """
        # From  previous_key_insights_and_findings extract only the necessary fields and create json list
        previous_key_insights_and_findings_list = []
        for previous_key_insights_and_findings_item in previous_key_insights_and_findings:
            previous_key_insights_and_findings_list.append(
                {
                    "key_insights": previous_key_insights_and_findings_item.get("keyInsights", []),
                    "new_findings": previous_key_insights_and_findings_item.get("newFindings", []),
                    "updated_time": str(previous_key_insights_and_findings_item.get("updatedAt", "")),
                }
            )
        prompts_list = [
            {
                "role": "system",
                "content": self.insight_board_identify_new_key_findings_instructions,
            },
            {
                "role": "user",
                "content": f"KPI: {kpi_param}, evaluation frequency: {kpi_frequency_info['frequency']}, current status: {kpi_status}",
            },
            {
                "role": "user",
                "content": f"Current Key Insights: {json.dumps(current_key_insights)}",
            },
            {
                "role": "user",
                "content": f"Previous Key Insights and New Findings: {json.dumps(previous_key_insights_and_findings_list)}",
            },
        ]
        try:
            response = self.client.get_llm_response(
                self,
                None,
                logger,
                prompts_list,
                self.llm_model_key_finding_analyzer,
                response_format=insight_board_identify_new_key_findings_schema,
            )
            if response is None:
                return GeneralResponse(success=False, message="LLM response is None for identifying new key findings")

            llm_output = response.choices[0].message.content
            output_dict = get_dict_from_json_or_python(llm_output)
            return GeneralResponse(success=True, data=output_dict)
        except Exception as e:
            logger.error(
                f"InsightBoardManagerService.identify_significant_new_key_findings | Error in identifying new key findings: {traceback.format_exc()}"
            )
            return GeneralResponse(success=False, message=f"Error in LLM response: {str(e)}")
