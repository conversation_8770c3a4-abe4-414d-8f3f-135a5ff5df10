"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class PythonExecuteAgent
* @description Agent responsible for executing Python code generated by other agents.
* <AUTHOR>
"""

import csv
import json
import shutil
import os
import traceback
import pickle
from logging import Logger
from utils.misc_utils import list_to_markdown
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationLLMStatus,
    ConversationStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    LLMAgentType,
    MessageContentType,
)
from PIL import Image
from utils.llm_utils import (
    check_image,
)
from utils.metalake import save_data

FRONTEND_URL = os.getenv("FRONTEND_URL")


class PythonExecuteAgent(BaseStateHandlerAgent):
    """
    Agent responsible for executing Python code generated by other agents.

    This agent takes Python code and executes it in a controlled environment,
    capturing the output and handling any execution errors.
    """

    def __init__(
        self, data_source_name: str, _default_model, initial_state: AgentState = AgentState.PYTHON_CODE_EXECUTION
    ):
        """
        Initialize the PythonExecuteAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing PythonExecuteAgent class")
        super().__init__(data_source_name, _default_model, initial_state, history_depth=0)

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Execute Python code based on the input data.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing code to execute
                Format: {
                    "python_code": Python code to execute,
                    "user_question": Original user question (optional)
                }
            follow_up_history (list): List of previous messages in the conversation
            current_session_history (list): List of current session messages
            chat_log (Logger): Logger object
            try_count (int): Number of times the agent is invoked for same conversation

        Returns:
            Dictionary containing the execution results and status
        """
        chat_log.debug("PythonExecuteAgent | on_agent_invoked | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.EXECUTE)

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.EXECUTING_PYTHON_CODE.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )

        # Extract Python code from input data
        python_code = input_data.get("python_code", "")
        if not python_code:
            return self.on_failure("No Python code provided for execution", input_data)

        # Execute the Python code
        try:
            # Log the code being executed
            chat_log.info(f"PythonExecuteAgent | Executing Python code:\n{python_code}")

            # Execute the code using the execution environment
            exec_result = conversation.execution_env.python_exec(action=python_code, chat_id=conversation.chat_id)

            # Check execution status
            if not exec_result["success"]:
                error_message = exec_result.get("output", "Unknown execution error")
                chat_log.error(f"PythonExecuteAgent | Execution failed: {error_message}")

                # Add execution error to conversation history
                self.add_to_history(
                    conversation,
                    "system",
                    [f"Execution Error: {error_message}"],
                    True,
                    True,
                    MessageContentType.TEXT.value,
                )
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data="\n\nExecution Error: " + error_message + "\n\n",
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )

                # Note: Even if execution failed, we are returning success because we need to go to re-invoke code writer agent
                return self.on_success(
                    input_data,
                    {"execution_error": error_message, "execution_success": False},
                )

            # Execution successful
            execution_output = exec_result.get("output", "")
            chat_log.info(f"PythonExecuteAgent | Execution successful with output: {execution_output}")

            csv_paths, img_paths, output_data_files = self.prepare_answer_from_file_output(
                input_data.get("file_paths", {}), execution_output, conversation, chat_log
            )

            # Process the execution result and file outputs (images, CSVs, etc.)
            # (
            #     is_final_answer_processing_needed,
            #     next_prompt,
            #     img_paths,
            #     csv_paths,
            # ) = self.process_file_output(execution_output, conversation, chat_log)
            # Append file paths to the output markdown content

            # Prepare input for data review
            next_input = {
                "user_question": conversation.user_inputs[-1],
                "python_code": python_code,
                "execution_result": execution_output,
                "execution_success": True,
                "csv_paths": csv_paths,
                "image_file_paths": img_paths,
                "output_data_files": output_data_files,
            }

            chat_log.debug("PythonExecuteAgent | on_agent_invoked | End")
            # Return success with execution output
            return self.on_success(input_data, next_input)

        except Exception as e:
            error_message = f"Exception during Python code execution: {str(e)}\n{traceback.format_exc()}"
            chat_log.error(f"PythonExecuteAgent | {error_message}")

            return self.on_failure(error_message, input_data)

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("PythonExecuteAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        error_message = "Failed to generate correct Python code.\n\n"
        if "execution_error" in input_data:
            error_message += "\n\nExecution error:\n\n" + input_data.get("execution_error", "")
        if "review_feedback" in input_data:
            error_message += "\n\nReview feedback:\n\n" + input_data.get("review_feedback", "")

        return self.on_failure(error_message, input_data)

    def prepare_answer_from_file_output(self, file_paths, markdown_content, conversation: Conversation, chat_log):
        """
        Process file paths and prepare media content for the conversation.

        Args:
            file_paths (dict): Dictionary containing image and csv file paths
            markdown_content (str): Markdown content to include in the answer
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger object
        Returns:
            tuple: <valid csv paths, valid image paths, output data file names>
        """
        image_file_paths_list = []  # Images generated by Python code execution
        data_file_paths_list = []  # CSV or other data files generated by Python code execution
        contents = []
        is_final_answer_processing_needed = False
        content_type = MessageContentType.MEDIA.value

        # Get image and CSV paths from input
        img_paths = file_paths.get("image", []) if file_paths else []
        csv_paths = file_paths.get("csv", []) if file_paths else []
        output_data_files = []
        if not img_paths and not csv_paths:
            return [], [], []

        # Validate paths and filter out invalid ones
        valid_img_paths = [path for path in img_paths if os.path.exists(path)]
        valid_csv_paths = [path for path in csv_paths if os.path.exists(path)]

        if valid_img_paths != img_paths:
            chat_log.warn(f"Some image paths were invalid: {set(img_paths) - set(valid_img_paths)}")

        if valid_csv_paths != csv_paths:
            chat_log.warn(f"Some CSV paths were invalid: {set(csv_paths) - set(valid_csv_paths)}")

        # Combine all valid files to process
        files = valid_img_paths + valid_csv_paths

        if not files:
            chat_log.debug("No valid files found to process")
            return [], [], []

        # Create storage directory
        new_path = f"storage/public/{conversation.parent_conversation_id}"

        for source_file in files:
            try:
                name, extension = os.path.splitext(os.path.basename(source_file))
                folder_name, file_name = os.path.split(source_file)

                # Create the new directory path including the folder name if not exists
                new_directory = os.path.join(new_path, folder_name)
                os.makedirs(new_directory, exist_ok=True)
                new_file_path = os.path.join(new_directory, file_name)

                # Process image files
                resized_width = 724
                if check_image(extension):
                    # Resize the image to a width of 724px, maintaining the aspect ratio
                    img_width = -1
                    img_height = -1
                    with Image.open(source_file) as img:
                        if img.width > resized_width:
                            aspect_ratio = img.height / img.width
                            new_height = int(resized_width * aspect_ratio)
                            resized_img = img.resize((resized_width, new_height), Image.Resampling.LANCZOS)
                            resized_img.save(new_file_path)
                            img_height = new_height
                            img_width = resized_width
                        else:
                            img_width = img.width
                            img_height = img.height
                            shutil.move(source_file, new_file_path)

                    image_file_paths_list.append(new_file_path)
                    # Store the image file names in the conversation object
                    conversation.insight_image_file_paths.append(new_file_path)

                    contents.append(
                        {
                            "mediaUrl": f"{FRONTEND_URL}/api/file/public/{conversation.parent_conversation_id}/download?file_name={file_name}",
                            "fileKey": file_name,
                            "width": img_width,
                            "height": img_height,
                            "mediaType": "image",
                            "fileName": file_name,
                            "isMetaLakeRedirect": False,
                        }
                    )
                elif extension in [".csv", ".pdf", ".txt", ".dat"]:
                    # Process other file types (csv, pdf, txt, dat)
                    shutil.move(source_file, new_file_path)
                    data_file_paths_list.append(new_file_path)
                    # Store the data file names in the conversation object
                    conversation.insight_data_file_paths.append(new_file_path)
                    data = []
                    data_file_name = file_name
                    # If extension is csv, then convert to dat and save to metalake
                    # Copy CSV file data to pickle
                    if extension == ".csv":
                        with open(new_file_path, "r", encoding="utf-8") as file:
                            reader = csv.DictReader(file)
                            data = [row for row in reader]
                            # Remove extension of file name
                            data_file_name = file_name.replace(extension, ".dat")
                            if save_data(conversation.chat_id, data_file_name, data):
                                chat_log.debug(
                                    f"python_execute_agent | _prepare_answer_from_file_output | Saved data to metalake for file {data_file_name}"
                                )
                                contents.append(
                                    {
                                        "mediaUrl": f"{FRONTEND_URL}/api/file/public/{conversation.parent_conversation_id}/download?file_name={file_name}",
                                        "fileKey": file_name,
                                        "mediaType": extension.replace(".", "").lower(),
                                        "fileName": file_name,
                                        "isMetaLakeRedirect": False,
                                    }
                                )
                            else:
                                chat_log.warning(
                                    f"python_execute_agent | _prepare_answer_from_file_output | Failed to save data file {data_file_name}"
                                )
                    elif extension == ".dat":
                        with open(new_file_path, "r", encoding="utf-8") as file:
                            # Read data to dictionary
                            data = json.load(file)
                        if save_data(conversation.chat_id, data_file_name, data):
                            chat_log.debug(
                                f"python_execute_agent | _prepare_answer_from_file_output | Saved data to metalake for file {data_file_name}"
                            )
                        else:
                            chat_log.warning(
                                f"python_execute_agent | _prepare_answer_from_file_output | Failed to save data file {data_file_name}"
                            )
                    if data:
                        output_data_files.append(
                            {
                                "data_file_name": data_file_name,
                                "record_count": len(data),
                                "first_10_records_preview": list_to_markdown(data[:10]),
                            }
                        )
                        # Add to the conversation data reference set
                        conversation.data_reference_set.add(data_file_name)
                else:
                    chat_log.warn(f"Unsupported file type: {extension} for file {source_file}")

            except Exception as e:
                chat_log.error(f"Error processing file {source_file}: {str(e)}\n{traceback.format_exc()}")
                continue

        # If no files were successfully processed, return early
        if not contents:
            chat_log.info("No files were successfully processed")
            return [], [], []

        # Flush the answer section before sending the media content
        conversation.active_session.session_answer_list = []

        # Process markdown content if available
        if markdown_content:
            # Add answer section before send markdown table and files - this is not required in case of data availability check agents
            if conversation.agent_type.value != LLMAgentType.AGENT_TYPE_ACTION.value:

                conversation.active_session.session_answer_list.append(
                    {
                        "content": "\n\n" + markdown_content,
                        "content_type": MessageContentType.TEXT.value,
                        "is_final_answer_processing_needed": is_final_answer_processing_needed,
                        "is_final_answer_processing_done": False,
                    }
                )

                # conversation.stop_stream = True

        # Add to session answer list
        conversation.active_session.session_answer_list.append(
            {
                "content": contents,
                "content_type": content_type,
                "is_final_answer_processing_needed": is_final_answer_processing_needed,
                "is_final_answer_processing_done": False,
            }
        )

        # # Add query logic as a separate entry if it exists
        # if hasattr(conversation, "retrieval_logic") and conversation.retrieval_logic:
        #     conversation.active_session.session_answer_list.append(
        #         {
        #             "content": str(conversation.retrieval_logic),
        #             "content_type": MessageContentType.TEXT.value,
        #             "is_final_answer_processing_needed": False,
        #             "is_final_answer_processing_done": False,
        #         }
        #     )
        return data_file_paths_list, image_file_paths_list, output_data_files

    def update_followup_history(self, conversation, chat_log, output_data):
        # Add execution result to conversation history
        self.add_to_history(
            conversation,
            "assistant",
            [f"Execution Result: {output_data['execution_result'] if 'execution_result' in output_data else ''}"],
            True,
        )
