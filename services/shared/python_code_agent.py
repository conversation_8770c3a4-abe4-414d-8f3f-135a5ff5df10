"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class PythonCodeAgent
* @description This class is responsible for generating Python code based on user instructions.
   It handles code generation, review, and execution processes.
* <AUTHOR>
"""

import os
import traceback
from logging import Logger
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    MessageContentType,
    SectionType,
)
from utils.llm_utils import get_dict_from_json_or_python, python_code_and_logic_schema


class PythonCodeAgent(BaseStateHandlerAgent):
    """
    Agent responsible for generating Python code based on user instructions.

    This agent takes user requirements and generates executable Python code
    that can be used to process data, create visualizations, or perform
    other programmatic tasks.
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the PythonCodeAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing PythonCodeAgent class")
        # Load most recent history for Python code generation
        super().__init__(data_source_name, _default_model, AgentState.PYTHON_CODE_GENERATION, history_depth=1)

        # Set model names from environment variables
        self.llm_model_name = os.getenv("MODEL_PYTHON_CODING", "gpt-4o")
        self.llm_model_name_structured_output = os.environ.get("MODEL_JSON_OUTPUT", "gpt-4o-mini")

        # Load SQL function usage instructions from "instructions/sources/sqldb.txt"
        with open("instructions/sources/sqldb.txt", "r", encoding="utf-8") as file:
            sql_function_instructions = str(file.read())
            self.main_system_instruction = self.main_system_instruction.replace(
                "<<SOURCE_INSTRUCTIONS>>", sql_function_instructions
            )

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Generate Python code based on user requirements.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing user requirements and context
                Format: {
                    "user_question": The latest user input,
                    "sql_query": SQL query from previous step,
                    "reasoning": Reasoning from previous step,
                    "data_source_name": Name of the data source
                    "sample_results": Sample results from SQL query
                    "review_feedback": Feedback from previous code review (if any)
                }
            follow_up_history (list): List of previous messages in the conversation
            current_session_history (list): List of current session messages
            chat_log (Logger): Logger object
            try_count (int): No of times the agent is invoked for same conversation previously

        Returns:
            Dictionary containing the generated Python code and execution status
        """
        chat_log.debug("PythonCodeAgent | on_agent_invoked | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.CODE)

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.GENERATING_PYTHON_CODE.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )

        # Generate Python code
        code_result = self.generate_python_code(
            conversation,
            input_data,
            chat_log,
            follow_up_history,
            current_session_history,
        )

        if code_result is None:
            chat_log.error(f"PythonCodeAgent | on_agent_invoked | Error in Python code generation with LLM")
            return self.on_failure("Error in Python code generation", input_data)

        # Send code to frontend and update the history
        formatted_python_code = "```python\n" + code_result["code"] + "\n```"
        conversation.llm_code_with_logic = formatted_python_code + "\n\n" + conversation.retrieval_logic
        # Now send the python code to the same analysis box
        # conversation.active_session.add_section(SectionType.ACTION.value)

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.PYTHON_CODE_GENERATED.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=formatted_python_code,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )

        # If review feedback exists, add that to history in case needed for next LLM round
        if input_data.get("review_feedback"):
            code_result["review_feedback"] = input_data["review_feedback"]
        if input_data.get("data_file_names"):
            code_result["data_file_names"] = input_data["data_file_names"]
        if input_data.get("data_input"):
            code_result["data_input"] = input_data["data_input"]
        if "execution_error" in input_data:
            code_result["execution_error"] = input_data["execution_error"]

        chat_log.debug(
            f"PythonCodeAgent | on_agent_invoked | End : Data reference set: {conversation.data_reference_set}"
        )
        return self.on_success(input_data, code_result)

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("PythonCodeAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return (
            "Failed to write correct Python code.\n\nLast python code:\n\n"
            + input_data.get("python_code", "")
            + "\n\Review feedback:\n\n"
            + input_data.get("review_feedback", "")
        )

    def generate_python_code(
        self, conversation, context_data, log_handle, follow_up_history, current_session_messages
    ):
        """
        Generate Python code based on user question and context data.

        Args:
            conversation (Conversation): The conversation object
            context_data (dict): Additional context data for code generation
                Format: {
                    "user_question": The latest user input,
                    "sql_query": SQL query from previous step,
                    "reasoning": Reasoning from previous step,
                    "data_source_name": Name of the data source
                    "sample_results": Sample results from SQL query
                    "review_feedback": Feedback from previous code review (if any)
                }
            log_handle (Logger): Logger object
            follow_up_history (list): List of previous session messages in the conversation
            current_session_messages (list): List of current session messages

        Returns:
            dict: Dictionary containing the generated code and execution status
        """
        # Prepare prompt for code generation
        llm_prompt_list = follow_up_history + current_session_messages
        if "execution_error" in context_data or "review_feedback" in context_data:
            # If this is a retry after a review feedback or execution error, then just add the feedback to the prompt
            if context_data.get("execution_error"):
                llm_prompt_list.append(
                    {
                        "role": "user",
                        "content": f"Please revise the code to correct the execution error: {context_data['execution_error']}",
                    }
                )
            if context_data.get("review_feedback"):
                llm_prompt_list.append(
                    {
                        "role": "user",
                        "content": f"Review Feedback: {context_data['review_feedback']}",
                    }
                )
        else:
            # Initial attempt
            llm_prompt_list.append(
                {
                    "role": "user",
                    "content": f"User request: {context_data['user_question']}",
                }
            )
            if "data_file_names" in context_data:
                # TODO: Enhance this for multiple data retrievals from multiple data sources
                llm_prompt_list.append(
                    {
                        "role": "user",
                        "content": f"Data file names: {str(context_data['data_file_names'])}",
                    }
                )
            if "data_input" in context_data:
                llm_prompt_list.append(
                    {
                        "role": "user",
                        "content": f"Data input: {context_data['data_input']}",
                    }
                )

            llm_prompt_list.append(
                {
                    "role": "user",
                    "content": f"chat_id: {conversation.chat_id}",
                }
            )

        invoke_count = 0
        python_code_and_logic_dict = None
        while invoke_count < 2:
            # Generate code using LLM
            response = self.invoke_llm(
                conversation,
                self.main_system_instruction,
                llm_prompt_list,
                log_handle,
                model_name=self.llm_model_name,
                response_format=python_code_and_logic_schema,
            )

            if response is None:
                return None

            python_code_and_logic_dict = get_dict_from_json_or_python(response)
            if python_code_and_logic_dict == -1:
                log_handle.error(f"PythonCodeAgent | generate_python_code | Error in structured output: {response}")
                python_code_and_logic_dict = None
                # Re-invoke the LLM
                llm_prompt_list.append({"role": "assistant", "content": response})
                llm_prompt_list.append(
                    {
                        "role": "user",
                        "content": "Cannot parse the given response. Please make sure to respond with proper JSON format.",
                    }
                )
                invoke_count += 1
            else:
                break

        return python_code_and_logic_dict

    def update_followup_history(self, conversation, chat_log, output_data):
        # Add user question to history
        self.add_to_history(
            conversation,
            "user",
            ["User Question: " + conversation.user_inputs[-1]],
            is_required_for_follow_ups=True,
        )
        if "data_file_names" in output_data:
            self.add_to_history(
                conversation,
                "user",
                ["Data file names: " + str(output_data["data_file_names"])],
                is_required_for_follow_ups=True,
            )
        if "data_input" in output_data:
            self.add_to_history(
                conversation,
                "user",
                [f"Data input: {output_data['data_input']}"],
                is_required_for_follow_ups=True,
            )
        if "chat_id" in output_data:
            self.add_to_history(
                conversation,
                "user",
                [f"chat_id: {output_data['chat_id']}"],
            )

        if output_data.get("review_feedback"):
            self.add_to_history(
                conversation,
                "user",
                ["Review Feedback: " + output_data["review_feedback"]],
            )
        if output_data.get("execution_error"):
            self.add_to_history(
                conversation,
                "user",
                ["Execution Error: " + output_data["execution_error"]],
            )

        self.add_to_history(
            conversation,
            "assistant",
            ["Python Code: " + output_data["code"] if "code" in output_data else ""],
            is_required_for_follow_ups=True,
        )
