"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class VisualReviewerAgent
* @description This class implements an agent for reviewing visualization outputs
"""

import os
import datetime
import base64
from logging import Logger
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    ConversationConclusion,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
)
from utils.llm_utils import get_dict_from_json_or_python, visualization_review_response_schema, handle_ai_exception


class VisualReviewerAgent(BaseStateHandlerAgent):
    """
    An agent responsible for reviewing visualization outputs.
    It checks for accuracy, proper visual presentation, and suggests improvements.
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the VisualReviewerAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing VisualReviewerAgent class")
        super().__init__(data_source_name, _default_model, AgentState.VISUAL_REVIEW)

        # Set model names from environment variables
        self.visual_reviewer_model = os.getenv("MODEL_VISUAL_REVIEWER", "gpt-4o")

        # Load visual reviewer instructions
        self.data_reviewer_visual_instructions = ""
        if os.path.exists("instructions/tools/visual_review/system_instructions.txt"):
            with open("instructions/tools/visual_review/system_instructions.txt", "r", encoding="utf-8") as file:
                self.data_reviewer_visual_instructions = str(file.read())

    def encode_image(self, image_path, chat_log):
        """
        Encode an image to base64 for inclusion in API requests.

        Args:
            image_path (str): Path to the image file
            chat_log (Logger): Logger object

        Returns:
            str: Base64 encoded image or None if encoding fails
        """
        try:
            with open(image_path, "rb") as image_file:
                return base64.b64encode(image_file.read()).decode("utf-8")
        except Exception as e:
            chat_log.error(f"Error encoding image {image_path}: {str(e)}")
            return None

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Review visualization outputs based on the input data.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing data to review and context
                Format: {
                    "user_question": The latest user input,
                    "python_code": Python code used for data retrieval and visualization,
                    "execution_result": Data observations from execution,
                    "image_file_paths": List of image file paths generated
                }
            follow_up_history (list): List of previous messages in the conversation
            current_session_history (list): List of current session messages
            chat_log (Logger): Logger object
            try_count (int): No of times the agent is invoked for same conversation previously

        Returns:
            Dictionary containing the review results and execution status
        """
        chat_log.debug("VisualReviewerAgent | on_agent_invoked | Start")

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.REVIEWING_VISUAL_DATA.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )

        # Extract necessary data from input
        task = input_data.get("user_question", "")
        python_code = input_data.get("python_code", "")
        data_observations_text = input_data.get("execution_result", "")
        image_file_paths = input_data.get("image_file_paths", [])

        # If no images to review, return success with no revision required
        if not image_file_paths:
            return self.on_success(
                input_data, {"is_revision_required": False, "feedback": "", "is_data_issues": False}
            )

        # Prepare instructions with task and current datetime
        instructions = self.data_reviewer_visual_instructions
        instructions = instructions.replace("<<task>>", task)
        instructions = instructions.replace(
            "<<CURRENT_DATETIME>>", str(datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"))
        )

        # Prepare the prompt for visual review
        llm_prompt = [{"role": "system", "content": instructions}]

        # Add visualization issue feedback history if available
        visualization_issue_feedback = [
            feedback
            for feedback in conversation.data_reviewer_feedback_history
            if feedback.get("is_visualization_issue")
        ]
        if visualization_issue_feedback:
            llm_prompt.append(
                {
                    "role": "assistant",
                    "content": f"Feedback History: \n {str(visualization_issue_feedback)}",
                }
            )

        # Add Python code and data observations
        llm_prompt.append(
            {
                "role": "assistant",
                "content": f"Python Code: \n {python_code}",
            }
        )
        llm_prompt.append(
            {
                "role": "assistant",
                "content": f"Data Observations: \n {data_observations_text}",
            }
        )

        # Add images to the prompt
        content_list = []
        for file_path in image_file_paths:
            content_list.append(
                {
                    "type": "text",
                    "text": f"The visual representations of the data observations are in the image inputs. Review images according to instruction provided.",
                }
            )
            if file_path is not None and (file_path.endswith(".png") or file_path.endswith(".jpg")):
                base64_image = self.encode_image(file_path, chat_log)
                if base64_image is None:
                    continue
                content_list.append(
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/jpeg;base64,{base64_image}"},
                    }
                )

        if content_list:
            llm_prompt.append({"role": "user", "content": content_list})

        # Invoke LLM for visual review
        try:
            response = self.invoke_llm(
                conversation,
                instructions,
                llm_prompt,
                chat_log,
                response_format=visualization_review_response_schema,
                model_name=self.visual_reviewer_model,
            )

            if not response:
                return self.on_failure("Failed to get response from LLM for visual review", input_data)

            # Parse the response
            llm_response_dict = get_dict_from_json_or_python(response)

            if not llm_response_dict or llm_response_dict == -1:
                chat_log.error(f"Visual Reviewer Response Error: {response}")
                return self.on_failure("Failed to parse LLM response for visual review", input_data)

            # Process the review result
            is_revision_required = False
            if (
                llm_response_dict.get("is_revision_required", False)
                or llm_response_dict.get("is_visualization_issue", False)
            ) and llm_response_dict.get("issue_severity", "") == "CRITICAL":

                is_revision_required = True
                llm_response_dict[
                    "feedback"
                ] += "\n Special Instructions: The data retrieval logic must remain completely unchanged. Focus exclusively on resolving the specified visualization issues."

            if is_revision_required:
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.VISUAL_DATA_REVIEW_FAILED.value,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )
            else:
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.VISUAL_DATA_REVIEW_PASSED.value,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                    user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
                )

            # Return the review result
            return self.on_success(
                input_data,
                {
                    "is_revision_required": is_revision_required,
                    "feedback": llm_response_dict.get("feedback", ""),
                    "is_data_issues": False,
                    "is_visualization_issue": llm_response_dict.get("is_visualization_issue", False),
                    "issue_severity": llm_response_dict.get("issue_severity", ""),
                    "output_data_files": input_data.get("output_data_files", []),
                    "python_code": python_code,
                    "csv_paths": input_data.get("csv_paths", []),
                    "image_file_paths": input_data.get("image_file_paths", []),
                },
            )

        except Exception as e:
            conversation.data_retrieval_status = ConversationConclusion.SYSTEM_FAILURE
            is_raise_error, error_msg = handle_ai_exception(e, chat_log)
            if is_raise_error:
                raise
            return self.on_failure(f"Error in visual review: {error_msg}", input_data)

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("VisualReviewerAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return "Failed to build correct visualization."

    def update_followup_history(self, conversation, chat_log, output_data):
        pass
        # TODO : Have history for visual review
