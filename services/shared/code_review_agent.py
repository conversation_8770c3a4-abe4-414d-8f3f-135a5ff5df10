"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class CodeReviewAgent
* @description This class implements an agent for reviewing Python code generated by other agents

"""

import os
from logging import Logger
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    MessageContentType,
)
from utils.llm_utils import get_dict_from_json_or_python, code_review_schema


class CodeReviewAgent(BaseStateHandlerAgent):
    """
    An agent responsible for reviewing Python code generated by other agents.
    It checks for compliance with coding standards, data retrieval practices,
    and other specified guidelines.
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the CodeReviewAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing CodeReviewAgent class")
        super().__init__(data_source_name, _default_model, AgentState.CODE_REVIEW)

        # Set model names from environment variables
        self.code_review_model = os.getenv("CODE_REVIEW_MODEL", "gpt-4o")

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Review Python code based on the input data.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing code to review and context
                Format: {
                    "user_question": The latest user input,
                    "python_code": Python code to review,
                    "reasoning": Logic/reasoning behind the code
                }
            follow_up_history (list): List of previous messages in the conversation
            current_session_history (list): List of messages in the current session
            chat_log (Logger): Logger object
            try_count (int): No of times the agent is invoked for same conversation previously

        Returns:
            Dictionary containing the review results and execution status
        """
        chat_log.debug("CodeReviewAgent | on_agent_invoked | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.CODE_REVIEW)

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.REVIEWING_CODE.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )

        # Extract necessary data from input
        user_question = input_data.get("user_question", "")
        python_code = input_data.get("python_code", "")
        reasoning = input_data.get("reasoning", "")
        reusable_variables = input_data.get("reusable_variables", [])

        # If code review is disabled, always pass the code
        if os.getenv("CODE_REVIEW_OFF", "False") == "True" or conversation.is_skip_code_review:
            return self.on_success(
                input_data,
                {
                    "is_revision_required": False,
                    "review_feedback": "",
                    "python_code": python_code,
                    "reasoning": reasoning,
                    "file_paths": input_data.get("file_paths", {}),
                },
            )

        # Prepare the prompt for code review
        prompt_messages = [
            {"role": "user", "content": f"User Question: {user_question}"},
            {"role": "user", "content": f"Python Code to Review:\n```python\n{python_code}\n```"},
            {"role": "user", "content": f"Code Logic/Reasoning:\n{reasoning}"},
        ]
        if reusable_variables:
            prompt_messages.append(
                {
                    "role": "user",
                    "content": f"Reusable Variables: {reusable_variables}",
                }
            )
        # If there are available data input, add these to the prompt
        data_input = input_data.get("data_input", "")
        if data_input:
            prompt_messages.append({"role": "user", "content": f"User Data Input:\n{data_input}"})

        # Invoke LLM for code review
        response = self.invoke_llm(
            conversation,
            self.main_system_instruction,
            prompt_messages,
            chat_log,
            response_format=code_review_schema,
            model_name=self.code_review_model,
            is_current_session_history_required=True,
            follow_up_history_load_depth=0,
        )

        if not response:
            return self.on_failure("Failed to get response from LLM for code review", input_data)

        # Parse the response
        review_result = get_dict_from_json_or_python(response)

        if not review_result:
            return self.on_failure("Failed to parse LLM response for code review", input_data)

        # Log the review result
        chat_log.info(f"Code Review Result: {review_result}")

        # Update the frontend with review feedback in case failed
        if review_result.get("is_revision_required", False):
            conversation.active_session.add_to_queue(
                "\n\nCode Review feedback:\n\n" + review_result["review_feedback"] + "\n\n",
                "assistant",
                ConversationStatus.IN_PROGRESS,
                MessageContentType.TEXT.value,
            )
            self.add_to_history(
                conversation,
                "assistant",
                ["\n\nCode Review feedback:\n\n" + review_result["review_feedback"] + "\n\n"],
                False,
                True,
                MessageContentType.TEXT.value,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.CODE_REVIEW_FAILED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data="\n\nCode Review feedback:\n\n" + review_result["review_feedback"] + "\n\n",
                block_tab_types=[FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
        else:
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.CODE_REVIEW_PASSED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

        # Return the review result
        return self.on_success(
            input_data,
            {
                "is_revision_required": review_result.get("is_revision_required", False),
                "review_feedback": review_result.get("review_feedback", ""),
                "issues": review_result.get("issues", []),
                "python_code": python_code,
                "file_paths": input_data.get("file_paths", {}),
                "reasoning": reasoning,
            },
        )

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("CodeReviewAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return (
            "Failed to write correct Python code.\n\nLast python code:\n\n"
            + input_data.get("python_code", "")
            + "\n\nReview feedback:\n\n"
            + input_data.get("review_feedback", "")
        )

    def update_followup_history(self, conversation, chat_log, output_data):
        # Update history - output from LLM
        self.add_to_history(conversation, "assistant", [output_data["review_feedback"]], True, True)
