"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class TextualDataReviewAgent
* @description This class implements an agent for reviewing data retrieved in textual format.

"""

import datetime
import os
from logging import Logger
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationStatus,
    DataBlockVisibility,
    DataReviewStage,
    ConversationConclusion,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    MessageContentType,
)
from utils.llm_utils import (
    get_dict_from_json_or_python,
    data_review_response_schema,
    response_analyzer_response_schema,
    handle_ai_exception,
)


class TextualDataReviewAgent(BaseStateHandlerAgent):
    """
    An agent responsible for reviewing data retrieved in textual format.
    It checks for textual presentation issues.
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the TextualDataReviewAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing TextualDataReviewAgent class")
        super().__init__(data_source_name, _default_model, AgentState.TEXTUAL_REVIEW)

        # Set model names from environment variables
        self.data_reviewer_model = os.getenv("MODEL_DATA_REVIEWER", "gpt-4o")
        # Load response analyzer instructions
        self.response_analyzer_instructions = ""
        if os.path.exists(f"instructions/tools/{self.current_state.value}/response_analyzer_instructions.txt"):
            with open(
                f"instructions/tools/{self.current_state.value}/response_analyzer_instructions.txt", "r"
            ) as file:
                self.response_analyzer_instructions = str(file.read())

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Review data based on the input data.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing data to review and context
                Format: {
                    "user_question": The latest user input,
                    "python_code": Python code used for data retrieval,
                    "execution_result": Data observations from execution,
                    "csv_paths": List of CSV file paths (if any)
                }
            history (list): List of previous messages in the conversation
            chat_log (Logger): Logger object
            try_count (int): No of times the agent is invoked for same conversation previously

        Returns:
            Dictionary containing the review results and execution status
        """
        chat_log.debug("TextualDataReviewAgent | on_agent_invoked | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.DATA_REVIEW)

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.REVIEWING_TEXTUAL_DATA.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )
        # Extract necessary data from input
        user_question = input_data.get("user_question", "")
        python_code = input_data.get("python_code", "")
        data_observations = input_data.get("execution_result", "")
        csv_paths = input_data.get("csv_paths", [])
        data_message = self._process_data_retrieval_results(conversation, data_observations, csv_paths, chat_log)
        # If first time: Initial data review
        if try_count == 0:
            review_result = self.get_data_review_feedback(
                conversation, chat_log, user_question, data_message, python_code, csv_paths, input_data
            )
            if not review_result or review_result == -1:
                return self.on_failure("Failed to parse LLM response for data review", input_data)

            chat_log.info(f"Data Review Result: {review_result}")

            # If revision is required, store feedback history
            if review_result.get("is_revision_required", False):
                review_result["is_data_issues"] = True

                # Update conversation with current answer
                conversation.publish_answer(is_final_answer=False)
        else:
            # Call response analyzer
            review_result = self.get_response_analyzer_feedback(
                conversation,
                chat_log,
                user_question,
                data_message,
                python_code,
                csv_paths,
                current_session_history,
                input_data,
            )
            if not review_result or review_result == -1:
                return self.on_failure("Failed to parse LLM response for response analysis", input_data)

            chat_log.info(f"Response Analyzer Result: {review_result}")

        if review_result.get("is_revision_required", False):
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.TEXTUAL_DATA_REVIEW_FAILED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data="\n\n" + str(review_result["feedback"]) + "\n\n",
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
        else:
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.TEXTUAL_DATA_REVIEW_PASSED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

        # Return the review result
        return self.on_success(
            input_data,
            {
                "is_revision_required": review_result.get("is_revision_required", False),
                "feedback": review_result.get("feedback", ""),
                "is_data_issues": review_result.get("is_data_issues", False),
                "python_code": python_code,
                "data_observations": data_message,
                "csv_paths": csv_paths,
                "image_file_paths": input_data.get("image_file_paths", []),
                "output_data_files": input_data.get("output_data_files", []),
            },
        )

    def get_data_review_feedback(
        self,
        conversation: Conversation,
        chat_log: Logger,
        user_question: str = "",
        data_observations: str = "",
        python_code: str = "",
        csv_paths: list = [],
        input_data: dict = {},
    ):
        # If data review is disabled, always pass the data
        if os.getenv("DATA_REVIEW_OFF", "False") == "True":
            return self.on_success(
                input_data, {"is_revision_required": False, "feedback": "", "is_data_issues": False}
            )

        current_date = datetime.datetime.now().strftime("%Y-%m-%d")
        # Prepare the prompt for data review
        prompt_messages = [{"role": "user", "content": f"User Question: {user_question}"}]
        if "additional_input" in input_data:
            prompt_messages.append({"role": "user", "content": f"Additional Input: {input_data['additional_input']}"})
        prompt_messages.append({"role": "user", "content": f"Data Observations: \n{data_observations}"})
        prompt_messages.append({"role": "user", "content": f"Python Code: \n{python_code}"})
        prompt_messages.append({"role": "user", "content": f"Current Date: {current_date}"})

        # Invoke LLM for data review
        response = self.invoke_llm(
            conversation,
            self.main_system_instruction,
            prompt_messages,
            chat_log,
            response_format=data_review_response_schema,
            model_name=self.data_reviewer_model,
            follow_up_history_load_depth=0,
            is_current_session_history_required=False,  # Already adding the prompt_messages to history separately
        )

        if not response:
            return self.on_failure("Failed to get response from LLM for data review", input_data)

        # Parse the response
        llm_output = response
        review_result = get_dict_from_json_or_python(llm_output)

        if not review_result or review_result == -1:
            chat_log.error(f"Data Reviewer Response Error: {llm_output}")
            return self.on_failure("Failed to parse LLM response for data review", input_data)

        # Log the review result
        chat_log.info(f"Data Review Result: {review_result}")
        # Set default values if not present
        review_result["is_data_issues"] = review_result.get("is_data_issues", False)

        # Update the frontend with review feedback in case failed
        if review_result.get("is_revision_required", False):
            conversation.active_session.add_to_queue(
                "\n\nData Review feedback:\n\n" + str(review_result["feedback"]) + "\n\n",
                "assistant",
                ConversationStatus.IN_PROGRESS,
                MessageContentType.TEXT.value,
            )
            self.add_to_history(
                conversation,
                "assistant",
                ["\n\nData Review feedback:\n\n" + str(review_result["feedback"]) + "\n\n"],
                False,
                True,
                MessageContentType.TEXT.value,
            )
        return review_result

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("TextualDataReviewAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return (
            "Data verification failed.\n\nPython Code:\n\n"
            + input_data.get("python_code", "")
            + "\n\nReviewer feedback:\n\n"
            + input_data.get("feedback", "")
        )

    def get_response_analyzer_feedback(
        self,
        conversation: Conversation,
        chat_log: Logger,
        user_question: str = "",
        data_observations: str = "",
        python_code: str = "",
        csv_paths: list = [],
        current_session_messages: list = [],
        input_data: dict = {},
    ):
        llm_prompt = [
            {
                "role": "user",
                "content": f"Updated Python code: \n {python_code}",
            },
            {
                "role": "user",
                "content": "Latest Data Output: \n" + data_observations,
            },
        ]
        chat_log.debug(
            f"get_response_analyzer_feedback - Data retrieval status updated to : {conversation.data_retrieval_status}"
        )

        try:
            response = self.invoke_llm(
                conversation,
                self.response_analyzer_instructions,
                current_session_messages + llm_prompt,
                chat_log,
                response_format=response_analyzer_response_schema,
                is_current_session_history_required=True,
                follow_up_history_load_depth=0,
            )
            if not response:
                return self.on_failure("Failed to get response from LLM for response analysis", input_data)

            llm_response_dict = get_dict_from_json_or_python(response)

            if llm_response_dict == -1:
                chat_log.error(f"Response Analyzer Response Error: {response}")
                llm_response_dict = {}

            if (
                conversation.data_retrieval_status != ConversationConclusion.TRUNCATED_RECORDS
                and llm_response_dict["data_validated"]
            ):
                conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE

            llm_response_dict["is_data_issues"] = False
            if llm_response_dict["data_validated"]:
                llm_response_dict["is_revision_required"] = False
                return llm_response_dict
            elif llm_response_dict["new_issues_detected"]:
                llm_response_dict["is_data_issues"] = True
                reviewer_feedback = self.get_data_review_feedback(
                    conversation,
                    chat_log,
                    user_question=user_question,
                    data_observations=data_observations,
                    python_code=python_code,
                    csv_paths=csv_paths,
                    input_data=input_data,
                )
                return reviewer_feedback
            else:
                # If data returned, no query logic issue and also no difference to the previous attempt, then this means we can confirm the data is correct
                if (
                    llm_response_dict["is_data_returned"]
                    and llm_response_dict["retrieval_logic_corrected"]
                    and llm_response_dict["is_data_similar_to_previous"]
                ):
                    llm_response_dict["is_revision_required"] = False
                    chat_log.debug(
                        f"get_response_analyzer_feedback | Assuming data is correct as no difference to the previous attempt and data is returned"
                    )
                else:
                    llm_response_dict["is_data_issues"] = True
                    llm_response_dict["is_revision_required"] = True
                return llm_response_dict

        except Exception as e:
            conversation.data_retrieval_status = ConversationConclusion.SYSTEM_FAILURE
            is_raise_error, error_msg = handle_ai_exception(e, chat_log)
            if is_raise_error:
                raise
            return None

    def update_followup_history(self, conversation, chat_log, output_data):
        # Add to history for the use of response analyzer and followup
        # User question
        self.add_to_history(
            conversation, "user", ["User Question: " + conversation.user_inputs[-1]], is_required_for_follow_ups=True
        )
        # Data observations
        self.add_to_history(
            conversation,
            "user",
            ["Data Observations: " + output_data.get("data_observations", "")],
            is_required_for_follow_ups=True,
        )
        # Data Review feedback
        self.add_to_history(
            conversation,
            "assistant",
            ["Review Feedback: " + output_data.get("feedback", "")],
            True,
            True,
            is_required_for_follow_ups=True,
        )
