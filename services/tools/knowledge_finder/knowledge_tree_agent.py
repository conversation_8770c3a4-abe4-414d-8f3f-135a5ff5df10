"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class KnowledgeTreeBuilderAgent
* @description KnowledgeTreeBuilderAgent builds and updates the knowledge tree structure based on generated knowledge blocks.
"""

import json
import os
import traceback
from logging import Logger
from typing import List, Dict, Any
from pydantic import BaseModel
from services.data_dictionary_service import DataDictionaryService
from models.conversation import Conversation
from utils.constant import AgentState, DataBlockVisibility, FrontendBlockType, FrontendTabContentType
from utils.llm_utils import get_dict_from_json_or_python, knowledge_tree_update_schema
from services.shared.base_state_handler_agent import BaseStateHandlerAgent


class KnowledgeTreeBuilderAgent(BaseStateHandlerAgent):
    """
    Agent responsible for building and updating the knowledge tree structure based on knowledge blocks.
    Loads the existing knowledge tree from the data dictionary service and adds or updates nodes.
    """

    def __init__(self, data_source_name: str, _default_model):
        print("Initializing KnowledgeTreeBuilderAgent class")
        BaseStateHandlerAgent.__init__(self, data_source_name, _default_model, AgentState.KNOWLEDGE_TREE_BUILDER)
        self.data_dictionary_service = DataDictionaryService()
        # Load instruction for knowledge tree update
        with open(
            "instructions/tools/knowledge_finder/knowledge_tree_update_instructions.txt", "r", encoding="utf-8"
        ) as file:
            self.knowledge_tree_update_instructions = str(file.read())

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log,
        try_count: int,
        prev_invoked_count: int = 0,
    ):
        """
        Main logic for invoking the KnowledgeTreeBuilderAgent. Should load the tree, add/update nodes, and persist changes.
        """
        try:
            chat_log.info("KnowledgeTreeBuilderAgent: Starting knowledge tree update process")
            knowledge_blocks = input_data.get("knowledge_blocks", [])
            knowledge_tree = self.data_dictionary_service.get_knowledge_tree()
            knowledge_tree_assign_response = self.llm_get_assign_knowledge_blocks_to_nodes(
                conversation, knowledge_blocks, knowledge_tree, chat_log
            )
            # First update the knowledge tree
            self._update_knowledge_tree(knowledge_tree, knowledge_tree_assign_response["new_patterns"], chat_log)
            # Show the updated tree
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data="\n\nUpdated knowledge tree:\n\n'''json\n"
                + json.dumps(knowledge_tree, indent=2)
                + "```\n\n---\n\n",
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

            # Then update the blocks with assigned pattern keys and also update the block id based on the order
            self._update_block_pattern_keys_and_ids(
                knowledge_blocks, knowledge_tree_assign_response["block_assignments"]
            )

            # Persist the updated tree
            self.data_dictionary_service.save_knowledge_tree_with_blocks(knowledge_tree, knowledge_blocks)
            return self.on_success(input_data, {"status": "knowledge_tree_updated"})
        except Exception as e:
            chat_log.error(f"Error in KnowledgeTreeBuilderAgent.on_agent_invoked: {e}\n{traceback.format_exc()}")
            return self.on_failure(str(e), input_data, {})

    def _update_block_pattern_keys_and_ids(self, knowledge_blocks: list[dict], block_assignments: list[dict]):
        """
        Update the pattern keys for each knowledge block based on the block assignments.
        Then update the actual id for the block by determining the order of the block for each pattern key.
        """
        all_blocks: list = self.data_dictionary_service.get_all_knowledge_blocks()
        for block in knowledge_blocks:
            assigned_keys = [
                assignment.get("pattern_keys", [])
                for assignment in block_assignments
                if assignment.get("block_id") == block.get("block_id")
            ]
            # Safely handle case where no assignments found or pattern_keys is empty
            if assigned_keys and assigned_keys[0]:
                block["pattern_keys"] = assigned_keys[0]
                first_pattern_key = assigned_keys[0][0]
                # Now check the tree to determine the order of the block for the first assigned pattern key
                # Traverse through all blocks added so far
                first_pattern_current_count = 0
                for existing_block in all_blocks:
                    # Safely check if pattern_keys exists and matches
                    existing_pattern_keys = existing_block.get("pattern_keys")
                    if existing_pattern_keys == first_pattern_key:
                        first_pattern_current_count += 1
                block_index = first_pattern_current_count + int(block.get("block_id", 0))
                block["block_id"] = f"{first_pattern_key}_{block_index}"
            else:
                # If no pattern keys assigned, keep the original block structure
                pass

    def _update_knowledge_tree(self, current_tree: dict, new_patterns: list[dict], chat_log) -> None:
        """
        Update the knowledge tree with new patterns. If a pattern already exists, update it. If not, add it.
        """
        for pattern in new_patterns:
            entity = pattern["entity"]
            if entity not in current_tree:
                current_tree[entity] = []
            # Check if pattern already exists
            existing_pattern = next(
                (p for p in current_tree[entity] if p["pattern_key"] == pattern["pattern_key"]), None
            )
            if existing_pattern:
                existing_pattern["pattern"] = pattern["pattern"]
            else:
                current_tree[entity].append({"pattern": pattern["pattern"], "pattern_key": pattern["pattern_key"]})

    def llm_get_assign_knowledge_blocks_to_nodes(
        self, conversation, knowledge_blocks_list: list, current_knowledge_tree: dict, chat_log
    ):
        """
        If the given blocks can be mapped to existing nodes, then assign to them, if not create new nodes and assign
        """
        # Assign a temporary incremental block id to each block to help assignment reference
        for index, block in enumerate(knowledge_blocks_list):
            block["block_id"] = f"{index+1}"
        prompts_list = [
            {"role": "user", "content": "Knowledge Blocks List:\n" + json.dumps(knowledge_blocks_list, indent=2)},
            {"role": "user", "content": "Current Knowledge Tree\n" + json.dumps(current_knowledge_tree, indent=2)},
        ]
        llm_output = self.invoke_llm(
            conversation,
            self.knowledge_tree_update_instructions,
            prompts_list,
            chat_log,
            response_format=knowledge_tree_update_schema,
        )
        knowledge_tree_update_res = get_dict_from_json_or_python(llm_output)
        return knowledge_tree_update_res

    def update_followup_history(self, conversation, chat_log, output_data: dict):
        pass

    def on_invoke_limit_reached(self, input_data: dict, conversation, chat_log, try_count: int) -> str:
        pass
