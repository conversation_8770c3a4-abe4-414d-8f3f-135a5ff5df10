import os
import traceback

from utils.constant import Agent<PERSON><PERSON>, AgentToolName, AnalysisStep
from models.conversation import Conversation
from services.tools.base_tool import BaseTool, ToolOutput
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.logger import get_debug_logger
from utils.llm_utils import query_knowledge_match_schema, get_dict_from_json_or_python
import json


class KnowledgeFinder(BaseTool, BaseStateHandlerAgent):
    def __init__(self):
        BaseTool.__init__(self)
        model_name = os.getenv("MODEL_CONTENT_ARCHIVE", "gpt-4.1")
        BaseStateHandlerAgent.__init__(self, "", model_name, AgentState.MEMORY_RECALL)

        # loading knowledge finder instructions (system instructions)
        instruction_file = "instructions/tools/knowledge_finder/knowledge_finder_instructions.txt"
        self.knowledge_finder_instructions = ""
        with open(instruction_file, "r", encoding="utf-8") as file:
            self.knowledge_finder_instructions = str(file.read())
        self.tool_summarize_instruction: dict[<PERSON><PERSON>ool<PERSON><PERSON>, str] = {}
        # loading knowledge memory tree
        knowledge_memory_tree_file = "instructions/tools/knowledge_finder/memory_tree_with_pattern_keys.json"
        with open(knowledge_memory_tree_file, "r") as file:
            self.knowledge_memory_tree = json.load(file)

    def get_knowledge_blocks(self, pattern_key_list: list):
        all_blocks = self.data_dictionary_service.get_all_knowledge_blocks()
        matched_blocks = []
        linked_block_ids = []
        for block in all_blocks:
            # Match is detected if we found on of "pattern_keys" in th block
            # Check if pattern_keys exists and is not None before accessing
            block_pattern_keys = block.get("pattern_keys", [])
            if block_pattern_keys and any(pattern_key in block_pattern_keys for pattern_key in pattern_key_list):
                # Remove pattern_keys and linked_knowledge_block_references to avoid confusion with block id
                # Safely extend linked_block_ids only if the key exists
                block_linked_refs = block.get("linked_knowledge_block_references", [])
                if block_linked_refs:
                    linked_block_ids.extend(block_linked_refs)

                # Safely remove keys only if they exist
                if "pattern_keys" in block:
                    del block["pattern_keys"]
                if "linked_knowledge_block_references" in block:
                    del block["linked_knowledge_block_references"]
                matched_blocks.append(block)
        # Get all linked blocks (Note: Currently only 1 level of linking is done)
        linked_blocks = []
        for block_id in linked_block_ids:
            for block in all_blocks:
                if block.get("block_id") == block_id:
                    # Safely remove keys only if they exist
                    if "pattern_keys" in block:
                        del block["pattern_keys"]
                    if "linked_knowledge_block_references" in block:
                        del block["linked_knowledge_block_references"]
                    linked_blocks.append(block)
                    break
        return matched_blocks + linked_blocks

    def extract_matched_nodes(self, llm_output_dict):
        # result = {}
        pattern_key_list = []
        for node in llm_output_dict.get("matched_nodes", []):
            entity = node.get("entity", "").lower()
            pattern_keys = [
                pattern.get("pattern_key", "")
                for pattern in node.get("analysis_patterns", [])
                if pattern.get("pattern_key")
            ]
            # result[entity] = pattern_keys
            pattern_key_list.extend(pattern_keys)

        return pattern_key_list

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
    ):
        conversation.active_session_send_analysis_step(AnalysisStep.KNOWLEDGE_FIND)
        chat_log = get_debug_logger(
            f"chat_{conversation.chat_id}", f"./logs/{base_log_path}chat_{conversation.chat_id}.log"
        )
        knowledge_memory_tree = self.data_dictionary_service.get_knowledge_tree()
        knowledge_json = json.dumps(knowledge_memory_tree, indent=2)
        prompt_list = [
            {"role": "user", "content": f"Knowledge Memory Tree: \n {knowledge_json}"},
            {"role": "user", "content": f"User's Query: {task_instruction}"},
        ]

        llm_output = self.invoke_llm(conversation, self.knowledge_finder_instructions, prompt_list, chat_log, None)
        llm_output_dict = get_dict_from_json_or_python(llm_output)
        pattern_key_list = self.extract_matched_nodes(llm_output_dict)

        # get knowledge blocks which has references to the pattern keys
        try:
            knowledge_blocks = self.get_knowledge_blocks(pattern_key_list)
        except Exception as e:
            chat_log.error(f"KnowledgeFinder | Error in get_knowledge_blocks: {traceback.format_exc()}")
            knowledge_blocks = []

        knowledge_blocks_str = json.dumps(knowledge_blocks, indent=2)
        return ToolOutput(
            display_output="```json\n" + knowledge_blocks_str + "\n````\n\n",
            result_text_list=[tuple([knowledge_blocks_str, False])],
        )

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log,
        try_count: int,
        prev_invoked_count: int = 0,
    ):
        pass

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log, try_count: int):
        pass

    def update_followup_history(self, conversation, chat_log, output_data):
        pass
