"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class KnowledgeManagerAgent
* @description KnowledgeManagerAgent is an agent that builds the knowledge base based on the analysis that is done by the complex analysis agent.
* <AUTHOR>
"""

import asyncio
import json
import os
import traceback
from logging import Logger
from typing import List, Dict, Any
from pydantic import BaseModel
from agents import Agent, Runner

from models.conversation import Conversation
from utils.constant import AgentState, DataBlockVisibility, FrontendBlockType, FrontendTabContentType
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from services.tools.knowledge_finder.knowledge_tree_agent import KnowledgeTreeBuilderAgent


class KnowledgeBlock(BaseModel):
    """Data model for a knowledge block"""

    purpose: str
    business_rules: List[str]
    linked_knowledge_block_references: List[str]
    sql_query_logic: str
    sql: str
    data_processing_logic: str
    donts: str
    additional_details: str


class KnowledgeGenerationOutput(BaseModel):
    """Output model for knowledge generation iteration"""

    is_generation_completed: bool
    current_reasoning: str
    knowledge_blocks_generated: List[KnowledgeBlock]


class KnowledgeGeneratorAgent(BaseStateHandlerAgent, Agent):
    """
    Agent responsible for building the knowledge base that is to be used by the next analysis questions.
    Inputs:
        1. User's request and subsequent feedbacks (Can extract some knowledge from the user's question as well)
        2. Analysis flow
    Tasks:
        1. Extract the key elements from a given analysis flow including the existing knowledge blocks used, query logics generated and successfully given results, data processing logics that successfully executed.
        2. Determine whether there are new knowledge that discovered both from the question or feedback given by user and identified by itself.
        3. If yes, generate one or more knowledge blocks containing these:
            - purpose or application scenario of the knowledge block
            - dependant knowledge blocks
            - query logics
            - data processing logics
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the KnowledgeManagerAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing KnowledgeManagerAgent class")
        model_name = os.getenv("MODEL_KNOWLEDGE_GENERATION", _default_model)
        BaseStateHandlerAgent.__init__(self, data_source_name, model_name, AgentState.KNOWLEDGE_MANAGER)

        # Initialize Agent with system instructions
        with open(
            "instructions/tools/knowledge_finder/knowledge_block_generate_instructions.txt", "r", encoding="utf-8"
        ) as file:
            block_generate_system_prompt = file.read()
        Agent.__init__(
            self,
            name="Knowledge Generation Agent",
            instructions=block_generate_system_prompt,
            model=model_name,
            output_type=KnowledgeGenerationOutput,
        )
        self.knowledge_tree_agent = KnowledgeTreeBuilderAgent(data_source_name, model_name)
        self.max_iterations = 5  # Maximum number of iterations for knowledge generation

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log,
        try_count: int,
        prev_invoked_count: int = 0,
    ):
        """
        Main logic for invoking the KnowledgeManagerAgent. Should extract knowledge, generate knowledge blocks, and update the knowledge tree as needed.
        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Input data for the agent
            follow_up_history (list): Conversation history for previous sessions
            current_session_history (list): Messages for the current session
            chat_log: Logger for chat interactions
            try_count (int): Number of times the agent is invoked for this conversation
            prev_invoked_count (int): Number of previous invocations for this state
        Returns:
            dict: Response from the agent
        """
        try:
            chat_log.info("KnowledgeManagerAgent: Starting knowledge extraction and generation process")
            # Extract knowledge from analysis flow and user feedback
            approved_analysis_trace = self.extract_knowledge_from_analysis(
                conversation.agent_state_follow_up_history.get(AgentState.COMPLEX_REASONING, {}),
                input_data.get("user_feedback", {}),
            )
            chat_log.info(f"KnowledgeManagerAgent: Extracted knowledge with {len(approved_analysis_trace)} categories")
            self.name = "Knowledge Generation Agent - " + conversation.chat_id

            # Generate knowledge blocks using iterative approach
            knowledge_blocks = self.generate_knowledge_blocks(
                conversation.user_inputs, approved_analysis_trace, chat_log
            )
            blocks_list = [block.model_dump() for block in knowledge_blocks]
            chat_log.info(f"KnowledgeManagerAgent: Generated {len(knowledge_blocks)} knowledge blocks")
            # Send the generated blocks to frontend
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data="\n\nGenerated knowledge blocks:\n\n```json\n"
                + json.dumps(blocks_list, indent=2)
                + "```\n\n---\n\n",
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

            # Call knowledge tree agent to update the knowledge tree
            knowledge_tree_response = self.knowledge_tree_agent.execute_agent(
                {"knowledge_blocks": blocks_list}, conversation, conversation.active_session.session_id, chat_log
            )

            return self.on_success(
                input_data,
                {
                    "knowledge_blocks": blocks_list,
                    "knowledge_tree_response": knowledge_tree_response,
                },
            )

        except Exception as e:
            chat_log.error(f"Error in KnowledgeManagerAgent.on_agent_invoked: {e}\n{traceback.format_exc()}")
            return self.on_failure(str(e), input_data, {})

    def extract_knowledge_from_analysis(self, analysis_history: dict[list], user_feedback: dict = None) -> list:
        """
        Extract key knowledge elements from the analysis flow and user feedback.
        Args:
            analysis_flow (dict): The analysis flow containing steps, logic, and results
            user_feedback (dict, optional): Feedback provided by the user
        Returns:
            dict: Extracted knowledge elements
        """
        # This is a placeholder implementation
        # In a real implementation, this would analyze the analysis_flow to extract:
        # - Successful query patterns
        # - Data processing steps that worked
        # - User feedback insights
        # - Business logic discovered
        # Return the latest state
        if not analysis_history:
            return []
        # Get only the last state
        # TODO: Select this based on thumbs-up session
        max_try_count = max(analysis_history.keys())
        final_analysis_state = analysis_history[max_try_count]
        if not final_analysis_state:
            return []
        analysis_trace_steps = []
        for analysis_step in final_analysis_state:
            content_dict = json.loads(analysis_step["content"])
            # Feed only the summary of the step if summary available
            if "summary" in content_dict and content_dict["summary"]:
                analysis_trace_steps.append({"role": analysis_step["role"], "content": content_dict["summary"]})
            else:
                analysis_trace_steps.append({"role": analysis_step["role"], "content": content_dict["content"]})
        # add flag answer_approved_by_user to final state
        analysis_trace_steps.append({"role": "user", "content": {"answer_approved_by_user": True}})
        return analysis_trace_steps

    def generate_knowledge_blocks(
        self, user_inputs: list, analysis_trace: list[dict], chat_log
    ) -> List[KnowledgeBlock]:
        """
        Generate one or more knowledge blocks from extracted knowledge using iterative approach.
        Args:
            user_inputs (list): List of user inputs
            extracted_knowledge (list): The extracted knowledge elements
            chat_log: Logger for chat interactions
        Returns:
            List[KnowledgeBlock]: List of knowledge blocks
        """
        knowledge_blocks_list = []
        iteration_count = 0
        is_completion_detected_first_time = False

        chat_log.info(
            f"KnowledgeManagerAgent: Starting knowledge block generation with max {self.max_iterations} iterations"
        )
        # Prepare user inputs list as ["Original Question": "", "Followup Question 1", Followup Question 2, ....]
        user_inputs_list = ["Original Question: " + user_inputs[0]]
        followup_index = 1
        for user_input in user_inputs[1:]:
            user_inputs_list.append("Followup Question: " + str(followup_index) + ": " + user_input)
            followup_index += 1

        # Initialize with extracted knowledge
        agent_inputs_list = [
            {
                "role": "user",
                "content": "User Inputs: " + json.dumps(user_inputs_list, indent=2),
            },
            {
                "role": "user",
                "content": f"Answer Generation Trace: {json.dumps(analysis_trace, indent=2)}",
            },
        ]
        # Handle event loop for threading
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            # If no event loop exists in current thread, create a new one
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        try:
            while iteration_count < self.max_iterations and not is_completion_detected_first_time:
                chat_log.info(f"KnowledgeManagerAgent: Running iteration {iteration_count + 1}")

                # Check if user has stopped the process
                # if hasattr(conversation, 'active_session') and conversation.active_session.user_stopped_stream:
                #     break

                result = self.run_iteration(iteration_count, agent_inputs_list, chat_log)

                if result["output"] is not None:
                    output: KnowledgeGenerationOutput = result["output"]
                    agent_inputs_list = result["next_input"]

                    chat_log.info(
                        f"KnowledgeManagerAgent: Iteration {iteration_count + 1} generated {len(output.knowledge_blocks_generated)} new blocks"
                    )

                    # Check if generation is complete
                    if output.is_generation_completed:
                        is_completion_detected_first_time = True
                        knowledge_blocks_list = output.knowledge_blocks_generated
                        break
                        # TODO: Check Final validation
                        chat_log.info("KnowledgeManagerAgent: Generation completed, starting final validation")
                        # Final validation iteration
                        agent_inputs_list.append(
                            {
                                "role": "user",
                                "content": "Please perform final validation of all generated knowledge blocks. Ensure they are accurate and complete.",
                                "iteration_index": iteration_count,
                            }
                        )
                    elif output.is_generation_completed and is_completion_detected_first_time:
                        # Final validation completed
                        chat_log.info(
                            f"KnowledgeManagerAgent: Final validation completed. Total blocks: {len(output.knowledge_blocks_generated)}"
                        )
                        # knowledge_blocks_list = output.knowledge_blocks_generated
                        break
                    else:
                        # Prompt it to complete the generation
                        agent_inputs_list.append(
                            {
                                "role": "user",
                                "content": "Please continue generating knowledge blocks.",
                                "iteration_index": iteration_count,
                            }
                        )
                else:
                    chat_log.warning(f"KnowledgeManagerAgent: Iteration {iteration_count + 1} returned no output")
                    break

                iteration_count += 1

        except Exception as e:
            # Log error but return what we have so far
            chat_log.error(f"Error in knowledge generation iteration: {e}\n{traceback.format_exc()}")

        chat_log.info(f"KnowledgeManagerAgent: Knowledge generation completed after {iteration_count} iterations")
        return knowledge_blocks_list

    def run_iteration(
        self,
        iteration_index: int,
        input_data: List[Dict[str, Any]],
        chat_log,
    ) -> Dict[str, Any]:
        """
        Run a single iteration of knowledge block generation.
        Args:
            iteration_index (int): Current iteration number
            input_data (List[Dict]): Input data for this iteration
            chat_log: Logger for chat interactions
        Returns:
            Dict containing iteration results
        """
        try:
            chat_log.debug(f"KnowledgeManagerAgent: Preparing input for iteration {iteration_index + 1}")

            # Invoke LLM with non-streaming
            chat_log.debug(f"KnowledgeManagerAgent: Invoking LLM for iteration {iteration_index + 1}")
            chat_log.debug(f"KnowledgeManagerAgent: Input\n\n: {input_data}")
            result = Runner.run_sync(self, input=input_data, max_turns=self.max_iterations)

            output: KnowledgeGenerationOutput = result.final_output
            chat_log.debug(
                f"KnowledgeManagerAgent: LLM invocation done. Generated {len(output.knowledge_blocks_generated)} blocks"
            )
            chat_log.debug(f"KnowledgeManagerAgent: Output\n\n: {output.knowledge_blocks_generated}")

            # Add the output to input data for next iteration
            input_data.append(
                {
                    "role": "assistant",
                    "content": output.current_reasoning,
                    "cycle_index": iteration_index,
                }
            )

            return {
                "output": output,
                "next_input": input_data,
                "is_complete": output.is_generation_completed,
            }

        except Exception as e:
            chat_log.error(f"Error in run_iteration: {e}\n{traceback.format_exc()}")
            return {"output": None, "next_input": input_data, "is_complete": False}

    def update_knowledge_tree(self, knowledge_blocks: List[KnowledgeBlock], chat_log) -> None:
        """
        Update the knowledge tree structure with new knowledge blocks.
        Args:
            knowledge_blocks (List[KnowledgeBlock]): List of knowledge blocks to add to the tree
            chat_log: Logger for chat interactions
        Returns:
            None
        """
        # This is a placeholder implementation
        # In a real implementation, this would:
        # 1. Load existing knowledge tree
        # 2. Add new nodes if necessary
        # 3. Link knowledge blocks to appropriate nodes
        # 4. Save updated knowledge tree

        chat_log.info(f"KnowledgeManagerAgent: Updating knowledge tree with {len(knowledge_blocks)} blocks")
        for block in knowledge_blocks:
            chat_log.debug(f"KnowledgeManagerAgent: Adding knowledge block: {block.purpose}")
            # Add to knowledge tree structure
            # save_to_knowledge_tree(block)

    def update_followup_history(self, conversation, chat_log, output_data: dict):
        pass

    def on_invoke_limit_reached(self, input_data: dict, conversation, chat_log, try_count: int) -> str:
        pass
