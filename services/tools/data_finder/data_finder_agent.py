import json
import os
from utils.logger import get_debug_logger
from models.conversation import Conversation
from services.tools.base_tool import BaseTool, ToolOutput
from services.sql_agent.sql_data_finder_agent import SQLDataFinderAgent
from services.data_dictionary_service import DataDictionaryService


class DataFinderAgent(BaseTool):
    def __init__(self):
        self.metadata_finder_sub_agents: dict[str, SQLDataFinderAgent] = {}
        data_dict_service = DataDictionaryService()
        data_source_names = data_dict_service.get_data_sources_list()
        # Load model for data finding from env variable
        data_find_model = os.getenv("MODEL_DATA_LOCATE", "gpt-4.1")
        for name in data_source_names:
            self.metadata_finder_sub_agents[name] = SQLDataFinderAgent(name, data_find_model)

    def execute_task(
        self,
        conversation: Conversation,
        data_source_name: str,
        data_file_name_list: list,
        task_instruction: str,
        base_log_path: str = "",
        additional_data: dict = None,
    ):
        """
        Executes a data finding task by checking with all data sources
        """
        all_data_source_metadata = []
        all_business_rules = []
        chat_log = get_debug_logger(
            f"chat_{conversation.chat_id}", f"./logs/{base_log_path}chat_{conversation.chat_id}.log"
        )
        # Testing: Send hard coded:
        # return {
        #     "data_sources_metadata": [
        #         {
        #             "data_source_name": "FieldServio",
        #             "selected_table_column_metadata":
        #                 {'GeneralLedgerDetail': {'table_name': 'GeneralLedgerDetail', 'fields': {'GeneralLedger_ChartType': {'name': 'GeneralLedger_ChartType', ... }} }
        #
        #         },
        #     ],
        #     "business_rules": [],
        # }
        data_source_er_diagrams = {}
        for data_source, metadata_finder_agent in self.metadata_finder_sub_agents.items():
            result = metadata_finder_agent.execute_agent(
                {"user_question": conversation.user_inputs[-1]},
                conversation,
                conversation.active_session.session_id,
                chat_log,
            )
            if result["is_success"]:
                metadata_obj = {
                    "data_source_name": data_source,
                    "selected_table_column_metadata": result.get("table_metadata", {}),
                    "reasoning": result.get("reasoning", ""),
                }
                if "is_initial_metadata_pending" in additional_data and additional_data["is_initial_metadata_pending"]:
                    # metadata_obj["ER_Diagram"] = (
                    #     metadata_finder_agent.data_dictionary_service.get_er_diagram_for_data_source(data_source)
                    # )
                    er_diagram = metadata_finder_agent.data_dictionary_service.get_er_diagram_for_data_source(
                        data_source
                    )
                    data_source_er_diagrams[data_source] = er_diagram
                    metadata_obj["other_table_columns"] = result.get("other_table_columns", {})
                all_data_source_metadata.append(metadata_obj)
                all_business_rules.extend(result.get("business_rules", []))
            else:
                all_data_source_metadata.append(
                    {
                        "data_source_name": data_source,
                        "reasoning": result.get("reasoning", ""),
                        "error": result.get("error_message", "Failed to retrieve metadata"),
                    }
                )

        # For display, only send data source name, selected table column names and business rules as markdown text
        output_data_display = ""
        for metadata in all_data_source_metadata:
            if "selected_table_column_metadata" not in metadata:
                continue
            output_data_display += f"**Data Source: {metadata['data_source_name']}**\n\n"
            output_data_display += f"**Selected Tables:**\n\n"
            for table_name in metadata["selected_table_column_metadata"]:
                table = metadata["selected_table_column_metadata"][table_name]
                output_data_display += f"  - {table_name}\n"
                output_data_display += "    - Columns:\n"
                for column in table["fields"]:
                    output_data_display += f"      - {column}\n"

        output_data_display += "\n**Business Rules:**\n\n"
        for rule in all_business_rules:
            output_data_display += f"- {rule}\n"

        tool_result_list = [
            tuple(["Business Rules:\n\n````\n" + json.dumps(all_business_rules, indent=2) + "\n````", False]),
            tuple(
                [
                    "Metadata of all data sources:\n\n````\n"
                    + json.dumps(all_data_source_metadata, indent=2)
                    + "\n````",
                    True,
                ]
            ),
        ]
        if (
            additional_data
            and "is_initial_metadata_pending" in additional_data
            and additional_data["is_initial_metadata_pending"]
        ):
            tool_result_list.append(
                tuple(
                    [
                        "ER Diagrams of all data sources:\n\n````\n"
                        + json.dumps(data_source_er_diagrams, indent=2)
                        + "\n````",
                        False,
                    ]
                )
            )
        return ToolOutput(display_output=output_data_display, result_text_list=tool_result_list)
