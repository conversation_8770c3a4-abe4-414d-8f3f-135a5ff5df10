"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class OrchestrationAgent
* @description This class is the entry point of data retrieval from SQL and non-SQL data sources
* <AUTHOR>
"""

from datetime import datetime, timedelta, timezone
from logging import Logger
import os
import pathlib
import traceback

from services.insight_dashboard_service import InsightDashboardService
from services.conversation_service import ConversationService
from models.schemas.responses import GeneralResponse
from services.insight_app.insight_generator_service import LlmInsightAgent
from services.shared.base_state_handler_agent import StateMachine
from services.tools.data_finder.data_finder_agent import DataFinderAgent
from services.upload_session_service import UploadSessionService
from services.tools.knowledge_finder.knowledge_generator_agent import KnowledgeGeneratorAgent
from utils.logger import get_debug_logger
from utils.constant import (
    ConversationConclusion,
    ConversationStatus,
    DataBlockHandlerType,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    LLMAgentType,
    SectionType,
    AgentState,
    SessionType,
)
from services.sql_agent.sql_agent import SQLAgent
from models.conversation import Conversation
from services.orchestration_agent.followup_handler_agent import FollowupHandlerAgent
from services.orchestration_agent.intent_router_agent import IntentRouterAgent
from services.orchestration_agent.answer_prepare_agent import AnswerPrepareAgent
from services.shared.python_code_agent import PythonCodeAgent
from services.shared.python_code_execution_agent import PythonExecuteAgent
from services.shared.code_review_agent import CodeReviewAgent
from services.shared.textual_review_agent import TextualDataReviewAgent
from services.shared.visual_reviewer_agent import VisualReviewerAgent
from services.shared.metadata_agent import MetadataAgent
from services.orchestration_agent.iterative_problem_solver_agent import IterativeProblemSolverAgent


class OrchestrationAgent(StateMachine):
    """
    This class is responsible for:
    1. Handling followups - combine the previous conversation history with the current prompt
    2. Handling pre-verified questions - if the question is exactly a one of previously verified question, then directly execute the python code and answer
    3. Sorting of question - whether it's a) out-of the scope one, b) greeting message, c) SQL question, d) non-SQL question, e) hybrid question (SQL + non-SQL)
    4. Identify the steps of data retrievals
    5. Invoke appropriate agents for each step
    6. Collect the results and present the final answer to the user
    """

    def __init__(self):
        """
        Initialize the OrchestrationAgent with necessary configurations
        """
        print("Initializing OrchestrationAgent")
        super().__init__()
        default_model = os.getenv("MODEL")
        self.followup_handler = FollowupHandlerAgent(default_model)
        self.tool_router = IntentRouterAgent(default_model)
        self.answer_prepare = AnswerPrepareAgent("", default_model)
        self.upload_session_service = UploadSessionService()
        self.conversation_service = ConversationService()

        # Email Time Threshold
        self.email_time_threshold = int(os.getenv("ANALYSIS_EMAIL_TIME_THRESHOLD", 300))

        # Model configurations
        self.orchestration_model = os.getenv("ORCHESTRATION_MODEL", "gpt-4o")

        # Initialize state handlers for Python code generation and execution
        self.state_handler_class_map[AgentState.PYTHON_CODE_GENERATION] = PythonCodeAgent
        self.state_handler_class_map[AgentState.PYTHON_CODE_EXECUTION] = PythonExecuteAgent
        self.state_handler_class_map[AgentState.CODE_REVIEW] = CodeReviewAgent
        self.state_handler_class_map[AgentState.TEXTUAL_REVIEW] = TextualDataReviewAgent
        self.state_handler_class_map[AgentState.VISUAL_REVIEW] = VisualReviewerAgent
        self.state_handler_class_map[AgentState.METADATA_QUERY] = MetadataAgent
        self.state_handler_class_map[AgentState.COMPLEX_REASONING] = IterativeProblemSolverAgent
        self.state_handler_class_map[AgentState.KNOWLEDGE_MANAGER] = KnowledgeGeneratorAgent

        # Functions to handle the output of each state
        self.state_handler_function_map[AgentState.PYTHON_CODE_GENERATION] = self.handle_python_code_generation_output
        self.state_handler_function_map[AgentState.PYTHON_CODE_EXECUTION] = self.handle_python_execution_output
        self.state_handler_function_map[AgentState.CODE_REVIEW] = self.handle_code_review_output
        self.state_handler_function_map[AgentState.TEXTUAL_REVIEW] = self.handle_data_review_output
        self.state_handler_function_map[AgentState.VISUAL_REVIEW] = self.handle_visual_review_output
        self.state_handler_function_map[AgentState.METADATA_QUERY] = self.handle_metadata_query_output
        self.state_handler_function_map[AgentState.COMPLEX_REASONING] = self.handle_complex_reasoning_output
        self.state_handler_function_map[AgentState.KNOWLEDGE_MANAGER] = self.handle_knowledge_manager_output

        # No. of max. invokes allowed for each state with whether we ask back from user on limit reached
        self.max_invokes_allowed[AgentState.PYTHON_CODE_GENERATION] = (5, False)
        self.max_invokes_allowed[AgentState.PYTHON_CODE_EXECUTION] = (4, False)
        self.max_invokes_allowed[AgentState.CODE_REVIEW] = (3, False)
        self.max_invokes_allowed[AgentState.TEXTUAL_REVIEW] = (3, False)
        self.max_invokes_allowed[AgentState.VISUAL_REVIEW] = (2, False)
        self.max_invokes_allowed[AgentState.METADATA_QUERY] = (2, False)
        self.max_invokes_allowed[AgentState.COMPLEX_REASONING] = (1, False)
        self.max_invokes_allowed[AgentState.KNOWLEDGE_MANAGER] = (1, False)

        self.data_finder_agent = DataFinderAgent()

    def run(
        self,
        conversation: Conversation,
        log_path: str,
        user_info: dict,
    ):
        """
        Execute the orchestration agent to handle user prompt.

        Args:
            conversation (Conversation): The conversation object
            log_path: Path to the log file

        Returns:
            Agent response
        """
        try:
            log_file_full_path = f"./logs/{log_path}chat_{conversation.chat_id}.log"
            if not os.path.exists(pathlib.Path(log_file_full_path).parent):
                os.makedirs(pathlib.Path(log_file_full_path).parent)

            conversation.log_file_full_path = log_file_full_path
            chat_log = get_debug_logger(f"chat_{conversation.chat_id}", log_file_full_path)
            user_prompt = conversation.user_inputs[-1]

            initial_frontend_tab = [
                {
                    "id": FrontendTabContentType.ANSWER.tab_type_value,
                    "title": FrontendTabContentType.ANSWER.tab_title_value,
                    "status": conversation.active_session.session_status.value,
                    "session_type": conversation.active_session.session_type.value,
                }
            ]

            # If this is insight query, then mark it (using @Insight prompt)
            is_insight = False
            if conversation.active_session.session_type == SessionType.DEEP_RESEARCH:
                user_prompt = user_prompt.replace("@insight", "", 1)
                is_insight = True
                conversation.agent_type = LLMAgentType.AGENT_TYPE_INSIGHT
                initial_frontend_tab = [
                    {
                        "id": FrontendTabContentType.DEEP_RESEARCH.tab_type_value,
                        "title": FrontendTabContentType.DEEP_RESEARCH.tab_title_value,
                        "status": conversation.active_session.session_status.value,
                        "session_type": conversation.active_session.session_type.value,
                    }
                ]
                conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.DEEP_RESEARCH)
            elif conversation.active_session.session_type == SessionType.COMPLEX_ANALYSIS:
                user_prompt = user_prompt.replace("@complex", "", 1)
                conversation.agent_type = LLMAgentType.AGENT_TYPE_COMPLEX_ANALYSIS
                conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.ANSWER)
            elif conversation.active_session.session_type == SessionType.USER_REACTION:
                conversation.agent_type = LLMAgentType.AGENT_TYPE_USER_REACTION
                # user_prompt = "Thanks for the feedback, we are updating the knowledge..."
            elif conversation.active_session.session_type == SessionType.ADD_TO_DASHBOARD:
                conversation.agent_type = LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT
                # user_prompt = "We are updating the insight dashboard..."
            else:
                conversation.active_session.session_type = SessionType.ANALYSIS
                conversation.agent_type = LLMAgentType.AGENT_TYPE_CHAT
                conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.ANSWER)
            chat_log.info(f"OrchestrationAgent | Processing prompt: {user_prompt}")

            # send user question as a block to frontend
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.USER_QUESTION.suffix_value}",
                block_data=user_prompt,
                block_type=FrontendBlockType.USER_QUESTION,
                block_tab_types=[FrontendTabContentType.QUERY],
            )

            # add initial tab to frontend
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
                block_type=FrontendBlockType.TAB_CONTENT,
                block_data=initial_frontend_tab,
                block_tab_types=[FrontendTabContentType.TABS],
            )

            # need to send empty data source block in order to keep frontend block order (data sources block should be before markdown block)
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.DATA_SOURCES.suffix_value}",
                block_type=FrontendBlockType.DATA_SOURCES,
                block_data=[],
                block_tab_types=[FrontendTabContentType.SOURCES, FrontendTabContentType.ANSWER],
            )

            # add task tab to db to show in frontend when retrieving from history
            conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.TASKS)
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
                block_type=FrontendBlockType.TAB_CONTENT,
                block_data=[
                    {
                        "id": FrontendTabContentType.TASKS.tab_type_value,
                        "title": FrontendTabContentType.TASKS.tab_title_value,
                        "status": conversation.active_session.session_status.value,
                        "session_type": conversation.active_session.session_type.value,
                    }
                ],
                handler_type=DataBlockHandlerType.PERSIST,
                block_tab_types=[FrontendTabContentType.TABS],
            )

            # Pre-Steps:
            if conversation.file_upload_sessions:
                # Process file uploads
                is_files_handled = self.handle_user_uploaded_files(conversation)
                if not is_files_handled.success:
                    return is_files_handled.message, conversation

            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.PRE_PROCESSING_USER_PROMPT.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )

            # Step 1: Handle potential follow-up questions
            # In case of complex query agent, combined query is not used - use original user prompt in followup
            if conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_ANALYSIS:
                enriched_prompt = user_prompt
                # check conversation history to check if this followup
                # conversation_history = conversation.get_llm_input_history([SectionType.QUESTION.value])
                if AgentState.COMPLEX_REASONING in conversation.agent_state_follow_up_history:
                    last_history_count = max(
                        conversation.agent_state_follow_up_history[AgentState.COMPLEX_REASONING].keys()
                    )
                    conversation.is_continue_previous = last_history_count > 0
            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT:
                enriched_prompt = user_prompt
                # check conversation history to check if this followup
                if AgentState.COMPLEX_REASONING in conversation.agent_state_follow_up_history:
                    last_history_count = max(
                        conversation.agent_state_follow_up_history[AgentState.COMPLEX_REASONING].keys()
                    )
                    conversation.is_continue_previous = last_history_count > 0
            else:
                enriched_prompt = self.followup_handler.execute_agent(
                    user_prompt, conversation, conversation.active_session.session_id, chat_log
                )

            # Save the current user question to conversation history - excluding @approve
            # if not user_prompt.startswith("@approve"):
            if (
                conversation.agent_type != LLMAgentType.AGENT_TYPE_USER_REACTION
                or conversation.agent_type != LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT
            ):
                conversation.add_to_history(
                    {"role": "user", "content": str(user_prompt)},
                    True,
                    True,
                    is_required_for_follow_ups=True,
                    llm_section_type=SectionType.QUESTION.value,
                )
                conversation.user_inputs[-1] = enriched_prompt

            if is_insight:
                enriched_prompt = "@Insight " + enriched_prompt

            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_USER_REACTION:
                conversation.agent_state = AgentState.KNOWLEDGE_MANAGER
                knowledge_manager_res = self.start(conversation, None, {"user_request": enriched_prompt}, log_path)
                # Final answer
                result, conversation_temp = self.answer_prepare.run(
                    enriched_prompt, conversation, conversation.active_session.session_id, log_path
                )
                return result, conversation

            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_ANALYSIS:
                conversation.agent_state = AgentState.COMPLEX_REASONING
                complex_analysis_res = self.start(conversation, None, {"user_request": enriched_prompt}, log_path)
                # Final answer
                result, conversation_temp = self.answer_prepare.run(
                    enriched_prompt, conversation, conversation.active_session.session_id, log_path
                )

                # send email if process time more than email time threshold
                print(f"this is the start time: {conversation.active_session.session_start_time}")
                print(f"this is the current time: {datetime.now(timezone.utc)}")
                print(
                    f"this is the start time + threshold: {conversation.active_session.session_start_time + timedelta(seconds=self.email_time_threshold)}"
                )
                if conversation.active_session.session_start_time + timedelta(
                    seconds=self.email_time_threshold
                ) < datetime.now(timezone.utc):
                    self.conversation_service.send_last_session_answer_email(
                        conversation.chat_id, chat_log, [user_info.get("email")], user_info
                    )

                return result, conversation

            elif conversation.agent_type == LLMAgentType.AGENT_TYPE_COMPLEX_INSIGHT:
                # invoke InsightDashboardService to generate insight
                insight_dashboard_service = InsightDashboardService()
                insight_dashboard_service.add_analysis_to_dashboard(
                    conversation,
                    conversation.active_session.dashboard_pin_request.sessionId,
                    conversation.active_session.dashboard_pin_request.frequencyInfo,
                    conversation.active_session.user_info,
                )
                result, conversation_temp = self.answer_prepare.run(
                    enriched_prompt, conversation, conversation.active_session.session_id, log_path
                )
                return result, conversation

            # Step 2: Determine the appropriate tool of the query
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.INTENT_CLASSIFICATION.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )
            tool_result = self.tool_router.execute_agent(
                {"user_question": enriched_prompt}, conversation, conversation.active_session.session_id, chat_log
            )
            if tool_result is None:
                chat_log.error("OrchestrationAgent | Failed to classify intent")
                return "I encountered an error while processing your request.", conversation

            if is_insight:
                self.insight_agent = LlmInsightAgent(True)
                insight_user_prompt = {
                    "query": enriched_prompt,
                    "initial_data": "",
                    "feedback": "",
                }
                return self.insight_agent.run(
                    insight_user_prompt, conversation, conversation.active_session.session_id, log_path
                )

            result_dict = {
                "is_success": False,
                "error_message": "",
            }
            # Step 3: Route to appropriate agent based on intent
            if tool_result.get("data_sources"):
                data_source_name = tool_result.get("data_sources")[0]  # Assume only one data source for now

                if tool_result.get("route_option") == "DATA":
                    # Handle SQL-related queries by SQL agent with selected data source
                    self.sql_agent = SQLAgent(data_source_name)
                    chat_log.info(
                        f"OrchestrationAgent | Routing to SQL Agent to retrieve data from {data_source_name}"
                    )
                    result_dict = self.sql_agent.start(conversation, data_source_name, None, log_path)
                    # Invoke the python code generation agent if data retrieval is successful and valid data is returned
                    if result_dict.get("total_record_count", 0) > 0 and (
                        conversation.data_retrieval_status == ConversationConclusion.DATA_AVAILABLE
                        or conversation.data_retrieval_status == ConversationConclusion.TRUNCATED_RECORDS
                    ):
                        chat_log.info(
                            "OrchestrationAgent | Data retrieval successful. Invoking Python code generation agent"
                        )
                        conversation.agent_state = AgentState.PYTHON_CODE_GENERATION
                        result_dict["user_question"] = conversation.user_inputs[-1]
                        self.start(conversation, data_source_name, result_dict, log_path)
                    else:
                        chat_log.info("OrchestrationAgent | Data retrieval aborted.")
                        conversation.agent_state = AgentState.FAILED
                elif tool_result.get("route_option") == "METADATA":
                    # Handle metadata queries
                    input_data = {
                        "user_question": conversation.user_inputs[-1],
                    }
                    conversation.agent_state = AgentState.METADATA_QUERY
                    self.start(conversation, data_source_name, input_data, log_path)
                else:
                    # Unsupported option
                    pass

            elif tool_result.get("route_option") == "PYTHON":
                # Handle calculations
                input_data = {
                    "user_question": conversation.user_inputs[-1],
                }
                conversation.agent_state = AgentState.PYTHON_CODE_GENERATION
                conversation.data_retrieval_status == ConversationConclusion.DATA_AVAILABLE
                self.start(conversation, None, input_data, log_path)

            # If valid answer is there, then save it for the use of query router
            if conversation.data_retrieval_status == ConversationConclusion.DATA_AVAILABLE:
                self.tool_router.add_to_history(
                    conversation,
                    "user",
                    ["Previous Question: " + enriched_prompt],
                    is_required_for_follow_ups=True,
                )
                if conversation.current_question_raw_answer:
                    self.tool_router.add_to_history(
                        conversation,
                        "assistant",
                        ["Previous Answer: " + conversation.current_question_raw_answer],
                        is_required_for_follow_ups=True,
                    )

            # Step 4: Prepare final answer
            result, conversation_temp = self.answer_prepare.run(
                enriched_prompt, conversation, conversation.active_session.session_id, log_path
            )

            return result, conversation

        except Exception as e:
            error_msg = f"OrchestrationAgent | Error: {str(e)}\n{traceback.format_exc()}"
            chat_log.error(error_msg)
            conversation.status = ConversationStatus.FAILED
            return f"I encountered an error while processing your request: {str(e)}", conversation

    def handle_python_code_generation_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):

        # After Python code generation, move to code review
        next_input = {
            "user_question": conversation.user_inputs[-1],
            "python_code": output_dict["code"],
            "reasoning": output_dict["logic"],
            "reusable_variables": conversation.global_var_list,
            "file_paths": output_dict["file_paths"],
        }

        return AgentState.CODE_REVIEW, next_input

    def handle_code_review_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the code review agent.

        Args:
            output_dict (dict): The output from the code review agent
            prev_invoked_count (int): Number of previous invocations
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging

        Returns:
            tuple: Next state and input data for the next state
        """
        # Check if revision is required
        if output_dict.get("is_revision_required", False):
            chat_log.info("SQLAgent | handle_code_review_output | Code review failed. Revising Python code")
            # If revision is required, go back to Python code generation
            next_input = {
                "review_feedback": output_dict.get("review_feedback", ""),
            }
            return AgentState.PYTHON_CODE_GENERATION, next_input

        # Reset the history for code review when it passes
        self.state_handler_instance_map[AgentState.CODE_REVIEW].clear_history(conversation)

        # If no revision is required, move to Python execution
        python_code = output_dict.get("python_code", "")

        next_input = {
            "user_question": conversation.user_inputs[-1],
            "python_code": python_code,
            "file_paths": output_dict.get("file_paths", {}),
        }

        # Move to Python execution
        return AgentState.PYTHON_CODE_EXECUTION, next_input

    def handle_python_execution_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the Python execution agent.

        Args:
            output_dict (dict): The output from the Python execution agent
            prev_invoked_count (int): Number of previous invocations
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging

        Returns:
            tuple: Next state and input data for the next state
        """
        # Check if execution was successful
        if not output_dict.get("execution_success", False):
            chat_log.info("SQLAgent | handle_python_execution_output | Execution failed. Revising Python code")
            # If execution failed, go back to Python code generation
            next_input = {
                "execution_error": output_dict.get("execution_error", "Unknown execution error"),
            }
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.PYTHON_CODE_GENERATION, next_input

        # Add the execution result to the history of python code generator agent
        self.state_handler_instance_map[AgentState.PYTHON_CODE_GENERATION].add_to_history(
            conversation,
            "user",
            [f"Execution Result: {output_dict.get('execution_result', '')}"],
            is_required_for_follow_ups=True,
        )

        # Move to data review
        conversation.current_question_raw_answer = str(output_dict.get("execution_result", ""))
        return AgentState.TEXTUAL_REVIEW, output_dict

    def handle_data_review_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the data review agent.

        Args:
            output_dict (dict): The output from the data review agent
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging

        Returns:
            tuple: Next state and input data for the next state
        """
        # Check if revision is required
        if output_dict.get("is_revision_required", False):
            chat_log.info("SQLAgent | handle_data_review_output | Data review failed. Revising Python code")
            # If revision is required, go back to Python code generation
            next_input = {
                "review_feedback": output_dict.get("feedback", ""),
                "execution_result": output_dict.get("data_observations", ""),
            }
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.PYTHON_CODE_GENERATION, next_input

        # If there are image files to review, move to visual review
        if output_dict.get("image_file_paths", []):
            # clear the history of python code writing and execution agents because now data issues are no more
            self.state_handler_instance_map[AgentState.PYTHON_CODE_GENERATION].clear_history(conversation, True)
            self.state_handler_instance_map[AgentState.PYTHON_CODE_EXECUTION].clear_history(conversation)
            chat_log.info("SQLAgent | handle_data_review_output | Moving to visual review")
            next_input = {
                "user_question": conversation.user_inputs[-1],
                "python_code": output_dict.get("python_code", ""),
                "execution_result": output_dict.get("execution_result", ""),
                "image_file_paths": output_dict.get("image_file_paths", []),
            }
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            return AgentState.VISUAL_REVIEW, next_input

        # If no revision is required and no images to review, mark as completed
        conversation.agent_state = AgentState.COMPLETED
        conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
        return AgentState.COMPLETED, {}

    def handle_visual_review_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the visual review agent.

        Args:
            output_dict (dict): The output from the visual review agent
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging
            is_last_attempt (bool): Whether this is the last attempt

        Returns:
            tuple: Next state and input data for the next state
        """
        # TODO: Mark answer correct even if visualization is not correct - required for insight
        # Check if revision is required
        if output_dict.get("is_revision_required", False):
            chat_log.info("SQLAgent | handle_visual_review_output | Visual review failed. Revising Python code")
            # If revision is required, go back to Python code generation
            next_input = {
                "review_feedback": output_dict.get("feedback", ""),
                "is_visualization_issue": output_dict.get("is_visualization_issue", False),
            }

            # Store feedback in conversation history if it's a visualization issue
            if output_dict.get("is_visualization_issue", False):
                if not hasattr(conversation, "data_reviewer_feedback_history"):
                    conversation.data_reviewer_feedback_history = []

                conversation.data_reviewer_feedback_history.append(
                    {
                        "feedback": output_dict.get("feedback", ""),
                        "is_visualization_issue": True,
                        "issue_severity": output_dict.get("issue_severity", ""),
                    }
                )

            return AgentState.PYTHON_CODE_GENERATION, next_input

        # If no revision is required, mark as completed
        conversation.agent_state = AgentState.COMPLETED
        return AgentState.COMPLETED, {}

    def handle_metadata_query_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        """
        Handle the output from the metadata query agent.

        Args:
            output_dict (dict): The output from the metadata query agent
            conversation (Conversation): The conversation object
            chat_log (Logger): Logger for logging
            is_last_attempt (bool): Whether this is the last attempt

        Returns:
            tuple: Next state and input data for the next state
        """
        # If metadata query was successful, mark as completed
        if output_dict.get("is_success", False):
            conversation.agent_state = AgentState.COMPLETED
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            conversation.current_question_raw_answer = output_dict.get("answer", "")
            return AgentState.COMPLETED, {}
        else:
            # If metadata query failed, mark as failed
            conversation.agent_state = AgentState.FAILED
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.FAILED, {}

    def handle_user_uploaded_files(self, conversation: Conversation) -> GeneralResponse[bool]:
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )
        # wait until all files are uploaded
        is_upload_sessions_ready = self.upload_session_service.wait_until_all_files_uploaded(
            conversation.file_upload_sessions
        )
        if not is_upload_sessions_ready.success:
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS_FAILED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )
            # return is_upload_sessions_ready.message, conversation
            return GeneralResponse(
                success=False,
                message=is_upload_sessions_ready.message,
                data=False,
            )

        else:
            # copy uploaded sessions' files to respective conversation directory
            is_files_copied = self.upload_session_service.copy_uploaded_files_to_conversation_directory(
                str(conversation.chat_id), conversation.file_upload_sessions, conversation
            )
            if is_files_copied.success:
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS_SUCCESS.value,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                )
                if is_files_copied.data:
                    # send data sources tab to frontend
                    if FrontendTabContentType.SOURCES not in conversation.active_session.session_used_tabs_all:
                        conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.SOURCES)
                        conversation.persist_and_stream_handler(
                            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
                            block_type=FrontendBlockType.TAB_CONTENT,
                            block_data=[
                                {
                                    "id": FrontendTabContentType.SOURCES.tab_type_value,
                                    "title": FrontendTabContentType.SOURCES.tab_title_value,
                                    "status": conversation.active_session.session_status.value,
                                    "session_type": conversation.active_session.session_type.value,
                                }
                            ],
                            block_tab_types=[FrontendTabContentType.TABS],
                        )
                    # stream all files as data sources
                    conversation.persist_and_stream_handler(
                        block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.DATA_SOURCES.suffix_value}",
                        block_type=FrontendBlockType.DATA_SOURCES,
                        block_data=is_files_copied.data,
                        block_tab_types=[FrontendTabContentType.SOURCES, FrontendTabContentType.ANSWER],
                    )
            else:
                # stream error message
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                    block_type=FrontendBlockType.MARKDOWN,
                    block_data=FrontendBlockDataMessages.CHECKING_FILE_UPLOADS_FAILED.value,
                    block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                )
                # return is_files_copied.message, conversation
                return GeneralResponse(
                    success=False,
                    message=is_files_copied.message,
                    data=False,
                )
        return GeneralResponse(
            success=True,
            message="Files processed successfully",
            data=True,
        )

    def handle_complex_reasoning_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        # If complex reasoning was successful, mark as completed
        if output_dict.get("is_success", False):
            conversation.agent_state = AgentState.COMPLETED
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            return AgentState.COMPLETED, {}
        else:
            # If complex reasoning failed, mark as failed
            conversation.agent_state = AgentState.FAILED
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.FAILED, {}

    def handle_knowledge_manager_output(
        self, output_dict: dict, conversation: Conversation, chat_log: Logger, is_last_attempt: bool
    ):
        # If knowledge manager was successful, mark as completed
        if output_dict.get("status", "") == "completed":
            conversation.agent_state = AgentState.COMPLETED
            conversation.data_retrieval_status = ConversationConclusion.DATA_AVAILABLE
            return AgentState.COMPLETED, {}
        else:
            # If knowledge manager failed, mark as failed
            conversation.agent_state = AgentState.FAILED
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            return AgentState.FAILED, {}
