"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class SQLReviewAgent
* @description This class implements an agent for reviewing SQL queries and their results
"""

import os
import datetime
from logging import Logger
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    MessageContentType,
)
from utils.llm_utils import get_dict_from_json_or_python
from utils.llm_utils import sql_output_data_review_response_schema


class SQLReviewAgent(BaseStateHandlerAgent):
    """
    An agent responsible for reviewing SQL queries and their results.
    It checks for accuracy, relevance, and correctness of the SQL query
    in relation to the user's question.
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the SQLReviewAgent with a default model.

        Args:
            data_source_name (str): Name of the data source
            _default_model: The default LLM model to use
        """
        print("Initializing SQLReviewAgent class")
        super().__init__(data_source_name, _default_model, AgentState.SQL_REVIEW)

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Review SQL query and its results based on the input data.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing SQL query and context
                Format: {
                    "user_question": The latest user input,
                    "query_plan": The query plan used to generate the SQL,
                    "sql_query": The SQL query to review,
                    "data_records": Sample results from SQL query execution
                }
            follow_up_history (list): List of previous session messages in the conversation
            current_session_history (list): List of current session messages
            chat_log (Logger): Logger object
            try_count (int): No of times the agent is invoked for same conversation previously

        Returns:
            Dictionary containing the review results and execution status
        """
        chat_log.debug("SQLReviewAgent | on_agent_invoked | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.SQL_REVIEW)
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.SQLReviewAgent_TASK.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )
        # Extract necessary data from input
        user_question = input_data.get("user_question", "")
        query_plan = input_data.get("query_plan", "")
        sql_query = input_data.get("sql_query", "")
        data_records = input_data.get("data_records", [])
        unstructured_columns = input_data.get("unstructured_columns", [])
        selected_table_names = list(input_data.get("table_metadata", {}).keys())

        # If SQL review is disabled, always pass the SQL
        if os.getenv("SQL_REVIEW_OFF", "False") == "True":
            return self.on_success(
                input_data,
                {
                    "is_revision_required": False,
                    "feedback": "",
                    "sql_query": sql_query,
                    "query_plan": query_plan,
                    "data_records": data_records,
                    "total_record_count": input_data.get("total_record_count", len(data_records)),
                    "data_input": data_records[:5],
                    "table_metadata": input_data.get("table_metadata", {}),
                },
            )

        # Prepare the prompt for SQL review
        prompt_messages = self.get_prompt_review_sql_output_data(
            user_question,
            selected_table_names,
            query_plan,
            unstructured_columns,
            input_data.get("table_metadata", {}),
            sql_query,
            data_records,
            follow_up_history,
            current_session_history,
            chat_log,
        )

        # Invoke LLM for SQL review
        response = self.invoke_llm(
            conversation,
            self.main_system_instruction,
            prompt_messages,
            chat_log,
            response_format=sql_output_data_review_response_schema,
            is_current_session_history_required=try_count
            > 1,  # Current session history not loaded from DB if this is a retry
            follow_up_history_load_depth=(
                0 if try_count > 1 else 1
            ),  # Follow up history loaded from DB if this is not a retry
        )

        if not response:
            return self.on_failure("Failed to get response from LLM for SQL review", input_data)

        # Parse the response
        review_result = get_dict_from_json_or_python(response)
        # Log the review result
        chat_log.info(f"SQL Review Result: {review_result}")

        if review_result.get("is_revision_required_for_SQL", False):
            # Update the frontend with review feedback in case failed
            conversation.active_session.add_to_queue(
                "\n\nSQL Review feedback:\n\n" + review_result["feedback"] + "\n\n",
                "assistant",
                ConversationStatus.IN_PROGRESS,
                MessageContentType.TEXT.value,
            )
            self.add_to_history(
                conversation,
                "assistant",
                ["SQL Review feedback:\n\n" + review_result["feedback"]],
                False,
                True,
                MessageContentType.TEXT.value,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.SQL_REVISION_REQUIRED.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data="\n\nReviewer Feedback:\n\n" + review_result["feedback"] + "\n\n",
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
        else:
            # Update frontend that review is success and going to generate python code
            conversation.active_session.add_to_queue(
                "\n\nSQL Review successful.\n\n---\n\n",
                "assistant",
                ConversationStatus.IN_PROGRESS,
                MessageContentType.TEXT.value,
            )
            self.add_to_history(
                conversation,
                "assistant",
                ["\n\nSQL Review successful.\n\n---\n\n"],
                False,
                True,
                MessageContentType.TEXT.value,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.SQL_REVIEW_SUCCESS.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

        # Return the review result
        return self.on_success(
            input_data,
            {
                "is_revision_required": review_result.get("is_revision_required_for_SQL", False),
                "feedback": review_result.get("feedback", ""),
                "data_availability_feedback": review_result.get("data_availability_feedback", ""),
                "sql_query": sql_query,
                "query_plan": query_plan,
                "reasoning": input_data.get("reasoning", ""),
                "total_record_count": input_data.get("total_record_count", len(data_records)),
                "data_records": data_records,
                "data_input": input_data.get("data_input", ""),
                "data_source_name": input_data.get("data_source_name", ""),
                "table_metadata": input_data.get("table_metadata", {}),
                "business_rules": input_data.get("business_rules", []),
                "data_file_names": input_data.get("data_file_names", ""),
            },
        )

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("SQLReviewAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return "Failed to build correct SQL query: " + str(input_data["review_feedback"])

    def get_prompt_review_sql_output_data(
        self,
        user_question: str,
        selected_table_names: list,
        query_plan: str,
        unstructured_columns: list,
        table_metadata: dict,
        sql_query: str,
        data_records: list,
        follow_up_history: list,
        current_session_history: list,
        chat_log,
    ):
        current_date_str = str(datetime.datetime.now().strftime("%Y-%m-%d"))
        llm_prompt = []
        relevant_business_rules = self.data_dictionary_service.get_table_overviews_with_business_rules(
            self.data_source_name, True, selected_table_names
        )
        llm_prompt.append({"role": "user", "content": "Table Metadata:\n\n" + str(table_metadata)})
        llm_prompt.append({"role": "user", "content": "Unstructured Columns:\n\n" + str(unstructured_columns)})
        er_diagram = self.data_dictionary_service.get_er_diagram_for_data_source(self.data_source_name)
        if er_diagram:
            llm_prompt.append({"role": "user", "content": "ER Diagram:\n\n" + str(er_diagram)})
        llm_prompt.extend(follow_up_history)
        llm_prompt.append({"role": "user", "content": "User question: " + str(user_question)})
        llm_prompt.extend(current_session_history)
        llm_prompt.append({"role": "user", "content": "SQL Query Plan:\n\n" + str(query_plan)})
        llm_prompt.append({"role": "user", "content": "SQL Query:\n\n" + str(sql_query)})
        if data_records:
            llm_prompt.append({"role": "user", "content": "SQL Output:\n\n" + str(data_records)})
        else:
            llm_prompt.append({"role": "user", "content": "SQL Output: No records retrieved"})
        llm_prompt.append({"role": "user", "content": "Current date: " + str(current_date_str)})

        return llm_prompt

    def update_followup_history(self, conversation, chat_log, output_data):
        # Add user question to history
        self.add_to_history(
            conversation,
            "user",
            ["Previous User Question: " + conversation.user_inputs[-1]],
            is_required_for_follow_ups=True,
            is_skip_for_current_session=True,
        )
        # Add SQL query to history
        self.add_to_history(
            conversation,
            "user",
            ["Previous SQL Query: " + output_data["sql_query"]],
            is_required_for_follow_ups=True,
        )
        # Add current review result to history
        self.add_to_history(
            conversation,
            "assistant",
            ["Previous Review Feedback: " + output_data["feedback"]],
            is_required_for_follow_ups=True,
        )
