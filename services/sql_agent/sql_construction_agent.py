"""
 * Copyright (c) 2025 LayerNext Inc.
 * all rights reserved.
 * This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.

* @class SQLConstructionAgent
 * @description Agent responsible for constructing SQL queries based on query plans
 * <AUTHOR>
"""

import os
import traceback
from logging import Logger
from models.conversation import Conversation
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationStatus,
    DataBlockHandlerType,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    MessageContentType,
    SectionType,
)
from utils.llm_utils import get_dict_from_json_or_python, sql_query_generate_schema
from utils.misc_utils import list_to_markdown
from utils.metalake import run_sql_query, save_data


class SQLConstructionAgent(BaseStateHandlerAgent):
    """
    Agent responsible for constructing SQL queries based on query plans.

    This agent takes the query plan generated in previous stages and creates
    a valid SQL query that can be executed against the database to answer
    the user's question.
    """

    def __init__(self, data_source_name: str, _default_model):
        """
        Initialize the SQLConstructionAgent with a default model.

        Args:
            _default_model: The default LLM model to use
        """
        print("Initializing SQLConstructionAgent class")
        super().__init__(data_source_name, _default_model, AgentState.SQL_CONSTRUCTION)
        # SQL review feedback instruction
        self.sql_review_feedback_instructions = ""
        with open(
            "instructions/tools/sql_construction/system_instructions_feedback.txt",
            "r",
            encoding="utf-8",
        ) as file:
            self.sql_review_feedback_instructions = str(file.read())

        # Set model names from environment variables
        self.llm_model_name = os.getenv("MODEL_SQL_GENERATION", "gpt-4o")
        self.llm_client_sql = self.llm_client_factory.get_llm_client(
            os.getenv("SQL_GENERATION_LLM_PROVIDER", "openai")
        )

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Construct a SQL query based on the query plan.

        Args:
            conversation (Conversation): The conversation object
            input_data (dict): Dictionary containing query plan and metadata
            follow_up_history (list): List of previous session messages in the conversation
            current_session_history (list): List of current session messages
            chat_log (Logger): Logger object
            try_count (int): No of times the agent is invoked for same conversation previously

        Returns:
            Dictionary containing the SQL query, reasoning, and execution status
        """
        chat_log.debug("SQLConstructionAgent | invoke_agent | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.SQL_GENERATION)
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.SQLConstructionAgent_TASK.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )

        # Extract necessary data from input
        user_question = conversation.user_inputs[-1]
        query_plan = input_data.get("query_plan", {})
        table_metadata = input_data.get("selected_table_fields", [])
        data_source_type = self.data_dictionary_service.get_data_source_type(self.data_source_name)
        if "review_feedback" in input_data and input_data["review_feedback"]:
            current_session_history.append(
                {"role": "user", "content": "Review Feedback: " + input_data["review_feedback"]}
            )
        # Generate SQL query
        sql_query_dict = self.generate_sql(
            conversation,
            user_question,
            table_metadata,
            query_plan,
            data_source_type,
            follow_up_history,
            current_session_history,
            chat_log,
        )
        error_message = ""
        if sql_query_dict is None or sql_query_dict.get("is_error", False):
            error_message = (
                "Error in SQL generation with LLM" if sql_query_dict is None else sql_query_dict["error_message"]
            )
            chat_log.error(f"SQLConstructionAgent | invoke_agent | {error_message}")

        chat_log.debug("SQLConstructionAgent | invoke_agent | End")
        sql_query_dict["data_source_name"] = self.data_source_name
        sql_query_dict["query_plan"] = query_plan
        sql_query_dict["unstructured_columns"] = input_data.get("unstructured_columns", [])
        sql_query_dict["table_metadata"] = table_metadata
        sql_query_dict["business_rules"] = input_data.get("business_rules", [])
        if "review_feedback" in input_data:
            sql_query_dict["review_feedback"] = input_data["review_feedback"]

        # Update the frontend with SQL generated and first 5 rows of results
        sql_construction_frontend_text = ""
        if not sql_query_dict["is_error"] and "sql_query" in sql_query_dict and sql_query_dict["sql_query"]:
            sql_construction_frontend_text = (
                "\n\n---\n\n**SQL query generated:**\n\n```sql\n"
                + sql_query_dict["sql_query"]
                + "\n```\n\n"
                + sql_query_dict["data_input"]
                + "\n\n"
            )
        else:
            sql_construction_frontend_text = "\n\n" + error_message

        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=FrontendBlockDataMessages.SQL_QUERY_GENERATED.value,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data=sql_construction_frontend_text,
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )

        return self.on_success(input_data, sql_query_dict)

    def _data_to_markdown_table(self, table_rows: list):
        import pandas as pd

        df = pd.DataFrame(table_rows)
        return df.to_markdown(index=False)

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("SQLConstructionAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
        return "Failed to build correct SQL query: " + str(
            input_data["review_feedback"] if "review_feedback" in input_data else ""
        )

    def generate_sql(
        self,
        conversation: Conversation,
        user_question: str,
        table_metadata: list,
        query_plan: dict,
        data_source_type: str,
        follow_up_history: list,
        current_session_history: list,
        log_handle: Logger,
    ):
        """
        Generate SQL query based on the query plan and metadata.

        Args:
            user_question (str): User question
            data_overview (str): Overview of the database tables
            table_metadata (list): Metadata of the database tables
            query_plan (str): Query plan generated in previous stage
            data_source_type (str): Type of the data source
            follow_up_history (list): List of previous session messages: previous SQL and review feedback
            current_session_history (list): List of current session messages: previous SQL and review feedback
            log_handle (Logger): Logger object

        Returns:
            dict: Dictionary containing the SQL query, reasoning, and execution status
        """

        db_specific_guides_text = self.data_dictionary_service.get_db_specific_instructions([self.data_source_name])
        llm_prompt_list = []
        llm_prompt_list.append({"role": "user", "content": "Selected tables and columns:\n\n" + str(table_metadata)})
        llm_prompt_list.append({"role": "user", "content": "Query Plan:\n\n" + str(query_plan)})
        llm_prompt_list.append(
            {
                "role": "user",
                "content": "Guidelines specific to " + str(data_source_type) + " :\n\n" + str(db_specific_guides_text),
            }
        )
        if follow_up_history:
            llm_prompt_list.extend(follow_up_history)
        llm_prompt_list.append({"role": "user", "content": "User question: " + str(user_question)})
        if current_session_history:
            llm_prompt_list.extend(current_session_history)

        sql_try_count = 0  # Maximum 2 tries to generate SQL without error
        is_sql_error_free = True

        while sql_try_count < 3:
            # structured_output, unstructured_output = self.invoke_llm(
            structured_output = self.invoke_llm(
                conversation,
                self.sql_review_feedback_instructions if current_session_history else self.main_system_instruction,
                llm_prompt_list,
                log_handle,
                response_format=sql_query_generate_schema,
                # additional_structuring_instructions="Extract SQL query and the reasoning logic.",
                llm_client=self.llm_client_sql,
            )
            if structured_output is None:
                return {
                    "error_message": "Error in SQL generation with LLM",
                    "is_error": True,
                }
            sql_query_dict = get_dict_from_json_or_python(structured_output)
            if sql_query_dict is None:
                return {
                    "error_message": "Error in SQL generation with LLM",
                    "is_error": True,
                }
            elif sql_query_dict.get("is_error", False):
                log_handle.debug(f"SQLConstructionAgent | generate_sql |Failed to generate SQL: {sql_query_dict}")
                return sql_query_dict

            # Validate the data file name - prompt if not available
            if "data_file_names" not in sql_query_dict or not sql_query_dict["data_file_names"]:
                llm_prompt_list.append({"role": "user", "content": "Please provide the data file names."})
                continue

            # Execute the SQL and get all rows
            last_sql_error = ""
            sql_query = sql_query_dict["sql_query"]
            try:
                data_rows = run_sql_query(self.data_source_name, sql_query)
                log_handle.debug(f"SQLConstructionAgent | generate_sql | Try {sql_try_count}: SQL result: {data_rows}")
                sql_query_dict["total_record_count"] = len(data_rows)
                sql_query_dict["first_100_row_results"] = data_rows[:100]
                sql_query_dict["data_input"] = "**First 5 rows of data:**\n\n" + list_to_markdown(data_rows[:5])
                sql_query_dict["is_success"] = True

                # Save the data to memory disk file
                data_refs = sql_query_dict["data_file_names"]
                for data_ref in data_refs:
                    if save_data(conversation.chat_id, data_ref, data_rows):
                        # Add to the conversation data reference set
                        conversation.data_reference_set.add(data_ref)
                    else:
                        log_handle.warning(f"Failed to save data {data_ref} to memory disk")

                is_sql_error_free = True
                break
            except Exception as e:
                log_handle.error(
                    f"SQLConstructionAgent | generate_sql | Try {sql_try_count}: Error in SQL execution: {str(e)}\n{traceback.format_exc()}\n"
                )
                llm_prompt_list.append({"role": "assistant", "content": "Generated SQL: " + sql_query})
                llm_prompt_list.append(
                    {"role": "user", "content": "Please re-write the SQL as it gives following error:\n " + str(e)}
                )
                # Invoke the LLM again with error to correct it
                sql_try_count += 1
                is_sql_error_free = False
                last_sql_error = str(e)

        # If we can't generate SQL correctly then return error
        if not is_sql_error_free:
            conversation.data_retrieval_status = ConversationConclusion.DATA_RETRIEVAL_FAILURE
            conversation.current_question_raw_answer = (
                "Failed to build SQL to retrieve data for your question: " + last_sql_error
            )
            return {
                "error_message": conversation.current_question_raw_answer,
                "is_error": True,
            }

        return sql_query_dict

    def update_followup_history(self, conversation, chat_log, output_data):
        # Add user question and SQL query to history
        self.add_to_history(
            conversation,
            "user",
            ["Previous User Question: " + conversation.user_inputs[-1]],
            is_required_for_follow_ups=True,
            is_skip_for_current_session=True,
        )
        if "review_feedback" in output_data and output_data["review_feedback"]:
            self.add_to_history(
                conversation,
                "user",
                ["Previous Review Feedback: " + output_data["review_feedback"]],
            )
        if "sql_query" in output_data and output_data["sql_query"]:
            self.add_to_history(
                conversation,
                "assistant",
                ["Previous SQL Query: " + output_data["sql_query"]],
                is_required_for_follow_ups=True,
            )
