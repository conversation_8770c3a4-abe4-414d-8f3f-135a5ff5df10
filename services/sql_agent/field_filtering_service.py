import traceback
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    ConversationStatus,
    DataBlockVisibility,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    MessageContentType,
)
from utils.llm_utils import get_dict_from_json_or_python
from utils.sql_llm_utils import (
    initial_table_selection_schema,
    initial_field_selection_schema,
    missing_field_detection_schema,
)
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from logging import Logger
from models.conversation import Conversation

FIELD_SEARCH_BLOCK_SIZE = 100


class SQLFieldSelectorAgent(BaseStateHandlerAgent):
    def __init__(
        self,
        data_source_name: str,
        _default_model,
    ):
        print("Initializing SQLFieldSelectorAgent class")
        super().__init__(data_source_name, _default_model, AgentState.COLUMN_FINDING)
        """
        full_data_dictionary: dict
            Full metadata: { table_name: [ {field_name, field_type, description}, ... ], ... }
        block_size: int
            Number of fields per block for blockwise scanning
        """

        self.task_mode = "initial_selection"  # or 'missing_field_detection'
        self.load_mode = "full_load"  # or 'blockwise_load'

        # Load full data dictionary using the data dictionary service
        self.full_data_dictionary = self.data_dictionary_service.get_full_data_dictionary(self.data_source_name)

    def on_agent_invoked(
        self,
        conversation,
        input_data,
        follow_up_history,
        current_session_history,
        chat_log,
        try_count,
        prev_invoked_count=0,
    ):
        """
        Description:
            Select relevant fields for a user query
        Parameters:
            conversation: Conversation object
            input_data: Dictionary containing user question, selected tables and fields : {
                "user_question": "What is the total sales for the year 2023?",
                "selected_table_fields": [{"table_name": "sales", "fields": ["id", "amount"]}, {"table_name": "products", "fields": ["name"]}],
                "task_mode": "initial_selection",
                "missing_description": "Missing data for product categories",
                "table_metadata": {"sales": {"fields": {"id": "Field 1 description", "amount": "Field 2 description"}}, "products": {"fields": {"name": "Field 3 description"}}},
                "business_rules": ["Rule 1", "Rule 2"],
                "data_find_reasoning": "Reasoning for selecting these business rules, tables and columns",
                "data_source_name": "sales_data"
            }
            follow_up_history: List of previous messages in the conversation
            current_session_history: List of current session messages
            chat_log: Logger object
            try_count (int): No of times the agent is invoked for same conversation previously
        Returns:
            Dictionary containing selected fields and reasoning
        """
        chat_log.debug("SQLFieldSelectorAgent | invoke_agent | Start")
        conversation.active_session_send_analysis_step(AnalysisStep.COLUMN_FINDING)
        # Get parameters from input_datas
        user_question = conversation.user_inputs[-1]
        task_mode = input_data.get("task_mode", "initial_selection")
        # get selected table list from "selected_table_fields" in input_data
        selected_tables = []
        for table_name in input_data["table_metadata"]:
            selected_tables.append(table_name)
        missing_data_list = input_data.get("missing_data_list", [])
        query_plan = input_data.get("query_plan", {})

        # Validate task mode
        assert task_mode in ["initial_selection", "missing_field_detection"]
        self.task_mode = task_mode
        self.load_mode = self.load_mode

        # Dispatch based on task mode
        if self.task_mode == "initial_selection":
            return self._initial_field_selection(user_question, selected_tables, chat_log)
        elif self.task_mode == "missing_field_detection":
            all_inferred_fields = []
            for missing_data_item in missing_data_list:
                # Full data dictionary is used here
                inferred_fields = self._missing_field_detection(
                    conversation, missing_data_item, None, query_plan, chat_log=chat_log
                )
                if inferred_fields:
                    all_inferred_fields.extend(inferred_fields)
            # If there is already missing data resolutions, then append them too
            if "resolved_missing_columns" in input_data:
                all_inferred_fields.extend(input_data["resolved_missing_columns"])
            missing_field_response_notification = "\n\nFailed to find missing columns requested.\n\n---\n\n"
            if all_inferred_fields:
                missing_field_response_notification = (
                    f"\n\n---\n\n**Found missing columns**: \n\n```json\n{all_inferred_fields}```\n\n---\n\n"
                )
            else:
                conversation.data_retrieval_status = ConversationConclusion.CANT_FIND_DATA
            # Update frontend with notification that missing field detection is in progress
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.SQLMissedDataFinderAgent_TASK.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=missing_field_response_notification,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )
            return self.on_success(
                input_data,
                {
                    "table_metadata": input_data["table_metadata"],
                    "business_rules": input_data["business_rules"],
                    "data_find_reasoning": input_data["data_find_reasoning"],
                    "query_plan": query_plan,
                    "resolved_missing_columns": all_inferred_fields,
                    "missing_data": missing_data_list,
                },
            )

        else:
            raise ValueError(f"Unsupported task_mode: {self.task_mode}")

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("SQLFieldSelectorAgent | on_invoke_limit_reached | Invoke limit reached.")
        err_msg = (
            "\n".join(input_data["missing_data_list"])
            if "missing_data_list" in input_data
            else "Failed to identify relevant columns."
        )
        conversation.data_retrieval_status = ConversationConclusion.CANT_FIND_DATA
        return err_msg

    def _initial_table_selection(self, user_question: str, chat_log: Logger = None):
        instructions = ""
        with open("instructions/sql_agent/initial_table_selection_instructions.txt", "r", encoding="utf-8") as file:
            instructions = str(file.read())
        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append(
            {
                "role": "user",
                "content": "Follow the instructions to select all relevant tables from the data dictionary overview to answer the user question.\n"
                + "Below are the inputs: \n"
                f"User Question: {user_question}\n\n"
                f"Table Overviews with Business Rules: {self.data_dictionary_service.get_table_overviews_with_business_rules(self.data_source_name, True)}",
            }
        )
        try:
            response = self.invoke_llm(
                self,
                None,
                chat_log,
                llm_prompt,
                self.llm_model_name,
                response_format=initial_table_selection_schema,
                is_current_session_history_required=False,
                follow_up_history_load_depth=0,  # History not loaded from followup or current session
            )
            llm_output = response.choices[0].message.content
            output_dict = get_dict_from_json_or_python(llm_output)
            return output_dict
        except Exception as e:
            print(f"Error in initial table selection: {str(e)}")
            return None

    # === Initial Field Selection ===

    def _initial_field_selection(self, user_question: str, selected_tables: list = None, chat_log: Logger = None):

        table_selection_output = self._initial_table_selection(user_question, chat_log)
        selected_tables = [table["table_name"] for table in table_selection_output["selected_tables"]]
        metadata = self._load_table_metadata(selected_tables)

        instructions = ""
        with open("instructions/sql_agent/initial_field_selection_instructions.txt", "r", encoding="utf-8") as file:
            instructions = str(file.read())
        llm_prompt = []
        llm_prompt.append({"role": "system", "content": instructions})
        llm_prompt.append(
            {
                "role": "user",
                "content": "Follow the instructions to select all relevant fields from the data dictionary overview to answer the user question.\n"
                + "Below are the inputs: \n"
                f"User Question: {user_question}\n\n"
                f"Business Rules: {self.data_dictionary_service.get_table_overviews_with_business_rules(self.data_source_name, True, selected_tables)}\n\n"
                f"Tables and Fields Metadata: {metadata}",
            }
        )
        try:
            response = self.client.get_llm_response(
                self,
                None,
                chat_log,
                llm_prompt,
                self.llm_model_name,
                response_format=initial_field_selection_schema,
            )
            llm_output = response.choices[0].message.content
            output_dict = get_dict_from_json_or_python(llm_output)
            return output_dict
        except Exception as e:
            print(f"Error in initial field selection: {str(e)}")
            return None

    # === Missing Field Detection ===

    def _missing_field_detection(
        self,
        conversation: Conversation,
        missing_data_item: str,
        selected_tables: list = None,
        query_plan: dict = {},
        chat_log: Logger = None,
    ):
        try:
            metadata = self.data_dictionary_service.get_metadata_for_table_list(
                selected_tables,
                is_skip_table_description=True,
                is_skip_business_rules=True,
                is_skip_data_type=True,
                data_source_name=self.data_source_name,
            )
            metadata_str = str(metadata)
            # Cut off metadata to fit with max length of 100000 for DeepSeek
            if self.llm_model_name.lower().startswith("deepseek"):
                metadata_str = str(metadata)[:100000]
            # Initialize prompt messages
            llm_prompt = [
                {
                    "role": "user",
                    "content": "Tables and Fields Metadata:\n " + metadata_str + "\n\n",
                },
                {
                    "role": "user",
                    "content": f"Missing Data Item:\n {missing_data_item}\n\n",
                },
            ]

            # Generate and validate field detection
            max_retries = 3
            retry_count = 0
            incorrect_table_names = []
            incorrect_column_names = []
            output_dict = None

            while retry_count < max_retries:
                # Add feedback from previous iteration if any
                if retry_count > 0:
                    feedback_prompt = {"role": "user", "content": ""}
                    if incorrect_table_names:
                        feedback_prompt["content"] += f"These table names are invalid: {incorrect_table_names}. "
                    if incorrect_column_names:
                        feedback_prompt["content"] += f"These column names are invalid: {incorrect_column_names}. "
                    feedback_prompt["content"] += "Please correct your response using only valid tables and fields."
                    llm_prompt.append(feedback_prompt)

                # Invoke LLM to generate the field detection
                response = self.invoke_llm(
                    conversation,
                    self.main_system_instruction,
                    llm_prompt,
                    chat_log,
                    response_format=missing_field_detection_schema,
                    is_current_session_history_required=False,
                    follow_up_history_load_depth=0,  # History not loaded from followup or current session
                )

                if response is None:
                    chat_log.error("SQLFieldSelectorAgent | _missing_field_detection | LLM invocation failed")
                    return []

                output_dict = get_dict_from_json_or_python(response)

                # Validate inferred tables and fields against metadata
                incorrect_table_names, incorrect_column_names = self._validate_inferred_fields(output_dict, metadata)

                # If validation passes, break the loop
                if not incorrect_table_names and not incorrect_column_names:
                    break

                chat_log.warning(
                    f"Missing field detection | Validation failed | Retry {retry_count+1} | "
                    f"Invalid tables: {incorrect_table_names}, Invalid columns: {incorrect_column_names}"
                )
                retry_count += 1

            # Verify whether all missing data is found
            if not output_dict.get("has_inferred_fields", False):
                return []

            # Check whether all missing data is found by count
            inferred_fields = output_dict.get("inferred_missing_fields", [])
            inferred_fields_count = sum(len(field["fields"]) for field in inferred_fields)
            if inferred_fields_count == 0:
                return []

            # Enrich fields with full metadata
            for table_entry in inferred_fields:
                table_name = table_entry["table_name"]
                for field_entry in table_entry["fields"]:
                    field_name = field_entry["field_name"]
                    field_entry["metadata"] = metadata[table_name]["fields"][field_name]
            return inferred_fields

        except Exception as e:
            chat_log.error(f"Error in missing field detection: {str(e)}:\n {traceback.format_exc()}")
            return []

    # === Load Metadata -- Blockwise ===
    def _load_table_metadata_blockwise(self, table_names=None):
        BLOCK_SIZE = 500  # Number of fields to process in each block
        metadata_blocks = []
        metadata = self._load_table_metadata(table_names)
        for table_name, fields in metadata.items():
            field_names = list(fields.keys())
            for i in range(0, len(field_names), BLOCK_SIZE):
                field_names_block = field_names[i : i + BLOCK_SIZE]
                block = {field_name: fields.get(field_name, "") for field_name in field_names_block}
                metadata_blocks.append({table_name: block})
        return metadata_blocks

    # === Metadata Helpers ===

    def _load_table_metadata(self, table_names: list):
        if not table_names:
            return self.full_data_dictionary
        return {table: self.full_data_dictionary.get(table, []) for table in table_names}

    def _validate_inferred_fields(self, output_dict, metadata):
        """Validate tables and fields in inferred_missing_fields against metadata"""
        incorrect_table_names = []
        incorrect_column_names = []

        if not output_dict.get("has_inferred_fields", False):
            return incorrect_table_names, incorrect_column_names

        for table in output_dict.get("inferred_missing_fields", []):
            table_name = table["table_name"]

            # Check if table exists in metadata
            if table_name not in metadata:
                incorrect_table_names.append(table_name)
                continue

            # Check if fields exist in this table
            table_fields = metadata[table_name]
            for field_entry in table["fields"]:
                field_name = field_entry["field_name"]
                if field_name not in table_fields["fields"]:
                    incorrect_column_names.append(field_name)

        return incorrect_table_names, incorrect_column_names

    def update_followup_history(self, conversation, chat_log, output_data):
        pass
