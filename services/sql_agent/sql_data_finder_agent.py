"""
* Copyright (c) 2025 LayerNext Inc.
* all rights reserved.
* This source code is licensed under the license found in the LICENSE file in the root directory of this source tree.
*
* @class SQLDataFinderAgent
* @description Agent responsible for selecting relevant SQL tables, columns and business rules for a user query
* <AUTHOR>
"""

import json
import os
from models.models import FormattedDataSource
from utils.constant import (
    AgentState,
    AnalysisStep,
    ConversationConclusion,
    DataBlockHandlerType,
    DataBlockVisibility,
    DataSourceCategory,
    FrontendBlockDataMessages,
    FrontendBlockType,
    FrontendTabContentType,
    SectionType,
    ConversationStatus,
    MessageContentType,
)
from services.shared.base_state_handler_agent import BaseStateHandlerAgent
from logging import Logger
from models.conversation import Conversation
from utils.llm_utils import (
    array_to_markdown,
    get_dict_from_json_or_python,
    data_find_output_with_indexes_schema,
    business_rule_output_schema,
    data_find_output_schema,
)

# Dictionary to XML
import xml.etree.ElementTree as ET
from xml.dom import minidom


def metadata_dict_to_xml_str(metadata_dict):
    def _dict_to_xml_table(table_index, table_dict):
        table_el = ET.Element("table", index=str(table_index))
        # Table name/details
        for key in ["table_name", "table_details"]:
            if key in table_dict:
                child = ET.SubElement(table_el, key)
                child.text = str(table_dict[key])
        # Fields
        if "fields" in table_dict:
            fields_el = ET.SubElement(table_el, "fields")
            for field_index, field in table_dict["fields"].items():
                field_el = ET.SubElement(fields_el, "field", index=str(field_index))
                for k, v in field.items():
                    if k in ("All distinct values", "Subset of values") and isinstance(v, (list, set)):
                        subel = ET.SubElement(field_el, k)
                        subel.text = "|".join(str(val) for val in v)
                    elif isinstance(v, (list, set)):
                        subel = ET.SubElement(field_el, k)
                        for val in v:
                            value_el = ET.SubElement(subel, "value")
                            value_el.text = str(val)
                    else:
                        subel = ET.SubElement(field_el, k)
                        subel.text = str(v)
        # Business rules
        if "business_rules" in table_dict:
            brules_el = ET.SubElement(table_el, "business_rules")
            for rule_index, rule in table_dict["business_rules"].items():
                rule_el = ET.SubElement(brules_el, "rule", index=str(rule_index))
                rule_el.text = str(rule)
        return table_el

    # Root element
    root = ET.Element("metadata")
    for table_index, table_dict in metadata_dict.items():
        table_el = _dict_to_xml_table(table_index, table_dict)
        root.append(table_el)

    # Pretty print
    rough_string = ET.tostring(root, encoding="utf-8")
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")


def er_dict_to_xml_str(data, root_tag="diagram"):
    def get_first_string_field(d):
        """Detects the first string field as the 'name'."""
        for k, v in d.items():
            if isinstance(v, str):
                return k, v
        return None, None

    root = ET.Element(root_tag)
    for top_level_key, entity_list in data.items():
        section_el = ET.SubElement(root, "section", type=top_level_key)
        if isinstance(entity_list, list):
            for obj in entity_list:
                entry_el = ET.SubElement(section_el, "entry")
                name_key, name_val = get_first_string_field(obj)
                if name_val:
                    ET.SubElement(entry_el, "name").text = name_val
                for k, v in obj.items():
                    if k == name_key:
                        continue  # already handled as <name>
                    if isinstance(v, list):
                        list_el = ET.SubElement(entry_el, "list", key=k)
                        for item in v:
                            ET.SubElement(list_el, "item").text = str(item)
                    elif isinstance(v, dict):
                        dict_el = ET.SubElement(entry_el, "object", key=k)
                        for subk, subv in v.items():
                            ET.SubElement(dict_el, "attribute", key=subk).text = str(subv)
                    else:
                        ET.SubElement(entry_el, "attribute", key=k).text = str(v)
        else:
            # For non-list values, just store as attribute
            ET.SubElement(section_el, "value").text = str(entity_list)

    rough_string = ET.tostring(root, encoding="utf-8")
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")


def business_rules_dict_to_xml_str(business_rules_dict, root_tag="business_rules"):
    root = ET.Element(root_tag)
    for idx, rule_text in business_rules_dict.items():
        rule_el = ET.SubElement(root, "rule", index=str(idx))
        rule_el.text = str(rule_text)
    rough_string = ET.tostring(root, encoding="utf-8")
    reparsed = minidom.parseString(rough_string)
    return reparsed.toprettyxml(indent="  ")


class SQLDataFinderAgent(BaseStateHandlerAgent):
    def __init__(self, data_source_name: str, _default_model):
        print("Initializing SQLDataFinderAgent class")
        # Use MODEL_DATA_LOCATE if specified, else use the default model
        llm_model_name = os.getenv("MODEL_DATA_LOCATE", _default_model)
        super().__init__(data_source_name, llm_model_name, AgentState.DATA_FINDING, history_depth=0)
        # Load instruction for metadata extraction
        with open("instructions/tools/data_finding/metadata_extract_instructions.txt", "r", encoding="utf-8") as file:
            self.metadata_extraction_instructions = str(file.read())

    def on_agent_invoked(
        self,
        conversation: Conversation,
        input_data: dict,
        follow_up_history: list,
        current_session_history: list,
        chat_log: Logger,
        try_count: int,
    ):
        """
        Description:
            Select relevant tables, columns and business rules for a user query
        Parameters:
            conversation: Conversation object
            input_data: Dictionary containing user question and data source name : {"user_question": "What is the total sales for the year 2023?", "data_source_name": "sales_data"}
            follow_up_history: List of previous messages in the conversation
            current_session_history: List of current session messages
            chat_log: Logger object
            try_count (int): No of times the agent is invoked for same conversation previously
        Returns:
            Dictionary containing reasoning, selected table columns and business rules
        """
        chat_log.debug("SQLDataFinderAgent | invoke_agent | Start")

        # conversation.active_session.add_to_queue(
        #     "\nFinding relevant data to answer the user question. Please wait...",
        #     "assistant",
        #     ConversationStatus.IN_PROGRESS,
        #     MessageContentType.TEXT.value,
        # )
        # self.add_to_history(
        #     conversation,
        #     "assistant",
        #     ["Finding relevant data to answer the user question. Please wait..."],
        #     False,
        #     True,
        #     MessageContentType.TEXT.value,
        # )
        # Do the data selection logic here
        # Input prompt:
        # 1. User question
        # 2. Table columns and business rules
        # 3. ER diagram
        # Output: Selected tables as a dictionary list with table name and reasoning, selected table columns and business rules
        is_metadata_info_included = input_data.get("is_metadata_included", False)
        table_metadata_all = self.data_dictionary_service.get_metadata_for_table_list(
            [],
            is_skip_table_description=True,
            is_skip_available_values=is_metadata_info_included,
            is_skip_data_type=is_metadata_info_included,
            data_source_name=self.data_source_name,
            is_use_indexing=False,  # not is_metadata_info_included,
        )
        # # Convert the dictionary 'table_metadata_all' to XML
        # table_metadata_str: str = metadata_dict_to_xml_str(table_metadata_all)
        # Convert the json to more readable string by adding new lines and indentation
        filtered_business_rule_indices = {}
        # If metadata already included in the question, then business rules and metadata filtering not required, just need to extract metadata from the question
        if is_metadata_info_included:
            metadata_res = self._get_metadata_from_question_llm(
                conversation, input_data["user_question"], table_metadata_all, chat_log
            )
        else:
            conversation.active_session_send_analysis_step(AnalysisStep.DATA_SECTION_LOCATE)
            # Update frontend with notification that data finding is in progress
            conversation.active_session.add_section(SectionType.DATA_SOURCE.value)
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=FrontendBlockDataMessages.SQLDataFinderAgent_TASK.value,
                block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            )
            global_rules = self.data_dictionary_service.get_business_rules(is_global_rules_only=True)
            # business_rules_str = business_rules_dict_to_xml_str(global_rules)
            # business_rules_str = json.dumps(global_rules, indent=2)
            all_tables_list = []
            for table_index, table_data in table_metadata_all.items():
                all_tables_list.append(table_data["table_name"])

            #########################################################
            # Filter Business Rules
            #########################################################

            filtered_business_rule_indices = self.filter_business_rules(
                conversation, chat_log, input_data["user_question"]
            )

            # based on filtered_business_rule_indices,
            # 1. create filtered_global_rules
            # 2. remove unnecessary business rules from table_metadata_all

            filtered_global_rules = {k: v for k, v in global_rules.items() if k in filtered_business_rule_indices}

            for table in table_metadata_all.values():
                business_rules = table["business_rules"]
                # Get keys not in the filtered list
                keys_to_remove = [idx for idx in business_rules.keys() if idx not in filtered_business_rule_indices]
                for idx in keys_to_remove:
                    business_rules.pop(idx)

            #########################################################
            # Get table column metadata
            #########################################################

            metadata_res = self._filter_metadata_llm(
                conversation,
                input_data["user_question"],
                follow_up_history,
                current_session_history,
                table_metadata_all,
                filtered_global_rules,
                chat_log,
            )

        if not metadata_res["is_valid"]:
            return self.on_failure(metadata_res["error_message"], input_data)
        data_selection_dict = metadata_res["data_selection_result"]
        # If no tables selected, then fail
        if not metadata_res["selected_tables"]:
            chat_log.error("SQLDataFinderAgent | invoke_agent | No tables selected.")
            conversation.data_retrieval_status = ConversationConclusion.CANT_FIND_DATA
            conversation.current_question_raw_answer = (
                "I couldn't find the relevant data to answer the question." + data_selection_dict["reasoning"]
            )
            return self.on_success(input_data, data_selection_dict)

        data_selection_dict["table_metadata"] = metadata_res["table_metadata"]
        data_selection_dict["selected_tables"] = metadata_res["selected_tables"]
        data_selection_dict["other_table_columns"] = metadata_res["other_table_columns"]
        # Set the actual business rules from indices
        data_selection_dict["business_rules"] = self.data_dictionary_service.get_business_rules_by_indexes(
            filtered_business_rule_indices
        )

        # Send selected tables to frontend - TODO: Handle this with new box
        # conversation.active_session.add_section(SectionType.DATA_SOURCE.value)
        # Format tables as objects with source_name, source_type, and section_name
        formatted_tables = []
        new_tables = []
        for table in data_selection_dict["selected_tables"]:
            _table = FormattedDataSource(
                source_name=self.data_source_name,
                source_type=self.data_dictionary_service.get_data_source_type(self.data_source_name),
                section_name=table,
                category=DataSourceCategory.DATABASE.value,
            )
            formatted_tables.append(_table)
            if _table not in conversation.active_session.session_used_data_sources_all:
                new_tables.append(_table)
                conversation.active_session.update_session_used_data_sources_all(_table)

        conversation.active_session.data_sources_info_list = formatted_tables

        # Update the frontend with data sources tab and data sources blocks
        if new_tables:
            # send data sources tab to frontend
            if FrontendTabContentType.SOURCES not in conversation.active_session.session_used_tabs_all:
                conversation.active_session.update_session_used_tabs_all(FrontendTabContentType.SOURCES)
                conversation.persist_and_stream_handler(
                    block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.TAB_CONTENT.suffix_value}",
                    block_type=FrontendBlockType.TAB_CONTENT,
                    block_data=[
                        {
                            "id": FrontendTabContentType.SOURCES.tab_type_value,
                            "title": FrontendTabContentType.SOURCES.tab_title_value,
                            "status": conversation.active_session.session_status.value,
                            "session_type": conversation.active_session.session_type.value,
                        }
                    ],
                    block_tab_types=[FrontendTabContentType.TABS],
                )

            # send data sources blocks to frontend
            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.DATA_SOURCES.suffix_value}",
                block_type=FrontendBlockType.DATA_SOURCES,
                block_data=new_tables,
                block_tab_types=[
                    FrontendTabContentType.SOURCES,
                    FrontendTabContentType.ANSWER,
                    FrontendTabContentType.FINAL_CONCLUSION,
                ],
            )

        conversation.active_session.add_to_queue(
            conversation.active_session.data_sources_info_list,
            "assistant",
            ConversationStatus.IN_PROGRESS,
            MessageContentType.DATA_SOURCE.value,
        )
        self.add_to_history(
            conversation,
            "assistant",
            [conversation.active_session.data_sources_info_list],
            False,
            True,
            MessageContentType.DATA_SOURCE.value,
        )

        # conversation.persist_and_stream_handler(
        #     block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
        #     block_type=FrontendBlockType.MARKDOWN,
        #     block_data=data_selection_dict["reasoning"],
        #     block_tab_types=[FrontendTabContentType.TASKS],
        # )

        chat_log.debug("SQLDataFinderAgent | invoke_agent | End")
        return self.on_success(input_data, data_selection_dict)

    def _filter_metadata_llm(
        self,
        conversation: Conversation,
        user_question: str,
        follow_up_history: list,
        current_session_history: list,
        table_metadata_all: dict,
        global_business_rules: dict,
        chat_log: Logger,
    ):
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data="Identifying related tables and columns...\n\n",
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )
        metadata_res = {
            "is_valid": False,
            "error_message": "",
        }
        # # Convert the dictionary 'table_metadata_all' to XML
        # table_metadata_str: str = metadata_dict_to_xml_str(table_metadata_all)
        # Convert the json to more readable string by adding new lines and indentation
        table_metadata_str = json.dumps(table_metadata_all, indent=2)
        business_rules_str = json.dumps(global_business_rules, indent=2)
        all_tables_list = []
        for table_index, table_data in table_metadata_all.items():
            all_tables_list.append(table_data["table_name"])

        er_diagram = self.data_dictionary_service.get_er_diagram_for_data_source(self.data_source_name)
        # er_diagram_str = er_dict_to_xml_str(er_diagram)
        er_diagram_str = json.dumps(er_diagram, indent=2)
        # If the model is deepseek, then cut off metadata to fit with max length of 100000
        # table_metadata_str = str(table_metadata_all)
        if self.llm_model_name.lower().startswith("deepseek"):
            table_metadata_str = str(table_metadata_str)[:100000]
        prompt_messages = [
            {
                "role": "user",
                "content": "Table columns and business rules:\n" + table_metadata_str + "\n",
            }
        ]
        if global_business_rules:
            prompt_messages.append(
                {"role": "user", "content": "General Business Rules:\n" + business_rules_str + "\n"}
            )
        if er_diagram:
            prompt_messages.append({"role": "user", "content": "ER Diagram:\n" + er_diagram_str + "\n"})

        if follow_up_history:
            prompt_messages.extend(follow_up_history)
        if current_session_history:
            prompt_messages.extend(current_session_history)

        prompt_messages.append(
            {
                "role": "user",
                "content": f"User Question: {user_question}",
            }
        )
        data_selection_dict = None
        try_count = 0
        while try_count < 3:
            structured_output, unstructured_output = self.invoke_llm(
                conversation,
                self.main_system_instruction,
                prompt_messages,
                chat_log,
                response_format=data_find_output_schema,
                additional_structuring_instructions="Extract selected table columns.",
                is_stream=False,
            )
            if structured_output is None:
                chat_log.error("SQLDataFinderAgent | invoke_agent | Failure.")
                metadata_res["error_message"] = "Failure in metadata selection."
                return metadata_res

            data_selection_dict = get_dict_from_json_or_python(structured_output)
            data_selection_dict["reasoning"] = unstructured_output

            metadata_res = self._get_metadata_for_table_columns(
                data_selection_dict["table_columns"], all_tables_list, chat_log
            )
            metadata_res["data_selection_result"] = data_selection_dict
            if metadata_res["is_valid"]:
                break

            conversation.persist_and_stream_handler(
                block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
                block_type=FrontendBlockType.MARKDOWN,
                block_data=metadata_res["error_message"],
                block_tab_types=[FrontendTabContentType.TASKS],
                user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
            )

            if try_count < 2:
                prompt_messages.append({"role": "assistant", "content": structured_output})
                prompt_messages.append(
                    {
                        "role": "user",
                        "content": str(metadata_res["error_message"])
                        + ". Please double check the table and column names with the provided list.",
                    }
                )
                try_count += 1
                continue
            else:
                chat_log.error("SQLDataFinderAgent | invoke_agent | " + metadata_res["error_message"])
                metadata_res["is_valid"] = False
                return metadata_res

        return metadata_res

    def _get_metadata_from_question_llm(
        self,
        conversation: Conversation,
        user_question: str,
        table_metadata_all: dict,
        chat_log: Logger,
    ):
        metadata_res = {
            "is_valid": False,
            "error_message": "",
        }
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data="Extracting tables and columns from the request...\n\n",
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
            user_visibility=DataBlockVisibility.SUPER_ADMIN_ONLY,
        )
        all_table_column_names = []
        all_tables_list = []
        for table_name, table_data in table_metadata_all.items():
            table_info = {
                "table_name": table_name,
                "column_names": [],
            }
            for field_name in table_data["fields"].keys():
                table_info["column_names"].append(field_name)
            all_tables_list.append(table_name)
            all_table_column_names.append(table_info)

        prompt_messages = [
            {
                "role": "user",
                "content": "User Question:\n" + user_question + "\n",
            },
            {
                "role": "user",
                "content": "All available Tables and Columns:\n" + str(all_table_column_names) + "\n",
            },
        ]

        data_selection_dict = None
        try_count = 0
        while try_count < 3:
            structured_output = self.invoke_llm(
                conversation,
                self.metadata_extraction_instructions,
                prompt_messages,
                chat_log,
                data_find_output_schema,
            )
            if structured_output is None:
                chat_log.error("SQLDataFinderAgent | invoke_agent | Failure.")
                metadata_res["error_message"] = "Failure in metadata selection."
                return metadata_res
            data_selection_dict = get_dict_from_json_or_python(structured_output)
            data_selection_dict["reasoning"] = "Extracted the tables and columns from the request."
            metadata_res = self._get_metadata_for_table_columns(
                data_selection_dict["table_columns"], all_tables_list, chat_log
            )
            metadata_res["data_selection_result"] = data_selection_dict
            if metadata_res["is_valid"]:
                break
            if try_count < 2:
                prompt_messages.append({"role": "assistant", "content": structured_output})
                prompt_messages.append(
                    {
                        "role": "user",
                        "content": "Please double check the table and column names with the provided list.",
                    }
                )
                try_count += 1
                continue
            else:
                chat_log.error("SQLDataFinderAgent | invoke_agent | " + metadata_res["error_message"])
                metadata_res["is_valid"] = False
                break
        return metadata_res

    def _get_metadata_for_table_columns(self, selected_table_columns: list, all_tables_list: list, chat_log: Logger):
        """
        Verify the selected columns are valid and Get metadata for selected tables and columns
        Parameters:
            selected_table_columns (list): List of selected tables and columns
                [
                    {
                        "table_name": "<table1>",
                        "columns": ["column1", "column2"]
                    },
                    ...
                ]
            List of all the available tables
        Returns:
            Dictionary containing metadata for the each table and column in the format:
            {
                "is_valid": true/false,
                "error_message": "Error message if is_valid is false",
                "selected_tables": {
                    "table1": {
                        "fields": {
                            "column1": {"name": "column1", "dataType": "string", "description": "Column 1 description", "availableValues": ["value1", "value2"]}
                        }
                    }
                },
                "other_table_columns": {
                    "table2": ["column3", "column4"]
                }
            }
        """
        if not selected_table_columns:
            return {
                "is_valid": True,
                "selected_tables": {},
            }
        selected_tables_list = [table_data["table_name"] for table_data in selected_table_columns]
        # Check if all selected tables are valid
        invalid_table = []
        for table_name in selected_tables_list:
            if table_name not in all_tables_list:
                invalid_table.append(table_name)
        if invalid_table:
            chat_log.debug(f"These table selected is invalid: {str(invalid_table)}")
            return {
                "is_valid": False,
                "error_message": "These table you selected is invalid: " + str(invalid_table),
            }

        table_metadata = self.data_dictionary_service.get_metadata_for_table_list(
            selected_tables_list,
            is_skip_business_rules=True,
            is_skip_table_description=True,
            data_source_name=self.data_source_name,
        )
        # Check if all selected columns are valid
        invalid_columns = []
        for table_data in selected_table_columns:
            table_name = table_data["table_name"]
            if table_name not in table_metadata:
                invalid_columns.append(f"{table_name}.<all columns>")
                continue
            column_list = table_data["columns"]
            for column_name in column_list:
                if column_name not in table_metadata[table_name]["fields"]:
                    invalid_columns.append(f"{table_name}.{column_name}")
        if invalid_columns:
            chat_log.debug(f"These columns selected are invalid: {str(invalid_columns)}")
            return {
                "is_valid": False,
                "error_message": "These columns you selected are invalid: " + str(invalid_columns),
            }

        # Filter out the columns that are not selected
        for table_name, table_data in table_metadata.items():
            table_data["fields"] = {
                field_name: field_data
                for field_name, field_data in table_data["fields"].items()
                if field_name
                in [
                    table_data["columns"]
                    for table_data in selected_table_columns
                    if table_data["table_name"] == table_name
                ][0]
            }
        return {
            "is_valid": True,
            "selected_tables": selected_tables_list,
            "table_metadata": table_metadata,
            "other_table_columns": self._get_other_table_columns_by_names(
                all_tables_list, selected_tables_list, selected_table_columns
            ),
        }

    def _get_metadata_for_table_column_indexes(
        self, metadata_dict: dict, selected_table_column_indexes: list, all_tables_list: list, chat_log: Logger
    ):
        """
        Verify the selected columns are valid and Get metadata for selected tables and columns
        Parameters:
            selected_table_columns (list): List of selected tables and column indexes
                [
                    {
                        "table_index": 1,
                        "column_indices": [1, 2]
                    },
                    ...
                ]
            List of all the available tables
        Returns:
            Dictionary containing metadata for the each table and column in the format:
            {
                "is_valid": true/false,
                "error_message": "Error message if is_valid is false",
                "selected_tables": {
                    "table1": {
                        "fields": {
                            "column1": {"name": "column1", "dataType": "string", "description": "Column 1 description", "availableValues": ["value1", "value2"]}
                        }
                    }
                },
                "other_table_columns": {
                    "table2": ["column3", "column4"]
                }
            }
        """
        if not selected_table_column_indexes:
            metadata_dict["is_valid"] = True
            metadata_dict["selected_tables"] = {}
            return

        selected_table_index_list = [table_data["table_index"] for table_data in selected_table_column_indexes]
        # Validate index range
        if any(i < 1 or i > len(all_tables_list) for i in selected_table_index_list):
            metadata_dict["is_valid"] = False
            metadata_dict["error_message"] = "These table indices you selected are invalid: " + str(
                selected_table_index_list
            )
            return

        selected_tables_list = [all_tables_list[i - 1] for i in selected_table_index_list]

        table_metadata = self.data_dictionary_service.get_metadata_for_table_list(
            selected_tables_list,
            is_skip_business_rules=True,
            is_skip_table_description=True,
            data_source_name=self.data_source_name,
        )
        # Check if all selected columns are valid
        invalid_columns = []
        for table_data in selected_table_column_indexes:
            table_name = all_tables_list[table_data["table_index"] - 1]
            column_list = table_data["column_indices"]
            # Convert fields dictionary to list of field names to help find index
            table_column_names = list(table_metadata[table_name]["fields"].keys())
            for column_index in column_list:
                # Check the range of column index
                if column_index < 1 or column_index > len(table_column_names):
                    invalid_columns.append(f"{table_name}.{column_index}")
                    continue

        if invalid_columns:
            chat_log.debug(f"These column indices selected are invalid: {str(invalid_columns)}")
            metadata_dict["is_valid"] = False
            metadata_dict["error_message"] = "These column indices you selected are invalid: " + str(invalid_columns)
            return

        # Filter out the columns that are not selected
        for table_name, table_data in table_metadata.items():
            # Find the corresponding table data from selected_table_columns
            table_index = all_tables_list.index(table_name) + 1
            selected_table_data = next(
                (data for data in selected_table_column_indexes if data["table_index"] == table_index), None
            )

            if selected_table_data:
                # Get the list of column names for this table
                table_column_names = list(table_data["fields"].keys())

                # Get the selected column names based on indices
                selected_column_names = [
                    table_column_names[col_index - 1]
                    for col_index in selected_table_data["column_indices"]
                    if 1 <= col_index <= len(table_column_names)
                ]

                # Filter the fields to only include selected columns
                table_data["fields"] = {
                    field_name: field_data
                    for field_name, field_data in table_data["fields"].items()
                    if field_name in selected_column_names
                }

        # Get other table and column names that not selected
        metadata_dict["is_valid"] = True
        metadata_dict["selected_tables"] = selected_tables_list
        metadata_dict["table_metadata"] = table_metadata
        metadata_dict["other_table_columns"] = self._get_other_table_columns_by_indexes(
            all_tables_list, selected_table_column_indexes, selected_tables_list
        )

    def _get_other_table_columns_by_indexes(
        self, all_tables_list: list, selected_table_column_indexes: list, selected_tables_list: dict
    ):
        other_table_column_names = {}
        all_table_columns_list = self.data_dictionary_service.get_metadata_for_table_list(
            [], True, True, True, True, self.data_source_name
        )
        # First find other columns in the selected tables
        for table_data in selected_table_column_indexes:
            table_name = all_tables_list[table_data["table_index"] - 1]
            all_columns_metadata = all_table_columns_list[table_name]["fields"]
            for column_index, column_data in all_columns_metadata.items():
                if column_index not in table_data["column_indices"]:
                    if table_name not in other_table_column_names:
                        other_table_column_names[table_name] = []
                    other_table_column_names[table_name].append(column_index)
        # Then find other tables that are not in the selected tables
        for table_name, table_data in all_table_columns_list.items():
            if table_name not in selected_tables_list:
                other_table_column_names[table_name] = list(table_data["fields"].keys())
        return other_table_column_names

    def _get_other_table_columns_by_names(
        self, all_tables_list: list, selected_tables_list: list, selected_table_columns: list
    ):
        other_table_column_names: dict[str, list[str]] = {}
        all_table_columns_list = self.data_dictionary_service.get_metadata_for_table_list(
            [], True, True, True, True, self.data_source_name
        )

        for table_name, table_data in all_table_columns_list.items():
            # First find other tables that are not in the selected tables
            if table_name not in selected_tables_list:
                other_table_column_names[table_name] = list(table_data["fields"].keys())
            # Then find other columns in the selected tables
            else:
                # Find the selected columns for this table
                selected_table_data = next(
                    (data for data in selected_table_columns if data["table_name"] == table_name), None
                )
                if selected_table_data:
                    selected_columns = selected_table_data["columns"]
                    for column_name, column_data in table_data["fields"].items():
                        if column_name not in selected_columns:
                            if table_name not in other_table_column_names:
                                other_table_column_names[table_name] = []
                            other_table_column_names[table_name].append(column_name)
        return other_table_column_names

    def update_followup_history(self, conversation: Conversation, chat_log: Logger, output_data: dict):
        # Add user question to history
        self.add_to_history(
            conversation,
            "user",
            ["Previous User Question: " + conversation.user_inputs[-1]],
            is_required_for_follow_ups=True,
            is_skip_for_current_session=True,
        )
        # Add selected table columns to history
        if "table_metadata" in output_data:
            self.add_to_history(
                conversation,
                "assistant",
                [
                    "Previous Selected Table Columns: "
                    + str(output_data["table_metadata"] if "table_metadata" in output_data else [])
                ],
                is_required_for_follow_ups=True,
            )
        # Add selected business rules to history
        if "business_rules" in output_data:
            self.add_to_history(
                conversation,
                "assistant",
                [
                    "Previous Selected Business Rules: "
                    + str(output_data["business_rules"] if "business_rules" in output_data else [])
                ],
                is_required_for_follow_ups=True,
            )

    def on_invoke_limit_reached(self, input_data: dict, conversation: Conversation, chat_log: Logger, try_count: int):
        # If invoke limit reached, then fail
        chat_log.error("SQLDataFinderAgent | on_invoke_limit_reached | Invoke limit reached.")
        conversation.data_retrieval_status = ConversationConclusion.CANT_FIND_DATA
        return "I couldn't find the relevant data for the question. Please provide more context with your question and try again."

    def filter_business_rules(
        self,
        conversation: Conversation,
        chat_log: Logger,
        user_question: str,
    ):
        conversation.persist_and_stream_handler(
            block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
            block_type=FrontendBlockType.MARKDOWN,
            block_data="Identifying related business rules...\n\n",
            block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        )
        _system_instruction = ""

        # open file
        with open(
            "instructions/tools/data_finding/system_instructions_business_rules.txt", "r", encoding="utf-8"
        ) as file:
            _system_instruction = file.read()

        all_business_rules = self.data_dictionary_service.get_business_rules(is_global_rules_only=False)

        prompt_messages = [
            {
                "role": "user",
                "content": "Business Rules: " + json.dumps(all_business_rules, indent=2),
            },
            {
                "role": "user",
                "content": "User Question: " + user_question,
            },
        ]
        structured_output = self.invoke_llm(
            conversation,
            _system_instruction,
            prompt_messages,
            chat_log,
            business_rule_output_schema,
        )

        filtered_business_rule_indices = get_dict_from_json_or_python(structured_output)["business_rule_indices"]

        filtered_business_rules = self.data_dictionary_service.get_business_rules_by_indexes(
            filtered_business_rule_indices
        )

        filtered_business_rules_str = array_to_markdown(filtered_business_rules, "Business Rules")

        # # stream and persist the filtered business rules
        # conversation.persist_and_stream_handler(
        #     block_id=f"{conversation.active_session.session_id}.{FrontendBlockType.MARKDOWN.suffix_value}",
        #     block_type=FrontendBlockType.MARKDOWN,
        #     block_data=filtered_business_rules_str,
        #     block_tab_types=[FrontendTabContentType.ANSWER, FrontendTabContentType.TASKS],
        # )

        return filtered_business_rule_indices
